/**下单类型 */
export const orderTypes = [
  // {
  //   label: "ID拍单",
  //   value: "paidan",
  //   disabled: false,
  // },
  // {
  //   label: "多多进宝",
  //   value: "jinbao",
  //   disabled: false,
  // },
  {
    label: "多多果园",
    value: "guoyuan",
    disabled: false,
  },
  {
    label: "多多批发",
    value: "pifa",
    disabled: false,
    tooltip: true,
    tooltipContent: "每单最低两件",
  },
  {
    label: "关键词",
    value: "keyword",
    disabled: false,
  },
  {
    label: "万人团",
    value: "wanrentuan",
    disabled: false,
  },
  {
    label: "活动",
    value: "activity",
    showInCreate:false,
    disabled: false,
  },
  {
    label: "赠品",
    value: "gift",
    disabled: false,
    showInCreate:false,
  },
  {
    label: "全球购",
    value: "globalBuy",
    disabled: false,
    showInCreate:false,
  },
];
// 下单方式
export const orderModes: Array<{
  label: string;
  value: string;
  disabled: (data: {order_type:string,isGroupOrder:boolean}) => boolean;
}> = [
  {
    label: "开团",
    value: "open_group",
    disabled: ({ order_type,isGroupOrder }) => false,
  },
  {
    label: "参团",
    value: "spell_group",
    disabled: ({ order_type,isGroupOrder }) => order_type === "wanrentuan" || !isGroupOrder, 
  },
  {
    label: "单独购买",
    value: "alone",
    disabled: ({ order_type,isGroupOrder }) => order_type === "wanrentuan" ,
  },
];
