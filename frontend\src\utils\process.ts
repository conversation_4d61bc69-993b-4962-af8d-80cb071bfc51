import { systemProcessList } from "../apis/mainWindow";

/**获取系统中的软件 */
export function getProcessList() {
    return systemProcessList()
      .then(res => {
        let flag = false
        res.forEach(processMessage => {
          let processName = processMessage[0]; //processMessage[0]进程名称 ， processMessage[1]进程id
          if (processName.toUpperCase().includes('FIDDLER')) {
            flag = true
          }
        })
        return flag
      })
  }