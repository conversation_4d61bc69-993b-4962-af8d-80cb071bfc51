{"name": "ckpdd", "version": "1.2.14", "description": "---", "main": "main.js", "scripts": {"start": "electron . ", "dev": "electron . --env=local", "reload": "nodemon --config ./electron/config/nodemon.json", "test": "set DEBUG=* && electron . --env=local", "build-w": "electron-builder -w=nsis --ia32", "build-w-64": "electron-builder -w=nsis --x64", "build-w-arm64": "electron-builder -w=nsis --arm64", "build-wz": "electron-builder -w=7z --ia32", "build-wz-64": "electron-builder -w=7z --x64", "build-wz-arm64": "electron-builder -w=7z --arm64", "build-m": "electron-builder -m", "build-m-arm64": "electron-builder -m --arm64", "build-l": "electron-builder -l=deb --ia32", "build-l-64": "electron-builder -l=deb --x64", "build-l-arm64": "electron-builder -l=deb --arm64", "build-l-armv7l": "electron-builder -l=deb --armv7l", "build-lr-64": "electron-builder -l=rpm --x64", "build-lp-64": "electron-builder -l=pacman --x64", "rd": "ee-core rd --dist_dir=./frontend/dist", "encrypt": "ee-core encrypt", "rebuild": "electron-rebuild", "re-sqlite": "electron-rebuild -f -w better-sqlite3"}, "build": {"productName": "CK多多助手", "appId": "ck", "copyright": "xp", "directories": {"output": "out"}, "asar": true, "files": ["**/*", "!frontend/", "!run/", "!logs/", "!data/", "!electron/", "!resources", "!temp"], "extraResources": {"from": "./build/extraResources/", "to": "extraResources"}, "electronDownload": {"mirror": "https://npmmirror.com/mirrors/electron/"}, "nsis": {"oneClick": false, "allowElevation": true, "perMachine": true, "allowToChangeInstallationDirectory": true, "installerIcon": "./build/icons/icon.ico", "uninstallerIcon": "./build/icons/icon.ico", "installerHeaderIcon": "./build/icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "CK多多助手", "include": "./build/script/installer.nsh"}, "publish": [{"provider": "generic", "url": "https://github.com/wallace5303/electron-egg"}], "mac": {"icon": "./build/icons/icon.icns", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": ["dmg", "zip"]}, "win": {"requestedExecutionLevel": "requireAdministrator", "icon": "./build/icons/icon.ico", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": [{"target": "nsis"}]}, "linux": {"icon": "./build/icons/icon.icns", "artifactName": "${productName}-${os}-${version}-${arch}.${ext}", "target": ["deb"], "category": "Utility"}}, "repository": "https://github.com/wallace5303/ee.git", "keywords": ["Electron"], "author": "none", "license": "Apache", "devDependencies": {"debug": "^4.3.3", "electron": "^13.6.9", "electron-builder": "^23.1.0", "electron-rebuild": "^3.2.8", "eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "node-gyp-build": "4.8.0", "nodemon": "^2.0.16"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "1.4.1", "anyproxy": "^4.1.3", "axios": "^1.3.4", "better-sqlite3": "^7.6.0", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "ee-core": "^1.5.1", "electron-is": "^3.0.0", "electron-updater": "^5.3.0", "false": "^0.0.4", "ffi-napi": "^4.0.3", "fluent-ffmpeg": "^2.1.2", "form-data": "^4.0.0", "iconv-lite": "^0.6.3", "jimp": "^0.22.8", "jschardet": "^3.0.0", "lodash": "^4.17.21", "node-xlsx": "^0.21.2", "ref-napi": "^3.0.3", "uuid": "^9.0.0"}}