import { defineStore } from "pinia";
import { ipc } from "../apis/config";
import { random } from "../utils/common";
import { cloneDeep } from "lodash";

type paySetting = {
  type: "code" | "auto";
  chance: "immediate" | "delay";
  zfb_pass: string;
  winCount: number;
  isNew:boolean
};
type addressSetting = {
  type: "random" | "diy";
  filterStr: string;
  /**指定地址 */
  appoint_address: string;
  nameCode_active: boolean;
  nameCode_position: "before" | "after";
  nameCode_str: string;
  addr_active: boolean;
  addr_position: "before" | "after";
  addr_str: string;
};

type visitSetting = {
  compare: {
    max: number;
    min: number;
    active: boolean;
  };
  visitDetails: {
    max: number;
    min: number;
    active: boolean;
  };
  visitCount: {
    max: number;
    min: number;
    active: boolean;
  };
  orderDelay: {
    max: number;
    min: number;
    active: boolean;
  };
  payDelay: {
    max: number;
    min: number;
    active: boolean;
  };
  confirm: {
    max: number;
    min: number;
    active: boolean;
  };
};

type chatSetting = {
  active: boolean;
  type: "random" | "order";
  num: number;
  content: string;
};

type TaskCreate = {
  pifaTargetPrice: number;
  othersTargetDiscount: number;
  /**自动供设货 */
  auto_supply: boolean;
  /**自动改价 */
  auto_change_price: boolean;

  setting: string[];

  type: string;
};

type exportOrder = {
  savePath: string;
  config: string[];
};

type State = {
  pay: paySetting;
  address: addressSetting;
  visit: visitSetting;
  chat: chatSetting;
  taskCreate: TaskCreate;
  appWindow: appWindow;
  orderManage: OrderManage;
  exportOrder: exportOrder;
  others:Others
};

type appWindow = {
  scale: number;
  appWidth: number;
  appHeight: number;
  pddWidth: number;
  pddHeight: number;
  pddx: number;
  pddy: number;
};

type OrderManage = {
  commentFolder: string;
};

type Others = {
  installCa:boolean
}

type settingItem = keyof State;

export const useSetting = defineStore("use-setting", {
  state() {
    const state: State = {
      pay: {
        type: "auto",
        chance: "delay",
        zfb_pass: "",
        winCount: 1,
        isNew:true,
      },
      address: {
        type: "random",
        /** 过滤地址*/
        filterStr: "新疆维吾尔自治区,西藏自治区,青海省,宁夏回族自治区,吉林省,辽宁省,北京市,云南省,甘肃省,贵州省,海南省,黑龙江省,内蒙古自治区,上海市,天津市",

        appoint_address: "",
        nameCode_active: true,
        nameCode_position: "after",
        nameCode_str: "a",
        addr_active: true,
        addr_position: "after",
        addr_str: "b",
      },
      visit: {
        compare: {
          min: 0,
          max: 0,
          active: false,
        },
        visitDetails: {
          min: 0,
          max: 0,
          active: false,
        },
        visitCount: {
          min: 0,
          max: 0,
          active: false,
        },
        orderDelay: {
          min: 0,
          max: 0,
          active: false,
        },
        payDelay: {
          min: 0,
          max: 0,
          active: false,
        },
        confirm: {
          min: 0.5,
          max: 0.5,
          active: true,
        },
      },
      chat: {
        active: false,
        type: "order",
        num: 1,
        content: "",
      },
      taskCreate: {
        pifaTargetPrice: 0.01,
        othersTargetDiscount: 1,
        auto_change_price: true,
        auto_supply: true,
        setting: [],
        type: "pifa",
      },
      appWindow: {
        scale: (1 / window.devicePixelRatio) * 100,
        appWidth: 1550,
        appHeight: 925,
        pddWidth: 412,
        pddHeight: 818,
        // pddx: 13,
        pddx: 1125,
        // pddy: 86,
        pddy: 80,
      },
      orderManage: {
        commentFolder: "",
      },
      exportOrder: {
        savePath: "",
        config: [
          "shop_name",
          "add_time",
          "type",
          "order_status",
          "order_sn",
          "order_amount",
          "goods_number",
          "goods_id",
          "goods_name",
          "sku_spec",
          "express",
          "tracking_number",
          "recipient",
        ],
      },
      others:{
        installCa:false
      }
    };
    return state;
  },
  getters: {
    chatContentStr(): string[] {
      const { content } = this.chat;
      return content.split("\n").filter((item) => item);
    },
    zfbPassArr(): Array<string | number> {
      if (!this.pay.zfb_pass) {
        return [];
      }
      const passSplit = this.pay.zfb_pass.split("").filter((item) => item);
      const passArr: string[] = [];
      passSplit.forEach((item, ind) => {
        passArr.push(passSplit.slice(0, ind + 1).join(""));
      });
      return passArr;
    },
  },
  actions: {
    getOrderSetting(key: settingItem) {
      // return ipc.invoke("controller.setting.getSetting", key)
      return this.getAnySetting(key).then((res) => {
        if (res) {
          if (key === "visit" && !res.confirm) {
            res.confirm = this.visit.confirm;
          }else if(key === 'appWindow'){
            res.pddx = this.appWindow.pddx
            res.pddy = this.appWindow.pddy
          }
          this[key] = res;
        }

        return res;
      });
    },
    getOrderSettingAll() {
      (Object.keys(this.$state) as Array<settingItem>).forEach((key) => {
        this.getOrderSetting(key);
      });
    },
    setOrderSetting<T extends settingItem, K extends State>(
      key: T,
      value: K[T]
    ) {
      // return ipc.invoke("controller.setting.setSetting", { key, value });
      return this.setAnySetting(key, value);
    },
    getChatArr(type?: "order" | "random", count?: number): string[] {
      if (!this.chatContentStr.length) {
        return [];
      }
      const _type = type || this.chat.type;
      const max = count || this.chat.num;
      const txtArr: string[] = [];
      let index: number = -1;
      const resetIndex = () => {
        switch (_type) {
          case "order": {
            index = ++index % this.chatContentStr.length;
            break;
          }
          case "random": {
            index = random(0, this.chatContentStr.length);
          }
        }
      };

      for (let i = 0; i < max; i++) {
        resetIndex();
        txtArr.push(this.chatContentStr[index] || "");
      }
      return txtArr;
    },
    getAnySetting(key: string) {
      return ipc.invoke("controller.setting.getSetting", key);
    },
    setAnySetting(key: string, value: anyObj | number | string) {
      const data = { key, value }
      return ipc.invoke("controller.setting.setSetting", cloneDeep(data));
    },
  },
});
