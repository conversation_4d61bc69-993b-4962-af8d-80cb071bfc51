<template>
  <div class="ds-scroll">
    <div class="left-button" @click="scroll('left')">
      <slot name="leftButton">
        <el-link :underline="false">
          <el-icon><ArrowLeft /></el-icon>
        </el-link>
      </slot>
    </div>
    <el-scrollbar ref="scrollbar">
      <div class="ds-scroll-container" ref="scrollContainer">
        <div
          class="item"
          :class="{ 'is-active': item[options.pk] === state.active }"
          v-for="item in list"
          :key="item[options.pk]"
        >
          <slot name="scrollItem">
            <el-link :underline="false" @click="change(item)">{{
              item[options.label]
            }}</el-link>
          </slot>
        </div>
      </div>
    </el-scrollbar>
    <div class="right-button" @click="scroll('right')">
      <slot name="rightButton">
        <el-link :underline="false">
          <el-icon><ArrowRight /></el-icon>
        </el-link>
      </slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { ScrollbarInstance } from "element-plus";
import { nextTick, onMounted, reactive, ref, watch } from "vue";

const emits = defineEmits(["update:modelValue", "change"]);
const props = defineProps<{
  modelValue: string | number;
  list: anyObj[];
  option?: {
    pk?: string;
    label?: string;
  };
}>();

const options: { pk: string; label: string } = Object.assign(
  { pk: "value", label: "label" },
  props.option || {}
);

const state = reactive({
  active: props.modelValue,
});

watch(()=>props.modelValue,(val)=>{
    if(val !== state.active){
        state.active = val
    }
},{immediate:true})

function change(item: anyObj) {
  state.active = item[options.pk];
  emits("change", item[options.pk], item);
  emits("update:modelValue", item[options.pk]);
}

const scrollContainer = ref<HTMLDivElement>();
const scrollbar = ref<ScrollbarInstance>();
const scrollState = reactive({
  max: 0,
  step: 0,
  current: 0,
});

function scrollHandle(start: number, result: number) {
  //   console.log(val);

  const step = 20;
  function scrollRepeat(end: number) {
    if (end === result) {
      return;
    }
    scrollbar.value?.setScrollLeft(end);
    setTimeout(() => {
      if (start > result) {
        let _end = end - step;
        scrollRepeat(_end < result ? result : _end);
      } else {
        let _end = end + step;
        scrollRepeat(_end > result ? result : _end);
      }
    }, 16);
  }

  scrollRepeat(start)
}

function scroll(type: "left" | "right") {
  scrollState.step = Math.floor((scrollbar.value!.$el.offsetWidth / 3) * 2);
  scrollState.max = scrollContainer.value!.offsetWidth - scrollState.step;
  const start = scrollState.current;
  let result: number;
  const { step, max } = scrollState;
  switch (type) {
    case "left": {
      result = scrollState.current - step;
      result = result < 0 ? 0 : result;
      break;
    }
    case "right": {
      result = scrollState.current + step;
      result = result > max ? max : result;
    }
  }

  scrollState.current = result;
  scrollHandle(start, result);
}
</script>
<style lang="scss" rel="stylesheet/scsss">
.ds-scroll {
  --ds-scroll-height: 32px;
  --ds-scroll-gap: 8px;
  height: var(--ds-scroll-height);
  display: flex;
  .el-link {
    padding: 5px 12px;
    background-color: var(--el-fill-color-light);
    border-radius: 6px;
    flex-shrink: 0;
    height: 100%;
    box-sizing: border-box;
  }
  .el-scrollbar {
    margin: 0 var(--ds-scroll-gap);
    flex-grow: 1;
    .el-scrollbar__bar {
      display: none;
    }
    .el-scrollbar__view {
      position: relative;
    }
  }
  .ds-scroll-container {
    width: max-content;
    display: flex;
    height: var(--ds-scroll-height);
    .item {
      border-radius: 6px;
      margin-left: var(--ds-scroll-gap);
      flex-shrink: 0;
      &:first-child {
        margin-left: 0;
      }
      &.is-active {
        font-weight: bolder;
        color: var(--el-fill-color-blank);
        background-color: var(--el-color-primary);
        .el-link {
          color: var(--el-fill-color-blank);
          background-color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>
