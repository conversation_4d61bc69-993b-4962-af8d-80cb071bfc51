import { useSetting } from "../stores/setting";
import { ipc } from "./config";
const controller = 'controller.analyze.'
export function start_wechat():Promise<{
    success:boolean
    msg:string
}>{
    const {installCa} = useSetting().others
    return ipc.invoke(controller + 'start_wechat',{install:installCa})
}
export function stop_wechat():Promise<string>{
    return ipc.invoke(controller + 'stop_wechat')
}
export function execJStoAnalyzeWindow(str:string){
    return ipc.invoke(controller + 'execJStoAnalyzeWindow',str)
}