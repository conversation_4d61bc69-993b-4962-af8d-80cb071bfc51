const { Controller } = require("ee-core");
const Storage = require("ee-core").Storage;



class SettingController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  //   下单设置
  async getSetting(item) {
    const settingJson = Storage.JsonDB.connection("setting");
    return settingJson.getItem(item);
  }
  async setSetting({key,value}) {
    const settingJson = Storage.JsonDB.connection("setting");
    settingJson.setItem(key, value);
  }
}

SettingController.toString = () => "[class SettingController]";
module.exports = SettingController;
