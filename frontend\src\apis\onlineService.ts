import { LoadingOptions } from "element-plus";
import request, { Options } from "./page";

/**产品列表 */
export function getGoodsList(
  data = {},
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/serve/getGoodsList",
      method: "get",
      data,
    },
    options,
    loading
  );
}

/** 订单列表 */
export function getOrderList(
  data: {
    limit?: number;
    page?: number;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/serve/getOrderList",
      data,
    },
    options,
    loading
  );
}

/**发起支付 */
export function buyGoods(
  data: {
    id: number;
    num: number;
    pay_type: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/serve/buyGoods",
      data,
    },
    options,
    loading
  );
}

/**获取支付状态 */
export function getPayResult(
  data: {
    id: number | string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/serve/getPayResult",
      data
    },
    options,
    loading
  );
}
