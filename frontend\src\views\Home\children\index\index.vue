<template>
  <div class="index no-padding">
    <wechat-analyze ref='wechatAnalyzeEl' v-model="state.wechatAnalyzeShow" @set-goods-info="(data) => {
      state.goodsInfo = data;
      orderCreate.couponList = []
    }" @coupon-list="(list) => orderCreate.couponList = list"></wechat-analyze>
    <h5>
      <div class="left">
        <span>任务设置</span>
        <el-input placeholder="输入商品ID或链接" v-model="state.id" @keyup.enter="goodsDetails" @click="() => {
      paste()
        .then(res => {
          state.id = res
          goodsDetails(false, 'databse')
        })
    }">
          <template #append>
            <el-button type="primary" @click="goodsDetails()"><el-icon class="m-r-5"><Search /></el-icon>解析</el-button>
          </template>
        </el-input>
        <el-button :loading="buttonLoading.goodsDetails_web" :disabled="buttonLoading.goodsDetails_web"
          @click="goodsDetails(true, 'web')">云端解析</el-button>
        <el-button :loading="buttonLoading.goodsDetails_pifa" :disabled="buttonLoading.goodsDetails_pifa"
          @click="goodsDetails(true, 'pifa')">批发解析</el-button>
        <el-button @click="goodsDetails(false, 'wechat')">微信解析</el-button>
        <el-button @click="goodsDetails(true, 'mall')" :loading="buttonLoading.goodsDetails_mall">店铺解析</el-button>
        <el-button :loading="buttonLoading.getGroup_id" v-blur plain type="primary" :icon="Tickets" @click="() => {
      state.goodsLogVisible = true
    }">
        </el-button>

        <!-- <el-button @click="visitTest()">访客测试</el-button> -->
        <!-- <el-button @click="goodsDetails(true, 'test')">测试解析</el-button> -->
      </div>
      <div class="right">
        <el-space>
          <!-- <el-button type="primary" @click="$router.push({path:'/home/<USER>',params:{tab:'CSGift'}})">改百万销量</el-button> -->
          <!-- <CsLimit />
          <changeSales :gooods-info="state.goodsInfo"
            @goods-details="(id) => { state.id = String(id), goodsDetails(false) }"
            :coupon-list="orderCreate.couponList" /> -->
        </el-space>
      </div>
    </h5>

    <!-- 读取记录 -->
    <el-dialog title="商品读取记录" v-model="state.goodsLogVisible" :width="864" top="100px" class="read-log">

      <goodsLogVue :visible="state.goodsLogVisible" :select-goods-record="selectGoodsRecord" @select-goods="(val: string) => {
      state.id = val
      goodsDetails(false)
      state.goodsLogVisible = false
    }" />
      <template #footer>
        <el-button @click="state.goodsLogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <div class="config-box ">
      <div class="img-box able-select">
        <el-image
          :src="state.goodsInfo?.skus.find(item => item.skuId == orderCreate.sku)?.skuImg || state.goodsInfo?.goods_img"
          :preview-src-list="[state.goodsInfo?.skus.find(item => item.skuId == orderCreate.sku)?.skuImg || state.goodsInfo?.goods_img || '']">
          <template #error>
            <span></span>
          </template>
        </el-image>
        <div class="infos">
          <p class="name">
            {{ state.goodsInfo?.goods_name || "-" }}
          </p>
          <p class="other-infos">
            <el-space :size="24">
              <span class="item">
                <span class="label">商品ID：</span>
                <span class="value">
                  {{ state.goodsInfo?.goods_id || "-" }}</span>
              </span>
              <span class="item">
                <span class="label">店铺名称：</span>
                <span class="value">
                  {{ state.goodsInfo?.mallName || "-" }}</span>
              </span>
              <span class="item">
                <span class="label">拼团价格：</span>
                <span class="value">￥{{ state.goodsInfo?.groupPrice || "-" }}</span>
              </span>
              <span class="item">
                <span class="label">独买价格：</span>
                <span class="value">￥{{ state.goodsInfo?.normalPrice || "-" }}</span>
              </span>
            </el-space>
          </p>
          <p class="sku">
            <span class="label">商品规格：</span>

            <el-select v-model="skuState.value" style="width: 120px;flex-grow: 0;" @change="() => {
      orderCreate.sku = selectSku()?.skuId || ''
    }" placement="bottom">
              <el-option v-for="item in skuState.list" :key="item.value" :value="item.value"
                :label="item.label"></el-option>
            </el-select>

            <el-select v-if="!skuState.value.includes('random')" popper-class="sku-select-popper" placement="bottom-end"
              placeholder="请选择" v-model="orderCreate.sku" @change="skuState.value = '1'">
              <el-option v-for="item in state.goodsInfo?.skus || []" :key="item.skuId" :value="item.skuId"
                :label="item.spec + `-拼团价${item.groupPrice}-单买价${item.normalPrice}`">
              </el-option>
            </el-select>
            <template v-else-if="skuState.value === 'random-5'">
              <el-space class="m-l-20">
                <el-input-number v-model="skuState.min_price" placeholder="最小值" :controls="false" :precision="2"
                  :min="0"></el-input-number>
                <span>至</span>
                <el-input-number v-model="skuState.max_price" placeholder="最大值" :controls="false" :precision="2"
                  :min="0"></el-input-number>
              </el-space>
            </template>
          </p>
        </div>
      </div>
      <div class="bottom">
        <el-form :inline="true">
          <el-button type="primary" @click="createTask()" class="btn" :loading="buttonLoading.addTask"><Icon href="icon-circle-plus-filled" style="margin-right: 4px;"></Icon>添加任务</el-button>
          <el-form-item label="订单数量：">
            <el-input-number v-model="orderCreate.order_num" :precision="0" :min="1"></el-input-number>
          </el-form-item>
          <el-divider direction="vertical"></el-divider>
          <el-form-item label="每次购买：">
            <el-input-number v-model="orderCreate.spell_num" :precision="0" :min="1"></el-input-number>
          </el-form-item>
          <el-divider direction="vertical"></el-divider>
          <el-form-item label="小号开始位置：">
            <el-input-number :precision="0" :min="1" :max="subAccoutStore.total || 1"
              :placeholder="`1-${subAccoutStore.total + 1}`" v-model="orderCreate.start_site"></el-input-number>
          </el-form-item>
          <br />
          <el-form-item label="下单类型：">
            <!-- <el-radio-group v-model="orderCreate.type" @change="order_type_change"> -->
            <el-radio-group v-model="settingStore.taskCreate.type" @change="order_type_change">
              <template v-for="item in orderTypes.filter(item => item.showInCreate !== false)">
                <el-radio border :label="item.value" :title="item.tooltipContent" v-if="!item.disabled">{{ item.label
                  }}</el-radio>
              </template>
              <!-- <el-radio border label="paidan">ID拍单</el-radio> -->
              <!-- <el-radio border label="jinbao">多多进宝</el-radio>
            <el-radio border label="pifa">多多批发</el-radio>
                                                                                                                                                                        <el-radio border label="guoyuan">多多果园</el-radio> -->
              <!-- <el-radio border :label="5">历史足迹</el-radio>
            <el-radio border :label="6">商品收藏</el-radio>
            <el-radio border :label="7">拼内购</el-radio>
            <el-radio border :label="8">万人团</el-radio>
            <el-radio border :label="9">限时秒杀</el-radio>
            <el-radio border :label="10">关键词</el-radio>
            <el-radio border :label="11">跟着好评买</el-radio>
                                                                                                                                                                        <el-radio border :label="12">0.01改销量或折扣</el-radio> -->
            </el-radio-group>
          </el-form-item>
          <br />
          <el-form-item label="下单方式：">
            <el-radio-group v-model="orderCreate.mode">
              <!-- <el-radio border label="open_group">开团</el-radio>
            <el-radio border label="spell_group">参团</el-radio>
                                                                                                                      <el-radio border label="alone">单独购买</el-radio> -->
              <el-radio border v-for="{ label, value, disabled } in orderModes" :label="value"
                :disabled="disabled({ order_type: settingStore.taskCreate.type, isGroupOrder: !!state.goodsInfo?.group_order_ids.length })">{{
                label }}</el-radio>
              <el-radio border label="random">随机</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-divider direction="vertical"></el-divider>
        <el-form-item label="优惠券：">
          <el-select v-model="orderCreate.coupon"> </el-select>
                                                                                                                                                                    </el-form-item> -->
          <el-divider direction="vertical"></el-divider>
          <el-form-item label="下单关键词：">
            <el-input v-model="orderCreate.keyword" :disabled="settingStore.taskCreate.type !== 'keyword'"
              placeholder="请输入"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="其他设置：">
            <el-checkbox-group v-model="settingStore.taskCreate.setting">
              <el-checkbox label="collect">商品收藏</el-checkbox>
              <el-checkbox label="attention">店铺关注</el-checkbox>
              <el-checkbox label="coupon" :disabled="!state.goodsInfo?.coupon_code"
                title="需要有可领优惠券且必须关注店铺">领取优惠券</el-checkbox>
              <!-- <el-checkbox>代理IP下单</el-checkbox>
            <el-checkbox>任务提示音</el-checkbox>
                                                                                                                                                                        <el-checkbox>任务同时进行</el-checkbox> -->
            </el-checkbox-group>
          </el-form-item>

          <el-divider direction='vertical'></el-divider>
          <el-form-item label="使用优惠券">
            <el-switch v-model="orderCreate.use_coupon" title="需要有可领优惠券" :disabled="!state.goodsInfo?.coupon_code"
              :active-value="1" :inactive-value="0"></el-switch>

          </el-form-item>

          <template v-if="state.goodsInfo && orderCreate.couponList.length">
            <el-divider direction='vertical'></el-divider>
            <el-form-item label="优惠券列表">
              <el-select v-model="state.goodsInfo!.coupon_code" @change="() => { orderCreate.use_coupon = 1 }">
                <template v-for="item in orderCreate.couponList">
                  <el-option v-if="item.batchSn"
                    :label="`${Number(item.discount / 100).toFixed(0)}元-` + item.tagDesc + '-' + (item.richRulesDesc?.map((item: anyObj) => item.txt).join('-') || '')"
                    :value="item.sn">
                  </el-option>
                </template>

              </el-select>
            </el-form-item>
          </template>

          <!-- <el-form-item label=" ">
          <el-checkbox label="自动设供货" v-model="orderCreate.auto_supply"></el-checkbox>
        </el-form-item>
        <el-form-item label=" ">
          <el-checkbox label="自动改价" v-model="orderCreate.auto_change_price" @change="(val) => {
            if (val) {
              change_sale_or_discount.dialog = true
            }
          }"></el-checkbox>
                                  </el-form-item> -->

          <br />
          <el-form-item label="开始时间：">
            <el-date-picker v-model="orderCreate.start_time" style="width: 292px" type="datetime"
              placeholder="选择开始时间(可不选)" value-format="x" />
          </el-form-item>
        </el-form>
        <div class="action-box">

          <el-checkbox size='default' label="自动设供货" title="仅对 '批发下单' 有效"
            v-model="settingStore.taskCreate.auto_supply"></el-checkbox>

          <el-checkbox size='default' label="自动改价" v-model="settingStore.taskCreate.auto_change_price"
            @change="(val: any) => val && (change_sale_or_discount.dialog = true)"></el-checkbox>

          <el-checkbox size="default" v-model="autoStartTaskState.active" @change="autoStartTaskSwitch"
            title="自动开始的任务到时间自动开始,手动开始的任务无效">自动开始</el-checkbox>

          <el-checkbox size="default" v-model="taskState.action_open_log" label="自动打开日志"></el-checkbox>

          <div class="btns">
            <el-button type="primary" @click="taskState.logDialog = true">打开日志</el-button>
            <el-button :underline="false" type="primary" plain @click="taskStore.finish.clear()"
              title="清除已完成的任务缓存数据">清除任务缓存</el-button>
          </div>
        </div>
      </div>

      <!-- <div class="btn" @click="createTask()"></div> -->

    </div>
    <el-dialog v-model="change_sale_or_discount.dialog" :width="480" :append-to-body="true" title="0.01改销量或折扣"
      class="change_sale_or_discount_dialog dialog-480">
      <el-alert type="info" :closable="false">
        <strong>请先确认好下单的店铺已登录。未登录可前往【店铺管理】-【添加店铺】进行添加。添加完成后继续执行任务！！</strong>
        <p><el-link :underline="false" type="primary" @click="() => {
      change_sale_or_discount.dialog = false
      $router.push('/Home/storeManage')
    }">添加店铺&gt;&gt;</el-link> </p>
      </el-alert>
      <el-form class="m-t-10" size="default">
        <el-form-item label="批发类型改价">
          <el-input-number :controls="false" size="default" :precision="2" :min="0.01"
            v-model="settingStore.taskCreate.pifaTargetPrice"></el-input-number>
          <span class="m-l-5">元</span>
        </el-form-item>
        <el-form-item label="其他类型改价">
          <el-input-number :controls="false" size="default" :precision="2" :min="1" :max="9.99"
            v-model="settingStore.taskCreate.othersTargetDiscount"></el-input-number> <span class="m-l-5">折</span>
        </el-form-item>
      </el-form>
      <div class="tips">
        <p>注意：</p>
        <p>供货管理折扣设置不高于9折后下单，可低于1折改价格且不影响最低价 </p>
        <p>其他方式下单不能低于1折改价且影响报活动最低价</p>
        <p class="danger">修改值同时也会影响正在进行的订单</p>
      </div>
      <template #footer>
        <!-- <el-button></el-button> -->
        <el-button type="primary" @click="change_sale_or_discount.dialog = false">确定</el-button>
      </template>
    </el-dialog>

    <div class="table-header">
      <el-space>
        <el-radio-group size="default" v-model="taskState.status">
          <el-radio-button :label="0">未开始({{ taskStore.pageListData.unStart.length }})</el-radio-button>
          <el-radio-button :label="1">进行中({{ taskStore.pageListData.loading.length }})</el-radio-button>
          <el-radio-button :label="2">已暂停({{ taskStore.pageListData.paused.length }})</el-radio-button>
          <el-radio-button :label="3">已完成({{ taskStore.completeTotal }})</el-radio-button>
        </el-radio-group>
        <!-- 自动设供货、改价不再绑定到任务里 2023-05-29 -->
        <el-button :underline="false" type="primary" @click="getTaskList()" title="刷新列表"><el-icon class="m-r-5"><Refresh /></el-icon>刷新</el-button>

      </el-space>
      <div class="btns">
        <el-button v-show="taskState.status === 0" size="default" v-blur type="success"
          @click="taskAction('start')"><Icon href="icon-play-filled" style="margin-right: 4px;"></Icon>开始任务</el-button>
        <el-button v-show="taskState.status === 2" size="default" v-blur type="success"
          @click="taskAction('reStart')"><Icon href="icon-play-filled" style="margin-right: 4px;"></Icon>继续任务</el-button>
        <el-button v-show="taskState.status !== 1" size="default" v-blur :icon="Delete" type="danger" plain
          @click="taskAction('delete')">删除任务</el-button>
        <el-button v-show="taskState.status == 1" size="default" v-blur @click="taskAction('pause')">暂停任务</el-button>

      </div>
    </div>


    <el-table :border="true" :data="tableData" v-loading="taskState.tableLoading"
      @selection-change="(data: anyObj[]) => (taskState.selection = data)" :stripe="true">
      <el-table-column type="selection" width="40"></el-table-column>
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column label="开始时间" prop="start_time" so rtable width="150">
        <template #default="scope">
          <span v-if="scope.row.start_time">{{ dayjs(scope.row.start_time).format("YYYY-MM-DD HH:mm:ss") }}</span>
          <span v-else>手动开始</span>
        </template>
      </el-table-column>
      <el-table-column label="下一次执行时间">
        <template #header>
          <span class="text-overflow" title="下一次执行时间(预计)">下一次执行时间</span>
        </template>
        <template #default='scope'>
          {{ taskNextExecTime(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column width="70" label="下单数量" prop="order_num"></el-table-column>
      <el-table-column width="70" label="已团数量" prop="complete_num"></el-table-column>
      <el-table-column width="70" label="起始位置" prop="start_site"></el-table-column>
      <el-table-column width="70" label="当前位置" prop="account_site"></el-table-column>
      <el-table-column label="商品ID" width="105" prop="goods_id"></el-table-column>
      <el-table-column label="店铺名" width="105" prop="shop_name" show-overflow-tooltip></el-table-column>
      <el-table-column label="商品信息" width="105" prop="" show-overflow-tooltip>
        <template #default='scope'>
          {{ scope.row.goods_name + '-' + scope.row.sku_spec }}
        </template>
      </el-table-column>

      <el-table-column label="下单类型" prop="type">
        <template #default='scope'>
          {{ orderTypes.find(item => item.value == scope.row.type)?.label }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="何时付款" prop=""></el-table-column> -->
      <el-table-column width="70" label="每单件数" prop="spell_num"></el-table-column>

      <el-table-column label="优惠券" prop="use_coupon">
        <template #default='scope'>
          <span>{{ scope.row.use_coupon ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="自动改价" prop="auto_change_price">
        <template #default='scope'>
          <span>{{ scope.row.auto_change_price ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="自动供货" prop="auto_supply">
        <template #default='scope'>
          <span>{{ scope.row.auto_supply ? '是' : '否' }}</span>
        </template>
                          </el-table-column> -->
      <el-table-column label="商品规格" prop="sku_spec" width="100" show-overflow-tooltip></el-table-column>
      <el-table-column label="价格(元)" prop="price">

      </el-table-column>
      <el-table-column label="商品收藏" prop="">
        <template #default="scope">
          {{ scope.row.setting?.includes("collect") ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column label="店铺关注" prop="">
        <template #default="scope">
          {{ scope.row.setting?.includes("attention") ? "是" : "否" }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="是否假聊" prop=""></el-table-column> -->
      <!-- <el-table-column fixed="right" label="状态" prop="">
        <template #default="scope"> </template>
                                                                                                                                          </el-table-column> -->
      <el-table-column fixed="right" label="操作" prop="" width="200" align="center">
        <template #default="scope">
          <el-link v-show="taskStore.loading.has(scope.row.task_id)" :underline="false"
            @click="taskAction('pause', [scope.row])">
            <Icon href="icon-pause-filled"></Icon> 暂停
          </el-link>
          <el-link
            v-show="!taskStore.loading.has(scope.row.task_id) && !taskStore.paused.has(scope.row.task_id) && taskState.status !== 3"
            :underline="false" type="success" @click="taskAction('start', [scope.row])">
            <Icon href="icon-play-filled"></Icon> 开始
          </el-link>
          <el-link v-show="taskStore.paused.has(scope.row.task_id)" :underline="false" type="success"
            @click="taskAction('reStart', [scope.row])">
            <Icon href="icon-play-filled"></Icon> 恢复
          </el-link>
          <el-link v-show="!taskStore.loading.has(scope.row.task_id)" :underline="false" type="danger"
            @click="taskAction('delete', [scope.row])">
            <Icon href="icon-delete-filled"></Icon> 删除
          </el-link>

          <el-link :underline="false" @click="copyTask(scope.row)">
            <Icon href="icon-circle-plus-filled"></Icon> 复制
          </el-link>

          <el-link
            v-if="taskStore.paused.has(scope.row.task_id) || taskStore.loading.has(scope.row.task_id) || taskStore.finish.has(scope.row.task_id)"
            :underline="false" @click="showTaskDetails(scope.row)">查看任务状态</el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="taskDetailsState.dialog" title="任务详情" :append-to-body="true" class="task-details-dialog">
      <taskDetailsVue :task-details-state="taskDetailsState" />
    </el-dialog>

    <footer class="table-footer">

      <div class="table-configs">
        <el-space :size="20">
          <!-- <el-checkbox size="default" v-model="state.isGetList">有任务更新时，刷新列表</el-checkbox> -->

          <!-- <el-switch active-text="快速下单模式" v-model="taskStore.requestCountMax" :inactive-value="1"
                      :active-value="5"></el-switch> -->

          <span title="执行下单时允许任务同时进行的最大值;当数据为0时表示不限制;改变时会影响正在下单的任务">
            任务最大：
            <el-input-number style="width: 50px;" v-model="taskStore.requestCountMax" :min="0" :precision="0"
              :controls="false"></el-input-number>
          </span>
        </el-space>
      </div>
      <ds-pagination v-show="taskState.status === 3" :total="taskStore.completeTotal" :pager-count="3"
        v-model:current-page="taskState.pagination.page" v-model:page-size="taskState.pagination.limit"
        @size-change="getTaskList()" @current-change="getTaskList()" />

    </footer>



    <el-dialog title="任务日志" :width="864" v-model="taskState.logDialog" class="task-log-dialog">

      <taskLogVue :visible="taskState.logDialog" />

      <template #footer>
        <el-button type="success" :disabled="!taskStore.paused.size"
          @click="taskAction('reStart', taskStore.pageListData.paused)">恢复全部请求</el-button>
        <el-button type="danger" :disabled="!taskStore.loading.size"
          @click="taskAction('pause', taskStore.pageListData.loading)">暂停全部请求</el-button>
        <el-button @click="taskState.logDialog = false">关闭窗口</el-button>
      </template>
    </el-dialog>

    <el-dialog title="多多进宝【请在商家后台开通】" :width="480" class="dialog-480" v-model="jinbaoState.dialog">
      <el-form label-position="top">
        <el-form-item label="推广链接">
          <el-input v-model="jinbaoState.input"></el-input>
        </el-form-item>
      </el-form>
      <p class="f-s-12" style="color:var(--el-text-color-secondary)">
        <!-- 不填自动生成推广链接，自动领优惠券，也可以给成你自己的链接 -->
      </p>
      <template #footer>
        <el-button @click="() => {
      settingStore.taskCreate.type = 'paidan';
      jinbaoState.dialog = false
    }">取消</el-button>
        <el-button type="primary" @click="jinbaoStateSubmit">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog v-model="tableState.copyDialog" title="复制任务" :width="480" class="dialog-480">
      <el-form label-position="top">
        <el-form-item label="下单数量" prop="">
          <el-input-number v-model="tableState.row.order_num" :precision="0" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="每次购买：">
          <el-input-number v-model="tableState.row.spell_num" :precision="0"
            :min="tableState.row.type === 'pifa' ? 2 : 1"></el-input-number>
        </el-form-item>
        <el-form-item label="小号开始位置：">
          <el-input-number :precision="0" :min="1" :max="subAccoutStore.total || 1"
            :placeholder="`1-${subAccoutStore.total + 1}`" v-model="tableState.row.start_site"></el-input-number>
        </el-form-item>
        <el-form-item label="开始时间：">
          <el-date-picker v-model="tableState.row.start_time" style="width: 292px" type="datetime" placeholder="选择开始时间"
            value-format="x" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="tableState.copyDialog = false">取消</el-button>
        <el-button type="primary" :loading="tableState.copyBtnLoading" :disabled="tableState.copyBtnLoading"
          @click='copyTaskSubmit()'>创建</el-button>
      </template>
    </el-dialog>


  </div>
</template>
<script lang="ts" setup>

  import { Delete, Tickets,Refresh,Search } from "@element-plus/icons-vue";
  import { ElMessageBox, ElInput, ElMessage, ScrollbarInstance, TableInstance, ElLoading, ElSelect, ElOption } from "element-plus";
  import { reactive, h, watch, ref, computed, nextTick, provide, toRef } from "vue";
  import { orderModes, orderTypes } from "../../commdata";
  // import changeSales from "./changeSales.vue";
  // import CsLimit from "./csLimit.vue"; 
  import {
    addTask,
    getGoodsDetails_web,
    getGoodsDetails_pifa,
    getJinBaoData,
    getPifaGroupId,
    taskList,
    autoSupply,
  } from "/@/apis/page";
  import { useCurrentGoods, useCurrentPddUrl } from "/@/stores/current";
  import { usePddAccount, useSubAccount } from "/@/stores/pageData";
  import { copyStr, dateFormat, delayPromise, paste, pddGoodsDetailUrl, random, retry } from "/@/utils/common";
  import dayjs from "dayjs";
  import { TaskItem, useTask } from "/@/stores/task";
  import { cloneDeep, throttle } from "lodash";
  import { addGoodsRecord, getReadList, delGoodsRecord } from "/@/apis/goods";
  import { getStoreGoodsList } from "/@/apis/store";
  import { visit as visitApi } from "/@/apis/page";
  import { useSetting } from "/@/stores/setting";
  import taskLogVue from "./taskLog.vue";
  import taskDetailsVue from "./taskDetails.vue";
  import goodsLogVue from "./goodsLog.vue";
  import wechatAnalyze from "./wechatAnalyze.vue";
  import { isVirtual } from "/@/apis/extra";
  import { useMallStore } from "/@/stores/store";

  async function visitTest () {
    const { value } = await ElMessageBox.prompt('请输入商品ID')
    visitApi({ goods_id: value, num: 1 })
  }

  const wechatAnalyzeEl = ref < InstanceType < typeof wechatAnalyze >> ()

  const addGoodsRecordSet = new Set < goodsReadRecord['goods_id'] > ()
  const state = reactive({
    wechatAnalyzeShow: false,
    goodsInfo: void 0 as GoodsInfo | undefined,

    goodsLogVisible: false,


    // 搜索用的id
    id: "",
  });

  // function tableHeaderDrag(data:any){
  //   console.log(data)
  // }

  async function selectGoodsRecord (row: goodsReadRecordInDateBase) {
    const goodsInfo: anyObj = { ...row }
    goodsInfo.skus = JSON.parse(row.skus)
    goodsInfo.group_order_ids = JSON.parse(row.group_order_ids)
    goodsInfo.group_id = JSON.parse(row.group_id)
    // console.log(goodsInfo)
    if (!goodsInfo.group_id.alone) {
      state.goodsLogVisible = false
      await autoSupply({ mallId: row.mallId, goods_id: Number(row.goods_id) })
      await new Promise((resolve, reject) => {
        let index = 0
        buttonLoading.getGroup_id = true
        const fn = () => {
          const sku = goodsInfo.skus[index++]
          if (!sku) {
            ElMessage.warning({
              message: '初始化拼团id失败',
              grouping: true
            })
            buttonLoading.getGroup_id = false
            reject()
            return
          }
          getPifaGroupId({
            goods_id: row.goods_id,
            sku_id: sku.skuId
          }, { showErrorMsg: false, loading: false })
            .then(res => {
              const group_id: GoodsInfo['group_id'] = {
                alone: res.data,
                multiple: [res.data]
              }
              goodsInfo.group_id = group_id
              buttonLoading.getGroup_id = false
              resolve(true)
            })
            .catch(res => {
              fn()
            })
        }
        fn()
      })
      state.goodsInfo = goodsInfo as GoodsInfo
      orderCreate.couponList = []
      addGoodsRecord(state.goodsInfo)
    } else {
      state.goodsInfo = goodsInfo as GoodsInfo
      state.goodsLogVisible = false
      orderCreate.couponList = []
    }
  }



  const taskStore = useTask()

  const autoStartTaskState: {
    tid?: NodeJS.Timer,
    delay: number,
    active: boolean
  } = reactive({
    delay: 5,
    active: false
  })

  watch(() => taskStore.pageListData.unStart.length, (len) => {
    autoStartTask()
  }, { immediate: true })

  function autoStartTaskSwitch () {
    const { active, tid } = autoStartTaskState
    if (active) {
      autoStartTask()
    } else {
      autoStartTaskState.tid = void 0
      tid && clearTimeout(tid)
    }
  }

  function autoStartTask () {
    console.log('autoStartTask')
    const { tid, delay, active } = autoStartTaskState
    if (tid || !active) {
      return
    }
    autoStartTaskState.tid = setTimeout(() => {
      autoStartTaskState.tid = void 0
      const list = taskStore.pageListData.unStart.filter(item => {
        return item.start_time && item.start_time <= Date.now()
      })

      if (list.length) {
        const Status = taskStore.getEnumStatus()
        if (taskStore.status === Status.PAUSED) {
          taskStore.status = Status.IDLE
        }
        taskAction('start', list, false)
      }
      autoStartTask()
    }, delay * 1000)
  }


  const skuState = reactive({
    value: "random",
    visible: false,
    min_price: 0,
    max_price: 1,
    list: [
      {
        label: "指定规格",
        value: "1",
      },
      {
        label: "随机规格",
        value: "random",
      },
      {
        label: "最高价格",
        value: "3",
      },
      {
        label: "最高价格随机",
        value: "random-3",
      },
      {
        label: "最低价格",
        value: "4",
      },
      {
        label: "最低价格随机",
        value: "random-4",
      },
      {
        label: "指定价格随机",
        value: 'random-5'
      }
    ],
  });

  function selectSku (type = skuState.value) {
    if (state.goodsInfo && state.goodsInfo.skus.length) {
      const skus = state.goodsInfo?.skus
      switch (type) {
        case '1': {
          return skus.find(item => String(item.skuId) == orderCreate.sku)
        }

        case '3': {
          let sku: typeof skus[number] | undefined = void 0
          skus.forEach(item => {
            if (!sku || Number(sku.groupPrice) < Number(item.groupPrice)) {
              sku = item
            }
          })
          return sku
        }
        case '4': {
          let sku: typeof skus[number] | undefined = void 0
          skus.forEach(item => {
            if (!sku || Number(sku.groupPrice) > Number(item.groupPrice)) {
              sku = item
            }
          })
          return sku
        }
        default: {
          const data: GoodsInfo['skus'][number] = {
            skuId: 0,
            skuImg: '',
            spec: (skuState.list.find(item => item.value === type)?.label || ''),
            groupPrice: 0,
            normalPrice: 0
          }
          return data
        }
      }
    }

  }

  const change_sale_or_discount = reactive({
    dialog: false,
    type: "1",
  });

  const jinbaoState = reactive({
    dialog: false,
    input: "",
  })

  function jinbaoStateSubmit () {
    const { input } = jinbaoState
    if (!/^http/.test(input)) {
      return ElMessage.warning({
        message: "请输入合法的链接",
        grouping: true
      })
    }
    getJinBaoData(input)
      .then(res => {
        // ElMessage.success(res.data)
        const searchParams = new URLSearchParams(res.data.split("?")[1]);
        const params: anyObj = {};
        for (const [key, value] of searchParams.entries()) {
          Reflect.set(params, key, value)
        }
        console.log(params)
        const { cpsSign, pid, goods_id } = params
        if (cpsSign && pid && goods_id) {
          orderCreate.cpsSign = cpsSign
          orderCreate.pid = pid
          state.id = goods_id
          goodsDetails()
        } else {
          ElMessage.warning('获取信息错误')
          settingStore.taskCreate.type = 'paidan'
        }
        jinbaoState.dialog = false
      })
      .catch(res => {
        ElMessage.warning(res.msg)
        console.log(res)
      })
  }

  // 下单类型变化
  function order_type_change (type: any) {
    // 多多进宝
    if (type === "jinbao") {
      jinbaoState.dialog = true
    }
    // else if (type == "12") {
    //   change_sale_or_discount.dialog = true;
    // }

    else if (type === 'wanrentuan') {
      orderCreate.mode = 'open_group'
    }

    if (type == 'pifa') {
      orderCreate.spell_num >= 2 || (orderCreate.spell_num = 2)
    } else {
      orderCreate.spell_num == 2 && (orderCreate.spell_num = 1)
    }
  }


  // 任务列表
  type Task = anyObj;
  const taskState: {
    task?: Task;
    logDialog: boolean;
    taskList: Task[];
    selection: Task[];
    pagination: Pick<Pagination, 'page' | 'limit'>;
    status: number | "";
    tableLoading: boolean;

    /**在有开始暂停等操作时是否自动打开日志 */
    action_open_log: boolean
  } = reactive({
    task: void 0,
    logDialog: false,
    taskList: [],
    selection: [],
    pagination: {
      page: 1,
      limit: 100,
    },
    status: 0,
    tableLoading: false,
    labelList: [
      // {
      //   label: "全部",
      //   value: "",
      //   count: 0,
      // },
      {
        label: "未开始",
        value: 0,
        count: 0,
      },
      {
        label: "进行中",
        value: 1,
        count: 0,
      },
      {
        label: "已暂停",
        value: 2,
        count: 0,
      },
      {
        label: "已完成",
        value: 3,
        count: 0,
      },
    ],
    action_open_log: false
  });


  const tableData = computed(() => {
    switch (taskState.status) {
      case 0: {
        return taskStore.pageListData.unStart
      }
      case 1: {
        return taskStore.pageListData.loading
      }
      case 2: {
        return taskStore.pageListData.paused
      }
      case 3: {
        return taskStore.completeList
      }

    }
  })






  const getTaskList = throttle(function () {
    const { status } = taskState;
    const { page, limit } = taskState.pagination;
    taskState.tableLoading = true;

    taskStore.getTaskList({ page, limit })
      .finally(() => {
        taskState.tableLoading = false;
      });
  }, 1000)
  getTaskList();
  const currentGoods = useCurrentGoods();
  const currentUrl = useCurrentPddUrl();
  const subAccoutStore = useSubAccount();
  if (!subAccoutStore.total) {
    subAccoutStore.getList();
  }
  const settingStore = useSetting()

  watch(() => settingStore.taskCreate, (value) => {
    if (value) {
      const data = { ...value }
      data.setting = [...(data.setting || [])]
      settingStore.setOrderSetting('taskCreate', data)
    }
  }, { deep: true })
  const orderCreate = reactive({
    /**下单数量 */
    order_num: 1,
    /**购买数量 */
    spell_num: 2,
    /**小号位置 */
    start_site: 1,
    /**下单类型 （已经移动到settingStore）*/
    // type: "paidan",
    /**下单方式 */
    mode: "open_group",
    /**关键词 */
    keyword: "",
    /**优惠券 */
    coupon: "",
    // /**其他设置 （已转移到settingStore.taskCreate.setting 2023-06-10） */
    // setting: [] as string[],
    /**使用优惠券 */
    use_coupon: 0,
    /**开始时间 */
    start_time: 0,

    /**多多进宝推广信息1 */
    cpsSign: "",
    /**多多进宝推广信息2 */
    pid: '',

    /**商品选择的sku */
    sku: "" as string | number,

    /**优惠券列表 */
    couponList: [] as anyObj[],
    /**自动供设货 */
    // auto_supply: false,
    /**自动改价 */
    // auto_change_price: false,
  });
  provide('start_site', toRef(orderCreate, 'start_site'))
  // watch(() => settingStore.taskCreate.setting, (val) => {
  //   // if(val.includes('coupon'))
  //   const settingSet = new Set(val)
  //   if (settingSet.has('coupon') && !settingSet.has('attention')) {
  //     settingStore.taskCreate.setting.push('attention')
  //   }
  // })

  watch(() => subAccoutStore.list.length, (val, oldv) => {
    if (val && !oldv) {
      settingStore.getAnySetting('start_site')
        .then(res => {
          if (res) {
            orderCreate.start_site = res
          }
        })
    }
  }, { immediate: true })
  watch(() => orderCreate.start_site, (val) => {
    settingStore.setAnySetting('start_site', val)
  })
  watch(
    () => currentGoods.infos,
    (value) => {
      if (value && currentGoods.goods && currentGoods.mall) {
        const {
          goodsName,
          thumbUrl,
          minNormalPrice,
          minGroupPrice,
          sideSalesTip,
          goodsID,
          skus,
          combineGroups,
          groupTypes,
          activity
        } = currentGoods.goods;
        const { mallName, mallId } = currentGoods.mall;


        const goodsInfo: GoodsInfo = {
          goods_id: goodsID,
          mallName,
          mallId,
          goods_name: goodsName,
          activity_id: activity?.activityID || 0,
          groupPrice: minGroupPrice,
          normalPrice: minNormalPrice,
          goods_img: thumbUrl,
          skus: skus.map((item: anyObj) => {
            const { skuId, thumbUrl, specs, normalPrice, groupPrice } = item
            return {
              skuId,
              spec: specs.map((spec: anyObj) => spec.spec_value).join('-'),
              skuImg: thumbUrl,
              groupPrice: Number(groupPrice),
              normalPrice: Number(normalPrice)
            }
          }),
          group_order_ids: combineGroups.combineGroupList.map((item: anyObj) => item.groupOrderId),
          group_id: {
            multiple: []
          },
          coupon_code: "",
        }
        // console.log(goodsInfo)
        groupTypes.forEach((item: anyObj) => {
          if (Number(item.requireNum) <= 1) {
            goodsInfo.group_id.alone = item.groupID;
          } else {
            goodsInfo.group_id.multiple.push(item.groupID);
          }
        })
        state.goodsInfo = goodsInfo
        if (state.goodsInfo.skus.length) {
          // const sku = selectSku()
          // orderCreate.sku = sku?.skuId || goodsInfo.skus[0].skuId;
        } else {
          orderCreate.sku = "";
        }
        // console.log(currentGoods.infos)
        const list = currentGoods.infos?.store.initDataObj.oakData.subSections.discountPopSection?.data?.mallPromoList
        if (list && list.length) {
          // list.find((item: anyObj) => {
          //   if (item.tagDesc === '店铺关注券') {
          //     goodsInfo.coupon_code = item.batchSn
          //     return true
          //   } else {
          //     return false
          //   }
          // })
          console.log(list)
          orderCreate.couponList = list
        }

      } else {
        state.goodsInfo || (orderCreate.sku = "");
      }
    },
    { immediate: true }
  );

  watch(() => state.goodsInfo, (val) => {
    if (!val) {
      return
    }
    if (!val.coupon_code) {
      const settingSet = new Set(settingStore.taskCreate.setting)
      orderCreate.use_coupon = 0
      if (settingSet.has('coupon')) {
        settingSet.delete('coupon')
        settingStore.taskCreate.setting = [...settingSet]
      }
    }
    orderCreate.sku = selectSku()?.skuId || val.skus[0]?.skuId || ''
    if (addGoodsRecordSet.has(Number(val.goods_id))) {
      addGoodsRecord(val)
      addGoodsRecordSet.delete(Number(val.goods_id))
    }
  })

  const buttonLoading = reactive({
    goodsDetails_pifa: false,
    goodsDetails_web: false,
    goodsDetails_mall: false,
    getGroup_id: false,
    addTask: false
  })




  async function goodsDetails (record = true, type = 'local' as 'local' | 'web' | 'pifa' | 'databse' | 'test' | 'wechat' | 'mall') {
    if (type === 'wechat') {
      state.id = state.id || ''
    }
    else if (!state.id) {
      return ElMessage.warning("请输入商品id");
    } else if (state.id.toString().length < 3) {
      return ElMessage.warning("id太短");
    }
    let key = String(state.id);
    if (key.startsWith("https://")) {
      const reg = /goods_id=(\d+)/;
      const res = key.match(reg);
      res && (key = res[1]);
    }
    key = key.trim()
    orderCreate.couponList = []
    switch (type) {
      case 'local': {
        currentUrl.changeUrl(pddGoodsDetailUrl(key));

        break
      }
      case 'web': {
        const result = await isVirtual()
        if (result) {
          ElMessage.warning({
            message: '-检测到虚拟机环境',
            grouping: true
          })
          return
        }
        buttonLoading.goodsDetails_web = true
        getGoodsDetails_web({ goods_id: key }, { showErrorMsg: false })
          .then(async res => {
            console.log(res)
            if (res.data?.goods_id) {
              const { goods_id, goods_name, mallId, mallName, groupPrice, normalPrice, goods_img, skus, group_id } = res.data
              const goodsInfo: GoodsInfo = {
                goods_name,
                goods_id,
                activity_id: 0,
                mallId,
                mallName,
                groupPrice: Number(groupPrice),
                normalPrice: Number(normalPrice),
                goods_img,
                skus: skus.map((item: anyObj) => {
                  const { skuId, thumbUrl, specs, normalPrice, groupPrice } = item
                  return {
                    skuId,
                    spec: specs.map((spec: anyObj) => spec.spec_value).join('-'),
                    skuImg: thumbUrl,
                    groupPrice: Number(groupPrice),
                    normalPrice: Number(normalPrice)
                  }
                }),
                coupon_code: "",
                group_id: group_id,
                group_order_ids: []
              }
              console.log(goodsInfo)
              state.goodsInfo = goodsInfo
            } else {
              return Promise.reject({
                msg: "没有获取到商品信息"
              })
            }
          })
          .catch((res) => {
            console.log(res)
            ElMessage.error({
              message: res.msg,
              grouping: true
            })
          })
          .finally(() => {
            buttonLoading.goodsDetails_web = false
          })
        break
      }
      case 'pifa': {
        buttonLoading.goodsDetails_pifa = true

        getGoodsDetails_pifa({ goods_id: key })
          .then(async res => {

            const { goodsId, goodsName, imageUrl, minWholesalePrice, maxWholesalePrice, goodsSkuInfos, mallCard } = res.result
            const goodsInfo: GoodsInfo = {
              goods_id: goodsId,
              goods_name: goodsName,
              goods_img: imageUrl,
              mallName: mallCard.mallName,
              mallId: mallCard.realMallId,
              normalPrice: maxWholesalePrice / 100,
              groupPrice: minWholesalePrice / 100,
              activity_id: 0,
              skus: goodsSkuInfos.map((item: anyObj) => {
                const data: GoodsInfo['skus'][number] = {
                  skuId: item.skuId,
                  spec: item.skuSpecs.map((spec: anyObj) => spec.specValue).join(','),
                  skuImg: item.thumbUrl,
                  groupPrice: item.groupPrice / 100,
                  normalPrice: item.wholesalePrice / 100
                }
                return data
              }),
              coupon_code: '',
              group_order_ids: [],
              group_id: {
                multiple: []
              }
            }
            const data = {
              goods_id: goodsId,
              sku_id: goodsInfo.skus[0].skuId
            }
            // console.log(data)
            const groupIdRes = await getPifaGroupId(data, { showErrorMsg: false })
            if (typeof groupIdRes.data !== 'string' || !groupIdRes.data) {
              return Promise.reject({
                errorMsg: '获取拼团ID失败'
              })
            }
            orderCreate.mode = 'open_group'
            goodsInfo.group_id.alone = Number(groupIdRes.data)
            goodsInfo.group_id.multiple = [Number(groupIdRes.data)]
            state.goodsInfo = goodsInfo


          })
          .catch((res) => {
            // console.log(res)
            const msg = res.msg || res.errorMsg || res.error_msg
            ElMessage.error('获取taskAction：' + (msg || '网络超时,请再次尝试'))
          })
          .finally(() => {
            buttonLoading.goodsDetails_pifa = false
          })
        break
      }
      case 'databse': {

        const { list } = await getReadList({ page: 1, limit: 20, goods_id: key })
        if (list.length) {
          selectGoodsRecord(list[0])
          return
        }
        break
      }

      case 'wechat': {
        if (!state.wechatAnalyzeShow) {
          copyStr('https://mobile.yangkeduo.com/goods.html?goods_id=' + key)
          wechatAnalyzeEl.value?.proxyAction('start')
            .then(() => {
              // state.wechatAnalyzeShow = true
            })
        }
        break
      }
      case 'mall': {

        try {
          const mallStore = useMallStore()
          if (!mallStore.tableList.length) {
            ElMessage.warning({
              message: '没有店铺列表信息',
              grouping: true
            })
            return
          }
          const mallId = ref < number > ()
          await ElMessageBox({
            title: '选择店铺',
            showCancelButton: true,
            message: () => h('div', {}, [
              h('p', {}, `请选择商品ID${key}所在店铺`),
              h(ElSelect, {
                modelValue: mallId.value,
                'onUpdate:modelValue': (val) => {
                  mallId.value = val
                }
              },
                {
                  default: () => mallStore.tableList.map(mall => {
                    return h(ElOption, { label: mall.mallName, value: mall.mallId, disabled: !mallStore.checkAvailable(mall) })
                  })
                })
            ])
          })
          const mall = mallStore.tableList.find(item => item.mallId === mallId.value)
          if (!mall) {
            ElMessage.warning({
              message: '没有找到店铺',
              grouping: true
            })
            return Promise.reject()
          }
          buttonLoading.goodsDetails_mall = true
          const res = await getStoreGoodsList(mall, { goods_id_list: [key] })
          const list = res.result?.goods_list as anyObj[]
          const item = list.find(item => item.id == Number(key))
          if (!item) {
            ElMessage.warning({
              message: '没有找到商品',
              grouping: true
            })
            buttonLoading.goodsDetails_mall = false
            return Promise.reject()
          }
          const goodsInfo: GoodsInfo = {
            goods_name: item.goods_name,
            goods_id: item.id,
            goods_img: item.thumb_url,
            activity_id: 0,
            mallId: mall!.mallId,
            mallName: mall!.mallName,
            groupPrice: item.sku_group_price[0] / 100,
            normalPrice: item.sku_price[0] / 100,
            skus: item.sku_list.filter((skuItem: anyObj) => skuItem.isOnsale).map((skuItem: anyObj) => {
              return {
                skuId: skuItem.skuId,
                spec: skuItem.spec,
                skuImg: skuItem.skuThumbUrl,
                groupPrice: skuItem.groupPrice / 100,
                normalPrice: skuItem.normalPrice / 100
              }
            }),
            group_id: {
              multiple: [],
            },
            group_order_ids: [],
            coupon_code: ''
          }
          const get_group_id = async () => {
            return retry(async (index) => {
              return getPifaGroupId({ goods_id: goodsInfo.goods_id, sku_id: goodsInfo.skus[0].skuId }, { showErrorMsg: false })
            }, 3)
              .then(res => {
                const groupId = res.data
                if (groupId) {
                  goodsInfo.group_id = {
                    alone: Number(groupId),
                    multiple: [Number(groupId)]
                  }
                } else {
                  return Promise.reject()
                }

              })
          }

          await Promise.allSettled([
            get_group_id()
              .catch(async () => {
                return autoSupply({ mallId: goodsInfo.mallId, goods_id: Number(goodsInfo.goods_id) })
              })
              .then(async () => {
                await delayPromise(1500)
                return get_group_id()
              })
              .catch((res) => {
                console.log(res)
                ElMessage.warning({
                  message: '获取拼团ID失败',
                  grouping: true
                })
                return Promise.reject()
              })
          ])
          buttonLoading.goodsDetails_mall = false
          if (goodsInfo.group_id.alone) {
            state.goodsInfo = goodsInfo
          }
        } catch (e) { }

      }
    }
    record && addGoodsRecordSet.add(Number(key))
  }

  function checkModelDisabled (item: typeof orderModes[number]) {
    const { type } = settingStore.taskCreate

    return item.disabled({ order_type: type, isGroupOrder: !!state.goodsInfo?.group_order_ids.length })
  }

  async function createTask () {
    if (buttonLoading.addTask) {
      return
    }
    if (!state.goodsInfo) {
      return ElMessage.warning({
        message: "没有商品信息",
        grouping: true,
      });
    }
    // if (!state.goodsInfo.mallId) {
    //   return ElMessage.warning({
    //     message: "没有获取到店铺信息",
    //     grouping: true,
    //   });
    // }
    const { skus } = state.goodsInfo
    const {
      order_num,
      spell_num,
      start_site,
      // type,
      coupon,
      start_time,
      keyword,
      sku,
      cpsSign,
      pid,
      use_coupon,
      // auto_change_price,
      // auto_supply
    } = orderCreate;
    const { setting, type } = settingStore.taskCreate
    let { mode } = orderCreate

    if (!type) {
      return ElMessage.warning({
        message: '请选择下单类型',
        grouping: true
      })
    }


    if (type == 'keyword' && !keyword) {
      return ElMessage.warning({
        message: "请先输入下单关键词",
        grouping: true
      })
    }
    const { group_id, group_order_ids, mallName, mallId, goods_id, goods_name, activity_id, coupon_code } = state.goodsInfo!
    if (mode === 'random') {
      const modeFilterList = orderModes.filter(item => !checkModelDisabled(item))
      const ind = random(0, modeFilterList.length)
      mode = modeFilterList[ind]?.value || 'open_group'
    }


    let fin_group_id;
    switch (mode) {
      case "spell_group": {
        fin_group_id = group_id.multiple[0];
        break;
      }
      case "open_group": {
        fin_group_id = group_id.multiple[0];
        break;
      }
      case "alone": {
        fin_group_id = group_id.alone || '';
        break;
      }
      default:
        fin_group_id = group_id.multiple[0];
        break;
    }

    if (type === 'wanrentuan') {
      if (!group_id.alone) {
        return ElMessage.warning({
          message: "查询到万人团需要的参数缺失，不可创建!",
          grouping: true
        })
      }
      fin_group_id = group_id.alone!
    }


    // const skuSelect = skus.find((item: anyObj) => item.skuId === sku)!;
    const skuSelect = selectSku()
    if (!skuSelect) {
      return ElMessage.warning('请选择sku')
    }
    // console.log(skuSelect)
    // await ElMessageBox({
    //   title: "提示",
    //   type: "info",
    //   message: "确认创建任务吗?",
    //   showCancelButton: true
    // });
    let filterSkuList: typeof skus = skus

    {
      const { value, max_price, min_price } = skuState
      const map = new Map < number, typeof skus > ()
      skus.forEach(item => {
        const list = map.get(item.groupPrice)
        if (list) {
          list.push(item)
        } else {
          map.set(item.groupPrice, [item])
        }
      })
      const maxPrice = Math.max(...[...map.keys()])
      const minPrice = Math.min(...[...map.keys()])
      switch (value) {
        case '1': {
          const sku = skus.find(item => String(item.skuId) == orderCreate.sku)
          filterSkuList = sku ? [sku] : []
          break
        }
        case 'random': {
          filterSkuList = [...skus]
          break
        }
        case '3': {
          const sku = skus.find(item => item.groupPrice <= minPrice)
          filterSkuList = sku ? [sku] : []
          break
        }
        case '4': {
          const sku = skus.find(item => item.groupPrice >= maxPrice)
          filterSkuList = sku ? [sku] : []
          break
        }
        case 'random-3': {
          // 最高价格随机
          filterSkuList = map.get(maxPrice) || []
          break
        }
        case 'random-4': {
          filterSkuList = map.get(minPrice) || []
          break
        }
        case 'random-5': {
          const keyList = [...map.keys()].filter(price => price >= min_price && price <= max_price)
          filterSkuList = keyList.map(key => {
            return (map.get(key) || [])
          }).flat()
        }
      }
    }

    if (!filterSkuList.length) {
      return ElMessage.warning({
        message: 'SKU筛选后没有可用的商品',
        grouping: true
      })
    }
    const data: Parameters<typeof addTask>[0] = {
      order_num,
      spell_num,
      start_site,
      type,
      mode,
      coupon,
      setting: setting.join(","),
      start_time: start_time,
      goods_id,
      goods_name,
      price: mode === "alone" ? skuSelect.normalPrice : skuSelect.groupPrice,
      sku: skuSelect?.skuId,
      skus: JSON.stringify(filterSkuList.map(({ skuId, spec }) => ({ skuId, spec })).slice(0, 30)),
      sku_spec: skuSelect?.spec,
      shop_id: mallId || 0,
      group_id: fin_group_id,
      // keyword : type  === 'keyword' ? keyword : '',
      keyword,
      activity_id,
      // group_order_id:
      //   mode === "spell_group" ? groupData.group_order_ids[0] : void 0,
      group_order_id: group_order_ids[0],
      shop_name: mallName,
      cps_sign: type === 'jinbao' ? cpsSign : '',
      duo_duo_pid: type === 'jinbao' ? pid : '',
      use_coupon,
      coupon_code,
      coupon_type: orderCreate.couponList.find(item => item.sn == coupon_code)?.tagDesc == '店铺关注券' ? 'mall' : 'goods'
      // auto_change_price: auto_change_price ? 1 : 0,
      // auto_supply: auto_supply ? 1 : 0
    }
    if (type == 'pifa' && data.spell_num <= 1) {
      ElMessage.warning({
        message: '批发下单每次至少需要两单,已自动调整',
        grouping: true
      })
      data.spell_num = 2
    }
    buttonLoading.addTask = true
    const _data = cloneDeep(data)
    console.log(_data)
    addTask(_data)
      .then(() => {
        orderCreate.start_site = (start_site + order_num) % subAccoutStore.list.length

        ElMessage.success("创建成功");
        getTaskList()
      })
      .catch((res) => {
        console.log(res)
      })
      .finally(() => {
        buttonLoading.addTask = false
      })

  }

  async function taskAction (
    action: "pause" | "start" | "delete" | 'reStart',
    list = taskState.selection,
    notice = true
  ) {
    if (!list.length) {
      return ElMessage.warning({
        message: "请先选择需要操作的任务",
        grouping: true,
      });
    }
    let filterList: Task[];
    let str = "";
    switch (action) {
      case 'reStart': {
        str = "恢复执行";
        filterList = list.filter(item => taskStore.paused.has(item.task_id))
        break
      }
      case "pause": {
        str = "暂停";
        filterList = list.filter((item) => taskStore.loading.has(item.task_id));
        break;
      }
      case "start": {
        str = "开始";
        filterList = list.filter((item) => !taskStore.loading.has(item.task_id) && !taskStore.paused.has(item.task_id));
        // filterList = list
        break;
      }
      case "delete": {
        str = "删除";
        filterList = list.filter((item) => !taskStore.loading.has(item.task_id));
        break;
      }

      default:
        return ElMessage.warning("错误的行为");
        break;
    }
    if (!filterList.length) {
      return ElMessage.warning({
        message: `没有可以${str}的任务`,
        grouping: true,
      });
    }

    notice && await ElMessageBox({
      title: "提示",
      type: "warning",
      message: `确定要对选中的任务执行${str}操作吗?`,
      showCancelButton: true,
    });
    const { limit, page } = taskState.pagination
    taskStore.taskAction(action, filterList, { limit, page: 1 })
    if (action !== 'delete' && taskState.action_open_log) {
      taskState.logDialog = true
    }


    // taskStore.taskAction(action, filterList).then(() => {
    //   if (action === 'start') {
    //     filterList.forEach(item => item.status = 1)

    //     taskState.logDialog = true
    //     return
    //   }
    //   ElMessage.success("操作成功");

    //   getTaskList();
    // });
  }



  const tableState: {
    row: anyObj
    copyDialog: boolean
    copyBtnLoading: boolean
  } = reactive({
    row: {},
    copyDialog: false,
    copyBtnLoading: false
  })

  /**任务状态 */
  const taskDetailsState: {
    dialog: boolean
    taskItem?: TaskItem
    // row:anyObj
  } = reactive({
    dialog: false,

  })
  function showTaskDetails (row: anyObj) {
    const { task_id } = row
    const taskItem = taskStore.loading.get(task_id) || taskStore.paused.get(task_id) || taskStore.finish.get(task_id)
    if (!taskItem) {
      return ElMessage.warning({
        message: "没有找到任务详情数据",
        grouping: true
      })
    }
    taskDetailsState.dialog = true
    taskDetailsState.taskItem = taskItem
  }

  function taskNextExecTime (row: anyObj) {
    const time = taskStore.loading.get(row.task_id)?.nextExecTime || taskStore.paused.get(row.task_id)?.nextExecTime
    if (time) {
      return dayjs(time).format('HH:mm:ss')
    } else {
      return '-'
    }
  }

  function copyTask (row: anyObj) {
    tableState.row = {
      ...row
    }
    tableState.row.start_site = orderCreate.start_site
    tableState.copyDialog = true
  }

  function copyTaskSubmit () {
    const { order_num, spell_num, start_site, type, mode, coupon, setting,
      start_time,
      goods_id,
      goods_name,
      price,
      sku,
      sku_spec,
      shop_id,
      group_id,
      keyword,
      group_order_id,
      shop_name,
      cps_sign,
      duo_duo_pid, activity_id, use_coupon, coupon_code, auto_change_price, auto_supply, skus, coupon_type } = tableState.row
    tableState.copyBtnLoading = true
    const data = {
      order_num, spell_num,
      start_site,
      type,
      mode,
      coupon,
      setting,
      start_time,
      goods_id,
      goods_name,
      price,
      sku,
      sku_spec,
      shop_id,
      group_id,
      keyword,
      group_order_id,
      shop_name,
      cps_sign,
      duo_duo_pid, activity_id,
      use_coupon,
      coupon_code,
      skus,
      coupon_type
    }
    // console.log(data)
    addTask(data)
      .then(() => {
        taskStore.getTaskList()
        ElMessage.success('创建成功')
        orderCreate.start_site = (start_site + order_num) % subAccoutStore.list.length
        tableState.copyDialog = false
      })
      .finally(() => {
        tableState.copyBtnLoading = false
      })
  }

</script>
<style src="./css/index.scss" scoped></style>

<style lang="scss">
  .el-dialog.change_sale_or_discount_dialog {
    strong {
      color: var(--el-text-color-primary);
      font-size: 14px;
      line-height: 22px;
    }

    .tips {
      font-size: 12px;

      p {
        line-height: 20px;
      }
    }

  }



  .el-dialog.task-details-dialog {
    // margin-left: 550px; 
    color: var(--el-text-color-secondary);
  }

  .el-dialog.task-log-dialog {
    // margin-left: 550px;
    // overflow: hidden;

    .el-dialog__header {


      .el-dialog__title,
      .el-dialog__close {
        // color: #fff !important;
      }

      .el-dialog__close {
        font-weight: bolder;
        font-size: 24px;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

  }

  .el-popper.sku-select-popper {
    // inset: 261px 49px auto auto !important;
    max-width: 740px;
  }
</style>