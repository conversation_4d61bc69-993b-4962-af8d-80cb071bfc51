import { Ref, reactive, watch } from "vue";
import { VxeListInstance, VxeListProps } from "vxe-table";
import { useAppStore } from "../stores/app";


export function useList(
  height: number,
  listProps?: VxeListProps,
  options: { pagination?: Partial<Pagination>,ListInstance?:Ref<VxeListInstance|undefined> }={}
) {
  const props = reactive(listProps || {});
  props.height = height;



  const pagination: Pagination = reactive({
    total: 0,
    pageSizes: options.pagination?.pageSizes || [100, 200, 500, 1000, 2000],
    limit: options.pagination?.limit || 100,
    page: options.pagination?.page || 1,
  });
  return {
    listProps:props,
    pagination
  }
}
