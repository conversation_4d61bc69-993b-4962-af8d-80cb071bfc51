<template>
    <div class="collect-comment">
        <el-dialog :width="700" title="采集评论" v-model="state.dialog" :append-to-body="true" class="collect-comment-config"
            :close-on-press-escape="false" :close-on-click-modal="false">
            <el-form label-position="top">
                <el-form-item label="文件规则">
                    <el-alert type='success' :closable="false">
                        <p>1.该选项决定了采集的每一条评论的文件夹名字</p>
                        <p>2.【按评论ID】，则每一条评论的文件夹名字是该评论的ID</p>
                        <p>3.【按数字顺序排序】,则按照（1,2,3...）的顺序定义评论文件夹名字（同名文件会被覆盖）</p>
                    </el-alert>
                    <el-radio-group v-model="downloadState.folderType" class="m-t-10">
                        <el-radio border label="review-id">按评论ID</el-radio>
                        <el-radio border label="number">按数字顺序排序</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="采集内容">
                    <el-checkbox-group v-model="downloadState.collect_type">
                        <el-checkbox label="txt">文字</el-checkbox>
                        <el-checkbox label="img">图片</el-checkbox>
                        <el-checkbox label="video">视频</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="存储路径">
                    <el-space>
                        <span>{{ downloadState.path }}</span>
                        <el-link :underline="false" type="primary" @click="selectPath">选择</el-link>
                    </el-space>
                </el-form-item>
                <el-form-item label="文件名(可为空)">
                    <el-input v-model="downloadState.fileName"></el-input>
                </el-form-item>

                <el-form-item label="最终路径">
                    {{ savePath || '-' }}
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="state.dialog = false">取消</el-button>
                <el-button type="primary" @click="startDown" :disabled="buttonLoading.down"
                    :loading="buttonLoading.down">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang='ts' setup>
import { computed, reactive } from 'vue';
import { pathFn, selectFolder, download, writeFile } from '/@/apis/mainWindow';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';


const emits = defineEmits<{
    (e: 'log', data: { msg: string, type: 'danger' | 'success' }): any,
    (e: 'finally', data: {
        userSelectFolders: { name: string, path: string }[]
        path: string
    }): any
}>()

const state: {
    dialog: boolean
    commentList: anyObj[]
} = reactive({
    dialog: false,
    commentList: []
})

const savePath = computed(() => {
    const { path, fileName } = downloadState
    if (!path) {
        return ''
    } else {
        return path + (fileName ? `/${fileName}` : '')
    }
})

/**下载评论的配置 */
const downloadState = reactive({
    path: '',
    fileName: '',
    collect_type: ['txt', 'img', 'video'],
    folderType: 'review-id'
})

function openConfigDialog(list: typeof state.commentList) {
    state.commentList = list
    downloadState.fileName = dayjs(Date.now()).format('YYYYMMDDHHmmss')
    state.dialog = true
}

async function selectPath() {
    const path = await selectFolder()
    if (path) {
        downloadState.path = path
    }
}

const buttonLoading = reactive({
    down: false
})

async function startDown() {
    const folderName = savePath.value
    if (!folderName) {
        return ElMessage.warning({
            message: '请选择文件路径',
            grouping: true
        })
    }
    buttonLoading.down = true
    const downList: Array<{
        type: 'video' | 'img' | 'txt',
        txt?: string,
        dest?: string
        url?: string
        folderName: string
    }> = []
    const { folderType, collect_type } = downloadState


    const downPromiseArr = state.commentList.map((item, index) => {
        return new Promise(async resolve => {
            const _folderName = `${folderType === 'number' ? index + 1 : item.review_id}`
            const basePath = `${folderName}/${_folderName}`
            if (collect_type.includes("txt")) {
                // 只筛选5星评价，并过滤 该用户觉得商品较好 该用户觉得商品很好，给出了5星好评 该用户未填写文字评价
                if (
                    item.comment.includes("该用户觉得商品较好") ||
                    item.comment.includes("该用户觉得商品很好，给出了5星好评") ||
                    item.comment.includes("该用户未填写文字评价") ||
                    item.desc_score < 5
                ) {
                    resolve(true)
                    return;
                }

                downList.push({
                    type: 'txt',
                    dest: `${basePath}/comment.txt`,
                    txt: item.comment,
                    folderName: _folderName
                });
            }
            if (collect_type.includes('img')) {

                await Promise.allSettled((item.pictures as anyObj[]).map((img, _index) => {
                    return new Promise(async (resolve) => {
                        const extname = await pathFn('extname', img.url)
                        downList.push({
                            type: 'img',
                            url: img.url,
                            dest: `${basePath}/img${_index + 1}${extname}`,
                            folderName: _folderName
                        });
                        resolve(true)
                    })
                }))
            }
            if (collect_type.includes('video')) {
                if (item.video && item.video.url) {
                    const url = item.video.url;
                    await new Promise(async (resolve) => {
                        const extname = await pathFn('extname', url)
                        downList.push({
                            type: 'video',
                            url,
                            dest: `${basePath}/video${extname}`,
                            folderName: _folderName
                        });
                        resolve(true)
                    })
                }

            }
            resolve(true)
        })
    })
    await Promise.allSettled(downPromiseArr)
    state.dialog = false
    const max = downList.length
    let current = 0
    const _downList = [...downList]
    const requestList = downList.splice(0, 20)

    await new Promise((_end => {
        requestList.forEach(_item => {
            const fn = (item:typeof _item) => {
                if(!item){
                   _end(true)
                   return
                }
                new Promise((resolve) => {
                    switch (item.type) {
                        case 'img':
                        case 'video': {
                            download({
                                url: item.url!,
                                dest: item.dest!,
                                delay: item.type === 'video' ? 5 * 60 * 1000 : void 0
                            })
                                .then(res => {
                                    current++
                                    emits('log', { msg: `${item.dest!}下载成功(${current}/${max})`, 'type': 'success' })
                                })
                                .catch(res => {
                                    current++
                                    emits('log', { msg: `${item.dest!}下载失败，${res.msg}(${current}/${max})`, 'type': 'danger' })
                                })
                                .finally(() => {

                                    resolve(true)
                                })
                            break
                        }
                        case 'txt': {
                            writeFile({ dest: item.dest!, data: item.txt! })
                                .finally(() => {
                                    current++
                                    emits('log', { msg: `${item.dest!}下载成功(${current}/${max})`, 'type': 'success' })
                                    resolve(true)
                                })
                        }
                    }
                })
                    .finally(() => {
                        if(downList.length){
                            fn(downList.shift()!)
                        }else{
                            _end(true)
                        }
                    })
            }
            fn(_item)
        })
    }))
    buttonLoading.down = false
    emits('log', { type: 'success', msg: '下载完成' })
    const set = new Set(_downList.map(item => item.folderName))
    const data = [...set.values()].map(item => ({ name: item, path: `${folderName}/${item}` }))
    emits('finally', { userSelectFolders: data, path: folderName })
}

defineExpose({
    openConfigDialog
})

</script>
<style lang='scss'  rel="stylesheet/scsss" scoped></style>
<style>
/* .el-dialog.collect-comment-config {
    margin-left: 600px;
} */
</style>
