<template>
    <div class="task-details able-select">
        <div class="task-info">
            <h5>
                任务ID：{{ taskDetailsState.taskItem?.task_id }}
                <el-tag type="warning" v-if="taskStore.paused.has(taskDetailsState.taskItem?.task_id || '')">已暂停</el-tag>
                <el-tag type="success" v-if="taskStore.loading.has(taskDetailsState.taskItem?.task_id || '')">执行中</el-tag>
            </h5>
            <p>
                <el-space>
                    <span> <span class="label">正在执行：</span> <span class="value success">{{
                        taskDetailsState.taskItem?.loading.size
                    }}</span> </span>
                    <span> <span class="label">正在排队：</span> <span class="value ">{{ taskDetailsState.taskItem?.pending.size
                    }}</span> </span>
                    <span> <span class="label">下次可执行：</span> <span class="value ">
                            {{ dateFormat(taskDetailsState.taskItem!.nextExecTime) }} <el-tag type="warning"
                                v-if="taskStore.paused.has(taskDetailsState.taskItem?.task_id || '')">已暂停</el-tag> </span>
                    </span>
                </el-space>
            </p>
            <p>
                <span class="label">已完成：</span> <span> <span class="primary">{{ taskDetailsState.taskItem?.complete_num
                }}</span>/{{ taskDetailsState.taskItem?.order_num }} </span>
                <el-progress v-if="taskDetailsState.taskItem?.complete_num && taskDetailsState.taskItem?.order_num"
                    :percentage="Math.round((taskDetailsState.taskItem?.complete_num / taskDetailsState.taskItem?.order_num) * 100)"></el-progress>
            </p>
        </div>
        <el-space>
            <el-radio-group v-model="state.type">
                <el-radio-button label="pending">排队中({{ taskDetailsState.taskItem?.pending.size }})</el-radio-button>
                <el-radio-button label="loading">正在执行 ({{ taskDetailsState.taskItem?.loading.size }})</el-radio-button>
                <el-radio-button label="finish">已结束 ({{ taskDetailsState.taskItem?.finish.size }})</el-radio-button>

            </el-radio-group>
            <el-checkbox label="仅显示已结束返回成功但无订单信息" v-model="state.checkError"></el-checkbox>
        </el-space>
        <el-scrollbar height="450">
            <div class="record-item" v-for="value in showList">
                <p> <span class="label">执行位置:</span> <span class="value">{{ value.sort }}</span> <el-divider
                        direction='vertical'></el-divider> <span class="label">当前请求次数：</span> <span class="value">{{
                            value.requestCount }}</span> </p>
                <p>
                    <span class="label">小号位置:{{ value.account_site }}</span> <el-divider direction='vertical'></el-divider>
                    <span class="label">小号：{{ value.account }}</span> <el-divider direction='vertical'></el-divider>
                    <span class="label">标识：{{ value.id }}</span><el-divider direction='vertical'></el-divider>
                    <span class="label">order_site标识：{{ value.order_site }}</span>
                </p>
                <div class="history" v-if="value.history.length">
                    <h5>执行历史：</h5>
                    <div v-for="item in value.history" class="history-item" :class="{'error':!item.res.code && !item.res.data?.order_sn}">
                        <p>
                            <span class="label">小号位置:{{ item.account_site }}</span> <el-divider
                                direction='vertical'></el-divider>
                            <span class="label">小号：{{ item.account }}</span> <el-divider direction='vertical'></el-divider>
                            <span class="label">请求次序：<strong class="success">{{ item.requestCount }}</strong>
                            </span><el-divider direction='vertical'></el-divider>
                            <span class="label">order_site标识：{{ item.order_site }}</span>
                        </p>
                        <p class="res">
                            <span class="label">结果： <el-tag v-if="!item.res.code" type="success">成功</el-tag> <el-tag
                                    v-else-if="item.res.code" type="danger">失败</el-tag> </span>

                        </p>
                        <p class="res" v-if="item.res.code">
                            <span class="label">错误信息: <span class="danger">{{ item.res.msg }}</span> </span>
                        </p>
                        <p class="res" v-else>
                            <span class="label">订单编号：{{ item.res.data?.order_sn }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </el-scrollbar>
    </div>
</template>
<script lang='ts' setup>
import { computed, reactive } from 'vue';
import { TaskItem, useTask } from '/@/stores/task';
import { dateFormat } from '/@/utils/common';

const taskStore = useTask()
const props = defineProps<{
    taskDetailsState: {
        taskItem?: TaskItem
    }
}>()



const state: {
    type: "finish" | 'loading' | 'pending'
    checkError: boolean
} = reactive({
    type: 'finish',
    checkError: false
})

const showList = computed(() => {
    const taskItem = props.taskDetailsState.taskItem!
    switch (state.type) {
        case 'pending': {
            return [...taskItem.pending.values()].sort((a, b) => a.sort - b.sort)
        }
        case 'loading': {
            return [...taskItem.loading.values()].sort((a, b) => a.sort - b.sort)
        }
        case 'finish': {
            const list = [...taskItem.finish.values()].sort((a, b) => a.sort - b.sort)
            if (state.checkError) {
                return list.filter(item => {
                    return !!item.history.find(history => {
                        return !history.res.code && !history.res.data?.order_sn
                    })
                })
            } else {
                return list
            }
        }
    }
})



</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
.label {
    font-size: 12px;
}

.value {
    color: var(--el-text-color-primary);
}

.task-info {
    border: var(--el-border);
    border-radius: 6px;
    box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.08);
    min-height: 92px;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 13px;

    h5 {
        color: var(--el-text-color-primary);
        font-size: 16px;
        font-weight: bolder;
    }
}

.el-radio-group {
    margin-bottom: 5px;
}

.record-item {
    min-height: 58px;
    box-sizing: border-box;
    padding: 8px;
    border-radius: 6px;
    background-color: var(--el-fill-color-light);
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    font-size: 14px;

    p {
        margin: 5px 0;
    }

    .history {
        padding: 10px;
        box-sizing: border-box;
        border: 1px solid var(--el-color-primary);
        border-radius: 4px;

        .history-item {
            margin-top: 20px;
            box-shadow: 0 0 5px rgba($color: #000000, $alpha: .5) inset;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 5px;
            &.error{
                box-shadow: 0 0 5px rgba($color: var(--el-color-warning), $alpha: .5) inset;
            }
        }
    }

    &+.record-item {
        margin-top: 4px;
    }
}
</style>
