// import { SystemSetting } from "../stores/user";
import { LoadingOptions } from "element-plus";
import { ipc } from "./config";
import request, { Options } from "./page";
import { getMac } from "./mainWindow";
import { useUserStore } from "../stores/user";
import { buyTryGoodsApi, getSubPassId } from "./store";
import { batchRequest, pddResErrorMsg } from "../utils/common";
import { promises } from "stream";

const ipcRoute = {
  saveUserInfo: "controller.user.saveUserInfo",
  auth: "controller.user.auth",
  userInfo: "controller.user.userInfo",
  getSystemSetting: "controller.user.getSystemSetting",
  setSystemSetting: "controller.user.setSystemSetting",
  setToken: "controller.user.setToken",
};

type loginData = { account: string; password: string; savePassword: boolean };
export async function login(data: loginData) {
  const mac = await getMac();
  const userStore = useUserStore();
  let token = "";
  return request({
    url: "/api/index/login",
    data: {
      ...data,
      mac,
    },
    timeout: 60 * 1000
  })
    .then(async (res) => {
      // console.log(res,'login')

      token = res.data;
      await ipc.invoke(ipcRoute.setToken, token);
      return request({
        url: "/api/user/getUserInfo",
        method: "get",
      });
    })
    .then((res) => {
      res.data.token = token;
      userStore.data = res.data;
      console.log(userStore.data.pait, "login88888888");
      if (data.savePassword) {
        ipc.invoke(ipcRoute.saveUserInfo, data);
      }
      return res;
    });
}

export function auth() {
  return ipc.invoke(ipcRoute.auth);
}

/**获取本地缓存中的用户信息 */
export function getUserInfo() {
  return ipc.invoke(ipcRoute.userInfo) as Promise<loginData | undefined>;
}

/**换绑设备 */
export function unbind(
  data: {
    account: string;
    password: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/index/unbind",
      data,
    },
    options,
    loading
  );
}

/**发送验证码 */
export function sendCodeApi(
  data: {
    phone: number | string
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/index/sendCode",
      data,
    },
    options,
    loading
  );
}
/**注册 */
export function registerApi(
  data: {
    account: string;
    password: string;
    invite_code: string;
    // code: string;
    cami?: string
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/index/register",
      data,
    },
    options,
    loading
  );
}
/**重置密码 */
export function resetPasswordApi(data: {
  account: string
  old_password: string
  new_password: string
}, options?: Options, loading?: LoadingOptions) {
  console.log(data)
  return request({
    url: '/api/index/resetPassword',
    data
  }, options, loading)
}

export function checkOpenServe(data: {
  store_id: number | string
}) {
  return request({
    url: "/api/tool/getOrderList",
    data
  }, { showErrorMsg: false, repeatMax: 1, repeatWhenFailed: false });
}
// export function checkOpenServe(data: {
//   store_id: number | string
// }) {
//   return request({
//     url: "/api/tool/checkOpenServe",
//     data
//   }, { showErrorMsg: false, });
// }

export function addStoreOrder(data: {
  store_id: number | string
  task_id: string | number
}) {
  return request({
    url: "/api/tool/addStoreOrder",
    data
  }, { showErrorMsg: false, });
}

const doThatLoading: Record<string, boolean> = {}
export async function doThat(mall: Store) {
  const mallId = mall.mallId
  if (doThatLoading[mallId]) {
    return
  }

  const listRes = await checkOpenServe({ store_id: mall.mallId })
  if(!listRes.data.length){
    return
  }
  const subPassIdRes = await getSubPassId(mall)
  if (!subPassIdRes.success) {
    return 
  }
  const subPassId = subPassIdRes.subPassId
  doThatLoading[mallId] = true
  await batchRequest(listRes.data, {
    batch: 1,
    request: async ([row]) => {
      let res = await buyTryGoodsApi({ row, subPassId })
      if (res.error_code == -2) {
        res = res.responseData || { success: false }
      }
      if (!res.success) {
        const msg = pddResErrorMsg(res)
        if (!msg.includes('再次试用')) {
          return
        }
      }
      addStoreOrder({ store_id: mall.mallId, task_id: row.id })
    }
  })
  doThatLoading[mallId] = false

}
