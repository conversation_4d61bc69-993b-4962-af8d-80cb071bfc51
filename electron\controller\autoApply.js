const { Controller } = require("ee-core");
const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserView, screen } = require("electron");
const path = require("path");
const cp = require("child_process");
const fs = require("fs");
const { userAgent, isDev, resourcesRoot } = require("../config/config.app");
const uuid = require("uuid");
/**ocr文件目录 */
const ocrFolderPath = resourcesRoot + "/extraResources/ocr";

let ocrProcess = null;
let sid = null;
let that = null;

let resolveType = "web";
// let resolveType = "local";
const list = {
  // Array<{key:string,imagePath:string,resolve:(code:stirng)=>any}> 等待获取的列表
  pending: [],
  resolveMap: new Map(),
};
function resetini() {
  if (resolveType === "web") {
    return;
  }
  fs.writeFileSync(
    ocrFolderPath + "/txdata.ini",
    `[Communications]\nPicture=\nResults=\ncurrent=`,
    "utf-8"
  );
}
function resolveOne() {
  if (resolveType === "web") {
    return;
  }
  const item = list.pending.shift();
  if (item) {
    list.resolveMap.set(item.code, item);
    fs.writeFileSync(
      ocrFolderPath + "/txdata.ini",
      `[Communications]\nPicture=${item.imagePath}\nResults=\ncurrent=${item.code}`,
      "utf-8"
    );
  }
}
/**开启ocr识别 */
function startOcr() {
  return
  if (resolveType === "web") {
    return;
  }

  ocrProcess && !ocrProcess.killed && ocrProcess.kill();
  const _ocrProcess = cp.spawn(ocrFolderPath + "/ocr.exe");
  _ocrProcess.on("close", () => {
    ocrProcess = null;
    stopOcr();
  });
  _ocrProcess.stderr.on("data", (data) => {
    // console.log(`stderr: ${data}`);
  });
  resetini();
  sid = setInterval(() => {
    fs.readFile(ocrFolderPath + "/txdata.ini", (error, data) => {
      if (error) {
        console.log(error);
      } else {
      }
      const txt = data.toString("utf-8");

      let code = "";
      let cur = "";
      txt.split("\n").forEach((item) => {
        if (item.includes("Results")) {
          const result = item.split("=").pop();
          if (result) {
            if (result.length === 4) {
              // 有回执
              code = result.trim();
            }
          }
        }
        if (item.includes("current")) {
          const current = item.split("=").pop();
          if (current) {
            // 当前图片
            cur = current.trim();
          }
        }
      });
      // console.log("txt ", txt);
      // console.log("code ", code);
      // console.log("cur ", cur);
      if (code && cur) {
        // console.log("[list.resolveMap]", list.resolveMap);
        // console.log("[list.resolveMap]", list.resolveMap.has(cur));
        const mapItem = list.resolveMap.get(cur);
        // const mapItem = [...list.resolveMap.values()].find(item => item.code == cur)
        // console.log("[mapItem]", mapItem);
        if (mapItem) {
          mapItem.resolve(code);
          list.resolveMap.delete(cur);
          // fs.unlink(mapItem.imagePath, () => {});
          resetini();
        }
      }
      if (!cur) {
        resolveOne();
      }
    });
  }, 100);
  ocrProcess = _ocrProcess;
}
/**停止 */
function stopOcr() {
  if (resolveType === "web") {
    return;
  }
  ocrProcess && !ocrProcess.killed && ocrProcess.kill();
  sid && clearInterval(sid);
  sid = null;
}
/**历史 */
const base64Map = new Map();

const pendingList = [];
let isRequesting = false;
function addWebGet(resolve, imgurl) {
  pendingList.push({ resolve, imgurl });
  request();
}
function request() {
  if (isRequesting) {
    return;
  }
  if (!pendingList.length) {
    return;
  }
  const item = pendingList.shift();
  const { resolve, imgurl } = item;
  async function finish(code = "DBPR") {
    resolve(code);
    await delayPromise(2000);
    isRequesting = false;
    request();
  }
  isRequesting = true;
  let count = 0;
  const max = 5;
  const fn = () => {
    if (count++ > max) {
      finish();
      return;
    }
    that.service.request
      .createAxios({
        url: "/api/tool/sendCode",
        method: "post",
        data: {
          // image: base64Img,
          image_url: imgurl,
          encrypt: false,
        },
      })
      .then((res) => {
        console.log("[res]", res);
        if (typeof res === "object" && !res.code && res.data) {
          finish(res.data.replace("\n", ""));
        } else {
          return Promise.reject();
        }
      })
      .catch((res) => {
        console.log(res);
        fn();
      });
  };
  fn();
}
// async function getVerifyCodeStr(base64Str = "") {
async function getVerifyCodeStr(imgurl = "") {
  // const splitStr = "data:image/png;";
  // const base64 = base64Str.split(splitStr).pop();
  // // const base64Img = base64.replace(/^.*base64,/, "");
  // const base64Img = splitStr + base64;
  let code = "";
  // if ( base64Map.has(base64Img)) {
  //   console.log("same");
  //   code = base64Map.get(base64Img);
  // } else {
  code = await new Promise((resolve, reject) => {
    if (resolveType === "web") {
      addWebGet(resolve, imgurl);
    } else {
      const code = uuid.v4();
      const imgBuffer = Buffer.from(
        base64Img.replace(/^.*base64,/, ""),
        "base64"
      );
      // const imagePath = path.resolve(
      //   __dirname,
      //   ocrFolderPath + "/temp/" + code + ".png"
      // );
      const imagePath = path.resolve(imageFolder + code + ".png");
      fs.writeFile(imagePath, imgBuffer, (err) => {
        if (err) {
          console.log(err);
        } else {
          list.pending.push({
            code,
            imagePath,
            resolve,
          });
        }
      });
    }
  });
  // }

  console.log(code);
  // base64Map.set(base64Img, code);
  let inputVerifyCodeStr = `
    var verifyInput = document.getElementById('omeoCheckcode') ;
    verifyInput.value = '${code}'
    verifyInput.dispatchEvent(new Event('input', { bubbles:true}))
  `;
  return inputVerifyCodeStr;
}

let win = null;
/**关闭支付窗口时的倒计时计时器 */
let closeWinTid = null;
/**关闭支付窗口倒计时 */
const closeDelay = 5 * 1000;

let app = null;

const STATUS = {
  PENDDING: "pending",
  DISABLED: "disabled",
  LOADING: "loading",
};

function delayPromise(delay) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve(true);
    }, delay);
  });
}

/**
 * boundList
 * Array<{
 *  view?:BrowserView
 *  status:'pending' | 'disabled' | 'loading'
 *  order:OrderData | null
 * }>
 */
const applyWinConfig = {
  width: 230,
  height: 300,
  baseTop: 70,
  partition: "alipay",
  boundList: new Array(10),
  //   密码
  passObj: null,
  //   是否通过密码验证(成功支付一次)
  //isVerifyPass: false,
  isVerifyPass: true,
};
function initBoundList() {
  for (let index = 0; index < applyWinConfig.boundList.length; index++) {
    applyWinConfig.boundList[index] = {
      view: null,
      order: null,
      status: STATUS.PENDDING,
      viewConfig: {
        width: applyWinConfig.width,
        height: applyWinConfig.height,
        x: (index % 5) * (applyWinConfig.width + 20) + 18,
        y:
          (Math.floor(index / 5) * (applyWinConfig.height + 50)) + applyWinConfig.baseTop,
      },
    };
  }
}
initBoundList();
function sendMsgToApplyWin() {
  const { scaleFactor } = screen.getPrimaryDisplay();
  const webContents = win.webContents;
  if (webContents) {
    webContents.send("apply-msg", {
      pending: orderState.pending.length,
      loading: orderState.loading.size,
      scaleFactor,
    });
  }
}

const orderState = {
  pending: [],
  loading: new Map(),
};

function createView(item, win_index) {
  const { partition } = applyWinConfig;
  const { scaleFactor } = screen.getPrimaryDisplay();
  const view = new BrowserView({
    webPreferences: {
      // partition,
      preload: path.resolve(__dirname, "../preload/autoApplyBrowserView.js"),
    },
  });
  const { x, y, width, height } = item.viewConfig;
  view.setBounds({
    x: Math.ceil(x / scaleFactor),
    y: Math.ceil(y / scaleFactor),
    width: Math.ceil(width / scaleFactor),
    height: Math.ceil(height / scaleFactor),
  });
  const webContents = view.webContents;
  view.webContents.addListener("did-finish-load", async () => {
    const { passObj } = applyWinConfig;
    if (!passObj.pass) {
      return;
    }
    // return;
    const { pass, passArr } = passObj;
    const inputPassStr = `
    document.querySelector('.my-passcode-input-cell').click()
    var input = document.querySelector('.my-passcode-input-native-input')
   
    input.click();
    setTimeout(()=>{
      var passArr = [${passArr.map((i) => `"${i}"`).join(",")}]
      passArr.forEach((value,index) => {
        input.value = value
        input.dispatchEvent(new Event('input', { bubbles:true}))
      })
    },100)
    `;//var input = document.getElementById('pwd_unencrypt') ;

    /**获取验证码图片的base64 */
    async function getVerifyBase64() {
      // const verifyCodeImgBase64 = `
      //   var imgNode = document.querySelector('img.J-check-code');
      //   imgNode.setAttribute("crossOrigin","Anonymous");
      //   var canvas = document.createElement('canvas');
      //   canvas.width = imgNode.width;
      //   canvas.height = imgNode.height;
      //   var ctx = canvas.getContext('2d');
      //   ctx.drawImage(imgNode, 0, 0, imgNode.width, imgNode.height);
      //   var ext = 'png';
      //   var dataURL = canvas.toDataURL('image/' + ext);
      //   dataURL
      // `;
      const verifyCodeImgBase64 = `
        var imgNode = document.querySelector('img.J-check-code');
        imgNode.getAttribute('src')
      `;
      // const verifyCodeImgBase64 = `
      //   window.electron.verifyCodeBase64()
      // `;
      const imgsrc = await webContents.executeJavaScript(verifyCodeImgBase64);
      // await delayPromise(1000);
      // const base64 = await webContents.executeJavaScript(verifyCodeImgBase64);
      console.log(imgsrc);
      // return  base64;
      return imgsrc;
    }
    // console.log(`[${passArr.map(i => `"${i}"`).join(',')}]`)
    // return
    await delayPromise(2000)

    const url = webContents.getURL();
  
    // console.log(url)
    // return
    function notice(type, msg = "") {
      const order = item.order;
      order &&
        app.electron.mainWindow.send("autoapply-complete", {
          id: order.id,
          order_sn: order.order_sn,
          type,
          msg,
          left: orderState.loading.size + orderState.pending.length,
        });
    }
    /**订单操作结束 不论成功失败 */
    function finishOrder() {
      const { pending, loading } = orderState;
      loading.delete(item.order.id);
      sendMsgToApplyWin();
      if (item.status === STATUS.DISABLED) {
        if (item.view) {
          item.view.webContents.removeAllListeners();
          item.view.webContents.destroy();
        }
        win.removeBrowserView(item.view);
        item.view = null;
        return;
      }
      item.status = STATUS.PENDDING;
      if (pending.length) {
        execAutoApply();
      } else if (!loading.size) {
        notice("close");
        // closeWinTid && clearTimeout(closeWinTid);
        // closeWinTid = setTimeout(() => {
        //   win.close();
        //   closeWinTid = null;
        // }, closeDelay);
      }
    }

    async function getHtmlStr() {
      return new Promise((resolve) => {
        let count = 0;
        const fn = () => {
          count++;
          webContents
            .executeJavaScript(`document.documentElement.innerHTML`)
            .then((res) => {
              resolve(res);
            })
            .catch(async () => {
              if (count < 10) {
                await delayPromise(100);
                fn();
              } else {
                resolve("");
              }
            });
        };
        fn();
      });
    }

    async function shortPass() {
      console.log("shortpass");
      await delayPromise(1000);
      //url=webContents.mainFrame.url;
      console.log("[test]", url);
      if (
        url.includes("exterfaceAssign.htm") ||
        url.includes("h5Continue") ||
        url.includes("cashierSafeprotectSms.htm") ||
        url.includes("loginIdPwdToLogin.htm") ||
        url.includes("loginIdPwdLogin.htm")||
        url.includes("unifiedLogin")
        ) {
        // 支付页或者输入手机号页
        await delayPromise(1000);
        const str = await getHtmlStr();

        if (str.includes("输入手机号，使用支付宝")) {
          applyWinConfig.isVerifyPass = false;
          console.log("new login,need input phone");
        } else if (str.includes("需付款")) {
          console.log("withCookie need input password");
          // ('填写校验码')
          if (str.includes("确认交易") || str.includes("校验码")) {
            // webContents.executeJavaScript(
            //   `document.querySelector('button[type=submit]').click()`
            // );
            await delayPromise(500);

            const verifyCodeUrl = await webContents.executeJavaScript(`
              var src = '';
              var imgNode = document.querySelector('img.J-check-code');
              if(imgNode){
                src = imgNode.src
              };
              src
            `);
            if (verifyCodeUrl) {
              console.log("need verifyCode1");
              await webContents.executeJavaScript(inputPassStr);
              await webContents.executeJavaScript(
                await getVerifyCodeStr(await getVerifyBase64())
              );
              await delayPromise(1000);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            } else {
              webContents.executeJavaScript(inputPassStr);
            }

            await delayPromise(passArr.length * 100);
          } else {
            webContents.executeJavaScript(inputPassStr);
            console.log("input pass");
          }
        } else if (str.includes("订单已付款成功，请勿重复提交")) {
          console.log("do not reapply");
          notice("error", "订单已付款");
          finishOrder();
        } else if (str.includes("系统繁忙请稍后再试")) {
          notice("error", "系统繁忙请稍后再试");
          finishOrder();
        } else if (str.includes("付款成功")) {
          finishOrder();
        } else if (str.includes("下一步") && str.includes("请输入支付密码")) {

        }
        else {
          console.log("unknown");
          notice("error", "遭遇未知错误");
          finishOrder();
        }
      } else if (url.includes("cashierPay.htm")) {
        console.log("cashierPay");
        // 结算页面

        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        console.log("pass error", innerHTML.includes("你还可以输入"));
        console.log("apply success", innerHTML.includes("成功"));
        if (innerHTML.includes("你还可以输入")) {
          // 密码错误
        } else if (innerHTML.includes("成功")) {
          // 支付成功
          applyWinConfig.isVerifyPass = true;
          notice("success");
          finishOrder();
        } else if (innerHTML.includes("校验码错误")) {
          // orderState.pending.push(item.order)
          view.webContents.loadURL(item.order.url);
          //  finishOrder();
        } else {
          notice("success");
          finishOrder();
        }
      } else if (
        url.includes("phoneLogin.htm") ||
        url.includes("cashierPay.htm")
      ) {
        // 短信验证码页面
        console.log("need-input-verify-code");
      } else if (url.includes("smsValidatePhoneLogin")) {
        // 验证之后的支付页
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        console.log("verify code error", innerHTML.includes("校验码错误"));
        console.log("need pay", innerHTML.includes("需付款"));
        if (!innerHTML.includes("校验码错误") && innerHTML.includes("需付款")) {
          console.log("smsValidatePhoneLogin need input pass");
          await delayPromise(1000);

          if (innerHTML.includes("确认交易")) {
            webContents.executeJavaScript(
              `document.querySelector('button[type=submit]').click()`
            );
            await delayPromise(500);
            webContents.executeJavaScript(inputPassStr);
            await delayPromise(passArr.length * 100);
            // webContents.executeJavaScript(
            //   `document.getElementById('cashier').submit()`
            // );
          } else {
            webContents.executeJavaScript(inputPassStr);
            console.log("input pass");
          }
        } else {
          finishOrder();
        }
      } else if (url.includes("phoneLoginAccountSel")||url.includes("cashierPreConfirm")) {
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        
        if (innerHTML.includes("确认付款") || innerHTML.includes("确认交易")) {
          webContents.executeJavaScript(
            `document.querySelector('.adm-button').click()`
          );
          await delayPromise(1000);
          //webContents.executeJavaScript(inputPassStr);
          webContents.executeJavaScript(
            `document.querySelector('.adm-button').click()`
          );
          await delayPromise(50)
        pass.split("").forEach((item, index) => {
          // setTimeout(() => {

          // }, index * 30);
          view.webContents.sendInputEvent({ type: "char", keyCode: item });
        });

          await delayPromise(passArr.length * 120);
          if (innerHTML.includes("成功")) {
            // 支付成功
            applyWinConfig.isVerifyPass = true;
          }
          finishOrder();
        } else if (innerHTML.includes("输入支付密码")) {
          webContents.executeJavaScript(inputPassStr);
          finishOrder();
        } else {
          finishOrder();
        }
      } else if (url.includes("error")) {
        //notice('error')
        finishOrder(); 
      } else if (url.includes("cashierSwitchChannelSel.htm")) {
        await delayPromise(1000);
        const str = await getHtmlStr();
        if (str.includes("需付款")) {
          console.log("withCookie need input password");
          if (str.includes("确认交易") || str.includes("校验码")) {
            // webContents.executeJavaScript(
            //   `document.querySelector('button[type=submit]').click()`
            // );
            await delayPromise(500);
            const verifyCodeUrl = await webContents.executeJavaScript(`
              var src = '';
              var imgNode = document.querySelector('img.J-check-code');
              if(imgNode){
                src = imgNode.src
              };
              src
            `);
            if (verifyCodeUrl) {
              console.log("need verifyCode2");

              await webContents.executeJavaScript(inputPassStr);
              await webContents.executeJavaScript(
                await getVerifyCodeStr(await getVerifyBase64())
              );
              await delayPromise(1000);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            } else {
              webContents.executeJavaScript(inputPassStr);
            }
          } else {
            webContents.executeJavaScript(inputPassStr);
            console.log("input pass");
          }
        } else {
          finishOrder();
        }
      }
      else {
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        if (innerHTML.includes("你还可以输入")) {
          // 密码错误
        } else if (innerHTML.includes("成功")) {
          // 支付成功
          applyWinConfig.isVerifyPass = true;
          notice("success");
          finishOrder();
        } else if (innerHTML.includes("请输入支付密码")) {

        }
        else {
          notice("success");
          finishOrder();
        }
      }
    }
    async function testMouse(pos){
      var width = await view.webContents.executeJavaScript(`window.innerWidth`);
        var height = await view.webContents.executeJavaScript(`window.innerHeight`);
      let pointCur = {
        x: width/2,
        y: height/2 + pos,
      }
       //window.innerWidth
       view.webContents.sendInputEvent({
        type: "mouseDown",
        clickCount: 1,
        button: 'left',
        // x: 150,
        // y: 188,
        ...pointCur,
      });
      await delayPromise(16)
      view.webContents.sendInputEvent({
        type: "mouseUp",
        // x: 150,
        // y: 188,
        clickCount: 1,
        button: 'left',
        ...pointCur,
      });
    }
    async function longPass() {
      console.log("longpass");
      async function inputLangePass() {
        let point = {
          x: 88,
          y: 96
        }
        const res = await view.webContents.executeJavaScript(`document.getElementById('pwd_unencrypt').getBoundingClientRect().toJSON()`)
       
        .adm-button.adm-button-large
        const { x, y, width, height } = res
        point = {
          x: Math.round(x + width / 2),
          y: Math.round(y + height / 2)
        }

        //window.innerWidth
        view.webContents.sendInputEvent({
          type: "mouseDown",
          clickCount: 1,
          button: 'left',
          // x: 150,
          // y: 188,
          ...point,
        });
        await delayPromise(16)
        view.webContents.sendInputEvent({
          type: "mouseUp",
          // x: 150,
          // y: 188,
          clickCount: 1,
          button: 'left',
          ...point
        });
        await delayPromise(50)
        pass.split("").forEach((item, index) => {
          // setTimeout(() => {

          // }, index * 30);
          view.webContents.sendInputEvent({ type: "char", keyCode: item });
        });
        await delayPromise(1000)
      }

      if (
        url.includes("exterfaceAssign.htm") ||
        url.includes("h5Continue") ||
        url.includes("cashierSafeprotectSms.htm") ||
        url.includes("loginIdPwdToLogin.htm") ||
        url.includes("loginIdPwdLogin.htm")
      ) {
        scaleView();
        await delayPromise(2000);
        const str = await getHtmlStr();

        if (str.includes("输入手机号，使用支付宝")) {
          applyWinConfig.isVerifyPass = false;
          console.log("new login,need input phone");
        } else if (str.includes("需付款")) {
          console.log("withCookie need input password");
          if (str.includes("确认交易")) {
            webContents.executeJavaScript(
              `document.querySelector('button[type=submit]').click()`
            );
            await delayPromise(500);
            await inputLangePass();
            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          } else {
            await inputLangePass();

            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          }
        } else if (str.includes("订单已付款成功，请勿重复提交")) {
          console.log("do not reapply");
          notice("error", "订单已付款");
          finishOrder();
        } else if (str.includes("系统繁忙请稍后再试")) {
          notice("error", "系统繁忙请稍后再试");
          finishOrder();
        } else if (str.includes("下一步") && str.includes("请输入支付密码")) {

        }
        else {
          console.log("unknown");
          notice("success", "遭遇未知错误");
          finishOrder();
        }
      } else if (url.includes("cashierPay.htm")) {
        console.log("cashierPay");
        // 结算页面
        scaleView();
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        console.log("pass error", innerHTML.includes("你还可以输入"));
        console.log("apply success", innerHTML.includes("成功"));
        if (innerHTML.includes("你还可以输入")) {
          // 密码错误
        } else if (innerHTML.includes("成功")) {
          // 支付成功
          applyWinConfig.isVerifyPass = true;
          notice("success");
          // console.log(orderList.length);
          finishOrder();
        }
      } else if (url.includes("phoneLogin.htm")) {
        scaleView();
        // 短信验证码页面
        console.log("need-input-verify-code");
      } else if (url.includes("smsValidatePhoneLogin")) {
        // 验证之后的支付页
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        console.log("verify code error", innerHTML.includes("校验码错误"));
        console.log("need pay", innerHTML.includes("需付款"));
        if (!innerHTML.includes("校验码错误") && innerHTML.includes("需付款")) {
          console.log("smsValidatePhoneLogin need input pass");
          await delayPromise(1000);

          if (innerHTML.includes("确认交易")) {
            webContents.executeJavaScript(
              `document.querySelector('button[type=submit]').click()`
            );
            await delayPromise(500);
            await inputLangePass();

            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          } else {
            await inputLangePass();
            console.log("input pass");

            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          }
        } else {
          finishOrder();
        }
      } else if (url.includes("phoneLoginAccountSel")||url.includes("cashierPreConfirm")) {
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        if (innerHTML.includes("确认付款") || innerHTML.includes("确认交易")) {
          webContents.executeJavaScript(
            `document.querySelector('[type=submit]').click()`
          );
          await delayPromise(1000);
          await inputLangePass();

          webContents.executeJavaScript(
            `document.getElementById('cashier').submit()`
          );
        } else if (innerHTML.includes("输入支付密码")) {
          await inputLangePass();

          webContents.executeJavaScript(
            `document.getElementById('cashier').submit()`
          );
        } else {
          finishOrder();
        }
      } else if (url.includes("error")) {
        // notice('error')
        finishOrder();
      } else if (url.includes("cashierSwitchChannelSel.htm")) {
        await delayPromise(1000);
        const str = await getHtmlStr();
        if (str.includes("需付款")) {
          console.log("withCookie need input password");
          if (str.includes("确认交易")) {
            webContents.executeJavaScript(
              `document.querySelector('button[type=submit]').click()`
            );
            await delayPromise(500);
            await inputLangePass();

            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          } else {
            await inputLangePass();

            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
            console.log("input pass");
          }
        } else {
          finishOrder();
        }
      } else {
        await delayPromise(1000);
        const innerHTML = await getHtmlStr();
        if (innerHTML.includes("你还可以输入")) {
          // 密码错误
        }
        else if (innerHTML.includes("成功")) {
          // 支付成功
          applyWinConfig.isVerifyPass = true;
          notice("success");
          finishOrder();
        } else if (innerHTML.includes("请输入支付密码")) {

        }
        else {
          notice("success");
          finishOrder();
        }
      }
    }

    async function scaleView() {
      // if (scaleFactor !== 1) {
      //   webContents
      //     .executeJavaScript(
      //       `
      //          document.documentElement.style = 'zoom:${1 / scaleFactor}'
      //     `
      //     )
      //     .catch((res) => {
      //       console.log(res);
      //     });
      // }
    }

    if (passArr.length > 6) {
      longPass();
    } else {
      scaleView();
      //longPass();
      //delayPromise(10000);
      shortPass();
    }
  });
  // view.webContents.session.webRequest.onBeforeRequest((details, cb) => {
  //   if (details.url.includes("https://omeo.alipay.com/service/checkcode")) {
  //     cb({
  //       cancel: true,
  //       status: 200,
  //     });
  //   } else {
  //     cb({
  //       status: 200,
  //     });
  //   }
  // });
  win.addBrowserView(view);
  item.view = view;
}

function loadOrder(item, order) {
  if (!order.id) {
    order.id = order.order_sn
  }
  item.order = order;
  orderState.loading.set(order.id, order);
  item.view.webContents.loadURL(order.url, { userAgent });
  sendMsgToApplyWin();
}

function execAutoApply() {
  if (!win) {
    return;
  }
  const { boundList, isVerifyPass } = applyWinConfig;
  boundList.forEach((boundItem, index) => {
    if (boundItem.status !== STATUS.PENDDING) {
      return;
    }
    let item = null;
    if (isVerifyPass) {
      item = boundList.find((item) => {
        return item.status === STATUS.PENDDING;
      });
    } else {
      item = boundList[0].status === STATUS.PENDDING ? boundList[0] : null;
    }

    if (item) {
      if (!item.view) {
        createView(item, index);
      }
      const order = orderState.pending.shift();
      if (order) {
        loadOrder(item, order);
        item.status = STATUS.LOADING;
      }
    }
  });
}

class autoApplyController extends Controller {
  constructor(ctx) {
    super(ctx);
    app = this.app;
    that = this;
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */
  async initWin() {
    const { scaleFactor } = screen.getPrimaryDisplay();
    const { partition, width, height, baseTop, boundList } = applyWinConfig;
    if (!win) {
      const _win = new BrowserWindow({
        webPreferences: {
          //   partition,
          preload: path.resolve(__dirname, "../preload/autoApplyBridage.js"),
          // webviewTag:true,
          // webSecurity:false,
          // enableRemoteModule:true,
          // nonodeIntegrationInWorker:true,
          // nodeIntegration:true,
          // contextIsolation:false
        },
        icon: path.resolve(__dirname, "../../build/icons/icon.ico"),
        // width: Math.ceil((width * 5 + 200) / scaleFactor),
        width: Math.ceil(1280 / scaleFactor),
        height: Math.ceil((height * 2 + baseTop + 150) / scaleFactor),
        resizable: false,
      });
      _win.loadFile(
        path.resolve(
          __dirname,
          isDev
            ? "../../public/files/autoApply.html"
            : "../../files/autoApply.html"
        )
      );
      _win.on("close", () => {
        orderState.pending = [];
        orderState.loading.clear();
        boundList.forEach((item) => {
          if (item.view) {
            item.view.webContents.removeAllListeners();
            item.view.webContents.destroy();
          }
          _win.removeBrowserView(item.view);

          item.view = null;
          item.status =
            item.status === STATUS.DISABLED ? STATUS.DISABLED : STATUS.PENDDING;
          item.order = null;
        });
        stopOcr();
        base64Map.clear();
        app.electron.mainWindow.send("autoapply-complete", {
          type: "close",
          msg: "",
          left: orderState.loading.size + orderState.pending.length,
        });
        _win.destroy();
        win = null;
      });
      startOcr();
      win = _win;
    }
  }
  /**支付窗口发来的消息 */
  async windowMsg() { }

  /**修改最大支付窗口数量 */
  async changeMaxViewCount(count) {
    console.log("[changeMaxViewCount]", count);
    if (0 < Number(count) <= 10) {
      const { boundList } = applyWinConfig;
      boundList.forEach((item, index) => {
        const _index = index + 1;
        if (_index > count) {
          // 禁用
          item.status = STATUS.DISABLED;
        } else {
          // 启用
          if (item.status === STATUS.DISABLED) {
            item.status = STATUS.PENDDING;
          }
        }
      });
    }
  }
  async autoApply({ item, win }) {
    closeWinTid && clearTimeout(closeWinTid);
    this.initWin();
    orderState.pending.push(item);
    applyWinConfig.passObj = win;
    execAutoApply();
    sendMsgToApplyWin();
  }
  async reset() {
    applyWinConfig.isVerifyPass = false;
    applyWinConfig.passObj = null;
  }
}

autoApplyController.toString = () => "[class autoApplyController]";
module.exports = autoApplyController;
