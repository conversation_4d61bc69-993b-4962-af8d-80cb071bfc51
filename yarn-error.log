Arguments: 
  D:\NODE\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\bin\yarn.js add ffi-napi refnapi

PATH: 
  C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;D:\python;D:\devTools\Git\cmd;C:\Users\<USER>\AppData\Local\Yarn\Data\global\node_modules\.bin;D:\devTools\bin;C:\Program Files\GraphicsMagick-1.3.40-Q16;D:\OpenSSL-Win64\bin;D:\NODE\;D:\python;D:\python3\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\IntelliJ IDEA 2022.2.3\bin;;C:\Users\<USER>\Desktop\工具\Microsoft VS Code\bin;D:\OpenSSL-Win64\bin;C:\Users\<USER>\AppData\Roaming\npm

Yarn version: 
  1.22.19

Node version: 
  16.20.1

Platform: 
  win32 x64

Trace: 
  Error: http://registry.npm.taobao.org/refnapi: Not found
      at Request.params.callback [as _callback] (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:66145:18)
      at Request.self.callback (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:140890:22)
      at Request.emit (node:events:513:28)
      at Request.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:141862:10)
      at Request.emit (node:events:513:28)
      at IncomingMessage.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:141784:12)
      at Object.onceWrapper (node:events:627:28)
      at IncomingMessage.emit (node:events:525:35)
      at endReadableNT (node:internal/streams/readable:1358:12)
      at processTicksAndRejections (node:internal/process/task_queues:83:21)

npm manifest: 
  {
    "name": "ddqz",
    "version": "1.5.9",
    "description": "",
    "main": "main.js",
    "scripts": {
      "start": "electron . ",
      "dev": "electron . --env=local",
      "reload": "nodemon --config ./electron/config/nodemon.json",
      "test": "set DEBUG=* && electron . --env=local",
      "build-w": "electron-builder -w=nsis --ia32",
      "build-w-64": "electron-builder -w=nsis --x64",
      "build-w-arm64": "electron-builder -w=nsis --arm64",
      "build-wz": "electron-builder -w=7z --ia32",
      "build-wz-64": "electron-builder -w=7z --x64",
      "build-wz-arm64": "electron-builder -w=7z --arm64",
      "build-m": "electron-builder -m",
      "build-m-arm64": "electron-builder -m --arm64",
      "build-l": "electron-builder -l=deb --ia32",
      "build-l-64": "electron-builder -l=deb --x64",
      "build-l-arm64": "electron-builder -l=deb --arm64",
      "build-l-armv7l": "electron-builder -l=deb --armv7l",
      "build-lr-64": "electron-builder -l=rpm --x64",
      "build-lp-64": "electron-builder -l=pacman --x64",
      "rd": "ee-core rd --dist_dir=./frontend/dist",
      "encrypt": "ee-core encrypt",
      "rebuild": "electron-rebuild",
      "re-sqlite": "electron-rebuild -f -w better-sqlite3"
    },
    "build": {
      "productName": "ddqz",
      "appId": "com.jycdy.ddqz",
      "copyright": "wallace5303",
      "directories": {
        "output": "out"
      },
      "asar": true,
      "files": [
        "**/*",
        "!frontend/",
        "!run/",
        "!logs/",
        "!data/",
        "!electron/",
        "!resources",
        "!temp"
      ],
      "extraResources": {
        "from": "./build/extraResources/",
        "to": "extraResources"
      },
      "electronDownload": {
        "mirror": "https://npmmirror.com/mirrors/electron/"
      },
      "nsis": {
        "oneClick": false,
        "allowElevation": true,
        "allowToChangeInstallationDirectory": true,
        "installerIcon": "./build/icons/icon.ico",
        "uninstallerIcon": "./build/icons/icon.ico",
        "installerHeaderIcon": "./build/icons/icon.ico",
        "createDesktopShortcut": true,
        "createStartMenuShortcut": true,
        "shortcutName": "D",
        "include": "./build/script/installer.nsh"
      },
      "publish": [
        {
          "provider": "generic",
          "url": "https://github.com/wallace5303/electron-egg"
        }
      ],
      "mac": {
        "icon": "./build/icons/icon.icns",
        "artifactName": "${productName}-${os}-${version}-${arch}.${ext}",
        "target": [
          "dmg",
          "zip"
        ]
      },
      "win": {
        "icon": "./build/icons/icon.ico",
        "artifactName": "${productName}-${os}-${version}-${arch}.${ext}",
        "target": [
          {
            "target": "nsis"
          }
        ]
      },
      "linux": {
        "icon": "./build/icons/icon.icns",
        "artifactName": "${productName}-${os}-${version}-${arch}.${ext}",
        "target": [
          "deb"
        ],
        "category": "Utility"
      }
    },
    "repository": "https://github.com/wallace5303/ee.git",
    "keywords": [
      "Electron"
    ],
    "author": "wallace5303, Inc <<EMAIL>>",
    "license": "Apache",
    "devDependencies": {
      "debug": "^4.3.3",
      "electron": "13.6.9",
      "electron-builder": "^23.1.0",
      "electron-rebuild": "^3.2.8",
      "eslint": "^5.13.0",
      "eslint-plugin-prettier": "^3.0.1",
      "nodemon": "^2.0.16"
    },
    "dependencies": {
      "@ffmpeg-installer/ffmpeg": "^1.1.0",
      "@ffprobe-installer/ffprobe": "1.4.1",
      "anyproxy": "^4.1.3",
      "axios": "^1.3.4",
      "better-sqlite3": "^7.6.0",
      "crypto-js": "^4.1.1",
      "dayjs": "^1.10.7",
      "ee-core": "^1.5.1",
      "electron-is": "^3.0.0",
      "electron-updater": "^5.3.0",
      "fluent-ffmpeg": "^2.1.2",
      "form-data": "^4.0.0",
      "iconv-lite": "^0.6.3",
      "jimp": "^0.22.8",
      "jschardet": "^3.0.0",
      "lodash": "^4.17.21",
      "node-xlsx": "^0.21.2",
      "sharp": "^0.32.1",
      "uuid": "^9.0.0"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "7zip-bin@~5.1.1":
    version "5.1.1"
    resolved "https://registry.npmmirror.com/7zip-bin/-/7zip-bin-5.1.1.tgz"
    integrity sha512-sAP4LldeWNz0lNzmTird3uWfFDWWTeg6V/MsmyyLR9X1idwKBWIgt/ZvinqQldJm3LecKEs1emkbquO6PCiLVQ==
  
  "@babel/code-frame@^7.0.0":
    version "7.21.4"
    resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.21.4.tgz"
    integrity sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g==
    dependencies:
      "@babel/highlight" "^7.18.6"
  
  "@babel/helper-validator-identifier@^7.18.6":
    version "7.19.1"
    resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"
    integrity sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==
  
  "@babel/highlight@^7.18.6":
    version "7.18.6"
    resolved "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz"
    integrity sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==
    dependencies:
      "@babel/helper-validator-identifier" "^7.18.6"
      chalk "^2.0.0"
      js-tokens "^4.0.0"
  
  "@develar/schema-utils@~2.6.5":
    version "2.6.5"
    resolved "https://registry.npmmirror.com/@develar/schema-utils/-/schema-utils-2.6.5.tgz"
    integrity sha512-0cp4PsWQ/9avqTVMCtZ+GirikIA36ikvjtHweU4/j8yLtgObI0+JUPhYFScgwlteveGB1rt3Cm8UhN04XayDig==
    dependencies:
      ajv "^6.12.0"
      ajv-keywords "^3.4.1"
  
  "@electron/get@^1.0.1":
    version "1.14.1"
    resolved "https://registry.npmmirror.com/@electron/get/-/get-1.14.1.tgz"
    integrity sha512-BrZYyL/6m0ZXz/lDxy/nlVhQz+WF+iPS6qXolEU8atw7h6v1aYkjwJZ63m+bJMBTxDE66X+r2tPS4a/8C82sZw==
    dependencies:
      debug "^4.1.1"
      env-paths "^2.2.0"
      fs-extra "^8.1.0"
      got "^9.6.0"
      progress "^2.0.3"
      semver "^6.2.0"
      sumchecker "^3.0.1"
    optionalDependencies:
      global-agent "^3.0.0"
      global-tunnel-ng "^2.7.1"
  
  "@electron/universal@1.2.1":
    version "1.2.1"
    resolved "https://registry.npmmirror.com/@electron/universal/-/universal-1.2.1.tgz"
    integrity sha512-7323HyMh7KBAl/nPDppdLsC87G6RwRU02dy5FPeGB1eS7rUePh55+WNWiDPLhFQqqVPHzh77M69uhmoT8XnwMQ==
    dependencies:
      "@malept/cross-spawn-promise" "^1.1.0"
      asar "^3.1.0"
      debug "^4.3.1"
      dir-compare "^2.4.0"
      fs-extra "^9.0.1"
      minimatch "^3.0.4"
      plist "^3.0.4"
  
  "@ffmpeg-installer/darwin-arm64@4.1.5":
    version "4.1.5"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/darwin-arm64/-/darwin-arm64-4.1.5.tgz#b7b5c262dd96d1aea4807514e1cdcf6e11f82743"
    integrity sha512-hYqTiP63mXz7wSQfuqfFwfLOfwwFChUedeCVKkBtl/cliaTM7/ePI9bVzfZ2c+dWu3TqCwLDRWNSJ5pqZl8otA==
  
  "@ffmpeg-installer/darwin-x64@4.1.0":
    version "4.1.0"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/darwin-x64/-/darwin-x64-4.1.0.tgz#48e1706c690e628148482bfb64acb67472089aaa"
    integrity sha512-Z4EyG3cIFjdhlY8wI9aLUXuH8nVt7E9SlMVZtWvSPnm2sm37/yC2CwjUzyCQbJbySnef1tQwGG2Sx+uWhd9IAw==
  
  "@ffmpeg-installer/ffmpeg@^1.1.0":
    version "1.1.0"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/ffmpeg/-/ffmpeg-1.1.0.tgz#87fdb9e7d180e8d78f7903f9441e36f978938a90"
    integrity sha512-Uq4rmwkdGxIa9A6Bd/VqqYbT7zqh1GrT5/rFwCwKM70b42W5gIjWeVETq6SdcL0zXqDtY081Ws/iJWhr1+xvQg==
    optionalDependencies:
      "@ffmpeg-installer/darwin-arm64" "4.1.5"
      "@ffmpeg-installer/darwin-x64" "4.1.0"
      "@ffmpeg-installer/linux-arm" "4.1.3"
      "@ffmpeg-installer/linux-arm64" "4.1.4"
      "@ffmpeg-installer/linux-ia32" "4.1.0"
      "@ffmpeg-installer/linux-x64" "4.1.0"
      "@ffmpeg-installer/win32-ia32" "4.1.0"
      "@ffmpeg-installer/win32-x64" "4.1.0"
  
  "@ffmpeg-installer/linux-arm64@4.1.4":
    version "4.1.4"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/linux-arm64/-/linux-arm64-4.1.4.tgz#7219f3f901bb67f7926cb060b56b6974a6cad29f"
    integrity sha512-dljEqAOD0oIM6O6DxBW9US/FkvqvQwgJ2lGHOwHDDwu/pX8+V0YsDL1xqHbj1DMX/+nP9rxw7G7gcUvGspSoKg==
  
  "@ffmpeg-installer/linux-arm@4.1.3":
    version "4.1.3"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/linux-arm/-/linux-arm-4.1.3.tgz#c554f105ed5f10475ec25d7bec94926ce18db4c1"
    integrity sha512-NDf5V6l8AfzZ8WzUGZ5mV8O/xMzRag2ETR6+TlGIsMHp81agx51cqpPItXPib/nAZYmo55Bl2L6/WOMI3A5YRg==
  
  "@ffmpeg-installer/linux-ia32@4.1.0":
    version "4.1.0"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/linux-ia32/-/linux-ia32-4.1.0.tgz#adad70b0d0d9d8d813983d6e683c5a338a75e442"
    integrity sha512-0LWyFQnPf+Ij9GQGD034hS6A90URNu9HCtQ5cTqo5MxOEc7Rd8gLXrJvn++UmxhU0J5RyRE9KRYstdCVUjkNOQ==
  
  "@ffmpeg-installer/linux-x64@4.1.0":
    version "4.1.0"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/linux-x64/-/linux-x64-4.1.0.tgz#b4a5d89c4e12e6d9306dbcdc573df716ec1c4323"
    integrity sha512-Y5BWhGLU/WpQjOArNIgXD3z5mxxdV8c41C+U15nsE5yF8tVcdCGet5zPs5Zy3Ta6bU7haGpIzryutqCGQA/W8A==
  
  "@ffmpeg-installer/win32-ia32@4.1.0":
    version "4.1.0"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/win32-ia32/-/win32-ia32-4.1.0.tgz#6eac4fb691b64c02e7a116c1e2d167f3e9b40638"
    integrity sha512-FV2D7RlaZv/lrtdhaQ4oETwoFUsUjlUiasiZLDxhEUPdNDWcH1OU9K1xTvqz+OXLdsmYelUDuBS/zkMOTtlUAw==
  
  "@ffmpeg-installer/win32-x64@4.1.0":
    version "4.1.0"
    resolved "https://registry.npmmirror.com/@ffmpeg-installer/win32-x64/-/win32-x64-4.1.0.tgz#17e8699b5798d4c60e36e2d6326a8ebe5e95a2c5"
    integrity sha512-Drt5u2vzDnIONf4ZEkKtFlbvwj6rI3kxw1Ck9fpudmtgaZIHD4ucsWB2lCZBXRxJgXR+2IMSti+4rtM4C4rXgg==
  
  "@ffprobe-installer/darwin-arm64@5.0.1":
    version "5.0.1"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/darwin-arm64/-/darwin-arm64-5.0.1.tgz#a020a623955d55aa8daf45cb668c3044876b553b"
    integrity sha512-vwNCNjokH8hfkbl6m95zICHwkSzhEvDC3GVBcUp5HX8+4wsX10SP3B+bGur7XUzTIZ4cQpgJmEIAx6TUwRepMg==
  
  "@ffprobe-installer/darwin-x64@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/darwin-x64/-/darwin-x64-5.0.0.tgz#66116b642cb1759939f4f4b3b18b0aea5d7814b0"
    integrity sha512-Zl0UkZ+wW/eyMKBPLTUCcNQch2VDnZz/cBn1DXv3YtCBVbYd9aYzGj4MImdxgWcoE0+GpbfbO6mKGwMq5HCm6A==
  
  "@ffprobe-installer/ffprobe@1.4.1":
    version "1.4.1"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/ffprobe/-/ffprobe-1.4.1.tgz#64e46bb21580e3383b86ec79606222117acfac90"
    integrity sha512-3WJvxU0f4d7IOZdzoVCAj9fYtiQNC6E0521FJFe9iP5Ej8auTXU7TsrUzIAG1CydeQI+BnM3vGog92SCcF9KtA==
    optionalDependencies:
      "@ffprobe-installer/darwin-arm64" "5.0.1"
      "@ffprobe-installer/darwin-x64" "5.0.0"
      "@ffprobe-installer/linux-arm" "5.0.0"
      "@ffprobe-installer/linux-arm64" "5.0.0"
      "@ffprobe-installer/linux-ia32" "5.0.0"
      "@ffprobe-installer/linux-x64" "5.0.0"
      "@ffprobe-installer/win32-ia32" "5.0.0"
      "@ffprobe-installer/win32-x64" "5.0.0"
  
  "@ffprobe-installer/linux-arm64@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/linux-arm64/-/linux-arm64-5.0.0.tgz#8e50c2ac0fa9f1ccaf553114c09cc9d2f9571de2"
    integrity sha512-IwFbzhe1UydR849YXLPP0RMpHgHXSuPO1kznaCHcU5FscFBV5gOZLkdD8e/xrcC8g/nhKqy0xMjn5kv6KkFQlQ==
  
  "@ffprobe-installer/linux-arm@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/linux-arm/-/linux-arm-5.0.0.tgz#0a67b1610284e4be115da1656f150abc4706fe08"
    integrity sha512-mM1PPxP2UX5SUvhy0urcj5U8UolwbYgmnXA/eBWbW78k6N2Wk1COvcHYzOPs6c5yXXL6oshS2rZHU1kowigw7g==
  
  "@ffprobe-installer/linux-ia32@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/linux-ia32/-/linux-ia32-5.0.0.tgz#38714de93ce3842887e51a8b0c9dab3297777dbb"
    integrity sha512-c3bWlWEDMST59SAZycVh0oyc2eNS/CxxeRjoNryGRgqcZX3EJWJJQL1rAXbpQOMLMi8to1RqnmMuwPJgLLjjUA==
  
  "@ffprobe-installer/linux-x64@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/linux-x64/-/linux-x64-5.0.0.tgz#dc51a99acf74675035c02f59c4d73270ba3742f7"
    integrity sha512-zgLnWJFvMGCaw1txGtz84sMEQt6mQUzdw86ih9S/kZOWnp06Gj/ams/EXxEkAxgAACCVM6/O0mkDe/6biY5tgA==
  
  "@ffprobe-installer/win32-ia32@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/win32-ia32/-/win32-ia32-5.0.0.tgz#77f5cfba1aa91e47255562d23a07432b3e087bb1"
    integrity sha512-NnDdAZD6ShFXzJeCkAFl2ZjAv7GcJWYudLA+0T/vjZwvskBop+sq1PGfdmVltfFDcdQiomoThRhn9Xiy9ZC71g==
  
  "@ffprobe-installer/win32-x64@5.0.0":
    version "5.0.0"
    resolved "https://registry.npmmirror.com/@ffprobe-installer/win32-x64/-/win32-x64-5.0.0.tgz#8ed741260b7db346c7bfe77d2fd9acbe3bd70d2f"
    integrity sha512-P4ZMRFxVMnfMsOyTfBM/+nkTodLeOUfXNPo+X1bKEWBiZxRErqX/IHS5sLA0yAH8XmtKZcL7Cu6M26ztGcQYxw==
  
  "@gar/promisify@^1.1.3":
    version "1.1.3"
    resolved "https://registry.npmmirror.com/@gar/promisify/-/promisify-1.1.3.tgz"
    integrity sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==
  
  "@javascript-obfuscator/escodegen@2.3.0":
    version "2.3.0"
    resolved "https://registry.npmmirror.com/@javascript-obfuscator/escodegen/-/escodegen-2.3.0.tgz"
    integrity sha512-QVXwMIKqYMl3KwtTirYIA6gOCiJ0ZDtptXqAv/8KWLG9uQU2fZqTVy7a/A5RvcoZhbDoFfveTxuGxJ5ibzQtkw==
    dependencies:
      "@javascript-obfuscator/estraverse" "^5.3.0"
      esprima "^4.0.1"
      esutils "^2.0.2"
      optionator "^0.8.1"
    optionalDependencies:
      source-map "~0.6.1"
  
  "@javascript-obfuscator/estraverse@5.4.0", "@javascript-obfuscator/estraverse@^5.3.0":
    version "5.4.0"
    resolved "https://registry.npmmirror.com/@javascript-obfuscator/estraverse/-/estraverse-5.4.0.tgz"
    integrity sha512-CZFX7UZVN9VopGbjTx4UXaXsi9ewoM1buL0kY7j1ftYdSs7p2spv9opxFjHlQ/QGTgh4UqufYqJJ0WKLml7b6w==
  
  "@jimp/bmp@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/bmp/-/bmp-0.22.8.tgz#19ce17bfef7f3d34a0dbe220d640f384d28178bc"
    integrity sha512-JEMKgM1AEvvWfn9ZCHn62nK+QCE3Pb/ZhPdL3NF0ZgKNww6pqOmo6KqXzqY18JLB7c0epuTp4GPDPDhOh/ou1g==
    dependencies:
      "@jimp/utils" "^0.22.8"
      bmp-js "^0.1.0"
  
  "@jimp/core@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/core/-/core-0.22.8.tgz#6851513756e81887864d64d1786f212795076065"
    integrity sha512-vkN28aFikzQieA6bGxN+qe20pseCAemCyUI0YmRkJIArlb6OujtAwWAKyokv2lylV56bq8EQGIz+Y30OXUnRqg==
    dependencies:
      "@jimp/utils" "^0.22.8"
      any-base "^1.1.0"
      buffer "^5.2.0"
      exif-parser "^0.1.12"
      file-type "^16.5.4"
      isomorphic-fetch "^3.0.0"
      mkdirp "^2.1.3"
      pixelmatch "^4.0.2"
      tinycolor2 "^1.6.0"
  
  "@jimp/custom@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/custom/-/custom-0.22.8.tgz#098cee27bb3e460d24cdcc6774c1680f164b5e50"
    integrity sha512-u6lP9x/HNeGHB0Oojv4c2mhuDvn7G0ikzYbK4IKLsH4HzHxt62faMjBzQMcFhKJhR6UiiKE/jiHrhGvBT/fMkw==
    dependencies:
      "@jimp/core" "^0.22.8"
  
  "@jimp/gif@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/gif/-/gif-0.22.8.tgz#6692a9117fb4b25620d92327fa305dd95cd57334"
    integrity sha512-I0l6koS67IPU40RPxCJTD1NvePEd8vUIHTejx1ly0jrjGnumbqdarAlBUkDrKfPPc+Fnqp84hBbSN1w5hNPT6w==
    dependencies:
      "@jimp/utils" "^0.22.8"
      gifwrap "^0.9.2"
      omggif "^1.0.9"
  
  "@jimp/jpeg@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/jpeg/-/jpeg-0.22.8.tgz#182c41108833bdc8c88598ef4c11bf4f197228eb"
    integrity sha512-hLXrQ7/0QiUhAVAF10dfGCSq3hvyqjKltlpu/87b3wqMDKe9KdvhX1AJHiUUrAbJv1fAcnOmQGTyXGuySa1D6A==
    dependencies:
      "@jimp/utils" "^0.22.8"
      jpeg-js "^0.4.4"
  
  "@jimp/plugin-blit@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-blit/-/plugin-blit-0.22.8.tgz#b7ea7cbcbb0d7da87fb4124f0129ee539bf5ae49"
    integrity sha512-rQ19txVCKIwo74HtgFodFt4//0ATPCJK+f24riqzb+nx+1JaOo1xRvpJqg4moirHwKR2fhwdDxmY7KX20kCeYA==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-blur@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-blur/-/plugin-blur-0.22.8.tgz#d561de68d7154175b7a443d12949ba7d9fe255ca"
    integrity sha512-GWbNK3YW6k2EKiGJdpAFEr0jezPBtiVxj2wG/lCPuWJz7KmzSSN99hQjIy73xQxoBCRdALfJlkhe3leFNRueSQ==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-circle@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-circle/-/plugin-circle-0.22.8.tgz#****************************************"
    integrity sha512-qPCw8XFW8opT89ciFDuvs+eB3EB1mZIJWVajD2qAlprHiE7YGr34TkM7N5MNr3qZ1pJgkYdW6+HbBrJwBaonqw==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-color@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-color/-/plugin-color-0.22.8.tgz#3f64888b28438ac986a97c672babe6abec886cb3"
    integrity sha512-ogkbg6rpDVH/mMLgAQKg17z3oZE0VN7ZWxNoH12fUHchqKz1I57zpa65fxZe2I8T5Xz97HR3x+7V7oI8qQGdSA==
    dependencies:
      "@jimp/utils" "^0.22.8"
      tinycolor2 "^1.6.0"
  
  "@jimp/plugin-contain@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-contain/-/plugin-contain-0.22.8.tgz#5afad638558ccf69abfac0c85b6354534e22c0eb"
    integrity sha512-oiaPLdJt9Dk+XEEhM/OU3lFemM51mA9NgMCAdburSCjDzKacJYBGFSHjTOhXzcxOie/ZDpOYN/UzFGKy8Dgl9A==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-cover@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-cover/-/plugin-cover-0.22.8.tgz#b7e2aa869ff3d68c6b3d68e46f9ddb309bf5fa8a"
    integrity sha512-mO68w1m/LhfuHU8LKHY05a4/hhWnY4t+T+8JCw9t+5yfzA4+LofBZZKtFtWgwf/QGe1y3X2rtUU/avAzDUKyyA==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-crop@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-crop/-/plugin-crop-0.22.8.tgz#2683de20d34eac0d457a5d231a0e3885023a2192"
    integrity sha512-ns4oH0h0gezYsbuH8RThcMLY5uTLk/vnqOVjWCehMHEzxi0DHMWCmpcb6bC//vJ+XFNhtVGn1ALN7+ROmPrj+A==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-displace@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-displace/-/plugin-displace-0.22.8.tgz#d29992956fcdb01ab860bb00dd923964e88540f0"
    integrity sha512-Cj8nHYgsdFynOIx3dbbiVwRuZn3xO+RVfwkTRy0JBye+K2AU8SQJS+hSFNMQFTZt5djivh6kh0TzvR/6LkOd1w==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-dither@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-dither/-/plugin-dither-0.22.8.tgz#75d31db459852bd6c2caf10ff74bc7340c9d3bf5"
    integrity sha512-oE0Us/6bEgrgEg56plU3jSBzvB9iGhweKUHmxYMWnQbFCHP4mNCtPAs8+Fmq6c+m98ZgBgRcrJTnC7lphHkGyw==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-fisheye@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-fisheye/-/plugin-fisheye-0.22.8.tgz#980bd252ae9ebe903256fc238f224d7deb067813"
    integrity sha512-bWvYY/nfMcKclWEaRyAir+YsT6C5St823HUQAsewZowTrJmme+w4U2a6InsryTHUL01BBcV5BLH0aDHuV3StvA==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-flip@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-flip/-/plugin-flip-0.22.8.tgz#c359436260455af0ec4156d9823a55b75b776d94"
    integrity sha512-0NFTNzjsdmOQkaIkNjZqO3/yU4SQb9nnWQXsLS1fFo+9QrIL5v8vVkXpk/rhiND6PyTj2mMTNjOa76GuZcC+iQ==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-gaussian@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-gaussian/-/plugin-gaussian-0.22.8.tgz#bd52b0944055afa9344496af9a1f05c8d7127ded"
    integrity sha512-E/f14aLzCS50QAM7K+InI9V61KVy/Zx52vy7Jjfo1h7qKhQHss3PYaydaH0N6qlXRNeXgh+4/32P9JfieLMcdw==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-invert@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-invert/-/plugin-invert-0.22.8.tgz#d52c4c735720359d942a600827693b2e097c61a7"
    integrity sha512-UauP39FF2cwbA5VU+Tz9VlNa9rtULPSHZb0Huwcjqjm9/G/xVN69VJ8+RKiFC4zM1/kYAUp/6IRwPa6qdKJpSw==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-mask@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-mask/-/plugin-mask-0.22.8.tgz#80adff958c1cec3d4727a21018f4f7062a697394"
    integrity sha512-bhg5+3i8x1CmYj6cjvPBQZLwZEI3iK3gJWF25ZHF+12d3cqDuJngtr8oRQOQLlAgvKmrj9FXIiEPDczUI9cnWQ==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-normalize@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-normalize/-/plugin-normalize-0.22.8.tgz#e60e864c7b0270920517e6276d847c4604154051"
    integrity sha512-Yg5nreAR1JYuSObu3ExlgaLxVeW6VvjVL5qFwiPFxSNlG8JIwL1Ir3K3ChSnnvymyZvJMHb6YKTYNfXKw5Da6g==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-print@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-print/-/plugin-print-0.22.8.tgz#fa1e6659a1cc34dddceee33e887cbf6fc0d99038"
    integrity sha512-86O5ejCDi543IYl0TykSmNWErzAjEYhiAxNQb2F7rFRT38WJYNVsvJ6QhxhDQHKxSmF5iwmqbk0jYk5Wp2Z1kw==
    dependencies:
      "@jimp/utils" "^0.22.8"
      load-bmfont "^1.4.1"
  
  "@jimp/plugin-resize@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-resize/-/plugin-resize-0.22.8.tgz#d111c164512fb54c096da55dcabf681a5b57d458"
    integrity sha512-kg8ArQRPqv/iU3DWNXCa8kcVIhoq64Ze0aGCAeFLKlAq/59f5pzAci6m6vV4L/uOVdYmUa9/kYwIFY6RWKpfzQ==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-rotate@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-rotate/-/plugin-rotate-0.22.8.tgz#3b61655a1a38600e01c73210c201c690041c3bcb"
    integrity sha512-9a+VPZWMN/Cks76wf8LjM5RVA3ntP9+NAdsS1SZhhXel7U3Re/dWMouIEbo3QTt6K+igRo4txUCdZiw4ZucvkQ==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-scale@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-scale/-/plugin-scale-0.22.8.tgz#b5a4f798aebefa914e942a0feae6dc179230d974"
    integrity sha512-dQS4pG6DX6endu8zUpvBBOEtGC+ljDDDNw0scSXY71TxyQdNo5Ro0apfsppjmuAr8rNotRkfyxbITKkXQDRUDQ==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-shadow@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-shadow/-/plugin-shadow-0.22.8.tgz#ba6c14074aafbc47c550878044e7c18498c91907"
    integrity sha512-HyAhr7OblTQh+BoKHQg4qbS9MweNlH77yfpBqUEyDtfyjI5r06+5chf1ZdLRIPEWv/BdCfdI/g81Wv69muCMwA==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugin-threshold@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugin-threshold/-/plugin-threshold-0.22.8.tgz#63b96ce4f82250ef993bfb7e88f12dae94e17a04"
    integrity sha512-ZmkfH0PtjvF1UcKsjw0H7V6r+LC0yKzEfg76Jhs2nIqIgsxsSOVfHwS7z0/1IWnyXxSw36m+NjCAotNHRILGmA==
    dependencies:
      "@jimp/utils" "^0.22.8"
  
  "@jimp/plugins@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/plugins/-/plugins-0.22.8.tgz#07683e350d3578b319580bf445650df88fb7eccd"
    integrity sha512-ieI2+kCpmIfjwVlT7B67ULCzxMizfj7LspJh9HnIZCDXQB9GBOZ9KImLYc75Krae0dP/3FR7FglLiSI7fkOHbw==
    dependencies:
      "@jimp/plugin-blit" "^0.22.8"
      "@jimp/plugin-blur" "^0.22.8"
      "@jimp/plugin-circle" "^0.22.8"
      "@jimp/plugin-color" "^0.22.8"
      "@jimp/plugin-contain" "^0.22.8"
      "@jimp/plugin-cover" "^0.22.8"
      "@jimp/plugin-crop" "^0.22.8"
      "@jimp/plugin-displace" "^0.22.8"
      "@jimp/plugin-dither" "^0.22.8"
      "@jimp/plugin-fisheye" "^0.22.8"
      "@jimp/plugin-flip" "^0.22.8"
      "@jimp/plugin-gaussian" "^0.22.8"
      "@jimp/plugin-invert" "^0.22.8"
      "@jimp/plugin-mask" "^0.22.8"
      "@jimp/plugin-normalize" "^0.22.8"
      "@jimp/plugin-print" "^0.22.8"
      "@jimp/plugin-resize" "^0.22.8"
      "@jimp/plugin-rotate" "^0.22.8"
      "@jimp/plugin-scale" "^0.22.8"
      "@jimp/plugin-shadow" "^0.22.8"
      "@jimp/plugin-threshold" "^0.22.8"
      timm "^1.6.1"
  
  "@jimp/png@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/png/-/png-0.22.8.tgz#3fd5e546087aede4011ceb1f75e912dbc8ff696e"
    integrity sha512-XOj11kcCr8zKg24QSwlRfH9k4hbV6rkMGUVxMS3puRzzB0FBSQy42NBYEfYf2XlY2QJSAByPl4AYerOtKb805w==
    dependencies:
      "@jimp/utils" "^0.22.8"
      pngjs "^6.0.0"
  
  "@jimp/tiff@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/tiff/-/tiff-0.22.8.tgz#e584e087633cbfad90fcca82439fabcba4b6786e"
    integrity sha512-K0hYUVW5MLgwq3jiHVHa6LvP05J1rXOlRCC+5dMTUnAXVwi45+MKsqA/8lzzwhHYJ65CNhZwy6D3+ZNzM9SIBQ==
    dependencies:
      utif2 "^4.0.1"
  
  "@jimp/types@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/types/-/types-0.22.8.tgz#4dedda51186a1d950108ed9856f5d17a9f003597"
    integrity sha512-9+xc+mzuYwu0i+6dsnhXiUgfcS+Ktqn5q2jczoKyyBT0cOKgsk+57EIeFLgpTfVGRKRR0y/UIdHByeCzGguF3A==
    dependencies:
      "@jimp/bmp" "^0.22.8"
      "@jimp/gif" "^0.22.8"
      "@jimp/jpeg" "^0.22.8"
      "@jimp/png" "^0.22.8"
      "@jimp/tiff" "^0.22.8"
      timm "^1.6.1"
  
  "@jimp/utils@^0.22.8":
    version "0.22.8"
    resolved "https://registry.npmmirror.com/@jimp/utils/-/utils-0.22.8.tgz#04039431a00f62e0c630b376aab848f8718fb9a1"
    integrity sha512-AaqjfqDeLzSFzrbGRKHMXg/ntiWKvoG9tpVgWzgOx5/gPWj/IyGfztojLTTvY8HqZCr25z8z91u2lAQD2v46Jw==
    dependencies:
      regenerator-runtime "^0.13.3"
  
  "@malept/cross-spawn-promise@^1.1.0":
    version "1.1.1"
    resolved "https://registry.npmmirror.com/@malept/cross-spawn-promise/-/cross-spawn-promise-1.1.1.tgz"
    integrity sha512-RTBGWL5FWQcg9orDOCcp4LvItNzUPcyEU9bwaeJX0rJ1IQxzucC48Y0/sQLp/g6t99IQgAlGIaesJS+gTn7tVQ==
    dependencies:
      cross-spawn "^7.0.1"
  
  "@malept/cross-spawn-promise@^2.0.0":
    version "2.0.0"
    resolved "https://registry.npmmirror.com/@malept/cross-spawn-promise/-/cross-spawn-promise-2.0.0.tgz"
    integrity sha512-1DpKU0Z5ThltBwjNySMC14g0CkbyhCaz9FkhxqNsZI6uAPJXFS8cMXlBKo26FJ8ZuW6S9GCMcR9IO5k2X5/9Fg==
    dependencies:
      cross-spawn "^7.0.1"
  
  "@malept/flatpak-bundler@^0.4.0":
    version "0.4.0"
    resolved "https://registry.npmmirror.com/@malept/flatpak-bundler/-/flatpak-bundler-0.4.0.tgz"
    integrity sha512-9QOtNffcOF/c1seMCDnjckb3R9WHcG34tky+FHpNKKCW0wc/scYLwMtO+ptyGUfMW0/b/n4qRiALlaFHc9Oj7Q==
    dependencies:
      debug "^4.1.1"
      fs-extra "^9.0.0"
      lodash "^4.17.15"
      tmp-promise "^3.0.2"
  
  "@nodelib/fs.scandir@2.1.5":
    version "2.1.5"
    resolved "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
    integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
    dependencies:
      "@nodelib/fs.stat" "2.0.5"
      run-parallel "^1.1.9"
  
  "@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
    version "2.0.5"
    resolved "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
    integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==
  
  "@nodelib/fs.walk@^1.2.3":
    version "1.2.8"
    resolved "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
    integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
    dependencies:
      "@nodelib/fs.scandir" "2.1.5"
      fastq "^1.6.0"
  
  "@npmcli/fs@^2.1.0":
    version "2.1.2"
    resolved "https://registry.npmmirror.com/@npmcli/fs/-/fs-2.1.2.tgz"
    integrity sha512-yOJKRvohFOaLqipNtwYB9WugyZKhC/DZC4VYPmpaCzDBrA8YpK3qHZ8/HGscMnE4GqbkLNuVcCnxkeQEdGt6LQ==
    dependencies:
      "@gar/promisify" "^1.1.3"
      semver "^7.3.5"
  
  "@npmcli/move-file@^2.0.0":
    version "2.0.1"
    resolved "https://registry.npmmirror.com/@npmcli/move-file/-/move-file-2.0.1.tgz"
    integrity sha512-mJd2Z5TjYWq/ttPLLGqArdtnC74J6bOzg4rMDnN+p1xTacZ2yPRCk2y0oSWQtygLR9YVQXgOcONrwtnk3JupxQ==
    dependencies:
      mkdirp "^1.0.4"
      rimraf "^3.0.2"
  
  "@sindresorhus/is@^0.14.0":
    version "0.14.0"
    resolved "https://registry.npmmirror.com/@sindresorhus/is/-/is-0.14.0.tgz"
    integrity sha512-9NET910DNaIPngYnLLPeg+Ogzqsi9uM4mSboU5y6p8S5DzMTVEsJZrawi+BoDNUVBa2DhJqQYUFvMDfgU062LQ==
  
  "@sindresorhus/is@^4.0.0":
    version "4.6.0"
    resolved "https://registry.npmmirror.com/@sindresorhus/is/-/is-4.6.0.tgz"
    integrity sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==
  
  "@socket.io/component-emitter@~3.1.0":
    version "3.1.0"
    resolved "https://registry.npmmirror.com/@socket.io/component-emitter/-/component-emitter-3.1.0.tgz"
    integrity sha512-+9jVqKhRSpsc591z5vX+X5Yyw+he/HCB4iQ/RYxw35CEPaY1gnsNE43nf9n9AaYjAQrTiI/mOwKUKdUs9vf7Xg==
  
  "@szmarczak/http-timer@^1.1.2":
    version "1.1.2"
    resolved "https://registry.npmmirror.com/@szmarczak/http-timer/-/http-timer-1.1.2.tgz"
    integrity sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==
    dependencies:
      defer-to-connect "^1.0.1"
  
  "@szmarczak/http-timer@^4.0.5":
    version "4.0.6"
    resolved "https://registry.npmmirror.com/@szmarczak/http-timer/-/http-timer-4.0.6.tgz"
    integrity sha512-4BAffykYOgO+5nzBWYwE3W90sBgLJoUPRWWcL8wlyiM8IB8ipJz3UMJ9KXQd1RKQXpKp8Tutn80HZtWsu2u76w==
    dependencies:
      defer-to-connect "^2.0.0"
  
  "@tokenizer/token@^0.3.0":
    version "0.3.0"
    resolved "https://registry.npmmirror.com/@tokenizer/token/-/token-0.3.0.tgz#fe98a93fe789247e998c75e74e9c7c63217aa276"
    integrity sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==
  
  "@tootallnate/once@1":
    version "1.1.2"
    resolved "https://registry.npmmirror.com/@tootallnate/once/-/once-1.1.2.tgz"
    integrity sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==
  
  "@tootallnate/once@2":
    version "2.0.0"
    resolved "https://registry.npmmirror.com/@tootallnate/once/-/once-2.0.0.tgz"
    integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==
  
  "@types/babel-types@*", "@types/babel-types@^7.0.0":
    version "7.0.11"
    resolved "https://registry.npmmirror.com/@types/babel-types/-/babel-types-7.0.11.tgz#263b113fa396fac4373188d73225297fb86f19a9"
    integrity sha512-pkPtJUUY+Vwv6B1inAz55rQvivClHJxc9aVEPPmaq2cbyeMLCiDpbKpcKyX4LAwpNGi+SHBv0tHv6+0gXv0P2A==
  
  "@types/babylon@^6.16.2":
    version "6.16.6"
    resolved "https://registry.npmmirror.com/@types/babylon/-/babylon-6.16.6.tgz#a1e7e01567b26a5ebad321a74d10299189d8d932"
    integrity sha512-G4yqdVlhr6YhzLXFKy5F7HtRBU8Y23+iWy7UKthMq/OSQnL1hbsoeXESQ2LY8zEDlknipDG3nRGhUC9tkwvy/w==
    dependencies:
      "@types/babel-types" "*"
  
  "@types/cacheable-request@^6.0.1":
    version "6.0.3"
    resolved "https://registry.npmmirror.com/@types/cacheable-request/-/cacheable-request-6.0.3.tgz"
    integrity sha512-IQ3EbTzGxIigb1I3qPZc1rWJnH0BmSKv5QYTalEwweFvyBDLSAe24zP0le/hyi7ecGfZVlIVAg4BZqb8WBwKqw==
    dependencies:
      "@types/http-cache-semantics" "*"
      "@types/keyv" "^3.1.4"
      "@types/node" "*"
      "@types/responselike" "^1.0.0"
  
  "@types/cookie@^0.4.1":
    version "0.4.1"
    resolved "https://registry.npmmirror.com/@types/cookie/-/cookie-0.4.1.tgz"
    integrity sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==
  
  "@types/cors@^2.8.12":
    version "2.8.13"
    resolved "https://registry.npmmirror.com/@types/cors/-/cors-2.8.13.tgz"
    integrity sha512-RG8AStHlUiV5ysZQKq97copd2UmVYw3/pRMLefISZ3S1hK104Cwm7iLQ3fTKx+lsUH2CE8FlLaYeEA2LSeqYUA==
    dependencies:
      "@types/node" "*"
  
  "@types/debug@^4.1.6":
    version "4.1.7"
    resolved "https://registry.npmmirror.com/@types/debug/-/debug-4.1.7.tgz"
    integrity sha512-9AonUzyTjXXhEOa0DnqpzZi6VHlqKMswga9EXjpXnnqxwLtdvPPtlO8evrI5D9S6asFRCQ6v+wpiUKbw+vKqyg==
    dependencies:
      "@types/ms" "*"
  
  "@types/formidable@^2.0.4":
    version "2.0.5"
    resolved "https://registry.npmmirror.com/@types/formidable/-/formidable-2.0.5.tgz"
    integrity sha512-uvMcdn/KK3maPOaVUAc3HEYbCEhjaGFwww4EsX6IJfWIJ1tzHtDHczuImH3GKdusPnAAmzB07St90uabZeCKPA==
    dependencies:
      "@types/node" "*"
  
  "@types/fs-extra@^9.0.11":
    version "9.0.13"
    resolved "https://registry.npmmirror.com/@types/fs-extra/-/fs-extra-9.0.13.tgz"
    integrity sha512-nEnwB++1u5lVDM2UI4c1+5R+FYaKfaAzS4OococimjVm3nQw3TuzH5UNsocrcTBbhnerblyHj4A49qXbIiZdpA==
    dependencies:
      "@types/node" "*"
  
  "@types/glob@^7.1.1":
    version "7.2.0"
    resolved "https://registry.npmmirror.com/@types/glob/-/glob-7.2.0.tgz"
    integrity sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==
    dependencies:
      "@types/minimatch" "*"
      "@types/node" "*"
  
  "@types/http-cache-semantics@*":
    version "4.0.1"
    resolved "https://registry.npmmirror.com/@types/http-cache-semantics/-/http-cache-semantics-4.0.1.tgz"
    integrity sha512-SZs7ekbP8CN0txVG2xVRH6EgKmEm31BOxA07vkFaETzZz1xh+********************************+iRPQ==
  
  "@types/keyv@^3.1.4":
    version "3.1.4"
    resolved "https://registry.npmmirror.com/@types/keyv/-/keyv-3.1.4.tgz"
    integrity sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==
    dependencies:
      "@types/node" "*"
  
  "@types/minimatch@*":
    version "5.1.2"
    resolved "https://registry.npmmirror.com/@types/minimatch/-/minimatch-5.1.2.tgz"
    integrity sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==
  
  "@types/minimatch@^3.0.3":
    version "3.0.5"
    resolved "https://registry.npmmirror.com/@types/minimatch/-/minimatch-3.0.5.tgz"
    integrity sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ==
  
  "@types/ms@*":
    version "0.7.31"
    resolved "https://registry.npmmirror.com/@types/ms/-/ms-0.7.31.tgz"
    integrity sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==
  
  "@types/node@*", "@types/node@>=10.0.0":
    version "18.15.11"
    resolved "https://registry.npmmirror.com/@types/node/-/node-18.15.11.tgz"
    integrity sha512-E5Kwq2n4SbMzQOn6wnmBjuK9ouqlURrcZDVfbo9ftDDTFt3nk7ZKK4GMOzoYgnpQJKcxwQw+lGaBvvlMo0qN/Q==
  
  "@types/node@16.9.1":
    version "16.9.1"
    resolved "https://registry.npmmirror.com/@types/node/-/node-16.9.1.tgz#0611b37db4246c937feef529ddcc018cf8e35708"
    integrity sha512-QpLcX9ZSsq3YYUUnD3nFDY8H7wctAhQj/TFKL8Ya8v5fMm3CFXxo8zStsLAl780ltoYoo1WvKUVGBQK+1ifr7g==
  
  "@types/node@^14.6.2":
    version "14.18.42"
    resolved "https://registry.npmmirror.com/@types/node/-/node-14.18.42.tgz"
    integrity sha512-xefu+RBie4xWlK8hwAzGh3npDz/4VhF6icY/shU+zv/1fNn+ZVG7T7CRwe9LId9sAYRPxI+59QBPuKL3WpyGRg==
  
  "@types/plist@^3.0.1":
    version "3.0.2"
    resolved "https://registry.npmmirror.com/@types/plist/-/plist-3.0.2.tgz#61b3727bba0f5c462fe333542534a0c3e19ccb01"
    integrity sha512-ULqvZNGMv0zRFvqn8/4LSPtnmN4MfhlPNtJCTpKuIIxGVGZ2rYWzFXrvEBoh9CVyqSE7D6YFRJ1hydLHI6kbWw==
    dependencies:
      "@types/node" "*"
      xmlbuilder ">=11.0.1"
  
  "@types/responselike@^1.0.0":
    version "1.0.0"
    resolved "https://registry.npmmirror.com/@types/responselike/-/responselike-1.0.0.tgz"
    integrity sha512-85Y2BjiufFzaMIlvJDvTTB8Fxl2xfLo4HgmHzVBz08w4wDePCTjYw66PdrolO0kzli3yam/YCgRufyo1DdQVTA==
    dependencies:
      "@types/node" "*"
  
  "@types/semver@^7.3.6":
    version "7.3.13"
    resolved "https://registry.npmmirror.com/@types/semver/-/semver-7.3.13.tgz"
    integrity sha512-21cFJr9z3g5dW8B0CVI9g2O9beqaThGQ6ZFBqHfwhzLDKUxaqTIy3vnfah/UPkfOiF2pLq+tGz+W8RyCskuslw==
  
  "@types/validator@^13.7.10":
    version "13.7.14"
    resolved "https://registry.npmmirror.com/@types/validator/-/validator-13.7.14.tgz"
    integrity sha512-J6OAed6rhN6zyqL9Of6ZMamhlsOEU/poBVvbHr/dKOYKTeuYYMlDkMv+b6UUV0o2i0tw73cgyv/97WTWaUl0/g==
  
  "@types/verror@^1.10.3":
    version "1.10.6"
    resolved "https://registry.npmmirror.com/@types/verror/-/verror-1.10.6.tgz#3e600c62d210c5826460858f84bcbb65805460bb"
    integrity sha512-NNm+gdePAX1VGvPcGZCDKQZKYSiAWigKhKaz5KF94hG6f2s8de9Ow5+7AbXoeKxL8gavZfk4UquSAygOF2duEQ==
  
  "@types/yargs-parser@*":
    version "21.0.0"
    resolved "https://registry.npmmirror.com/@types/yargs-parser/-/yargs-parser-21.0.0.tgz"
    integrity sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==
  
  "@types/yargs@^17.0.1":
    version "17.0.24"
    resolved "https://registry.npmmirror.com/@types/yargs/-/yargs-17.0.24.tgz"
    integrity sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==
    dependencies:
      "@types/yargs-parser" "*"
  
  abbrev@1, abbrev@^1.0.0:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/abbrev/-/abbrev-1.1.1.tgz"
    integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==
  
  accepts@^1.3.5, accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
    version "1.3.8"
    resolved "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz"
    integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
    dependencies:
      mime-types "~2.1.34"
      negotiator "0.6.3"
  
  acorn-globals@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/acorn-globals/-/acorn-globals-3.1.0.tgz#fd8270f71fbb4996b004fa880ee5d46573a731bf"
    integrity sha512-uWttZCk96+7itPxK8xCzY86PnxKTMrReKDqrHzv42VQY0K30PUO8WY13WMOuI+cOdX4EIdzdvQ8k6jkuGRFMYw==
    dependencies:
      acorn "^4.0.4"
  
  acorn-jsx@^5.0.0:
    version "5.3.2"
    resolved "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
    integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==
  
  acorn-walk@^8.2.0:
    version "8.2.0"
    resolved "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.2.0.tgz"
    integrity sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==
  
  acorn@8.8.2, acorn@^8.7.0:
    version "8.8.2"
    resolved "https://registry.npmmirror.com/acorn/-/acorn-8.8.2.tgz"
    integrity sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw==
  
  acorn@^3.1.0:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/acorn/-/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"
    integrity sha512-OLUyIIZ7mF5oaAUT1w0TFqQS81q3saT46x8t7ukpPjMNk+nbs4ZHhs7ToV8EWnLYLepjETXd4XaCE4uxkMeqUw==
  
  acorn@^4.0.4, acorn@~4.0.2:
    version "4.0.13"
    resolved "https://registry.npmmirror.com/acorn/-/acorn-4.0.13.tgz#105495ae5361d697bd195c825192e1ad7f253787"
    integrity sha512-fu2ygVGuMmlzG8ZeRJ0bvR41nsAkxxhbyk8bZ1SS521Z7vmgJFTQQlfz/Mp/nJexGBz+v8sC9bM6+lNgskt4Ug==
  
  acorn@^6.0.7:
    version "6.4.2"
    resolved "https://registry.npmmirror.com/acorn/-/acorn-6.4.2.tgz"
    integrity sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ==
  
  adler-32@~1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/adler-32/-/adler-32-1.2.0.tgz"
    integrity sha512-/vUqU/UY4MVeFsg+SsK6c+/05RZXIHZMGJA+PX5JyWI0ZRcBpupnRuPLU/NXXoFwMYCPCoxIfElM2eS+DUXCqQ==
    dependencies:
      exit-on-epipe "~1.0.1"
      printj "~1.1.0"
  
  adler-32@~1.3.0:
    version "1.3.1"
    resolved "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz"
    integrity sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==
  
  agent-base@6, agent-base@^6.0.0, agent-base@^6.0.2:
    version "6.0.2"
    resolved "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz"
    integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
    dependencies:
      debug "4"
  
  agentkeepalive@^4.2.0, agentkeepalive@^4.2.1:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/agentkeepalive/-/agentkeepalive-4.3.0.tgz"
    integrity sha512-7Epl1Blf4Sy37j4v9f9FjICCh4+KAQOyXgHEwlyBiAQLbhKdq/i2QQU3amQalS/wPhdPzDXPL5DMR5bkn+YeWg==
    dependencies:
      debug "^4.1.0"
      depd "^2.0.0"
      humanize-ms "^1.2.1"
  
  aggregate-error@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz"
    integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
    dependencies:
      clean-stack "^2.0.0"
      indent-string "^4.0.0"
  
  ajv-keywords@^3.4.1:
    version "3.5.2"
    resolved "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
    integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==
  
  ajv@^6.0.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.0, ajv@^6.12.3, ajv@^6.9.1:
    version "6.12.6"
    resolved "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
    integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
    dependencies:
      fast-deep-equal "^3.1.1"
      fast-json-stable-stringify "^2.0.0"
      json-schema-traverse "^0.4.1"
      uri-js "^4.2.2"
  
  align-text@^0.1.1, align-text@^0.1.3:
    version "0.1.4"
    resolved "https://registry.npmmirror.com/align-text/-/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
    integrity sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg==
    dependencies:
      kind-of "^3.0.2"
      longest "^1.0.1"
      repeat-string "^1.5.2"
  
  ansi-escapes@^3.0.0, ansi-escapes@^3.2.0:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
    integrity sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==
  
  ansi-regex@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-3.0.1.tgz"
    integrity sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==
  
  ansi-regex@^4.1.0:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-4.1.1.tgz"
    integrity sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==
  
  ansi-regex@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
    integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==
  
  ansi-styles@^3.2.0, ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
    integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
    dependencies:
      color-convert "^1.9.0"
  
  ansi-styles@^4.0.0, ansi-styles@^4.1.0:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
    integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
    dependencies:
      color-convert "^2.0.1"
  
  any-base@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/any-base/-/any-base-1.1.0.tgz#ae101a62bc08a597b4c9ab5b7089d456630549fe"
    integrity sha512-uMgjozySS8adZZYePpaWs8cxB9/kdzmpX6SgJZ+wbz1K5eYk5QMYDVJaZKhxyIHUdnnJkfR7SVgStgH7LkGUyg==
  
  any-promise@^1.0.0, any-promise@^1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz"
    integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==
  
  anymatch@~3.1.2:
    version "3.1.3"
    resolved "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz"
    integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
    dependencies:
      normalize-path "^3.0.0"
      picomatch "^2.0.4"
  
  anyproxy@^4.1.3:
    version "4.1.3"
    resolved "https://registry.npmmirror.com/anyproxy/-/anyproxy-4.1.3.tgz#c3253ad261ded15a135a9d69b1ed0c49e9eb9cd7"
    integrity sha512-p3scfKosPo9eayAcBD1pfIycuFeDnGxa9hZ5Vc0r5geSQp+fTRFq/S+knrUgp9e716nHwbqlYHQPny8dtF+MOA==
    dependencies:
      async "~0.9.0"
      async-task-mgr ">=1.1.0"
      body-parser "^1.13.1"
      brotli "^1.3.2"
      classnames "^2.2.5"
      clipboard-js "^0.3.3"
      co "^4.6.0"
      colorful "^2.1.0"
      commander "~2.11.0"
      component-emitter "^1.2.1"
      compression "^1.4.4"
      es6-promise "^3.3.1"
      express "^4.8.5"
      fast-json-stringify "^0.17.0"
      iconv-lite "^0.4.6"
      inquirer "^5.2.0"
      ip "^0.3.2"
      juicer "^0.6.6-stable"
      mime-types "2.1.11"
      moment "^2.15.1"
      nedb "^1.8.0"
      node-easy-cert "^1.0.0"
      pug "^2.0.0-beta6"
      qrcode-npm "0.0.3"
      request "^2.74.0"
      stream-throttle "^0.1.3"
      svg-inline-react "^1.0.2"
      thunkify "^2.1.2"
      whatwg-fetch "^1.0.0"
      ws "^5.1.0"
  
  app-builder-bin@4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/app-builder-bin/-/app-builder-bin-4.0.0.tgz"
    integrity sha512-xwdG0FJPQMe0M0UA4Tz0zEB8rBJTRA5a476ZawAqiBkMv16GRK5xpXThOjMaEOFnZ6zabejjG4J3da0SXG63KA==
  
  app-builder-lib@23.6.0:
    version "23.6.0"
    resolved "https://registry.npmmirror.com/app-builder-lib/-/app-builder-lib-23.6.0.tgz"
    integrity sha512-dQYDuqm/rmy8GSCE6Xl/3ShJg6Ab4bZJMT8KaTKGzT436gl1DN4REP3FCWfXoh75qGTJ+u+WsdnnpO9Jl8nyMA==
    dependencies:
      "7zip-bin" "~5.1.1"
      "@develar/schema-utils" "~2.6.5"
      "@electron/universal" "1.2.1"
      "@malept/flatpak-bundler" "^0.4.0"
      async-exit-hook "^2.0.1"
      bluebird-lst "^1.0.9"
      builder-util "23.6.0"
      builder-util-runtime "9.1.1"
      chromium-pickle-js "^0.2.0"
      debug "^4.3.4"
      ejs "^3.1.7"
      electron-osx-sign "^0.6.0"
      electron-publish "23.6.0"
      form-data "^4.0.0"
      fs-extra "^10.1.0"
      hosted-git-info "^4.1.0"
      is-ci "^3.0.0"
      isbinaryfile "^4.0.10"
      js-yaml "^4.1.0"
      lazy-val "^1.0.5"
      minimatch "^3.1.2"
      read-config-file "6.2.0"
      sanitize-filename "^1.6.3"
      semver "^7.3.7"
      tar "^6.1.11"
      temp-file "^3.4.0"
  
  "aproba@^1.0.3 || ^2.0.0":
    version "2.0.0"
    resolved "https://registry.npmmirror.com/aproba/-/aproba-2.0.0.tgz"
    integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==
  
  are-we-there-yet@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz"
    integrity sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==
    dependencies:
      delegates "^1.0.0"
      readable-stream "^3.6.0"
  
  argparse@^1.0.7:
    version "1.0.10"
    resolved "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz"
    integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
    dependencies:
      sprintf-js "~1.0.2"
  
  argparse@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz"
    integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==
  
  array-differ@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/array-differ/-/array-differ-3.0.0.tgz"
    integrity sha512-THtfYS6KtME/yIAhKjZ2ul7XI96lQGHRputJQHO80LAWQnuGP4iCIN8vdMRboGbIEYBwU33q8Tch1os2+X0kMg==
  
  array-flatten@1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
    integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==
  
  array-union@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz"
    integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==
  
  arrify@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/arrify/-/arrify-2.0.1.tgz"
    integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==
  
  asap@^2.0.0, asap@~2.0.3:
    version "2.0.6"
    resolved "https://registry.npmmirror.com/asap/-/asap-2.0.6.tgz"
    integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==
  
  asar@^3.1.0:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/asar/-/asar-3.2.0.tgz"
    integrity sha512-COdw2ZQvKdFGFxXwX3oYh2/sOsJWJegrdJCGxnN4MZ7IULgRBp9P6665aqj9z1v9VwP4oP1hRBojRDQ//IGgAg==
    dependencies:
      chromium-pickle-js "^0.2.0"
      commander "^5.0.0"
      glob "^7.1.6"
      minimatch "^3.0.4"
    optionalDependencies:
      "@types/glob" "^7.1.1"
  
  asn1@~0.2.3:
    version "0.2.6"
    resolved "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
    integrity sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==
    dependencies:
      safer-buffer "~2.1.0"
  
  assert-plus@1.0.0, assert-plus@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
    integrity sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==
  
  assert@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/assert/-/assert-2.0.0.tgz"
    integrity sha512-se5Cd+js9dXJnu6Ag2JFc00t+HmHOen+8Q+L7O9zI0PqQXr20uk2J0XQqMxZEeo5U50o8Nvmmx7dZrl+Ufr35A==
    dependencies:
      es6-object-assign "^1.1.0"
      is-nan "^1.2.1"
      object-is "^1.0.1"
      util "^0.12.0"
  
  ast-types@^0.13.2:
    version "0.13.4"
    resolved "https://registry.npmmirror.com/ast-types/-/ast-types-0.13.4.tgz"
    integrity sha512-x1FCFnFifvYDDzTaLII71vG5uvDwgtmDTEVWAxrgeiR8VjMONcCXJx7E+USjDtHlwFmt9MysbqgF9b9Vjr6w+w==
    dependencies:
      tslib "^2.0.1"
  
  astral-regex@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/astral-regex/-/astral-regex-1.0.0.tgz"
    integrity sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==
  
  astral-regex@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
    integrity sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==
  
  async-exit-hook@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/async-exit-hook/-/async-exit-hook-2.0.1.tgz"
    integrity sha512-NW2cX8m1Q7KPA7a5M2ULQeZ2wR5qI5PAbw5L0UOMxdioVk9PMZ0h1TmyZEkPYrCvYjDlFICusOu1dlEKAAeXBw==
  
  async-limiter@~1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/async-limiter/-/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
    integrity sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==
  
  async-task-mgr@>=1.1.0, async-task-mgr@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/async-task-mgr/-/async-task-mgr-1.1.0.tgz#a82d11aef631ae567b5d76b1e83a999a4a34aaf6"
    integrity sha512-EJq1CfHr/b9pcdV1jV8jtg6/Hfti8cVZPIDYTjYbjD79M2AY/JnjY/Zf+hmRcr4fU1Bpy5Odr7EiXQt85YnCdA==
  
  async@0.2.10:
    version "0.2.10"
    resolved "https://registry.npmmirror.com/async/-/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"
    integrity sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==
  
  async@>=0.2.9, async@^3.2.3:
    version "3.2.4"
    resolved "https://registry.npmmirror.com/async/-/async-3.2.4.tgz"
    integrity sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==
  
  async@~0.9.0:
    version "0.9.2"
    resolved "https://registry.npmmirror.com/async/-/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
    integrity sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw==
  
  asynckit@^0.4.0:
    version "0.4.0"
    resolved "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz"
    integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==
  
  at-least-node@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/at-least-node/-/at-least-node-1.0.0.tgz"
    integrity sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==
  
  available-typed-arrays@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
    integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==
  
  aws-sign2@~0.7.0:
    version "0.7.0"
    resolved "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
    integrity sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==
  
  aws4@^1.8.0:
    version "1.12.0"
    resolved "https://registry.npmmirror.com/aws4/-/aws4-1.12.0.tgz#ce1c9d143389679e253b314241ea9aa5cec980d3"
    integrity sha512-NmWvPnx0F1SfrQbYwOi7OeaNGokp9XhzNioJ/CSBs8Qa4vxug81mhJEAVZwxXuBmYB5KDRfMq/F3RR0BIU7sWg==
  
  axios@^1.3.4:
    version "1.3.5"
    resolved "https://registry.npmmirror.com/axios/-/axios-1.3.5.tgz"
    integrity sha512-glL/PvG/E+xCWwV8S6nCHcrfg1exGx7vxyUIivIA1iL7BIh6bePylCfVHwp6k13ao7SATxB6imau2kqY+I67kw==
    dependencies:
      follow-redirects "^1.15.0"
      form-data "^4.0.0"
      proxy-from-env "^1.1.0"
  
  babel-runtime@^6.26.0:
    version "6.26.0"
    resolved "https://registry.npmmirror.com/babel-runtime/-/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
    integrity sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==
    dependencies:
      core-js "^2.4.0"
      regenerator-runtime "^0.11.0"
  
  babel-types@^6.26.0:
    version "6.26.0"
    resolved "https://registry.npmmirror.com/babel-types/-/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
    integrity sha512-zhe3V/26rCWsEZK8kZN+HaQj5yQ1CilTObixFzKW1UWjqG7618Twz6YEsCnjfg5gBcJh02DrpCkS9h98ZqDY+g==
    dependencies:
      babel-runtime "^6.26.0"
      esutils "^2.0.2"
      lodash "^4.17.4"
      to-fast-properties "^1.0.3"
  
  babylon@^6.18.0:
    version "6.18.0"
    resolved "https://registry.npmmirror.com/babylon/-/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"
    integrity sha512-q/UEjfGJ2Cm3oKV71DJz9d25TPnq5rhBVL2Q4fA5wcC3jcrdn7+SssEybFIxwAvvP+YCsCYNKughoF33GxgycQ==
  
  balanced-match@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz"
    integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==
  
  base64-js@^1.1.2, base64-js@^1.3.1, base64-js@^1.5.1:
    version "1.5.1"
    resolved "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz"
    integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==
  
  base64id@2.0.0, base64id@~2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/base64id/-/base64id-2.0.0.tgz"
    integrity sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==
  
  bcrypt-pbkdf@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
    integrity sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==
    dependencies:
      tweetnacl "^0.14.3"
  
  better-sqlite3@^7.6.0:
    version "7.6.2"
    resolved "https://registry.npmmirror.com/better-sqlite3/-/better-sqlite3-7.6.2.tgz"
    integrity sha512-S5zIU1Hink2AH4xPsN0W43T1/AJ5jrPh7Oy07ocuW/AKYYY02GWzz9NH0nbSMn/gw6fDZ5jZ1QsHt1BXAwJ6Lg==
    dependencies:
      bindings "^1.5.0"
      prebuild-install "^7.1.0"
  
  binary-extensions@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz"
    integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==
  
  binary-search-tree@0.2.5:
    version "0.2.5"
    resolved "https://registry.npmmirror.com/binary-search-tree/-/binary-search-tree-0.2.5.tgz#7dbb3b210fdca082450dad2334c304af39bdc784"
    integrity sha512-CvNVKS6iXagL1uGwLagSXz1hzSMezxOuGnFi5FHGKqaTO3nPPWrAbyALUzK640j+xOTVm7lzD9YP8W1f/gvUdw==
    dependencies:
      underscore "~1.4.4"
  
  bindings@^1.5.0:
    version "1.5.0"
    resolved "https://registry.npmmirror.com/bindings/-/bindings-1.5.0.tgz"
    integrity sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==
    dependencies:
      file-uri-to-path "1.0.0"
  
  bl@^4.0.3, bl@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz"
    integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
    dependencies:
      buffer "^5.5.0"
      inherits "^2.0.4"
      readable-stream "^3.4.0"
  
  bluebird-lst@^1.0.9:
    version "1.0.9"
    resolved "https://registry.npmmirror.com/bluebird-lst/-/bluebird-lst-1.0.9.tgz"
    integrity sha512-7B1Rtx82hjnSD4PGLAjVWeYH3tHAcVUmChh85a3lltKQm6FresXh9ErQo6oAv6CqxttczC3/kEg8SY5NluPuUw==
    dependencies:
      bluebird "^3.5.5"
  
  bluebird@^3.5.0, bluebird@^3.5.1, bluebird@^3.5.5:
    version "3.7.2"
    resolved "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz"
    integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==
  
  bmp-js@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmmirror.com/bmp-js/-/bmp-js-0.1.0.tgz#e05a63f796a6c1ff25f4771ec7adadc148c07233"
    integrity sha512-vHdS19CnY3hwiNdkaqk93DvjVLfbEcI8mys4UjuWrlX1haDmroo8o4xCzh4wD6DGV6HxRCyauwhHRqMTfERtjw==
  
  body-parser@1.20.1:
    version "1.20.1"
    resolved "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.1.tgz#b1812a8912c195cd371a3ee5e66faa2338a5c668"
    integrity sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==
    dependencies:
      bytes "3.1.2"
      content-type "~1.0.4"
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      on-finished "2.4.1"
      qs "6.11.0"
      raw-body "2.5.1"
      type-is "~1.6.18"
      unpipe "1.0.0"
  
  body-parser@^1.13.1:
    version "1.20.2"
    resolved "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.2.tgz#6feb0e21c4724d06de7ff38da36dad4f57a747fd"
    integrity sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==
    dependencies:
      bytes "3.1.2"
      content-type "~1.0.5"
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      on-finished "2.4.1"
      qs "6.11.0"
      raw-body "2.5.2"
      type-is "~1.6.18"
      unpipe "1.0.0"
  
  boolean@^3.0.1:
    version "3.2.0"
    resolved "https://registry.npmmirror.com/boolean/-/boolean-3.2.0.tgz"
    integrity sha512-d0II/GO9uf9lfUHH2BQsjxzRJZBdsjgsBiW4BvhWk/3qoKwQFjIDVN19PfX8F2D/r9PCMTtLWjYVCFrpeYUzsw==
  
  brace-expansion@^1.1.7:
    version "1.1.11"
    resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
    integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
    dependencies:
      balanced-match "^1.0.0"
      concat-map "0.0.1"
  
  brace-expansion@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz"
    integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
    dependencies:
      balanced-match "^1.0.0"
  
  braces@^3.0.2, braces@~3.0.2:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz"
    integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
    dependencies:
      fill-range "^7.0.1"
  
  brotli@^1.3.2:
    version "1.3.3"
    resolved "https://registry.npmmirror.com/brotli/-/brotli-1.3.3.tgz#7365d8cc00f12cf765d2b2c898716bcf4b604d48"
    integrity sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==
    dependencies:
      base64-js "^1.1.2"
  
  buffer-alloc-unsafe@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz"
    integrity sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg==
  
  buffer-alloc@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/buffer-alloc/-/buffer-alloc-1.2.0.tgz"
    integrity sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==
    dependencies:
      buffer-alloc-unsafe "^1.1.0"
      buffer-fill "^1.0.0"
  
  buffer-crc32@~0.2.3:
    version "0.2.13"
    resolved "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
    integrity sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==
  
  buffer-equal@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmmirror.com/buffer-equal/-/buffer-equal-0.0.1.tgz#91bc74b11ea405bc916bc6aa908faafa5b4aac4b"
    integrity sha512-RgSV6InVQ9ODPdLWJ5UAqBqJBOg370Nz6ZQtRzpt6nUjc8v0St97uJ4PYC6NztqIScrAXafKM3mZPMygSe1ggA==
  
  buffer-equal@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/buffer-equal/-/buffer-equal-1.0.0.tgz"
    integrity sha512-tcBWO2Dl4e7Asr9hTGcpVrCe+F7DubpmqWCTbj4FHLmjqO2hIaC383acQubWtRJhdceqs5uBHs6Es+Sk//RKiQ==
  
  buffer-fill@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/buffer-fill/-/buffer-fill-1.0.0.tgz"
    integrity sha512-T7zexNBwiiaCOGDg9xNX9PBmjrubblRkENuptryuI64URkXDFum9il/JGL8Lm8wYfAXpredVXXZz7eMHilimiQ==
  
  buffer-from@^1.0.0:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz"
    integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==
  
  buffer@^5.1.0, buffer@^5.2.0, buffer@^5.5.0:
    version "5.7.1"
    resolved "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
    integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
    dependencies:
      base64-js "^1.3.1"
      ieee754 "^1.1.13"
  
  builder-util-runtime@8.9.2:
    version "8.9.2"
    resolved "https://registry.npmmirror.com/builder-util-runtime/-/builder-util-runtime-8.9.2.tgz"
    integrity sha512-rhuKm5vh7E0aAmT6i8aoSfEjxzdYEFX7zDApK+eNgOhjofnWb74d9SRJv0H/8nsgOkos0TZ4zxW0P8J4N7xQ2A==
    dependencies:
      debug "^4.3.2"
      sax "^1.2.4"
  
  builder-util-runtime@9.1.1:
    version "9.1.1"
    resolved "https://registry.npmmirror.com/builder-util-runtime/-/builder-util-runtime-9.1.1.tgz"
    integrity sha512-azRhYLEoDvRDR8Dhis4JatELC/jUvYjm4cVSj7n9dauGTOM2eeNn9KS0z6YA6oDsjI1xphjNbY6PZZeHPzzqaw==
    dependencies:
      debug "^4.3.4"
      sax "^1.2.4"
  
  builder-util@23.6.0:
    version "23.6.0"
    resolved "https://registry.npmmirror.com/builder-util/-/builder-util-23.6.0.tgz"
    integrity sha512-QiQHweYsh8o+U/KNCZFSvISRnvRctb8m/2rB2I1JdByzvNKxPeFLlHFRPQRXab6aYeXc18j9LpsDLJ3sGQmWTQ==
    dependencies:
      "7zip-bin" "~5.1.1"
      "@types/debug" "^4.1.6"
      "@types/fs-extra" "^9.0.11"
      app-builder-bin "4.0.0"
      bluebird-lst "^1.0.9"
      builder-util-runtime "9.1.1"
      chalk "^4.1.1"
      cross-spawn "^7.0.3"
      debug "^4.3.4"
      fs-extra "^10.0.0"
      http-proxy-agent "^5.0.0"
      https-proxy-agent "^5.0.0"
      is-ci "^3.0.0"
      js-yaml "^4.1.0"
      source-map-support "^0.5.19"
      stat-mode "^1.0.0"
      temp-file "^3.4.0"
  
  bytenode@^1.3.6:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/bytenode/-/bytenode-1.4.0.tgz"
    integrity sha512-gDENCBnn15ZtgG6rTzLgVgJGdcfkPOddJU+UhvzTSKBtT2IogQw6+/9XUu7+K6OouH/eXnupRtQll13tAEl+gA==
  
  bytes@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
    integrity sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==
  
  bytes@3.1.2:
    version "3.1.2"
    resolved "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz"
    integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==
  
  cacache@^16.1.0:
    version "16.1.3"
    resolved "https://registry.npmmirror.com/cacache/-/cacache-16.1.3.tgz"
    integrity sha512-/+Emcj9DAXxX4cwlLmRI9c166RuL3w30zp4R7Joiv2cQTtTtA+jeuCAjH3ZlGnYS3tKENSrKhAzVVP9GVyzeYQ==
    dependencies:
      "@npmcli/fs" "^2.1.0"
      "@npmcli/move-file" "^2.0.0"
      chownr "^2.0.0"
      fs-minipass "^2.1.0"
      glob "^8.0.1"
      infer-owner "^1.0.4"
      lru-cache "^7.7.1"
      minipass "^3.1.6"
      minipass-collect "^1.0.2"
      minipass-flush "^1.0.5"
      minipass-pipeline "^1.2.4"
      mkdirp "^1.0.4"
      p-map "^4.0.0"
      promise-inflight "^1.0.1"
      rimraf "^3.0.2"
      ssri "^9.0.0"
      tar "^6.1.11"
      unique-filename "^2.0.0"
  
  cache-content-type@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/cache-content-type/-/cache-content-type-1.0.1.tgz"
    integrity sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==
    dependencies:
      mime-types "^2.1.18"
      ylru "^1.2.0"
  
  cacheable-lookup@^5.0.3:
    version "5.0.4"
    resolved "https://registry.npmmirror.com/cacheable-lookup/-/cacheable-lookup-5.0.4.tgz"
    integrity sha512-2/kNscPhpcxrOigMZzbiWF7dz8ilhb/nIHU3EyZiXWXpeq/au8qJ8VhdftMkty3n7Gj6HIGalQG8oiBNB3AJgA==
  
  cacheable-request@^6.0.0:
    version "6.1.0"
    resolved "https://registry.npmmirror.com/cacheable-request/-/cacheable-request-6.1.0.tgz"
    integrity sha512-Oj3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==
    dependencies:
      clone-response "^1.0.2"
      get-stream "^5.1.0"
      http-cache-semantics "^4.0.0"
      keyv "^3.0.0"
      lowercase-keys "^2.0.0"
      normalize-url "^4.1.0"
      responselike "^1.0.2"
  
  cacheable-request@^7.0.2:
    version "7.0.2"
    resolved "https://registry.npmmirror.com/cacheable-request/-/cacheable-request-7.0.2.tgz"
    integrity sha512-pouW8/FmiPQbuGpkXQ9BAPv/Mo5xDGANgSNXzTzJ8DrKGuXOssM4wIQRjfanNRh3Yu5cfYPvcorqbhg2KIJtew==
    dependencies:
      clone-response "^1.0.2"
      get-stream "^5.1.0"
      http-cache-semantics "^4.0.0"
      keyv "^4.0.0"
      lowercase-keys "^2.0.0"
      normalize-url "^6.0.1"
      responselike "^2.0.0"
  
  call-bind@^1.0.0, call-bind@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz"
    integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
    dependencies:
      function-bind "^1.1.1"
      get-intrinsic "^1.0.2"
  
  callsites@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
    integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==
  
  camelcase@^1.0.2:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/camelcase/-/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
    integrity sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==
  
  caseless@~0.12.0:
    version "0.12.0"
    resolved "https://registry.npmmirror.com/caseless/-/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
    integrity sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==
  
  center-align@^0.1.1:
    version "0.1.3"
    resolved "https://registry.npmmirror.com/center-align/-/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
    integrity sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ==
    dependencies:
      align-text "^0.1.3"
      lazy-cache "^1.0.3"
  
  cfb@^1.1.4:
    version "1.2.2"
    resolved "https://registry.npmmirror.com/cfb/-/cfb-1.2.2.tgz"
    integrity sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==
    dependencies:
      adler-32 "~1.3.0"
      crc-32 "~1.2.0"
  
  chalk@4.1.2, chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.1:
    version "4.1.2"
    resolved "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
    integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
    dependencies:
      ansi-styles "^4.1.0"
      supports-color "^7.1.0"
  
  chalk@^2.0.0, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
    version "2.4.2"
    resolved "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
    integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  chance@1.1.9:
    version "1.1.9"
    resolved "https://registry.npmmirror.com/chance/-/chance-1.1.9.tgz"
    integrity sha512-TfxnA/DcZXRTA4OekA2zL9GH8qscbbl6X0ZqU4tXhGveVY/mXWvEQLt5GwZcYXTEyEFflVtj+pG8nc8EwSm1RQ==
  
  char-regex@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/char-regex/-/char-regex-1.0.2.tgz"
    integrity sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==
  
  character-parser@^2.1.1:
    version "2.2.0"
    resolved "https://registry.npmmirror.com/character-parser/-/character-parser-2.2.0.tgz#c7ce28f36d4bcd9744e5ffc2c5fcde1c73261fc0"
    integrity sha512-+UqJQjFEFaTAs3bNsF2j2kEN1baG/zghZbdqoYEDxGZtJo9LBzl1A+m0D4n3qKx8N2FNv8/Xp6yV9mQmBuptaw==
    dependencies:
      is-regex "^1.0.3"
  
  chardet@^0.4.0:
    version "0.4.2"
    resolved "https://registry.npmmirror.com/chardet/-/chardet-0.4.2.tgz#b5473b33dc97c424e5d98dc87d55d4d8a29c8bf2"
    integrity sha512-j/Toj7f1z98Hh2cYo2BVr85EpIRWqUi7rtRSGxh/cqUjqrnJe9l9UE7IUGd2vQ2p+kSHLkSzObQPZPLUC6TQwg==
  
  chardet@^0.7.0:
    version "0.7.0"
    resolved "https://registry.npmmirror.com/chardet/-/chardet-0.7.0.tgz"
    integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==
  
  charenc@0.0.2:
    version "0.0.2"
    resolved "https://registry.npmmirror.com/charenc/-/charenc-0.0.2.tgz"
    integrity sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==
  
  chokidar@^3.5.2:
    version "3.5.3"
    resolved "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz"
    integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
    dependencies:
      anymatch "~3.1.2"
      braces "~3.0.2"
      glob-parent "~5.1.2"
      is-binary-path "~2.1.0"
      is-glob "~4.0.1"
      normalize-path "~3.0.0"
      readdirp "~3.6.0"
    optionalDependencies:
      fsevents "~2.3.2"
  
  chownr@^1.1.1:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/chownr/-/chownr-1.1.4.tgz"
    integrity sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==
  
  chownr@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/chownr/-/chownr-2.0.0.tgz"
    integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==
  
  chromium-pickle-js@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmmirror.com/chromium-pickle-js/-/chromium-pickle-js-0.2.0.tgz"
    integrity sha512-1R5Fho+jBq0DDydt+/vHWj5KJNJCKdARKOCwZUen84I5BreWoLqRLANH1U87eJy1tiASPtMnGqJJq0ZsLoRPOw==
  
  ci-info@^3.2.0:
    version "3.8.0"
    resolved "https://registry.npmmirror.com/ci-info/-/ci-info-3.8.0.tgz"
    integrity sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==
  
  circular-json-for-egg@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/circular-json-for-egg/-/circular-json-for-egg-1.0.0.tgz"
    integrity sha512-BzMR1dg0+YqcFoMETHq0gFeQNNKliXI1Oe+C0nx/4npLaohsR7/Oj3UFht65MLwF7zs6x13gOr+f4+JeYni6vw==
  
  class-validator@0.14.0:
    version "0.14.0"
    resolved "https://registry.npmmirror.com/class-validator/-/class-validator-0.14.0.tgz"
    integrity sha512-ct3ltplN8I9fOwUd8GrP8UQixwff129BkEtuWDKL5W45cQuLd19xqmTLu5ge78YDm/fdje6FMt0hGOhl0lii3A==
    dependencies:
      "@types/validator" "^13.7.10"
      libphonenumber-js "^1.10.14"
      validator "^13.7.0"
  
  classnames@^2.2.5:
    version "2.3.2"
    resolved "https://registry.npmmirror.com/classnames/-/classnames-2.3.2.tgz#351d813bf0137fcc6a76a16b88208d2560a0d924"
    integrity sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==
  
  clean-css@^4.1.11:
    version "4.2.4"
    resolved "https://registry.npmmirror.com/clean-css/-/clean-css-4.2.4.tgz#733bf46eba4e607c6891ea57c24a989356831178"
    integrity sha512-EJUDT7nDVFDvaQgAo2G/PJvxmp1o/c6iXLbswsBbUFXi1Nr+AjA2cKmfbKDMjMvzEe75g3P6JkaDDAKk96A85A==
    dependencies:
      source-map "~0.6.0"
  
  clean-stack@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz"
    integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==
  
  cli-cursor@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-2.1.0.tgz"
    integrity sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==
    dependencies:
      restore-cursor "^2.0.0"
  
  cli-cursor@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz"
    integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
    dependencies:
      restore-cursor "^3.1.0"
  
  cli-spinners@^2.5.0:
    version "2.8.0"
    resolved "https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.8.0.tgz"
    integrity sha512-/eG5sJcvEIwxcdYM86k5tPwn0MUzkX5YY3eImTGpJOZgVe4SdTMY14vQpcxgBzJ0wXwAYrS8E+c3uHeK4JNyzQ==
  
  cli-truncate@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
    integrity sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==
    dependencies:
      slice-ansi "^3.0.0"
      string-width "^4.2.0"
  
  cli-width@^2.0.0:
    version "2.2.1"
    resolved "https://registry.npmmirror.com/cli-width/-/cli-width-2.2.1.tgz"
    integrity sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw==
  
  clipboard-js@^0.3.3:
    version "0.3.6"
    resolved "https://registry.npmmirror.com/clipboard-js/-/clipboard-js-0.3.6.tgz#6260add69b5318fde40b80f9d3c8c863efdaf339"
    integrity sha512-hyrmvbrYCeRBHdiR3KrEz0tmrUTXXEU8HLeGW0Y0icUSwYmAsmc+d6wfE4EDb/TxZmAVJG0eTfMlulCIT+ecfw==
  
  cliui@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/cliui/-/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
    integrity sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==
    dependencies:
      center-align "^0.1.1"
      right-align "^0.1.1"
      wordwrap "0.0.2"
  
  cliui@^8.0.1:
    version "8.0.1"
    resolved "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz"
    integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
    dependencies:
      string-width "^4.2.0"
      strip-ansi "^6.0.1"
      wrap-ansi "^7.0.0"
  
  clone-response@^1.0.2:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/clone-response/-/clone-response-1.0.3.tgz"
    integrity sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==
    dependencies:
      mimic-response "^1.0.0"
  
  clone@^1.0.2:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz"
    integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==
  
  co-body@^5.1.1:
    version "5.2.0"
    resolved "https://registry.npmmirror.com/co-body/-/co-body-5.2.0.tgz"
    integrity sha512-sX/LQ7LqUhgyaxzbe7IqwPeTr2yfpfUIQ/dgpKo6ZI4y4lpQA0YxAomWIY+7I7rHWcG02PG+OuPREzMW/5tszQ==
    dependencies:
      inflation "^2.0.0"
      qs "^6.4.0"
      raw-body "^2.2.0"
      type-is "^1.6.14"
  
  co@^4.6.0:
    version "4.6.0"
    resolved "https://registry.npmmirror.com/co/-/co-4.6.0.tgz"
    integrity sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==
  
  codepage@~1.15.0:
    version "1.15.0"
    resolved "https://registry.npmmirror.com/codepage/-/codepage-1.15.0.tgz"
    integrity sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==
  
  color-convert@^1.9.0:
    version "1.9.3"
    resolved "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
    integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
    dependencies:
      color-name "1.1.3"
  
  color-convert@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
    integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
    dependencies:
      color-name "~1.1.4"
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
    integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==
  
  color-name@^1.0.0, color-name@~1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
    integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==
  
  color-string@^1.9.0:
    version "1.9.1"
    resolved "https://registry.npmmirror.com/color-string/-/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
    integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
    dependencies:
      color-name "^1.0.0"
      simple-swizzle "^0.2.2"
  
  color-support@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmmirror.com/color-support/-/color-support-1.1.3.tgz"
    integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==
  
  color@^4.2.3:
    version "4.2.3"
    resolved "https://registry.npmmirror.com/color/-/color-4.2.3.tgz#d781ecb5e57224ee43ea9627560107c0e0c6463a"
    integrity sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
    dependencies:
      color-convert "^2.0.1"
      color-string "^1.9.0"
  
  colorful@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/colorful/-/colorful-2.1.0.tgz#6a8bdcbc2da42a50db3b4882fa25263aaa1f2d8e"
    integrity sha512-DpDLDvi/vPzqoPX7Dw44ZZf004DCdEcCx1pf5hq5aipVHXjwgRSYGCz3m17rA2XCduW91wJUapge8/3qLvjYcg==
  
  colors@1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/colors/-/colors-1.0.3.tgz"
    integrity sha512-pFGrxThWcWQ2MsAz6RtgeWe4NK2kUE1WfsrvvlctdII745EW9I0yflqhe7++M5LEc7bV2c/9/5zc8sFcpL0Drw==
  
  combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
    version "1.0.8"
    resolved "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz"
    integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
    dependencies:
      delayed-stream "~1.0.0"
  
  commander@10.0.0:
    version "10.0.0"
    resolved "https://registry.npmmirror.com/commander/-/commander-10.0.0.tgz"
    integrity sha512-zS5PnTI22FIRM6ylNW8G4Ap0IEOyk62fhLSD0+uHRT9McRCLGpkVNvao4bjimpK/GShynyQkFFxHhwMcETmduA==
  
  commander@2.9.0:
    version "2.9.0"
    resolved "https://registry.npmmirror.com/commander/-/commander-2.9.0.tgz"
    integrity sha512-bmkUukX8wAOjHdN26xj5c4ctEV22TQ7dQYhSmuckKhToXrkUn0iIaolHdIxYYqD55nhpSPA9zPQ1yP57GdXP2A==
    dependencies:
      graceful-readlink ">= 1.0.0"
  
  commander@^2.2.0, commander@^2.9.0:
    version "2.20.3"
    resolved "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
    integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==
  
  commander@^5.0.0:
    version "5.1.0"
    resolved "https://registry.npmmirror.com/commander/-/commander-5.1.0.tgz"
    integrity sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==
  
  commander@~2.11.0:
    version "2.11.0"
    resolved "https://registry.npmmirror.com/commander/-/commander-2.11.0.tgz#157152fd1e7a6c8d98a5b715cf376df928004563"
    integrity sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ==
  
  compare-version@^0.1.2:
    version "0.1.2"
    resolved "https://registry.npmmirror.com/compare-version/-/compare-version-0.1.2.tgz"
    integrity sha512-pJDh5/4wrEnXX/VWRZvruAGHkzKdr46z11OlTPN+VrATlWWhSKewNCJ1futCO5C7eJB3nPMFZA1LeYtcFboZ2A==
  
  component-emitter@^1.2.1:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
    integrity sha512-Rd3se6QB+sO1TwqZjscQrurpEPIfO0/yYnSin6Q/rD3mOutHvUrCAhJub3r90uNb+SESBuE0QYoB90YdfatsRg==
  
  compressible@~2.0.16:
    version "2.0.18"
    resolved "https://registry.npmmirror.com/compressible/-/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
    integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
    dependencies:
      mime-db ">= 1.43.0 < 2"
  
  compression@^1.4.4:
    version "1.7.4"
    resolved "https://registry.npmmirror.com/compression/-/compression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
    integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
    dependencies:
      accepts "~1.3.5"
      bytes "3.0.0"
      compressible "~2.0.16"
      debug "2.6.9"
      on-headers "~1.0.2"
      safe-buffer "5.1.2"
      vary "~1.1.2"
  
  concat-map@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz"
    integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==
  
  concat-stream@^1.6.2:
    version "1.6.2"
    resolved "https://registry.npmmirror.com/concat-stream/-/concat-stream-1.6.2.tgz"
    integrity sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==
    dependencies:
      buffer-from "^1.0.0"
      inherits "^2.0.3"
      readable-stream "^2.2.2"
      typedarray "^0.0.6"
  
  config-chain@^1.1.11:
    version "1.1.13"
    resolved "https://registry.npmmirror.com/config-chain/-/config-chain-1.1.13.tgz"
    integrity sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==
    dependencies:
      ini "^1.3.4"
      proto-list "~1.2.1"
  
  console-control-strings@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/console-control-strings/-/console-control-strings-1.1.0.tgz"
    integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==
  
  constantinople@^3.0.1, constantinople@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmmirror.com/constantinople/-/constantinople-3.1.2.tgz#d45ed724f57d3d10500017a7d3a889c1381ae647"
    integrity sha512-yePcBqEFhLOqSBtwYOGGS1exHo/s1xjekXiinh4itpNQGCu4KA1euPh1fg07N2wMITZXQkBz75Ntdt1ctGZouw==
    dependencies:
      "@types/babel-types" "^7.0.0"
      "@types/babylon" "^6.16.2"
      babel-types "^6.26.0"
      babylon "^6.18.0"
  
  content-disposition@0.5.4, content-disposition@~0.5.2:
    version "0.5.4"
    resolved "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz"
    integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
    dependencies:
      safe-buffer "5.2.1"
  
  content-type@^1.0.2, content-type@^1.0.4, content-type@~1.0.4, content-type@~1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz"
    integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==
  
  cookie-signature@1.0.6:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
    integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==
  
  cookie@0.5.0:
    version "0.5.0"
    resolved "https://registry.npmmirror.com/cookie/-/cookie-0.5.0.tgz#d1f5d71adec6558c58f389987c366aa47e994f8b"
    integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==
  
  cookie@~0.4.1:
    version "0.4.2"
    resolved "https://registry.npmmirror.com/cookie/-/cookie-0.4.2.tgz"
    integrity sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==
  
  cookies@~0.8.0:
    version "0.8.0"
    resolved "https://registry.npmmirror.com/cookies/-/cookies-0.8.0.tgz"
    integrity sha512-8aPsApQfebXnuI+537McwYsDtjVxGm8gTIzQI3FDW6t5t/DAhERxtnbEPN/8RX+uZthoz4eCOgloXaE5cYyNow==
    dependencies:
      depd "~2.0.0"
      keygrip "~1.1.0"
  
  copy-to@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/copy-to/-/copy-to-2.0.1.tgz"
    integrity sha512-3DdaFaU/Zf1AnpLiFDeNCD4TOWe3Zl2RZaTzUvWiIk5ERzcCodOE20Vqq4fzCbNoHURFHT4/us/Lfq+S2zyY4w==
  
  core-js@^2.4.0:
    version "2.6.12"
    resolved "https://registry.npmmirror.com/core-js/-/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
    integrity sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==
  
  core-util-is@1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
    integrity sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==
  
  core-util-is@^1.0.2, core-util-is@~1.0.0:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz"
    integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==
  
  cors@~2.8.5:
    version "2.8.5"
    resolved "https://registry.npmmirror.com/cors/-/cors-2.8.5.tgz"
    integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
    dependencies:
      object-assign "^4"
      vary "^1"
  
  crc-32@~1.2.0:
    version "1.2.2"
    resolved "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.2.tgz"
    integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==
  
  crc@^3.8.0:
    version "3.8.0"
    resolved "https://registry.npmmirror.com/crc/-/crc-3.8.0.tgz#ad60269c2c856f8c299e2c4cc0de4556914056c6"
    integrity sha512-iX3mfgcTMIq3ZKLIsVFAbv7+Mc10kxabAGQb8HvjA1o3T1PIYprbakQ65d3I+2HGHt6nSKkM9PYjgoJO2KcFBQ==
    dependencies:
      buffer "^5.1.0"
  
  cross-spawn@^6.0.5:
    version "6.0.5"
    resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-6.0.5.tgz"
    integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
    dependencies:
      nice-try "^1.0.4"
      path-key "^2.0.1"
      semver "^5.5.0"
      shebang-command "^1.2.0"
      which "^1.2.9"
  
  cross-spawn@^7.0.1, cross-spawn@^7.0.3:
    version "7.0.3"
    resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
    integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
    dependencies:
      path-key "^3.1.0"
      shebang-command "^2.0.0"
      which "^2.0.1"
  
  crypt@0.0.2:
    version "0.0.2"
    resolved "https://registry.npmmirror.com/crypt/-/crypt-0.0.2.tgz"
    integrity sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==
  
  crypto-js@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.1.1.tgz#9e485bcf03521041bd85844786b83fb7619736cf"
    integrity sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw==
  
  dashdash@^1.12.0:
    version "1.14.1"
    resolved "https://registry.npmmirror.com/dashdash/-/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
    integrity sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==
    dependencies:
      assert-plus "^1.0.0"
  
  data-uri-to-buffer@3:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-3.0.1.tgz"
    integrity sha512-WboRycPNsVw3B3TL559F7kuBUM4d8CgMEvk6xEJlOp7OBPjt6G7z8WMWlD2rOFZLk6OYfFIUGsCOWzcQH9K2og==
  
  dayjs@^1.10.7:
    version "1.11.7"
    resolved "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.7.tgz"
    integrity sha512-+Yw9U6YO5TQohxLcIkrXBeY73WP3ejHWVvx8XCk3gxvQDCTEmS48ZrSZCKciI7Bhl/uCMyxYtE9UqRILmFphkQ==
  
  debug@2.6.9, debug@^2.6.8, debug@^2.6.9:
    version "2.6.9"
    resolved "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
    integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
    dependencies:
      ms "2.0.0"
  
  debug@4, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4, debug@~4.3.1, debug@~4.3.2:
    version "4.3.4"
    resolved "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
    integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
    dependencies:
      ms "2.1.2"
  
  debug@^3.1.0, debug@^3.2.7:
    version "3.2.7"
    resolved "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz"
    integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
    dependencies:
      ms "^2.1.1"
  
  decamelize@^1.0.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/decamelize/-/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
    integrity sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==
  
  decompress-response@^3.3.0:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/decompress-response/-/decompress-response-3.3.0.tgz"
    integrity sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==
    dependencies:
      mimic-response "^1.0.0"
  
  decompress-response@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmmirror.com/decompress-response/-/decompress-response-6.0.0.tgz"
    integrity sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==
    dependencies:
      mimic-response "^3.1.0"
  
  deep-equal@~1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/deep-equal/-/deep-equal-1.0.1.tgz"
    integrity sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==
  
  deep-extend@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmmirror.com/deep-extend/-/deep-extend-0.6.0.tgz"
    integrity sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==
  
  deep-is@~0.1.3:
    version "0.1.4"
    resolved "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz"
    integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==
  
  default-user-agent@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/default-user-agent/-/default-user-agent-1.0.0.tgz"
    integrity sha512-bDF7bg6OSNcSwFWPu4zYKpVkJZQYVrAANMYB8bc9Szem1D0yKdm4sa/rOCs2aC9+2GMqQ7KnwtZRvDhmLF0dXw==
    dependencies:
      os-name "~1.0.3"
  
  defaults@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz"
    integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
    dependencies:
      clone "^1.0.2"
  
  defer-to-connect@^1.0.1:
    version "1.1.3"
    resolved "https://registry.npmmirror.com/defer-to-connect/-/defer-to-connect-1.1.3.tgz"
    integrity sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==
  
  defer-to-connect@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/defer-to-connect/-/defer-to-connect-2.0.1.tgz"
    integrity sha512-4tvttepXG1VaYGrRibk5EwJd1t4udunSOVMdLSAL6mId1ix438oPwPZMALY41FCijukO1L0twNcGsdzS7dHgDg==
  
  define-properties@^1.1.3:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.0.tgz"
    integrity sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==
    dependencies:
      has-property-descriptors "^1.0.0"
      object-keys "^1.1.1"
  
  degenerator@^3.0.2:
    version "3.0.3"
    resolved "https://registry.npmmirror.com/degenerator/-/degenerator-3.0.3.tgz"
    integrity sha512-FTq/qYMeBJACu1gHcXJvzsRBTK6aw5zWCYbEnIOyamOt5UJufWJRQ5XfDb6OuayfJWvmWAHgcZyt43vm/hbj7g==
    dependencies:
      ast-types "^0.13.2"
      escodegen "^1.8.1"
      esprima "^4.0.0"
      vm2 "^3.9.11"
  
  delayed-stream@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz"
    integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==
  
  delegates@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/delegates/-/delegates-1.0.0.tgz"
    integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==
  
  depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz"
    integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==
  
  depd@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/depd/-/depd-1.1.2.tgz"
    integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==
  
  destroy@1.2.0, destroy@^1.0.4:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz"
    integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==
  
  detect-libc@^2.0.0, detect-libc@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/detect-libc/-/detect-libc-2.0.1.tgz"
    integrity sha512-463v3ZeIrcWtdgIg6vI6XUncguvr2TnGl4SzDXinkt9mSLpBJKXT3mW6xT3VQdDN11+WVs29pgvivTc4Lp8v+w==
  
  detect-node@^2.0.4:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/detect-node/-/detect-node-2.1.0.tgz"
    integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==
  
  dezalgo@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/dezalgo/-/dezalgo-1.0.4.tgz"
    integrity sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==
    dependencies:
      asap "^2.0.0"
      wrappy "1"
  
  digest-header@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/digest-header/-/digest-header-1.0.0.tgz"
    integrity sha512-sRTuakZ2PkOUCuAaVv+SLjhr/hRf8ldZP0XnGEQ69RFGxmll5fVaMsnRXWKKK4XsUTnJf8+eRPSFNgE/lWa9wQ==
    dependencies:
      utility "^1.17.0"
  
  dir-compare@^2.4.0:
    version "2.4.0"
    resolved "https://registry.npmmirror.com/dir-compare/-/dir-compare-2.4.0.tgz"
    integrity sha512-l9hmu8x/rjVC9Z2zmGzkhOEowZvW7pmYws5CWHutg8u1JgvsKWMx7Q/UODeu4djLZ4FgW5besw5yvMQnBHzuCA==
    dependencies:
      buffer-equal "1.0.0"
      colors "1.0.3"
      commander "2.9.0"
      minimatch "3.0.4"
  
  dir-glob@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz"
    integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
    dependencies:
      path-type "^4.0.0"
  
  dmg-builder@23.6.0:
    version "23.6.0"
    resolved "https://registry.npmmirror.com/dmg-builder/-/dmg-builder-23.6.0.tgz"
    integrity sha512-jFZvY1JohyHarIAlTbfQOk+HnceGjjAdFjVn3n8xlDWKsYNqbO4muca6qXEZTfGXeQMG7TYim6CeS5XKSfSsGA==
    dependencies:
      app-builder-lib "23.6.0"
      builder-util "23.6.0"
      builder-util-runtime "9.1.1"
      fs-extra "^10.0.0"
      iconv-lite "^0.6.2"
      js-yaml "^4.1.0"
    optionalDependencies:
      dmg-license "^1.0.11"
  
  dmg-license@^1.0.11:
    version "1.0.11"
    resolved "https://registry.npmmirror.com/dmg-license/-/dmg-license-1.0.11.tgz#7b3bc3745d1b52be7506b4ee80cb61df6e4cd79a"
    integrity sha512-ZdzmqwKmECOWJpqefloC5OJy1+WZBBse5+MR88z9g9Zn4VY+WYUkAyojmhzJckH5YbbZGcYIuGAkY5/Ys5OM2Q==
    dependencies:
      "@types/plist" "^3.0.1"
      "@types/verror" "^1.10.3"
      ajv "^6.10.0"
      crc "^3.8.0"
      iconv-corefoundation "^1.1.7"
      plist "^3.0.4"
      smart-buffer "^4.0.2"
      verror "^1.10.0"
  
  doctrine@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz"
    integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
    dependencies:
      esutils "^2.0.2"
  
  doctypes@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/doctypes/-/doctypes-1.1.0.tgz#ea80b106a87538774e8a3a4a5afe293de489e0a9"
    integrity sha512-LLBi6pEqS6Do3EKQ3J0NqHWV5hhb78Pi8vvESYwyOy2c31ZEZVdtitdzsQsKb7878PEERhzUk0ftqGhG6Mz+pQ==
  
  dom-walk@^0.1.0:
    version "0.1.2"
    resolved "https://registry.npmmirror.com/dom-walk/-/dom-walk-0.1.2.tgz#0c548bef048f4d1f2a97249002236060daa3fd84"
    integrity sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==
  
  dotenv-expand@^5.1.0:
    version "5.1.0"
    resolved "https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
    integrity sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==
  
  dotenv@^9.0.2:
    version "9.0.2"
    resolved "https://registry.npmmirror.com/dotenv/-/dotenv-9.0.2.tgz"
    integrity sha512-I9OvvrHp4pIARv4+x9iuewrWycX6CcZtoAu1XrzPxc5UygMJXJZYmBsynku8IkrJwgypE5DGNjDPmPRhDCptUg==
  
  duplexer3@^0.1.4:
    version "0.1.5"
    resolved "https://registry.npmmirror.com/duplexer3/-/duplexer3-0.1.5.tgz"
    integrity sha512-1A8za6ws41LQgv9HrE/66jyC5yuSjQ3L/KOpFtoBilsAK2iA2wuS5rTt1OCzIvtS2V7nVmedsUU+DGRcjBmOYA==
  
  ecc-jsbn@~0.1.1:
    version "0.1.2"
    resolved "https://registry.npmmirror.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
    integrity sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==
    dependencies:
      jsbn "~0.1.0"
      safer-buffer "^2.1.0"
  
  ee-core@^1.5.1:
    version "1.5.1"
    resolved "https://registry.npmmirror.com/ee-core/-/ee-core-1.5.1.tgz"
    integrity sha512-mwuCfxNZ5C4+QPQIbpQBHYzekc9V0Ubgr1JO75ypJ8HxHEOrxQ6g5V8htNBS02r3PrY0PmayhxCn518xokPd4w==
    dependencies:
      agentkeepalive "^4.2.0"
      bytenode "^1.3.6"
      co "^4.6.0"
      debug "^4.3.3"
      depd "^2.0.0"
      egg-errors "^2.3.0"
      egg-logger "^2.7.1"
      electron-is "^3.0.0"
      electron-updater "^4.6.1"
      extend2 "^1.0.1"
      fs-extra "^10.0.0"
      get-port "^5.1.1"
      globby "^10.0.0"
      humanize-ms "^1.2.1"
      is-type-of "^1.2.1"
      javascript-obfuscator "^4.0.0"
      koa "^2.13.4"
      koa-body "^5.0.0"
      koa-convert "^2.0.0"
      koa-static "^5.0.0"
      koa2-cors "^2.0.6"
      lodash "^4.17.21"
      lowdb "^1.0.0"
      path-to-regexp "^6.2.0"
      socket.io "^4.4.1"
      socket.io-client "^4.4.1"
      urllib "^2.38.0"
      utility "^1.17.0"
  
  ee-first@1.1.1, ee-first@~1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz"
    integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==
  
  egg-errors@^2.2.0, egg-errors@^2.3.0:
    version "2.3.2"
    resolved "https://registry.npmmirror.com/egg-errors/-/egg-errors-2.3.2.tgz"
    integrity sha512-E+Sx7IBVrfRyHSjFXaq4sCZ3Uk3ka9PYySaQ8VbRZmLEt9ENBCD99yVzLIeWUH2QfzvkrjY9El1eHmLeRx7cfw==
  
  egg-logger@^2.7.1:
    version "2.9.1"
    resolved "https://registry.npmmirror.com/egg-logger/-/egg-logger-2.9.1.tgz"
    integrity sha512-TPYdNthc7yGV+08A2U4g1T1wgRAjfTnsYC53JgfdKiYukaH3na1KPXEu+TEWni7IooqkFGkQ0t0WY+ylWFnvbw==
    dependencies:
      chalk "^2.4.1"
      circular-json-for-egg "^1.0.0"
      debug "^2.6.9"
      depd "^2.0.0"
      egg-errors "^2.2.0"
      iconv-lite "^0.4.24"
      mkdirp "^0.5.1"
      utility "^1.15.0"
  
  ejs@^3.1.7:
    version "3.1.9"
    resolved "https://registry.npmmirror.com/ejs/-/ejs-3.1.9.tgz"
    integrity sha512-rC+QVNMJWv+MtPgkt0y+0rVEIdbtxVADApW9JXrUVlzHetgcyczP/E7DJmWJ4fJCZF2cPcBk0laWO9ZHMG3DmQ==
    dependencies:
      jake "^10.8.5"
  
  electron-builder@^23.1.0:
    version "23.6.0"
    resolved "https://registry.npmmirror.com/electron-builder/-/electron-builder-23.6.0.tgz"
    integrity sha512-y8D4zO+HXGCNxFBV/JlyhFnoQ0Y0K7/sFH+XwIbj47pqaW8S6PGYQbjoObolKBR1ddQFPt4rwp4CnwMJrW3HAw==
    dependencies:
      "@types/yargs" "^17.0.1"
      app-builder-lib "23.6.0"
      builder-util "23.6.0"
      builder-util-runtime "9.1.1"
      chalk "^4.1.1"
      dmg-builder "23.6.0"
      fs-extra "^10.0.0"
      is-ci "^3.0.0"
      lazy-val "^1.0.5"
      read-config-file "6.2.0"
      simple-update-notifier "^1.0.7"
      yargs "^17.5.1"
  
  electron-is-dev@^0.3.0:
    version "0.3.0"
    resolved "https://registry.npmmirror.com/electron-is-dev/-/electron-is-dev-0.3.0.tgz"
    integrity sha512-jLttuuq8QK67n3mXmIe9pkrO7IH3LGIk12xJkhTmc852U2sCJaRAOpRGPSh+1Xnzck5v9escd9YXzuze9nGejg==
  
  electron-is@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/electron-is/-/electron-is-3.0.0.tgz"
    integrity sha512-aQv1y3WrDZ+mtO8acbhiiip/8fa0Et7cvZyvlqJm2H7fih4hiJWEFRyYxzLgDG2kmiLdF8l3y5tbek5JFOPQkQ==
    dependencies:
      electron-is-dev "^0.3.0"
      semver "^5.5.0"
  
  electron-osx-sign@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmmirror.com/electron-osx-sign/-/electron-osx-sign-0.6.0.tgz"
    integrity sha512-+hiIEb2Xxk6eDKJ2FFlpofCnemCbjbT5jz+BKGpVBrRNT3kWTGs4DfNX6IzGwgi33hUcXF+kFs9JW+r6Wc1LRg==
    dependencies:
      bluebird "^3.5.0"
      compare-version "^0.1.2"
      debug "^2.6.8"
      isbinaryfile "^3.0.2"
      minimist "^1.2.0"
      plist "^3.0.1"
  
  electron-publish@23.6.0:
    version "23.6.0"
    resolved "https://registry.npmmirror.com/electron-publish/-/electron-publish-23.6.0.tgz"
    integrity sha512-jPj3y+eIZQJF/+t5SLvsI5eS4mazCbNYqatv5JihbqOstIM13k0d1Z3vAWntvtt13Itl61SO6seicWdioOU5dg==
    dependencies:
      "@types/fs-extra" "^9.0.11"
      builder-util "23.6.0"
      builder-util-runtime "9.1.1"
      chalk "^4.1.1"
      fs-extra "^10.0.0"
      lazy-val "^1.0.5"
      mime "^2.5.2"
  
  electron-rebuild@^3.2.8:
    version "3.2.9"
    resolved "https://registry.npmmirror.com/electron-rebuild/-/electron-rebuild-3.2.9.tgz"
    integrity sha512-FkEZNFViUem3P0RLYbZkUjC8LUFIK+wKq09GHoOITSJjfDAVQv964hwaNseTTWt58sITQX3/5fHNYcTefqaCWw==
    dependencies:
      "@malept/cross-spawn-promise" "^2.0.0"
      chalk "^4.0.0"
      debug "^4.1.1"
      detect-libc "^2.0.1"
      fs-extra "^10.0.0"
      got "^11.7.0"
      lzma-native "^8.0.5"
      node-abi "^3.0.0"
      node-api-version "^0.1.4"
      node-gyp "^9.0.0"
      ora "^5.1.0"
      semver "^7.3.5"
      tar "^6.0.5"
      yargs "^17.0.1"
  
  electron-updater@^4.6.1:
    version "4.6.5"
    resolved "https://registry.npmmirror.com/electron-updater/-/electron-updater-4.6.5.tgz"
    integrity sha512-kdTly8O9mSZfm9fslc1mnCY+mYOeaYRy7ERa2Fed240u01BKll3aiupzkd07qKw69KvhBSzuHroIW3mF0D8DWA==
    dependencies:
      "@types/semver" "^7.3.6"
      builder-util-runtime "8.9.2"
      fs-extra "^10.0.0"
      js-yaml "^4.1.0"
      lazy-val "^1.0.5"
      lodash.escaperegexp "^4.1.2"
      lodash.isequal "^4.5.0"
      semver "^7.3.5"
  
  electron-updater@^5.3.0:
    version "5.3.0"
    resolved "https://registry.npmmirror.com/electron-updater/-/electron-updater-5.3.0.tgz"
    integrity sha512-iKEr7yQBcvnQUPnSDYGSWC9t0eF2YbZWeYYYZzYxdl+HiRejXFENjYMnYjoOm2zxyD6Cr2JTHZhp9pqxiXuCOw==
    dependencies:
      "@types/semver" "^7.3.6"
      builder-util-runtime "9.1.1"
      fs-extra "^10.0.0"
      js-yaml "^4.1.0"
      lazy-val "^1.0.5"
      lodash.escaperegexp "^4.1.2"
      lodash.isequal "^4.5.0"
      semver "^7.3.5"
      typed-emitter "^2.1.0"
  
  electron@13.6.9:
    version "13.6.9"
    resolved "https://registry.npmmirror.com/electron/-/electron-13.6.9.tgz"
    integrity sha512-Es/sBy85NIuqsO9MW41PUCpwIkeinlTQ7g0ainfnmRAM2rmog3GBxVCaoV5dzEjwTF7TKG1Yr/E7Z3qHmlfWAg==
    dependencies:
      "@electron/get" "^1.0.1"
      "@types/node" "^14.6.2"
      extract-zip "^1.0.3"
  
  emoji-regex@^7.0.1:
    version "7.0.3"
    resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.3.tgz"
    integrity sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==
  
  emoji-regex@^8.0.0:
    version "8.0.0"
    resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz"
    integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==
  
  encodeurl@^1.0.2, encodeurl@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz"
    integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==
  
  encoding@^0.1.13:
    version "0.1.13"
    resolved "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz"
    integrity sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
    dependencies:
      iconv-lite "^0.6.2"
  
  end-of-stream@^1.1.0, end-of-stream@^1.4.1:
    version "1.4.4"
    resolved "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz"
    integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
    dependencies:
      once "^1.4.0"
  
  engine.io-client@~6.4.0:
    version "6.4.0"
    resolved "https://registry.npmmirror.com/engine.io-client/-/engine.io-client-6.4.0.tgz"
    integrity sha512-GyKPDyoEha+XZ7iEqam49vz6auPnNJ9ZBfy89f+rMMas8AuiMWOZ9PVzu8xb9ZC6rafUqiGHSCfu22ih66E+1g==
    dependencies:
      "@socket.io/component-emitter" "~3.1.0"
      debug "~4.3.1"
      engine.io-parser "~5.0.3"
      ws "~8.11.0"
      xmlhttprequest-ssl "~2.0.0"
  
  engine.io-parser@~5.0.3:
    version "5.0.6"
    resolved "https://registry.npmmirror.com/engine.io-parser/-/engine.io-parser-5.0.6.tgz"
    integrity sha512-tjuoZDMAdEhVnSFleYPCtdL2GXwVTGtNjoeJd9IhIG3C1xs9uwxqRNEu5WpnDZCaozwVlK/nuQhpodhXSIMaxw==
  
  engine.io@~6.4.1:
    version "6.4.1"
    resolved "https://registry.npmmirror.com/engine.io/-/engine.io-6.4.1.tgz"
    integrity sha512-JFYQurD/nbsA5BSPmbaOSLa3tSVj8L6o4srSwXXY3NqE+gGUNmmPTbhn8tjzcCtSqhFgIeqef81ngny8JM25hw==
    dependencies:
      "@types/cookie" "^0.4.1"
      "@types/cors" "^2.8.12"
      "@types/node" ">=10.0.0"
      accepts "~1.3.4"
      base64id "2.0.0"
      cookie "~0.4.1"
      cors "~2.8.5"
      debug "~4.3.1"
      engine.io-parser "~5.0.3"
      ws "~8.11.0"
  
  env-paths@^2.2.0:
    version "2.2.1"
    resolved "https://registry.npmmirror.com/env-paths/-/env-paths-2.2.1.tgz"
    integrity sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==
  
  err-code@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/err-code/-/err-code-2.0.3.tgz"
    integrity sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==
  
  es6-error@^4.1.1:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/es6-error/-/es6-error-4.1.1.tgz"
    integrity sha512-Um/+FxMr9CISWh0bi5Zv0iOD+4cFh5qLeks1qhAopKVAJw3drgKbKySikp7wGhDL0HPeaja0P5ULZrxLkniUVg==
  
  es6-object-assign@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/es6-object-assign/-/es6-object-assign-1.1.0.tgz"
    integrity sha512-MEl9uirslVwqQU369iHNWZXsI8yaZYGg/D65aOgZkeyFJwHYSxilf7rQzXKI7DdDuBPrBXbfk3sl9hJhmd5AUw==
  
  es6-promise@^3.3.1:
    version "3.3.1"
    resolved "https://registry.npmmirror.com/es6-promise/-/es6-promise-3.3.1.tgz#a08cdde84ccdbf34d027a1451bc91d4bcd28a613"
    integrity sha512-SOp9Phqvqn7jtEUxPWdWfWoLmyt2VaJ6MpvP9Comy1MceMXqE6bxvaTu4iaxpYYPzhny28Lc+M87/c2cPK6lDg==
  
  escalade@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz"
    integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==
  
  escape-html@^1.0.3, escape-html@~1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz"
    integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==
  
  escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
    integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==
  
  escape-string-regexp@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
    integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==
  
  escodegen@^1.8.1:
    version "1.14.3"
    resolved "https://registry.npmmirror.com/escodegen/-/escodegen-1.14.3.tgz"
    integrity sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==
    dependencies:
      esprima "^4.0.1"
      estraverse "^4.2.0"
      esutils "^2.0.2"
      optionator "^0.8.1"
    optionalDependencies:
      source-map "~0.6.1"
  
  eslint-plugin-prettier@^3.0.1:
    version "3.4.1"
    resolved "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.4.1.tgz"
    integrity sha512-htg25EUYUeIhKHXjOinK4BgCcDwtLHjqaxCDsMy5nbnUMkKFvIhMVCp+5GFUXQ4Nr8lBsPqtGAqBenbpFqAA2g==
    dependencies:
      prettier-linter-helpers "^1.0.0"
  
  eslint-scope@7.1.1:
    version "7.1.1"
    resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.1.1.tgz"
    integrity sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==
    dependencies:
      esrecurse "^4.3.0"
      estraverse "^5.2.0"
  
  eslint-scope@^4.0.3:
    version "4.0.3"
    resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-4.0.3.tgz"
    integrity sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==
    dependencies:
      esrecurse "^4.1.0"
      estraverse "^4.1.1"
  
  eslint-utils@^1.3.1:
    version "1.4.3"
    resolved "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-1.4.3.tgz"
    integrity sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==
    dependencies:
      eslint-visitor-keys "^1.1.0"
  
  eslint-visitor-keys@3.3.0:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz"
    integrity sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==
  
  eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
    integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==
  
  eslint@^5.13.0:
    version "5.16.0"
    resolved "https://registry.npmmirror.com/eslint/-/eslint-5.16.0.tgz"
    integrity sha512-S3Rz11i7c8AA5JPv7xAH+dOyq/Cu/VXHiHXBPOU1k/JAM5dXqQPt3qcrhpHSorXmrpu2g0gkIBVXAqCpzfoZIg==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      ajv "^6.9.1"
      chalk "^2.1.0"
      cross-spawn "^6.0.5"
      debug "^4.0.1"
      doctrine "^3.0.0"
      eslint-scope "^4.0.3"
      eslint-utils "^1.3.1"
      eslint-visitor-keys "^1.0.0"
      espree "^5.0.1"
      esquery "^1.0.1"
      esutils "^2.0.2"
      file-entry-cache "^5.0.1"
      functional-red-black-tree "^1.0.1"
      glob "^7.1.2"
      globals "^11.7.0"
      ignore "^4.0.6"
      import-fresh "^3.0.0"
      imurmurhash "^0.1.4"
      inquirer "^6.2.2"
      js-yaml "^3.13.0"
      json-stable-stringify-without-jsonify "^1.0.1"
      levn "^0.3.0"
      lodash "^4.17.11"
      minimatch "^3.0.4"
      mkdirp "^0.5.1"
      natural-compare "^1.4.0"
      optionator "^0.8.2"
      path-is-inside "^1.0.2"
      progress "^2.0.0"
      regexpp "^2.0.1"
      semver "^5.5.1"
      strip-ansi "^4.0.0"
      strip-json-comments "^2.0.1"
      table "^5.2.3"
      text-table "^0.2.0"
  
  espree@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/espree/-/espree-5.0.1.tgz"
    integrity sha512-qWAZcWh4XE/RwzLJejfcofscgMc9CamR6Tn1+XRXNzrvUSSbiAjGOI/fggztjIi7y9VLPqnICMIPiGyr8JaZ0A==
    dependencies:
      acorn "^6.0.7"
      acorn-jsx "^5.0.0"
      eslint-visitor-keys "^1.0.0"
  
  esprima@^4.0.0, esprima@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz"
    integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==
  
  esquery@^1.0.1:
    version "1.5.0"
    resolved "https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz"
    integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
    dependencies:
      estraverse "^5.1.0"
  
  esrecurse@^4.1.0, esrecurse@^4.3.0:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz"
    integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
    dependencies:
      estraverse "^5.2.0"
  
  estraverse@^4.1.1, estraverse@^4.2.0:
    version "4.3.0"
    resolved "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz"
    integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==
  
  estraverse@^5.1.0, estraverse@^5.2.0:
    version "5.3.0"
    resolved "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
    integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==
  
  esutils@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz"
    integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==
  
  etag@~1.8.1:
    version "1.8.1"
    resolved "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
    integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==
  
  exif-parser@^0.1.12:
    version "0.1.12"
    resolved "https://registry.npmmirror.com/exif-parser/-/exif-parser-0.1.12.tgz#58a9d2d72c02c1f6f02a0ef4a9166272b7760922"
    integrity sha512-c2bQfLNbMzLPmzQuOr8fy0csy84WmwnER81W88DzTp9CYNPJ6yzOj2EZAh9pywYpqHnshVLHQJ8WzldAyfY+Iw==
  
  exit-on-epipe@~1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz"
    integrity sha512-h2z5mrROTxce56S+pnvAV890uu7ls7f1kEvVGJbw1OlFH3/mlJ5bkXu0KRyW94v37zzHPiUd55iLn3DA7TjWpw==
  
  expand-template@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/expand-template/-/expand-template-2.0.3.tgz"
    integrity sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==
  
  express@^4.8.5:
    version "4.18.2"
    resolved "https://registry.npmmirror.com/express/-/express-4.18.2.tgz#3fabe08296e930c796c19e3c516979386ba9fd59"
    integrity sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==
    dependencies:
      accepts "~1.3.8"
      array-flatten "1.1.1"
      body-parser "1.20.1"
      content-disposition "0.5.4"
      content-type "~1.0.4"
      cookie "0.5.0"
      cookie-signature "1.0.6"
      debug "2.6.9"
      depd "2.0.0"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      finalhandler "1.2.0"
      fresh "0.5.2"
      http-errors "2.0.0"
      merge-descriptors "1.0.1"
      methods "~1.1.2"
      on-finished "2.4.1"
      parseurl "~1.3.3"
      path-to-regexp "0.1.7"
      proxy-addr "~2.0.7"
      qs "6.11.0"
      range-parser "~1.2.1"
      safe-buffer "5.2.1"
      send "0.18.0"
      serve-static "1.15.0"
      setprototypeof "1.2.0"
      statuses "2.0.1"
      type-is "~1.6.18"
      utils-merge "1.0.1"
      vary "~1.1.2"
  
  extend-shallow@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-2.0.1.tgz"
    integrity sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==
    dependencies:
      is-extendable "^0.1.0"
  
  extend2@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/extend2/-/extend2-1.0.1.tgz"
    integrity sha512-ISoKeVhtewd5YHzMo+r9KC3Zx0fdpNBqoRzot+6BeEQ3bWQYQQOt0jkkY5gLveI2e7j+vdCJKeszHJIbg2Uceg==
  
  extend@~3.0.2:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
    integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==
  
  external-editor@^2.1.0:
    version "2.2.0"
    resolved "https://registry.npmmirror.com/external-editor/-/external-editor-2.2.0.tgz#045511cfd8d133f3846673d1047c154e214ad3d5"
    integrity sha512-bSn6gvGxKt+b7+6TKEv1ZycHleA7aHhRHyAqJyp5pbUFuYYNIzpZnQDk7AsYckyWdEnTeAnay0aCy2aV6iTk9A==
    dependencies:
      chardet "^0.4.0"
      iconv-lite "^0.4.17"
      tmp "^0.0.33"
  
  external-editor@^3.0.3:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/external-editor/-/external-editor-3.1.0.tgz"
    integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
    dependencies:
      chardet "^0.7.0"
      iconv-lite "^0.4.24"
      tmp "^0.0.33"
  
  extract-zip@^1.0.3:
    version "1.7.0"
    resolved "https://registry.npmmirror.com/extract-zip/-/extract-zip-1.7.0.tgz"
    integrity sha512-xoh5G1W/PB0/27lXgMQyIhP5DSY/LhoCsOyZgb+6iMmRtCwVBo55uKaMoEYrDCKQhWvqEip5ZPKAc6eFNyf/MA==
    dependencies:
      concat-stream "^1.6.2"
      debug "^2.6.9"
      mkdirp "^0.5.4"
      yauzl "^2.10.0"
  
  extsprintf@1.3.0:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
    integrity sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==
  
  extsprintf@^1.2.0:
    version "1.4.1"
    resolved "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
    integrity sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==
  
  fast-deep-equal@3.1.3, fast-deep-equal@^3.1.1:
    version "3.1.3"
    resolved "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  fast-diff@^1.1.2:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz"
    integrity sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w==
  
  fast-glob@^3.0.3:
    version "3.2.12"
    resolved "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.12.tgz"
    integrity sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==
    dependencies:
      "@nodelib/fs.stat" "^2.0.2"
      "@nodelib/fs.walk" "^1.2.3"
      glob-parent "^5.1.2"
      merge2 "^1.3.0"
      micromatch "^4.0.4"
  
  fast-json-stable-stringify@^2.0.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
    integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==
  
  fast-json-stringify@^0.17.0:
    version "0.17.0"
    resolved "https://registry.npmmirror.com/fast-json-stringify/-/fast-json-stringify-0.17.0.tgz#7c3e98ccde5e32802f7930bc360307e4d449c69f"
    integrity sha512-u6d857jtxcTTm00UIFDO6jCB3R/c0AzH89AxU3rI1twmkVPAHo/riUGH20UaDOMem9YXwcKrnoX6pF2dG3xlHA==
    dependencies:
      ajv "^6.0.0"
      fast-safe-stringify "^1.2.1"
  
  fast-levenshtein@~2.0.6:
    version "2.0.6"
    resolved "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
    integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==
  
  fast-safe-stringify@^1.2.1:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/fast-safe-stringify/-/fast-safe-stringify-1.2.3.tgz#9fe22c37fb2f7f86f06b8f004377dbf8f1ee7bc1"
    integrity sha512-QJYT/i0QYoiZBQ71ivxdyTqkwKkQ0oxACXHYxH2zYHJEgzi2LsbjgvtzTbLi1SZcF190Db2YP7I7eTsU2egOlw==
  
  fastq@^1.6.0:
    version "1.15.0"
    resolved "https://registry.npmmirror.com/fastq/-/fastq-1.15.0.tgz"
    integrity sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==
    dependencies:
      reusify "^1.0.4"
  
  fd-slicer@~1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/fd-slicer/-/fd-slicer-1.1.0.tgz"
    integrity sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==
    dependencies:
      pend "~1.2.0"
  
  figures@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/figures/-/figures-2.0.0.tgz"
    integrity sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==
    dependencies:
      escape-string-regexp "^1.0.5"
  
  file-entry-cache@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-5.0.1.tgz"
    integrity sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==
    dependencies:
      flat-cache "^2.0.1"
  
  file-type@^16.5.4:
    version "16.5.4"
    resolved "https://registry.npmmirror.com/file-type/-/file-type-16.5.4.tgz#474fb4f704bee427681f98dd390058a172a6c2fd"
    integrity sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==
    dependencies:
      readable-web-to-node-stream "^3.0.0"
      strtok3 "^6.2.4"
      token-types "^4.1.1"
  
  file-uri-to-path@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
    integrity sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==
  
  file-uri-to-path@2:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/file-uri-to-path/-/file-uri-to-path-2.0.0.tgz"
    integrity sha512-hjPFI8oE/2iQPVe4gbrJ73Pp+Xfub2+WI2LlXDbsaJBwT5wuMh35WNWVYYTpnz895shtwfyutMFLFywpQAFdLg==
  
  filelist@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/filelist/-/filelist-1.0.4.tgz"
    integrity sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==
    dependencies:
      minimatch "^5.0.1"
  
  fill-range@^7.0.1:
    version "7.0.1"
    resolved "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
    integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
    dependencies:
      to-regex-range "^5.0.1"
  
  finalhandler@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.2.0.tgz#7d23fe5731b207b4640e4fcd00aec1f9207a7b32"
    integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
    dependencies:
      debug "2.6.9"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      on-finished "2.4.1"
      parseurl "~1.3.3"
      statuses "2.0.1"
      unpipe "~1.0.0"
  
  flat-cache@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/flat-cache/-/flat-cache-2.0.1.tgz"
    integrity sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==
    dependencies:
      flatted "^2.0.0"
      rimraf "2.6.3"
      write "1.0.3"
  
  flatted@^2.0.0:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/flatted/-/flatted-2.0.2.tgz"
    integrity sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==
  
  fluent-ffmpeg@^2.1.2:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/fluent-ffmpeg/-/fluent-ffmpeg-2.1.2.tgz#c952de2240f812ebda0aa8006d7776ee2acf7d74"
    integrity sha512-IZTB4kq5GK0DPp7sGQ0q/BWurGHffRtQQwVkiqDgeO6wYJLLV5ZhgNOQ65loZxxuPMKZKZcICCUnaGtlxBiR0Q==
    dependencies:
      async ">=0.2.9"
      which "^1.1.1"
  
  follow-redirects@^1.15.0:
    version "1.15.2"
    resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.2.tgz"
    integrity sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==
  
  for-each@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmmirror.com/for-each/-/for-each-0.3.3.tgz"
    integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
    dependencies:
      is-callable "^1.1.3"
  
  forever-agent@~0.6.1:
    version "0.6.1"
    resolved "https://registry.npmmirror.com/forever-agent/-/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
    integrity sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==
  
  form-data@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/form-data/-/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
    integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
    dependencies:
      asynckit "^0.4.0"
      combined-stream "^1.0.8"
      mime-types "^2.1.12"
  
  form-data@~2.3.2:
    version "2.3.3"
    resolved "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
    integrity sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==
    dependencies:
      asynckit "^0.4.0"
      combined-stream "^1.0.6"
      mime-types "^2.1.12"
  
  formidable@^2.0.1:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/formidable/-/formidable-2.1.1.tgz"
    integrity sha512-0EcS9wCFEzLvfiks7omJ+SiYJAiD+TzK4Pcw1UlUoGnhUxDcMKjt0P7x8wEb0u6OHu8Nb98WG3nxtlF5C7bvUQ==
    dependencies:
      dezalgo "^1.0.4"
      hexoid "^1.0.0"
      once "^1.4.0"
      qs "^6.11.0"
  
  formstream@^1.1.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/formstream/-/formstream-1.2.0.tgz"
    integrity sha512-ef4F+FQLnQLly1/AZ5OGNgGzzlOmp+T7+L/TaXASJ1GrETrpZb78/Mz7z+1Ra5FX3nLZE0WIOInGOoa81LxWew==
    dependencies:
      destroy "^1.0.4"
      mime "^2.5.2"
      pause-stream "~0.0.11"
  
  forwarded@0.2.0:
    version "0.2.0"
    resolved "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
    integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==
  
  frac@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz"
    integrity sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==
  
  fresh@0.5.2, fresh@~0.5.2:
    version "0.5.2"
    resolved "https://registry.npmmirror.com/fresh/-/fresh-0.5.2.tgz"
    integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==
  
  fs-constants@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/fs-constants/-/fs-constants-1.0.0.tgz"
    integrity sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==
  
  fs-extra@^10.0.0, fs-extra@^10.1.0:
    version "10.1.0"
    resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-10.1.0.tgz"
    integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
    dependencies:
      graceful-fs "^4.2.0"
      jsonfile "^6.0.1"
      universalify "^2.0.0"
  
  fs-extra@^8.1.0:
    version "8.1.0"
    resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-8.1.0.tgz"
    integrity sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==
    dependencies:
      graceful-fs "^4.2.0"
      jsonfile "^4.0.0"
      universalify "^0.1.0"
  
  fs-extra@^9.0.0, fs-extra@^9.0.1:
    version "9.1.0"
    resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz"
    integrity sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==
    dependencies:
      at-least-node "^1.0.0"
      graceful-fs "^4.2.0"
      jsonfile "^6.0.1"
      universalify "^2.0.0"
  
  fs-minipass@^2.0.0, fs-minipass@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/fs-minipass/-/fs-minipass-2.1.0.tgz"
    integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
    dependencies:
      minipass "^3.0.0"
  
  fs.realpath@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
    integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==
  
  fsevents@~2.3.2:
    version "2.3.2"
    resolved "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
    integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==
  
  ftp@^0.3.10:
    version "0.3.10"
    resolved "https://registry.npmmirror.com/ftp/-/ftp-0.3.10.tgz"
    integrity sha512-faFVML1aBx2UoDStmLwv2Wptt4vw5x03xxX172nhA5Y5HBshW5JweqQ2W4xL4dezQTG8inJsuYcpPHHU3X5OTQ==
    dependencies:
      readable-stream "1.1.x"
      xregexp "2.0.0"
  
  function-bind@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz"
    integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==
  
  functional-red-black-tree@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
    integrity sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==
  
  gauge@^4.0.3:
    version "4.0.4"
    resolved "https://registry.npmmirror.com/gauge/-/gauge-4.0.4.tgz"
    integrity sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==
    dependencies:
      aproba "^1.0.3 || ^2.0.0"
      color-support "^1.1.3"
      console-control-strings "^1.1.0"
      has-unicode "^2.0.1"
      signal-exit "^3.0.7"
      string-width "^4.2.3"
      strip-ansi "^6.0.1"
      wide-align "^1.1.5"
  
  get-caller-file@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"
    integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==
  
  get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.0.tgz"
    integrity sha512-L049y6nFOuom5wGyRc3/gdTLO94dySVKRACj1RmJZBQXlbTMhtNIgkWkUHq+jYmZvKf14EW1EoJnnjbmoHij0Q==
    dependencies:
      function-bind "^1.1.1"
      has "^1.0.3"
      has-symbols "^1.0.3"
  
  get-port@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmmirror.com/get-port/-/get-port-5.1.1.tgz"
    integrity sha512-g/Q1aTSDOxFpchXC4i8ZWvxA1lnPqx/JHqcpIw0/LX9T8x/GBbi6YnlN5nhaKIFkT8oFsscUKgDJYxfwfS6QsQ==
  
  get-stream@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/get-stream/-/get-stream-4.1.0.tgz"
    integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
    dependencies:
      pump "^3.0.0"
  
  get-stream@^5.1.0:
    version "5.2.0"
    resolved "https://registry.npmmirror.com/get-stream/-/get-stream-5.2.0.tgz"
    integrity sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==
    dependencies:
      pump "^3.0.0"
  
  get-uri@3:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/get-uri/-/get-uri-3.0.2.tgz"
    integrity sha512-+5s0SJbGoyiJTZZ2JTpFPLMPSch72KEqGOTvQsBqg0RBWvwhWUSYZFAtz3TPW0GXJuLBJPts1E241iHg+VRfhg==
    dependencies:
      "@tootallnate/once" "1"
      data-uri-to-buffer "3"
      debug "4"
      file-uri-to-path "2"
      fs-extra "^8.1.0"
      ftp "^0.3.10"
  
  getpass@^0.1.1:
    version "0.1.7"
    resolved "https://registry.npmmirror.com/getpass/-/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
    integrity sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==
    dependencies:
      assert-plus "^1.0.0"
  
  gifwrap@^0.9.2:
    version "0.9.4"
    resolved "https://registry.npmmirror.com/gifwrap/-/gifwrap-0.9.4.tgz#f4eb6169ba027d61df64aafbdcb1f8ae58ccc0c5"
    integrity sha512-MDMwbhASQuVeD4JKd1fKgNgCRL3fGqMM4WaqpNhWO0JiMOAjbQdumbs4BbBZEy9/M00EHEjKN3HieVhCUlwjeQ==
    dependencies:
      image-q "^4.0.0"
      omggif "^1.0.10"
  
  github-from-package@0.0.0:
    version "0.0.0"
    resolved "https://registry.npmmirror.com/github-from-package/-/github-from-package-0.0.0.tgz"
    integrity sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==
  
  glob-parent@^5.1.2, glob-parent@~5.1.2:
    version "5.1.2"
    resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
    integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
    dependencies:
      is-glob "^4.0.1"
  
  glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
    version "7.2.3"
    resolved "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz"
    integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^3.1.1"
      once "^1.3.0"
      path-is-absolute "^1.0.0"
  
  glob@^8.0.1:
    version "8.1.0"
    resolved "https://registry.npmmirror.com/glob/-/glob-8.1.0.tgz"
    integrity sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==
    dependencies:
      fs.realpath "^1.0.0"
      inflight "^1.0.4"
      inherits "2"
      minimatch "^5.0.1"
      once "^1.3.0"
  
  global-agent@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/global-agent/-/global-agent-3.0.0.tgz"
    integrity sha512-PT6XReJ+D07JvGoxQMkT6qji/jVNfX/h364XHZOWeRzy64sSFr+xJ5OX7LI3b4MPQzdL4H8Y8M0xzPpsVMwA8Q==
    dependencies:
      boolean "^3.0.1"
      es6-error "^4.1.1"
      matcher "^3.0.0"
      roarr "^2.15.3"
      semver "^7.3.2"
      serialize-error "^7.0.1"
  
  global-tunnel-ng@^2.7.1:
    version "2.7.1"
    resolved "https://registry.npmmirror.com/global-tunnel-ng/-/global-tunnel-ng-2.7.1.tgz"
    integrity sha512-4s+DyciWBV0eK148wqXxcmVAbFVPqtc3sEtUE/GTQfuU80rySLcMhUmHKSHI7/LDj8q0gDYI1lIhRRB7ieRAqg==
    dependencies:
      encodeurl "^1.0.2"
      lodash "^4.17.10"
      npm-conf "^1.1.3"
      tunnel "^0.0.6"
  
  global@~4.4.0:
    version "4.4.0"
    resolved "https://registry.npmmirror.com/global/-/global-4.4.0.tgz#3e7b105179006a323ed71aafca3e9c57a5cc6406"
    integrity sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==
    dependencies:
      min-document "^2.19.0"
      process "^0.11.10"
  
  globals@^11.7.0:
    version "11.12.0"
    resolved "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz"
    integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==
  
  globalthis@^1.0.1:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/globalthis/-/globalthis-1.0.3.tgz"
    integrity sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==
    dependencies:
      define-properties "^1.1.3"
  
  globby@^10.0.0:
    version "10.0.2"
    resolved "https://registry.npmmirror.com/globby/-/globby-10.0.2.tgz"
    integrity sha512-7dUi7RvCoT/xast/o/dLN53oqND4yk0nsHkhRgn9w65C4PofCLOoJ39iSOg+qVDdWQPIEj+eszMHQ+aLVwwQSg==
    dependencies:
      "@types/glob" "^7.1.1"
      array-union "^2.1.0"
      dir-glob "^3.0.1"
      fast-glob "^3.0.3"
      glob "^7.1.3"
      ignore "^5.1.1"
      merge2 "^1.2.3"
      slash "^3.0.0"
  
  gopd@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz"
    integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
    dependencies:
      get-intrinsic "^1.1.3"
  
  got@^11.7.0:
    version "11.8.6"
    resolved "https://registry.npmmirror.com/got/-/got-11.8.6.tgz"
    integrity sha512-6tfZ91bOr7bOXnK7PRDCGBLa1H4U080YHNaAQ2KsMGlLEzRbk44nsZF2E1IeRc3vtJHPVbKCYgdFbaGO2ljd8g==
    dependencies:
      "@sindresorhus/is" "^4.0.0"
      "@szmarczak/http-timer" "^4.0.5"
      "@types/cacheable-request" "^6.0.1"
      "@types/responselike" "^1.0.0"
      cacheable-lookup "^5.0.3"
      cacheable-request "^7.0.2"
      decompress-response "^6.0.0"
      http2-wrapper "^1.0.0-beta.5.2"
      lowercase-keys "^2.0.0"
      p-cancelable "^2.0.0"
      responselike "^2.0.0"
  
  got@^9.6.0:
    version "9.6.0"
    resolved "https://registry.npmmirror.com/got/-/got-9.6.0.tgz"
    integrity sha512-R7eWptXuGYxwijs0eV+v3o6+XH1IqVK8dJOEecQfTmkncw9AV4dcw/Dhxi8MdlqPthxxpZyizMzyg8RTmEsG+Q==
    dependencies:
      "@sindresorhus/is" "^0.14.0"
      "@szmarczak/http-timer" "^1.1.2"
      cacheable-request "^6.0.0"
      decompress-response "^3.3.0"
      duplexer3 "^0.1.4"
      get-stream "^4.1.0"
      lowercase-keys "^1.0.1"
      mimic-response "^1.0.1"
      p-cancelable "^1.0.0"
      to-readable-stream "^1.0.0"
      url-parse-lax "^3.0.0"
  
  graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.6:
    version "4.2.11"
    resolved "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
    integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==
  
  "graceful-readlink@>= 1.0.0":
    version "1.0.1"
    resolved "https://registry.npmmirror.com/graceful-readlink/-/graceful-readlink-1.0.1.tgz"
    integrity sha512-8tLu60LgxF6XpdbK8OW3FA+IfTNBn1ZHGHKF4KQbEeSkajYw5PlYJcKluntgegDPTg8UkHjpet1T82vk6TQ68w==
  
  har-schema@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/har-schema/-/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
    integrity sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==
  
  har-validator@~5.1.3:
    version "5.1.5"
    resolved "https://registry.npmmirror.com/har-validator/-/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
    integrity sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==
    dependencies:
      ajv "^6.12.3"
      har-schema "^2.0.0"
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
    integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==
  
  has-flag@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
    integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==
  
  has-property-descriptors@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
    integrity sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==
    dependencies:
      get-intrinsic "^1.1.1"
  
  has-symbols@^1.0.2, has-symbols@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
    integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==
  
  has-tostringtag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
    integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
    dependencies:
      has-symbols "^1.0.2"
  
  has-unicode@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/has-unicode/-/has-unicode-2.0.1.tgz"
    integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==
  
  has@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/has/-/has-1.0.3.tgz"
    integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
    dependencies:
      function-bind "^1.1.1"
  
  hexoid@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/hexoid/-/hexoid-1.0.0.tgz"
    integrity sha512-QFLV0taWQOZtvIRIAdBChesmogZrtuXvVWsFHZTk2SU+anspqZ2vMnoLg7IE1+Uk16N19APic1BuF8bC8c2m5g==
  
  hosted-git-info@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-4.1.0.tgz"
    integrity sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==
    dependencies:
      lru-cache "^6.0.0"
  
  http-assert@^1.3.0:
    version "1.5.0"
    resolved "https://registry.npmmirror.com/http-assert/-/http-assert-1.5.0.tgz"
    integrity sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==
    dependencies:
      deep-equal "~1.0.1"
      http-errors "~1.8.0"
  
  http-cache-semantics@^4.0.0, http-cache-semantics@^4.1.0:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz"
    integrity sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==
  
  http-errors@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz"
    integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
    dependencies:
      depd "2.0.0"
      inherits "2.0.4"
      setprototypeof "1.2.0"
      statuses "2.0.1"
      toidentifier "1.0.1"
  
  http-errors@^1.6.3, http-errors@^1.7.3, http-errors@~1.8.0:
    version "1.8.1"
    resolved "https://registry.npmmirror.com/http-errors/-/http-errors-1.8.1.tgz"
    integrity sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==
    dependencies:
      depd "~1.1.2"
      inherits "2.0.4"
      setprototypeof "1.2.0"
      statuses ">= 1.5.0 < 2"
      toidentifier "1.0.1"
  
  http-errors@~1.6.2:
    version "1.6.3"
    resolved "https://registry.npmmirror.com/http-errors/-/http-errors-1.6.3.tgz"
    integrity sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==
    dependencies:
      depd "~1.1.2"
      inherits "2.0.3"
      setprototypeof "1.1.0"
      statuses ">= 1.4.0 < 2"
  
  http-proxy-agent@^4.0.0, http-proxy-agent@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmmirror.com/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz"
    integrity sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==
    dependencies:
      "@tootallnate/once" "1"
      agent-base "6"
      debug "4"
  
  http-proxy-agent@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
    integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
    dependencies:
      "@tootallnate/once" "2"
      agent-base "6"
      debug "4"
  
  http-signature@~1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
    integrity sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==
    dependencies:
      assert-plus "^1.0.0"
      jsprim "^1.2.2"
      sshpk "^1.7.0"
  
  http2-wrapper@^1.0.0-beta.5.2:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/http2-wrapper/-/http2-wrapper-1.0.3.tgz"
    integrity sha512-V+23sDMr12Wnz7iTcDeJr3O6AIxlnvT/bmaAAAP/Xda35C90p9599p0F1eHR/N1KILWSoWVAiOMFjBBXaXSMxg==
    dependencies:
      quick-lru "^5.1.1"
      resolve-alpn "^1.0.0"
  
  https-proxy-agent@5, https-proxy-agent@^5.0.0:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
    integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
    dependencies:
      agent-base "6"
      debug "4"
  
  humanize-ms@^1.2.0, humanize-ms@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/humanize-ms/-/humanize-ms-1.2.1.tgz"
    integrity sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==
    dependencies:
      ms "^2.0.0"
  
  iconv-corefoundation@^1.1.7:
    version "1.1.7"
    resolved "https://registry.npmmirror.com/iconv-corefoundation/-/iconv-corefoundation-1.1.7.tgz#31065e6ab2c9272154c8b0821151e2c88f1b002a"
    integrity sha512-T10qvkw0zz4wnm560lOEg0PovVqUXuOFhhHAkixw8/sycy7TJt7v/RrkEKEQnAw2viPSJu6iAkErxnzR0g8PpQ==
    dependencies:
      cli-truncate "^2.1.0"
      node-addon-api "^1.6.3"
  
  iconv-lite@0.4.24, iconv-lite@^0.4.15, iconv-lite@^0.4.17, iconv-lite@^0.4.24, iconv-lite@^0.4.6:
    version "0.4.24"
    resolved "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz"
    integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  iconv-lite@^0.6.2, iconv-lite@^0.6.3:
    version "0.6.3"
    resolved "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz"
    integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
    dependencies:
      safer-buffer ">= 2.1.2 < 3.0.0"
  
  ieee754@^1.1.13, ieee754@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz"
    integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==
  
  ignore-by-default@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/ignore-by-default/-/ignore-by-default-1.0.1.tgz"
    integrity sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==
  
  ignore@^4.0.6:
    version "4.0.6"
    resolved "https://registry.npmmirror.com/ignore/-/ignore-4.0.6.tgz"
    integrity sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==
  
  ignore@^5.1.1:
    version "5.2.4"
    resolved "https://registry.npmmirror.com/ignore/-/ignore-5.2.4.tgz"
    integrity sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==
  
  image-q@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/image-q/-/image-q-4.0.0.tgz#31e075be7bae3c1f42a85c469b4732c358981776"
    integrity sha512-PfJGVgIfKQJuq3s0tTDOKtztksibuUEbJQIYT3by6wctQo+Rdlh7ef4evJ5NCdxY4CfMbvFkocEwbl4BF8RlJw==
    dependencies:
      "@types/node" "16.9.1"
  
  immediate@~3.0.5:
    version "3.0.6"
    resolved "https://registry.npmmirror.com/immediate/-/immediate-3.0.6.tgz#9db1dbd0faf8de6fbe0f5dd5e56bb606280de69b"
    integrity sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==
  
  import-fresh@^3.0.0:
    version "3.3.0"
    resolved "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
    integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
    dependencies:
      parent-module "^1.0.0"
      resolve-from "^4.0.0"
  
  imurmurhash@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz"
    integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==
  
  indent-string@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz"
    integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==
  
  infer-owner@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/infer-owner/-/infer-owner-1.0.4.tgz"
    integrity sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==
  
  inflation@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/inflation/-/inflation-2.0.0.tgz"
    integrity sha512-m3xv4hJYR2oXw4o4Y5l6P5P16WYmazYof+el6Al3f+YlggGj6qT9kImBAnzDelRALnP5d3h4jGBPKzYCizjZZw==
  
  inflight@^1.0.4:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz"
    integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
    dependencies:
      once "^1.3.0"
      wrappy "1"
  
  inherits@2, inherits@2.0.4, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3:
    version "2.0.4"
    resolved "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  inherits@2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/inherits/-/inherits-2.0.3.tgz"
    integrity sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==
  
  ini@^1.3.4, ini@~1.3.0:
    version "1.3.8"
    resolved "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz"
    integrity sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==
  
  inquirer@^5.2.0:
    version "5.2.0"
    resolved "https://registry.npmmirror.com/inquirer/-/inquirer-5.2.0.tgz#db350c2b73daca77ff1243962e9f22f099685726"
    integrity sha512-E9BmnJbAKLPGonz0HeWHtbKf+EeSP93paWO3ZYoUpq/aowXvYGjjCSuashhXPpzbArIjBbji39THkxTz9ZeEUQ==
    dependencies:
      ansi-escapes "^3.0.0"
      chalk "^2.0.0"
      cli-cursor "^2.1.0"
      cli-width "^2.0.0"
      external-editor "^2.1.0"
      figures "^2.0.0"
      lodash "^4.3.0"
      mute-stream "0.0.7"
      run-async "^2.2.0"
      rxjs "^5.5.2"
      string-width "^2.1.0"
      strip-ansi "^4.0.0"
      through "^2.3.6"
  
  inquirer@^6.2.2:
    version "6.5.2"
    resolved "https://registry.npmmirror.com/inquirer/-/inquirer-6.5.2.tgz"
    integrity sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ==
    dependencies:
      ansi-escapes "^3.2.0"
      chalk "^2.4.2"
      cli-cursor "^2.1.0"
      cli-width "^2.0.0"
      external-editor "^3.0.3"
      figures "^2.0.0"
      lodash "^4.17.12"
      mute-stream "0.0.7"
      run-async "^2.2.0"
      rxjs "^6.4.0"
      string-width "^2.1.0"
      strip-ansi "^5.1.0"
      through "^2.3.6"
  
  inversify@6.0.1:
    version "6.0.1"
    resolved "https://registry.npmmirror.com/inversify/-/inversify-6.0.1.tgz"
    integrity sha512-B3ex30927698TJENHR++8FfEaJGqoWOgI6ZY5Ht/nLUsFCwHn6akbwtnUAPCgUepAnTpe2qHxhDNjoKLyz6rgQ==
  
  ip@^0.3.2:
    version "0.3.3"
    resolved "https://registry.npmmirror.com/ip/-/ip-0.3.3.tgz#8ee8309e92f0b040d287f72efaca1a21702d3fb4"
    integrity sha512-VXpBTSFo8wNvJVwCxlncVwd2hYbzX8egxidocX2oKt6H5tJzLjrzG6gTNoHSNsKtIyelb528n/7sa86kqlnNiA==
  
  ip@^1.1.5:
    version "1.1.8"
    resolved "https://registry.npmmirror.com/ip/-/ip-1.1.8.tgz"
    integrity sha512-PuExPYUiu6qMBQb4l06ecm6T6ujzhmh+MeJcW9wa89PoAz5pvd4zPgN5WJV104mb6S2T1AwNIAaB70JNrLQWhg==
  
  ip@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/ip/-/ip-2.0.0.tgz"
    integrity sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==
  
  ipaddr.js@1.9.1:
    version "1.9.1"
    resolved "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
    integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==
  
  is-arguments@^1.0.4:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz"
    integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-arrayish@^0.3.1:
    version "0.3.2"
    resolved "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
    integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==
  
  is-binary-path@~2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
    integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
    dependencies:
      binary-extensions "^2.0.0"
  
  is-buffer@^1.1.5, is-buffer@~1.1.6:
    version "1.1.6"
    resolved "https://registry.npmmirror.com/is-buffer/-/is-buffer-1.1.6.tgz"
    integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==
  
  is-callable@^1.1.3:
    version "1.2.7"
    resolved "https://registry.npmmirror.com/is-callable/-/is-callable-1.2.7.tgz"
    integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
  
  is-ci@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/is-ci/-/is-ci-3.0.1.tgz"
    integrity sha512-ZYvCgrefwqoQ6yTyYUbQu64HsITZ3NfKX1lzaEYdkTDcfKzzCI/wthRRYKkdjHKFVgNiXKAKm65Zo1pk2as/QQ==
    dependencies:
      ci-info "^3.2.0"
  
  is-class-hotfix@~0.0.6:
    version "0.0.6"
    resolved "https://registry.npmmirror.com/is-class-hotfix/-/is-class-hotfix-0.0.6.tgz"
    integrity sha512-0n+pzCC6ICtVr/WXnN2f03TK/3BfXY7me4cjCAqT8TYXEl0+JBRoqBo94JJHXcyDSLUeWbNX8Fvy5g5RJdAstQ==
  
  is-core-module@^2.11.0:
    version "2.12.1"
    resolved "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.12.1.tgz#0c0b6885b6f80011c71541ce15c8d66cf5a4f9fd"
    integrity sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==
    dependencies:
      has "^1.0.3"
  
  is-expression@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/is-expression/-/is-expression-3.0.0.tgz#39acaa6be7fd1f3471dc42c7416e61c24317ac9f"
    integrity sha512-vyMeQMq+AiH5uUnoBfMTwf18tO3bM6k1QXBE9D6ueAAquEfCZe3AJPtud9g6qS0+4X8xA7ndpZiDyeb2l2qOBw==
    dependencies:
      acorn "~4.0.2"
      object-assign "^4.0.1"
  
  is-extendable@^0.1.0:
    version "0.1.1"
    resolved "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz"
    integrity sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==
  
  is-extglob@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz"
    integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==
  
  is-fullwidth-code-point@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
    integrity sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==
  
  is-fullwidth-code-point@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
    integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==
  
  is-function@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/is-function/-/is-function-1.0.2.tgz#4f097f30abf6efadac9833b17ca5dc03f8144e08"
    integrity sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==
  
  is-generator-function@^1.0.7:
    version "1.0.10"
    resolved "https://registry.npmmirror.com/is-generator-function/-/is-generator-function-1.0.10.tgz"
    integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-glob@^4.0.1, is-glob@~4.0.1:
    version "4.0.3"
    resolved "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz"
    integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
    dependencies:
      is-extglob "^2.1.1"
  
  is-interactive@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz"
    integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==
  
  is-lambda@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/is-lambda/-/is-lambda-1.0.1.tgz"
    integrity sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==
  
  is-nan@^1.2.1:
    version "1.3.2"
    resolved "https://registry.npmmirror.com/is-nan/-/is-nan-1.3.2.tgz"
    integrity sha512-E+zBKpQ2t6MEo1VsonYmluk9NxGrbzpeeLC2xIViuO2EjU2xsXsBPwTr3Ykv9l08UYEVEdWeRZNouaZqF6RN0w==
    dependencies:
      call-bind "^1.0.0"
      define-properties "^1.1.3"
  
  is-number@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
    integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==
  
  is-promise@^2.0.0, is-promise@^2.1.0:
    version "2.2.2"
    resolved "https://registry.npmmirror.com/is-promise/-/is-promise-2.2.2.tgz"
    integrity sha512-+lP4/6lKUBfQjZ2pdxThZvLUAafmZb8OAxFb8XXtiQmS35INgr85hdOGoEs124ez1FCnZJt6jau/T+alh58QFQ==
  
  is-regex@^1.0.3:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
    integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-type-of@^1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/is-type-of/-/is-type-of-1.2.1.tgz"
    integrity sha512-uK0kyX9LZYhSDS7H2sVJQJop1UnWPWmo5RvR3q2kFH6AUHYs7sOrVg0b4nyBHw29kRRNFofYN/JbHZDlHiItTA==
    dependencies:
      core-util-is "^1.0.2"
      is-class-hotfix "~0.0.6"
      isstream "~0.1.2"
  
  is-typed-array@^1.1.10, is-typed-array@^1.1.3:
    version "1.1.10"
    resolved "https://registry.npmmirror.com/is-typed-array/-/is-typed-array-1.1.10.tgz"
    integrity sha512-PJqgEHiWZvMpaFZ3uTc8kHPM4+4ADTlDniuQL7cU/UDA0Ql7F70yGfHph3cLNe+c9toaigv+DFzTJKhc2CtO6A==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
  
  is-typedarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
    integrity sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==
  
  is-unicode-supported@^0.1.0:
    version "0.1.0"
    resolved "https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
    integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==
  
  isarray@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmmirror.com/isarray/-/isarray-0.0.1.tgz"
    integrity sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==
  
  isarray@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz"
    integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==
  
  isbinaryfile@^3.0.2:
    version "3.0.3"
    resolved "https://registry.npmmirror.com/isbinaryfile/-/isbinaryfile-3.0.3.tgz"
    integrity sha512-8cJBL5tTd2OS0dM4jz07wQd5g0dCCqIhUxPIGtZfa5L6hWlvV5MHTITy/DBAsF+Oe2LS1X3krBUhNwaGUWpWxw==
    dependencies:
      buffer-alloc "^1.2.0"
  
  isbinaryfile@^4.0.10:
    version "4.0.10"
    resolved "https://registry.npmmirror.com/isbinaryfile/-/isbinaryfile-4.0.10.tgz"
    integrity sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==
  
  isexe@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
    integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==
  
  isomorphic-fetch@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/isomorphic-fetch/-/isomorphic-fetch-3.0.0.tgz#0267b005049046d2421207215d45d6a262b8b8b4"
    integrity sha512-qvUtwJ3j6qwsF3jLxkZ72qCgjMysPzDfeV240JHiGZsANBYd+EEuu35v7dfrJ9Up0Ak07D7GGSkGhCHTqg/5wA==
    dependencies:
      node-fetch "^2.6.1"
      whatwg-fetch "^3.4.1"
  
  isstream@~0.1.2:
    version "0.1.2"
    resolved "https://registry.npmmirror.com/isstream/-/isstream-0.1.2.tgz"
    integrity sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==
  
  jake@^10.8.5:
    version "10.8.5"
    resolved "https://registry.npmmirror.com/jake/-/jake-10.8.5.tgz"
    integrity sha512-sVpxYeuAhWt0OTWITwT98oyV0GsXyMlXCF+3L1SuafBVUIr/uILGRB+NqwkzhgXKvoJpDIpQvqkUALgdmQsQxw==
    dependencies:
      async "^3.2.3"
      chalk "^4.0.2"
      filelist "^1.0.1"
      minimatch "^3.0.4"
  
  javascript-obfuscator@^4.0.0:
    version "4.0.2"
    resolved "https://registry.npmmirror.com/javascript-obfuscator/-/javascript-obfuscator-4.0.2.tgz"
    integrity sha512-MRBQREvjEwqEGdwNGG7yp0te5uZBFzrPZdxEs4rDYkPpHhhOQu+E0IhSXP7bO9dIUWXZU0Kkdohsuws525aVGQ==
    dependencies:
      "@javascript-obfuscator/escodegen" "2.3.0"
      "@javascript-obfuscator/estraverse" "5.4.0"
      acorn "8.8.2"
      assert "2.0.0"
      chalk "4.1.2"
      chance "1.1.9"
      class-validator "0.14.0"
      commander "10.0.0"
      eslint-scope "7.1.1"
      eslint-visitor-keys "3.3.0"
      fast-deep-equal "3.1.3"
      inversify "6.0.1"
      js-string-escape "1.0.1"
      md5 "2.3.0"
      mkdirp "2.1.3"
      multimatch "5.0.0"
      opencollective-postinstall "2.0.3"
      process "0.11.10"
      reflect-metadata "0.1.13"
      source-map-support "0.5.21"
      string-template "1.0.0"
      stringz "2.1.0"
      tslib "2.5.0"
  
  jimp@^0.22.8:
    version "0.22.8"
    resolved "https://registry.npmmirror.com/jimp/-/jimp-0.22.8.tgz#85db9a2de69370c36d8b5ae070381af83dbbb40f"
    integrity sha512-pBbrooJMX7795sDcxx1XpwNZC8B/ITyDV+JK2/1qNbQl/1UWqWeh5Dq7qQpMZl5jLdcFDv5IVTM+OhpafSqSFA==
    dependencies:
      "@jimp/custom" "^0.22.8"
      "@jimp/plugins" "^0.22.8"
      "@jimp/types" "^0.22.8"
      regenerator-runtime "^0.13.3"
  
  jpeg-js@^0.4.4:
    version "0.4.4"
    resolved "https://registry.npmmirror.com/jpeg-js/-/jpeg-js-0.4.4.tgz#a9f1c6f1f9f0fa80cdb3484ed9635054d28936aa"
    integrity sha512-WZzeDOEtTOBK4Mdsar0IqEU5sMr3vSV2RqkAIzUEV2BHnUfKGyswWFPFwK5EeDo93K3FohSHbLAjj0s1Wzd+dg==
  
  js-string-escape@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/js-string-escape/-/js-string-escape-1.0.1.tgz"
    integrity sha512-Smw4xcfIQ5LVjAOuJCvN/zIodzA/BBSsluuoSykP+lUvScIi4U6RJLfwHet5cxFnCswUjISV8oAXaqaJDY3chg==
  
  js-stringify@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/js-stringify/-/js-stringify-1.0.2.tgz#1736fddfd9724f28a3682adc6230ae7e4e9679db"
    integrity sha512-rtS5ATOo2Q5k1G+DADISilDA6lv79zIiwFd6CcjuIxGKLFm5C+RLImRscVap9k55i+MOZwgliw+NejvkLuGD5g==
  
  js-tokens@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  js-yaml@^3.13.0:
    version "3.14.1"
    resolved "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz"
    integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
    dependencies:
      argparse "^1.0.7"
      esprima "^4.0.0"
  
  js-yaml@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz"
    integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
    dependencies:
      argparse "^2.0.1"
  
  jsbn@~0.1.0:
    version "0.1.1"
    resolved "https://registry.npmmirror.com/jsbn/-/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
    integrity sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==
  
  jschardet@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/jschardet/-/jschardet-3.0.0.tgz"
    integrity sha512-lJH6tJ77V8Nzd5QWRkFYCLc13a3vADkh3r/Fi8HupZGWk2OVVDfnZP8V/VgQgZ+lzW0kG2UGb5hFgt3V3ndotQ==
  
  json-buffer@3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.0.tgz"
    integrity sha512-CuUqjv0FUZIdXkHPI8MezCnFCdaTAacej1TZYulLoAg1h/PhwkdXFN4V/gzY4g+fMBCOV2xF+rp7t2XD2ns/NQ==
  
  json-buffer@3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz"
    integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==
  
  json-schema-traverse@^0.4.1:
    version "0.4.1"
    resolved "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
    integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==
  
  json-schema@0.4.0:
    version "0.4.0"
    resolved "https://registry.npmmirror.com/json-schema/-/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
    integrity sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==
  
  json-stable-stringify-without-jsonify@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
    integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==
  
  json-stringify-safe@^5.0.1, json-stringify-safe@~5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
    integrity sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==
  
  json5@^2.2.0:
    version "2.2.3"
    resolved "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz"
    integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==
  
  jsonfile@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/jsonfile/-/jsonfile-4.0.0.tgz"
    integrity sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==
    optionalDependencies:
      graceful-fs "^4.1.6"
  
  jsonfile@^6.0.1:
    version "6.1.0"
    resolved "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
    integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
    dependencies:
      universalify "^2.0.0"
    optionalDependencies:
      graceful-fs "^4.1.6"
  
  jsprim@^1.2.2:
    version "1.4.2"
    resolved "https://registry.npmmirror.com/jsprim/-/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
    integrity sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==
    dependencies:
      assert-plus "1.0.0"
      extsprintf "1.3.0"
      json-schema "0.4.0"
      verror "1.10.0"
  
  jstransformer@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/jstransformer/-/jstransformer-1.0.0.tgz#ed8bf0921e2f3f1ed4d5c1a44f68709ed24722c3"
    integrity sha512-C9YK3Rf8q6VAPDCCU9fnqo3mAfOH6vUGnMcP4AQAYIEpWtfGLpwOTmZ+igtdK5y+VvI2n3CyYSzy4Qh34eq24A==
    dependencies:
      is-promise "^2.0.0"
      promise "^7.0.1"
  
  juicer@^0.6.6-stable:
    version "0.6.15"
    resolved "https://registry.npmmirror.com/juicer/-/juicer-0.6.15.tgz#110a9430e865288ea533da7013a47f8f99c5f876"
    integrity sha512-E9lXe+Id0RCQE3U0fR8kj/Ryyv5Ypse17dfk/FYmYzk8/mnpUoD8gO5jNGw5o3QHpKbZaBzzXnExzertIRW1Fw==
    dependencies:
      optimist "~0.3"
      uglify-js "~1.2"
  
  keygrip@~1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/keygrip/-/keygrip-1.1.0.tgz"
    integrity sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==
    dependencies:
      tsscmp "1.0.6"
  
  keyv@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/keyv/-/keyv-3.1.0.tgz"
    integrity sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==
    dependencies:
      json-buffer "3.0.0"
  
  keyv@^4.0.0:
    version "4.5.2"
    resolved "https://registry.npmmirror.com/keyv/-/keyv-4.5.2.tgz"
    integrity sha512-5MHbFaKn8cNSmVW7BYnijeAVlE4cYA/SVkifVgrh7yotnfhKmjuXpDKjrABLnT0SfHWV21P8ow07OGfRrNDg8g==
    dependencies:
      json-buffer "3.0.1"
  
  kind-of@^3.0.2:
    version "3.2.2"
    resolved "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
    integrity sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==
    dependencies:
      is-buffer "^1.1.5"
  
  koa-body@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/koa-body/-/koa-body-5.0.0.tgz"
    integrity sha512-nHwEODrQGiyKBILCWO8QSS40C87cKr2cp3y/Cw8u9Z8w5t0CdSkGm3+y9WK5BIAlPpo9tTw5RtSbxpVyG79vmw==
    dependencies:
      "@types/formidable" "^2.0.4"
      co-body "^5.1.1"
      formidable "^2.0.1"
  
  koa-compose@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/koa-compose/-/koa-compose-4.1.0.tgz"
    integrity sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==
  
  koa-convert@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/koa-convert/-/koa-convert-2.0.0.tgz"
    integrity sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==
    dependencies:
      co "^4.6.0"
      koa-compose "^4.1.0"
  
  koa-send@^5.0.0:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/koa-send/-/koa-send-5.0.1.tgz"
    integrity sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==
    dependencies:
      debug "^4.1.1"
      http-errors "^1.7.3"
      resolve-path "^1.4.0"
  
  koa-static@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/koa-static/-/koa-static-5.0.0.tgz"
    integrity sha512-UqyYyH5YEXaJrf9S8E23GoJFQZXkBVJ9zYYMPGz919MSX1KuvAcycIuS0ci150HCoPf4XQVhQ84Qf8xRPWxFaQ==
    dependencies:
      debug "^3.1.0"
      koa-send "^5.0.0"
  
  koa2-cors@^2.0.6:
    version "2.0.6"
    resolved "https://registry.npmmirror.com/koa2-cors/-/koa2-cors-2.0.6.tgz"
    integrity sha512-JRCcSM4lamM+8kvKGDKlesYk2ASrmSTczDtGUnIadqMgnHU4Ct5Gw7Bxt3w3m6d6dy3WN0PU4oMP43HbddDEWg==
  
  koa@^2.13.4:
    version "2.14.1"
    resolved "https://registry.npmmirror.com/koa/-/koa-2.14.1.tgz"
    integrity sha512-USJFyZgi2l0wDgqkfD27gL4YGno7TfUkcmOe6UOLFOVuN+J7FwnNu4Dydl4CUQzraM1lBAiGed0M9OVJoT0Kqw==
    dependencies:
      accepts "^1.3.5"
      cache-content-type "^1.0.0"
      content-disposition "~0.5.2"
      content-type "^1.0.4"
      cookies "~0.8.0"
      debug "^4.3.2"
      delegates "^1.0.0"
      depd "^2.0.0"
      destroy "^1.0.4"
      encodeurl "^1.0.2"
      escape-html "^1.0.3"
      fresh "~0.5.2"
      http-assert "^1.3.0"
      http-errors "^1.6.3"
      is-generator-function "^1.0.7"
      koa-compose "^4.1.0"
      koa-convert "^2.0.0"
      on-finished "^2.3.0"
      only "~0.0.2"
      parseurl "^1.3.2"
      statuses "^1.5.0"
      type-is "^1.6.16"
      vary "^1.1.2"
  
  lazy-cache@^1.0.3:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/lazy-cache/-/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
    integrity sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ==
  
  lazy-val@^1.0.4, lazy-val@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/lazy-val/-/lazy-val-1.0.5.tgz"
    integrity sha512-0/BnGCCfyUMkBpeDgWihanIAF9JmZhHBgUhEqzvf+adhNGLoP6TaiI5oF8oyb3I45P+PcnrqihSf01M0l0G5+Q==
  
  levn@^0.3.0, levn@~0.3.0:
    version "0.3.0"
    resolved "https://registry.npmmirror.com/levn/-/levn-0.3.0.tgz"
    integrity sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==
    dependencies:
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
  
  libphonenumber-js@^1.10.14:
    version "1.10.26"
    resolved "https://registry.npmmirror.com/libphonenumber-js/-/libphonenumber-js-1.10.26.tgz"
    integrity sha512-oB3l4J5gEhMV+ymmlIjWedsbCpsNRqbEZ/E/MpN2QVyinKNra6DcuXywxSk/72M3DZDoH/6kzurOq1erznBMwQ==
  
  lie@3.1.1:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/lie/-/lie-3.1.1.tgz#9a436b2cc7746ca59de7a41fa469b3efb76bd87e"
    integrity sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==
    dependencies:
      immediate "~3.0.5"
  
  limiter@^1.0.5:
    version "1.1.5"
    resolved "https://registry.npmmirror.com/limiter/-/limiter-1.1.5.tgz#8f92a25b3b16c6131293a0cc834b4a838a2aa7c2"
    integrity sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA==
  
  load-bmfont@^1.4.1:
    version "1.4.1"
    resolved "https://registry.npmmirror.com/load-bmfont/-/load-bmfont-1.4.1.tgz#c0f5f4711a1e2ccff725a7b6078087ccfcddd3e9"
    integrity sha512-8UyQoYmdRDy81Brz6aLAUhfZLwr5zV0L3taTQ4hju7m6biuwiWiJXjPhBJxbUQJA8PrkvJ/7Enqmwk2sM14soA==
    dependencies:
      buffer-equal "0.0.1"
      mime "^1.3.4"
      parse-bmfont-ascii "^1.0.3"
      parse-bmfont-binary "^1.0.5"
      parse-bmfont-xml "^1.1.4"
      phin "^2.9.1"
      xhr "^2.0.1"
      xtend "^4.0.0"
  
  localforage@^1.3.0:
    version "1.10.0"
    resolved "https://registry.npmmirror.com/localforage/-/localforage-1.10.0.tgz#5c465dc5f62b2807c3a84c0c6a1b1b3212781dd4"
    integrity sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==
    dependencies:
      lie "3.1.1"
  
  lodash.escaperegexp@^4.1.2:
    version "4.1.2"
    resolved "https://registry.npmmirror.com/lodash.escaperegexp/-/lodash.escaperegexp-4.1.2.tgz"
    integrity sha512-TM9YBvyC84ZxE3rgfefxUWiQKLilstD6k7PTGt6wfbtXF8ixIJLOL3VYyV/z+ZiPLsVxAsKAFVwWlWeb2Y8Yyw==
  
  lodash.isequal@^4.5.0:
    version "4.5.0"
    resolved "https://registry.npmmirror.com/lodash.isequal/-/lodash.isequal-4.5.0.tgz"
    integrity sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==
  
  lodash@4, lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.17.4, lodash@^4.3.0:
    version "4.17.21"
    resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
    integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==
  
  log-symbols@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz"
    integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
    dependencies:
      chalk "^4.1.0"
      is-unicode-supported "^0.1.0"
  
  longest@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/longest/-/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
    integrity sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg==
  
  lowdb@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/lowdb/-/lowdb-1.0.0.tgz"
    integrity sha512-2+x8esE/Wb9SQ1F9IHaYWfsC9FIecLOPrK4g17FGEayjUWH172H6nwicRovGvSE2CPZouc2MCIqCI7h9d+GftQ==
    dependencies:
      graceful-fs "^4.1.3"
      is-promise "^2.1.0"
      lodash "4"
      pify "^3.0.0"
      steno "^0.4.1"
  
  lowercase-keys@^1.0.0, lowercase-keys@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/lowercase-keys/-/lowercase-keys-1.0.1.tgz"
    integrity sha512-G2Lj61tXDnVFFOi8VZds+SoQjtQC3dgokKdDG2mTm1tx4m50NUHBOZSBwQQHyy0V12A0JTG4icfZQH+xPyh8VA==
  
  lowercase-keys@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/lowercase-keys/-/lowercase-keys-2.0.0.tgz"
    integrity sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==
  
  lru-cache@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz"
    integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
    dependencies:
      yallist "^3.0.2"
  
  lru-cache@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
    integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
    dependencies:
      yallist "^4.0.0"
  
  lru-cache@^7.7.1:
    version "7.18.3"
    resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-7.18.3.tgz"
    integrity sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==
  
  lzma-native@^8.0.5:
    version "8.0.6"
    resolved "https://registry.npmmirror.com/lzma-native/-/lzma-native-8.0.6.tgz"
    integrity sha512-09xfg67mkL2Lz20PrrDeNYZxzeW7ADtpYFbwSQh9U8+76RIzx5QsJBMy8qikv3hbUPfpy6hqwxt6FcGK81g9AA==
    dependencies:
      node-addon-api "^3.1.0"
      node-gyp-build "^4.2.1"
      readable-stream "^3.6.0"
  
  make-fetch-happen@^10.0.3:
    version "10.2.1"
    resolved "https://registry.npmmirror.com/make-fetch-happen/-/make-fetch-happen-10.2.1.tgz"
    integrity sha512-NgOPbRiaQM10DYXvN3/hhGVI2M5MtITFryzBGxHM5p4wnFxsVCbxkrBrDsk+EZ5OB4jEOT7AjDxtdF+KVEFT7w==
    dependencies:
      agentkeepalive "^4.2.1"
      cacache "^16.1.0"
      http-cache-semantics "^4.1.0"
      http-proxy-agent "^5.0.0"
      https-proxy-agent "^5.0.0"
      is-lambda "^1.0.1"
      lru-cache "^7.7.1"
      minipass "^3.1.6"
      minipass-collect "^1.0.2"
      minipass-fetch "^2.0.3"
      minipass-flush "^1.0.5"
      minipass-pipeline "^1.2.4"
      negotiator "^0.6.3"
      promise-retry "^2.0.1"
      socks-proxy-agent "^7.0.0"
      ssri "^9.0.0"
  
  matcher@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/matcher/-/matcher-3.0.0.tgz"
    integrity sha512-OkeDaAZ/bQCxeFAozM55PKcKU0yJMPGifLwV4Qgjitu+5MoAfSQN4lsLJeXZ1b8w0x+/Emda6MZgXS1jvsapng==
    dependencies:
      escape-string-regexp "^4.0.0"
  
  md5@2.3.0:
    version "2.3.0"
    resolved "https://registry.npmmirror.com/md5/-/md5-2.3.0.tgz"
    integrity sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
    dependencies:
      charenc "0.0.2"
      crypt "0.0.2"
      is-buffer "~1.1.6"
  
  media-typer@0.3.0:
    version "0.3.0"
    resolved "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz"
    integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==
  
  merge-descriptors@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
    integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==
  
  merge2@^1.2.3, merge2@^1.3.0:
    version "1.4.1"
    resolved "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz"
    integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==
  
  methods@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
    integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==
  
  micromatch@^4.0.4:
    version "4.0.5"
    resolved "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz"
    integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
    dependencies:
      braces "^3.0.2"
      picomatch "^2.3.1"
  
  mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
    version "1.52.0"
    resolved "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz"
    integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==
  
  mime-db@~1.23.0:
    version "1.23.0"
    resolved "https://registry.npmmirror.com/mime-db/-/mime-db-1.23.0.tgz#a31b4070adaea27d732ea333740a64d0ec9a6659"
    integrity sha512-lsX3UhcJITPHDXGOXSglBSPoI2UbcsWMmgX1VTaeXJ11TjjxOSE/DHrCl23zJk75odJc8MVpdZzWxdWt1Csx5Q==
  
  mime-types@2.1.11:
    version "2.1.11"
    resolved "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.11.tgz#c259c471bda808a85d6cd193b430a5fae4473b3c"
    integrity sha512-14dD2ItPaGFLVyhddUE/Rrtg+g7v8RmBLjN5Xsb3fJJLKunoZOw3I3bK6csjoJKjaNjcXo8xob9kHDyOpJfgpg==
    dependencies:
      mime-db "~1.23.0"
  
  mime-types@^2.1.12, mime-types@^2.1.18, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
    version "2.1.35"
    resolved "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz"
    integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
    dependencies:
      mime-db "1.52.0"
  
  mime@1.6.0, mime@^1.3.4:
    version "1.6.0"
    resolved "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
    integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==
  
  mime@^2.5.2:
    version "2.6.0"
    resolved "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz"
    integrity sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==
  
  mimic-fn@^1.0.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-1.2.0.tgz"
    integrity sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==
  
  mimic-fn@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz"
    integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==
  
  mimic-response@^1.0.0, mimic-response@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/mimic-response/-/mimic-response-1.0.1.tgz"
    integrity sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==
  
  mimic-response@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/mimic-response/-/mimic-response-3.1.0.tgz"
    integrity sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==
  
  min-document@^2.19.0:
    version "2.19.0"
    resolved "https://registry.npmmirror.com/min-document/-/min-document-2.19.0.tgz#7bd282e3f5842ed295bb748cdd9f1ffa2c824685"
    integrity sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==
    dependencies:
      dom-walk "^0.1.0"
  
  minimatch@3.0.4:
    version "3.0.4"
    resolved "https://registry.npmmirror.com/minimatch/-/minimatch-3.0.4.tgz"
    integrity sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
    version "3.1.2"
    resolved "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz"
    integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
    dependencies:
      brace-expansion "^1.1.7"
  
  minimatch@^5.0.1:
    version "5.1.6"
    resolved "https://registry.npmmirror.com/minimatch/-/minimatch-5.1.6.tgz"
    integrity sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==
    dependencies:
      brace-expansion "^2.0.1"
  
  minimist@^1.1.0, minimist@^1.2.0, minimist@^1.2.3, minimist@^1.2.6:
    version "1.2.8"
    resolved "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
    integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
  
  minipass-collect@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/minipass-collect/-/minipass-collect-1.0.2.tgz"
    integrity sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==
    dependencies:
      minipass "^3.0.0"
  
  minipass-fetch@^2.0.3:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/minipass-fetch/-/minipass-fetch-2.1.2.tgz"
    integrity sha512-LT49Zi2/WMROHYoqGgdlQIZh8mLPZmOrN2NdJjMXxYe4nkN6FUyuPuOAOedNJDrx0IRGg9+4guZewtp8hE6TxA==
    dependencies:
      minipass "^3.1.6"
      minipass-sized "^1.0.3"
      minizlib "^2.1.2"
    optionalDependencies:
      encoding "^0.1.13"
  
  minipass-flush@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/minipass-flush/-/minipass-flush-1.0.5.tgz"
    integrity sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==
    dependencies:
      minipass "^3.0.0"
  
  minipass-pipeline@^1.2.4:
    version "1.2.4"
    resolved "https://registry.npmmirror.com/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz"
    integrity sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==
    dependencies:
      minipass "^3.0.0"
  
  minipass-sized@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/minipass-sized/-/minipass-sized-1.0.3.tgz"
    integrity sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==
    dependencies:
      minipass "^3.0.0"
  
  minipass@^3.0.0, minipass@^3.1.1, minipass@^3.1.6:
    version "3.3.6"
    resolved "https://registry.npmmirror.com/minipass/-/minipass-3.3.6.tgz"
    integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
    dependencies:
      yallist "^4.0.0"
  
  minipass@^4.0.0:
    version "4.2.8"
    resolved "https://registry.npmmirror.com/minipass/-/minipass-4.2.8.tgz"
    integrity sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==
  
  minizlib@^2.1.1, minizlib@^2.1.2:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.2.tgz"
    integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
    dependencies:
      minipass "^3.0.0"
      yallist "^4.0.0"
  
  mkdirp-classic@^0.5.2, mkdirp-classic@^0.5.3:
    version "0.5.3"
    resolved "https://registry.npmmirror.com/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz"
    integrity sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==
  
  mkdirp@2.1.3:
    version "2.1.3"
    resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.3.tgz"
    integrity sha512-sjAkg21peAG9HS+Dkx7hlG9Ztx7HLeKnvB3NQRcu/mltCVmvkF0pisbiTSfDVYTT86XEfZrTUosLdZLStquZUw==
  
  mkdirp@^0.5.1, mkdirp@^0.5.4, mkdirp@~0.5.1:
    version "0.5.6"
    resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz"
    integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
    dependencies:
      minimist "^1.2.6"
  
  mkdirp@^1.0.3, mkdirp@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.4.tgz"
    integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==
  
  mkdirp@^2.1.3:
    version "2.1.6"
    resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-2.1.6.tgz#964fbcb12b2d8c5d6fbc62a963ac95a273e2cc19"
    integrity sha512-+hEnITedc8LAtIP9u3HJDFIdcLV2vXP33sqLLIzkv1Db1zO/1OxbvYf0Y1OC/S/Qo5dxHXepofhmxL02PsKe+A==
  
  moment@^2.15.1:
    version "2.29.4"
    resolved "https://registry.npmmirror.com/moment/-/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
    integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
    integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==
  
  ms@2.1.2:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz"
    integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==
  
  ms@2.1.3, ms@^2.0.0, ms@^2.1.1:
    version "2.1.3"
    resolved "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  multimatch@5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/multimatch/-/multimatch-5.0.0.tgz"
    integrity sha512-ypMKuglUrZUD99Tk2bUQ+xNQj43lPEfAeX2o9cTteAmShXy2VHDJpuwu1o0xqoKCt9jLVAvwyFKdLTPXKAfJyA==
    dependencies:
      "@types/minimatch" "^3.0.3"
      array-differ "^3.0.0"
      array-union "^2.1.0"
      arrify "^2.0.1"
      minimatch "^3.0.4"
  
  mute-stream@0.0.7:
    version "0.0.7"
    resolved "https://registry.npmmirror.com/mute-stream/-/mute-stream-0.0.7.tgz"
    integrity sha512-r65nCZhrbXXb6dXOACihYApHw2Q6pV0M3V0PSxd74N0+D8nzAdEAITq2oAjA1jVnKI+tGvEBUpqiMh0+rW6zDQ==
  
  mz@^2.7.0:
    version "2.7.0"
    resolved "https://registry.npmmirror.com/mz/-/mz-2.7.0.tgz"
    integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
    dependencies:
      any-promise "^1.0.0"
      object-assign "^4.0.1"
      thenify-all "^1.0.0"
  
  napi-build-utils@^1.0.1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/napi-build-utils/-/napi-build-utils-1.0.2.tgz"
    integrity sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==
  
  natural-compare@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz"
    integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==
  
  nedb@^1.8.0:
    version "1.8.0"
    resolved "https://registry.npmmirror.com/nedb/-/nedb-1.8.0.tgz#0e3502cd82c004d5355a43c9e55577bd7bd91d88"
    integrity sha512-ip7BJdyb5m+86ZbSb4y10FCCW9g35+U8bDRrZlAfCI6m4dKwEsQ5M52grcDcVK4Vm/vnPlDLywkyo3GliEkb5A==
    dependencies:
      async "0.2.10"
      binary-search-tree "0.2.5"
      localforage "^1.3.0"
      mkdirp "~0.5.1"
      underscore "~1.4.4"
  
  negotiator@0.6.3, negotiator@^0.6.3:
    version "0.6.3"
    resolved "https://registry.npmmirror.com/negotiator/-/negotiator-0.6.3.tgz"
    integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==
  
  netmask@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/netmask/-/netmask-2.0.2.tgz"
    integrity sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg==
  
  nice-try@^1.0.4:
    version "1.0.5"
    resolved "https://registry.npmmirror.com/nice-try/-/nice-try-1.0.5.tgz"
    integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==
  
  node-abi@^3.0.0, node-abi@^3.3.0:
    version "3.35.0"
    resolved "https://registry.npmmirror.com/node-abi/-/node-abi-3.35.0.tgz"
    integrity sha512-jAlSOFR1Bls963NmFwxeQkNTzqjUF0NThm8Le7eRIRGzFUVJuMOFZDLv5Y30W/Oaw+KEebEJLAigwO9gQHoEmw==
    dependencies:
      semver "^7.3.5"
  
  node-addon-api@^1.6.3:
    version "1.7.2"
    resolved "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-1.7.2.tgz#3df30b95720b53c24e59948b49532b662444f54d"
    integrity sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg==
  
  node-addon-api@^3.1.0:
    version "3.2.1"
    resolved "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-3.2.1.tgz"
    integrity sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A==
  
  node-addon-api@^6.1.0:
    version "6.1.0"
    resolved "https://registry.npmmirror.com/node-addon-api/-/node-addon-api-6.1.0.tgz#ac8470034e58e67d0c6f1204a18ae6995d9c0d76"
    integrity sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==
  
  node-api-version@^0.1.4:
    version "0.1.4"
    resolved "https://registry.npmmirror.com/node-api-version/-/node-api-version-0.1.4.tgz"
    integrity sha512-KGXihXdUChwJAOHO53bv9/vXcLmdUsZ6jIptbvYvkpKfth+r7jw44JkVxQFA3kX5nQjzjmGu1uAu/xNNLNlI5g==
    dependencies:
      semver "^7.3.5"
  
  node-easy-cert@^1.0.0:
    version "1.3.3"
    resolved "https://registry.npmmirror.com/node-easy-cert/-/node-easy-cert-1.3.3.tgz#2f0279b5d88fd104303456c8667e3d284f17f3ca"
    integrity sha512-deh43tQa2Ae0bLABFyDn6ISOgm+dEo1NM/FS3K1QHfNJjITnqetEJgO/hlaCxFaB9RRpKR6FmtTO7AqjxwX14g==
    dependencies:
      async-task-mgr "^1.1.0"
      colorful "^2.1.0"
      commander "^2.9.0"
      node-forge "^0.6.42"
      node-powershell "^3.3.1"
  
  node-fetch@^2.6.1:
    version "2.6.11"
    resolved "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.6.11.tgz#cde7fc71deef3131ef80a738919f999e6edfff25"
    integrity sha512-4I6pdBY1EthSqDmJkiNk3JIT8cswwR9nfeW/cPdUagJYEQG7R95WRH74wpz7ma8Gh/9dI9FP+OU+0E4FvtA55w==
    dependencies:
      whatwg-url "^5.0.0"
  
  node-forge@^0.6.42:
    version "0.6.49"
    resolved "https://registry.npmmirror.com/node-forge/-/node-forge-0.6.49.tgz#f1ee95d5d74623938fe19d698aa5a26d54d2f60f"
    integrity sha512-8632osJqcHqlbmdWOvngqc0T0YV+MLSPsHFpGiQGXPe1wjhcVY3srKQOLkpypyl8w0Q5rbqzTy9Wc0hs3NSWyg==
  
  node-gyp-build@^4.2.1:
    version "4.6.0"
    resolved "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.6.0.tgz"
    integrity sha512-NTZVKn9IylLwUzaKjkas1e4u2DLNcV4rdYagA4PWdPwW87Bi7z+BznyKSRwS/761tV/lzCGXplWsiaMjLqP2zQ==
  
  node-gyp@^9.0.0:
    version "9.3.1"
    resolved "https://registry.npmmirror.com/node-gyp/-/node-gyp-9.3.1.tgz"
    integrity sha512-4Q16ZCqq3g8awk6UplT7AuxQ35XN4R/yf/+wSAwcBUAjg7l58RTactWaP8fIDTi0FzI7YcVLujwExakZlfWkXg==
    dependencies:
      env-paths "^2.2.0"
      glob "^7.1.4"
      graceful-fs "^4.2.6"
      make-fetch-happen "^10.0.3"
      nopt "^6.0.0"
      npmlog "^6.0.0"
      rimraf "^3.0.2"
      semver "^7.3.5"
      tar "^6.1.2"
      which "^2.0.2"
  
  node-powershell@^3.3.1:
    version "3.3.1"
    resolved "https://registry.npmmirror.com/node-powershell/-/node-powershell-3.3.1.tgz#bbf76b29f091ed83eae16ad9e7a180a13f36d113"
    integrity sha512-HM9MYsI6hkDUXsx+21p52CMzhViiOxe7D9rzPigNxKFGFvyFVrkbQCPKKQwhB/uTX4IvEHMoAdMExp/aYOAOow==
    dependencies:
      bluebird "^3.5.1"
      chalk "^2.1.0"
  
  node-xlsx@^0.21.2:
    version "0.21.2"
    resolved "https://registry.npmmirror.com/node-xlsx/-/node-xlsx-0.21.2.tgz"
    integrity sha512-Hx/AQ+IEoNgiq8rjEOPtvv3F+KZ5iHZLmEO5Cf7oB6mrpzXsaLNTQvyZvJ3sEOMQ2HfGXwyks18ZVTI6+WmuQA==
    dependencies:
      xlsx "^0.17.4"
  
  nodemon@^2.0.16:
    version "2.0.22"
    resolved "https://registry.npmmirror.com/nodemon/-/nodemon-2.0.22.tgz"
    integrity sha512-B8YqaKMmyuCO7BowF1Z1/mkPqLk6cs/l63Ojtd6otKjMx47Dq1utxfRxcavH1I7VSaL8n5BUaoutadnsX3AAVQ==
    dependencies:
      chokidar "^3.5.2"
      debug "^3.2.7"
      ignore-by-default "^1.0.1"
      minimatch "^3.1.2"
      pstree.remy "^1.1.8"
      semver "^5.7.1"
      simple-update-notifier "^1.0.7"
      supports-color "^5.5.0"
      touch "^3.1.0"
      undefsafe "^2.0.5"
  
  nopt@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmmirror.com/nopt/-/nopt-6.0.0.tgz"
    integrity sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g==
    dependencies:
      abbrev "^1.0.0"
  
  nopt@~1.0.10:
    version "1.0.10"
    resolved "https://registry.npmmirror.com/nopt/-/nopt-1.0.10.tgz"
    integrity sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==
    dependencies:
      abbrev "1"
  
  normalize-path@^3.0.0, normalize-path@~3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz"
    integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==
  
  normalize-url@^4.1.0:
    version "4.5.1"
    resolved "https://registry.npmmirror.com/normalize-url/-/normalize-url-4.5.1.tgz"
    integrity sha512-9UZCFRHQdNrfTpGg8+1INIg93B6zE0aXMVFkw1WFwvO4SlZywU6aLg5Of0Ap/PgcbSw4LNxvMWXMeugwMCX0AA==
  
  normalize-url@^6.0.1:
    version "6.1.0"
    resolved "https://registry.npmmirror.com/normalize-url/-/normalize-url-6.1.0.tgz"
    integrity sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==
  
  npm-conf@^1.1.3:
    version "1.1.3"
    resolved "https://registry.npmmirror.com/npm-conf/-/npm-conf-1.1.3.tgz"
    integrity sha512-Yic4bZHJOt9RCFbRP3GgpqhScOY4HH3V2P8yBj6CeYq118Qr+BLXqT2JvpJ00mryLESpgOxf5XlFv4ZjXxLScw==
    dependencies:
      config-chain "^1.1.11"
      pify "^3.0.0"
  
  npmlog@^6.0.0:
    version "6.0.2"
    resolved "https://registry.npmmirror.com/npmlog/-/npmlog-6.0.2.tgz"
    integrity sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==
    dependencies:
      are-we-there-yet "^3.0.0"
      console-control-strings "^1.1.0"
      gauge "^4.0.3"
      set-blocking "^2.0.0"
  
  oauth-sign@~0.9.0:
    version "0.9.0"
    resolved "https://registry.npmmirror.com/oauth-sign/-/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
    integrity sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==
  
  object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.0:
    version "4.1.1"
    resolved "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz"
    integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==
  
  object-inspect@^1.9.0:
    version "1.12.3"
    resolved "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.3.tgz"
    integrity sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==
  
  object-is@^1.0.1:
    version "1.1.5"
    resolved "https://registry.npmmirror.com/object-is/-/object-is-1.1.5.tgz"
    integrity sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==
    dependencies:
      call-bind "^1.0.2"
      define-properties "^1.1.3"
  
  object-keys@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz"
    integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==
  
  omggif@^1.0.10, omggif@^1.0.9:
    version "1.0.10"
    resolved "https://registry.npmmirror.com/omggif/-/omggif-1.0.10.tgz#ddaaf90d4a42f532e9e7cb3a95ecdd47f17c7b19"
    integrity sha512-LMJTtvgc/nugXj0Vcrrs68Mn2D1r0zf630VNtqtpI1FEO7e+O9FP4gqs9AcnBaSEeoHIPm28u6qgPR0oyEpGSw==
  
  on-finished@2.4.1, on-finished@^2.3.0:
    version "2.4.1"
    resolved "https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz"
    integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
    dependencies:
      ee-first "1.1.1"
  
  on-headers@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/on-headers/-/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
    integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==
  
  once@^1.3.0, once@^1.3.1, once@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/once/-/once-1.4.0.tgz"
    integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
    dependencies:
      wrappy "1"
  
  onetime@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/onetime/-/onetime-2.0.1.tgz"
    integrity sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==
    dependencies:
      mimic-fn "^1.0.0"
  
  onetime@^5.1.0:
    version "5.1.2"
    resolved "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
    integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
    dependencies:
      mimic-fn "^2.1.0"
  
  only@~0.0.2:
    version "0.0.2"
    resolved "https://registry.npmmirror.com/only/-/only-0.0.2.tgz"
    integrity sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==
  
  opencollective-postinstall@2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/opencollective-postinstall/-/opencollective-postinstall-2.0.3.tgz"
    integrity sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==
  
  optimist@~0.3:
    version "0.3.7"
    resolved "https://registry.npmmirror.com/optimist/-/optimist-0.3.7.tgz#c90941ad59e4273328923074d2cf2e7cbc6ec0d9"
    integrity sha512-TCx0dXQzVtSCg2OgY/bO9hjM9cV4XYx09TVK+s3+FhkjT6LovsLe+pPMzpWf+6yXK/hUizs2gUoTw3jHM0VaTQ==
    dependencies:
      wordwrap "~0.0.2"
  
  optionator@^0.8.1, optionator@^0.8.2:
    version "0.8.3"
    resolved "https://registry.npmmirror.com/optionator/-/optionator-0.8.3.tgz"
    integrity sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==
    dependencies:
      deep-is "~0.1.3"
      fast-levenshtein "~2.0.6"
      levn "~0.3.0"
      prelude-ls "~1.1.2"
      type-check "~0.3.2"
      word-wrap "~1.2.3"
  
  ora@^5.1.0:
    version "5.4.1"
    resolved "https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz"
    integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
    dependencies:
      bl "^4.1.0"
      chalk "^4.1.0"
      cli-cursor "^3.1.0"
      cli-spinners "^2.5.0"
      is-interactive "^1.0.0"
      is-unicode-supported "^0.1.0"
      log-symbols "^4.1.0"
      strip-ansi "^6.0.0"
      wcwidth "^1.0.1"
  
  os-name@~1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/os-name/-/os-name-1.0.3.tgz"
    integrity sha512-f5estLO2KN8vgtTRaILIgEGBoBrMnZ3JQ7W9TMZCnOIGwHe8TRGSpcagnWDo+Dfhd/z08k9Xe75hvciJJ8Qaew==
    dependencies:
      osx-release "^1.0.0"
      win-release "^1.0.0"
  
  os-tmpdir@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
    integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==
  
  osx-release@^1.0.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/osx-release/-/osx-release-1.1.0.tgz"
    integrity sha512-ixCMMwnVxyHFQLQnINhmIpWqXIfS2YOXchwQrk+OFzmo6nDjQ0E4KXAyyUh0T0MZgV4bUhkRrAbVqlE4yLVq4A==
    dependencies:
      minimist "^1.1.0"
  
  p-cancelable@^1.0.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/p-cancelable/-/p-cancelable-1.1.0.tgz"
    integrity sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==
  
  p-cancelable@^2.0.0:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/p-cancelable/-/p-cancelable-2.1.1.tgz"
    integrity sha512-BZOr3nRQHOntUjTrH8+Lh54smKHoHyur8We1V8DSMVrl5A2malOOwuJRnKRDjSnkoeBh4at6BwEnb5I7Jl31wg==
  
  p-map@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz"
    integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
    dependencies:
      aggregate-error "^3.0.0"
  
  pac-proxy-agent@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/pac-proxy-agent/-/pac-proxy-agent-5.0.0.tgz"
    integrity sha512-CcFG3ZtnxO8McDigozwE3AqAw15zDvGH+OjXO4kzf7IkEKkQ4gxQ+3sdF50WmhQ4P/bVusXcqNE2S3XrNURwzQ==
    dependencies:
      "@tootallnate/once" "1"
      agent-base "6"
      debug "4"
      get-uri "3"
      http-proxy-agent "^4.0.1"
      https-proxy-agent "5"
      pac-resolver "^5.0.0"
      raw-body "^2.2.0"
      socks-proxy-agent "5"
  
  pac-resolver@^5.0.0:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/pac-resolver/-/pac-resolver-5.0.1.tgz"
    integrity sha512-cy7u00ko2KVgBAjuhevqpPeHIkCIqPe1v24cydhWjmeuzaBfmUWFCZJ1iAh5TuVzVZoUzXIW7K8sMYOZ84uZ9Q==
    dependencies:
      degenerator "^3.0.2"
      ip "^1.1.5"
      netmask "^2.0.2"
  
  pako@^1.0.11:
    version "1.0.11"
    resolved "https://registry.npmmirror.com/pako/-/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
    integrity sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==
  
  parent-module@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
    integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
    dependencies:
      callsites "^3.0.0"
  
  parse-bmfont-ascii@^1.0.3:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/parse-bmfont-ascii/-/parse-bmfont-ascii-1.0.6.tgz#11ac3c3ff58f7c2020ab22769079108d4dfa0285"
    integrity sha512-U4RrVsUFCleIOBsIGYOMKjn9PavsGOXxbvYGtMOEfnId0SVNsgehXh1DxUdVPLoxd5mvcEtvmKs2Mmf0Mpa1ZA==
  
  parse-bmfont-binary@^1.0.5:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/parse-bmfont-binary/-/parse-bmfont-binary-1.0.6.tgz#d038b476d3e9dd9db1e11a0b0e53a22792b69006"
    integrity sha512-GxmsRea0wdGdYthjuUeWTMWPqm2+FAd4GI8vCvhgJsFnoGhTrLhXDDupwTo7rXVAgaLIGoVHDZS9p/5XbSqeWA==
  
  parse-bmfont-xml@^1.1.4:
    version "1.1.4"
    resolved "https://registry.npmmirror.com/parse-bmfont-xml/-/parse-bmfont-xml-1.1.4.tgz#015319797e3e12f9e739c4d513872cd2fa35f389"
    integrity sha512-bjnliEOmGv3y1aMEfREMBJ9tfL3WR0i0CKPj61DnSLaoxWR3nLrsQrEbCId/8rF4NyRF0cCqisSVXyQYWM+mCQ==
    dependencies:
      xml-parse-from-string "^1.0.0"
      xml2js "^0.4.5"
  
  parse-headers@^2.0.0:
    version "2.0.5"
    resolved "https://registry.npmmirror.com/parse-headers/-/parse-headers-2.0.5.tgz#069793f9356a54008571eb7f9761153e6c770da9"
    integrity sha512-ft3iAoLOB/MlwbNXgzy43SWGP6sQki2jQvAyBg/zDFAgr9bfNWZIUj42Kw2eJIl8kEi4PbgE6U1Zau/HwI75HA==
  
  parseurl@^1.3.2, parseurl@~1.3.3:
    version "1.3.3"
    resolved "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz"
    integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==
  
  path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
    integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==
  
  path-is-inside@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/path-is-inside/-/path-is-inside-1.0.2.tgz"
    integrity sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w==
  
  path-key@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/path-key/-/path-key-2.0.1.tgz"
    integrity sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==
  
  path-key@^3.1.0:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
    integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==
  
  path-parse@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
    integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
  
  path-to-regexp@0.1.7:
    version "0.1.7"
    resolved "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
    integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==
  
  path-to-regexp@^6.2.0:
    version "6.2.1"
    resolved "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-6.2.1.tgz"
    integrity sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw==
  
  path-type@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
    integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==
  
  pause-stream@~0.0.11:
    version "0.0.11"
    resolved "https://registry.npmmirror.com/pause-stream/-/pause-stream-0.0.11.tgz"
    integrity sha512-e3FBlXLmN/D1S+zHzanP4E/4Z60oFAa3O051qt1pxa7DEJWKAyil6upYVXCWadEnuoqa4Pkc9oUx9zsxYeRv8A==
    dependencies:
      through "~2.3"
  
  peek-readable@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/peek-readable/-/peek-readable-4.1.0.tgz#4ece1111bf5c2ad8867c314c81356847e8a62e72"
    integrity sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==
  
  pend@~1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz"
    integrity sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==
  
  performance-now@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
    integrity sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==
  
  phin@^2.9.1:
    version "2.9.3"
    resolved "https://registry.npmmirror.com/phin/-/phin-2.9.3.tgz#f9b6ac10a035636fb65dfc576aaaa17b8743125c"
    integrity sha512-CzFr90qM24ju5f88quFC/6qohjC144rehe5n6DH900lgXmUe86+xCKc10ev56gRKC4/BkHUoG4uSiQgBiIXwDA==
  
  picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
    version "2.3.1"
    resolved "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"
    integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==
  
  pify@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/pify/-/pify-3.0.0.tgz"
    integrity sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==
  
  pixelmatch@^4.0.2:
    version "4.0.2"
    resolved "https://registry.npmmirror.com/pixelmatch/-/pixelmatch-4.0.2.tgz#8f47dcec5011b477b67db03c243bc1f3085e8854"
    integrity sha512-J8B6xqiO37sU/gkcMglv6h5Jbd9xNER7aHzpfRdNmV4IbQBzBpe4l9XmbG+xPF/znacgu2jfEw+wHffaq/YkXA==
    dependencies:
      pngjs "^3.0.0"
  
  plist@^3.0.1, plist@^3.0.4:
    version "3.0.6"
    resolved "https://registry.npmmirror.com/plist/-/plist-3.0.6.tgz"
    integrity sha512-WiIVYyrp8TD4w8yCvyeIr+lkmrGRd5u0VbRnU+tP/aRLxP/YadJUYOMZJ/6hIa3oUyVCsycXvtNRgd5XBJIbiA==
    dependencies:
      base64-js "^1.5.1"
      xmlbuilder "^15.1.1"
  
  pngjs@^3.0.0:
    version "3.4.0"
    resolved "https://registry.npmmirror.com/pngjs/-/pngjs-3.4.0.tgz#99ca7d725965fb655814eaf65f38f12bbdbf555f"
    integrity sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==
  
  pngjs@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmmirror.com/pngjs/-/pngjs-6.0.0.tgz#ca9e5d2aa48db0228a52c419c3308e87720da821"
    integrity sha512-TRzzuFRRmEoSW/p1KVAmiOgPco2Irlah+bGFCeNfJXxxYGwSw7YwAOAcd7X28K/m5bjBWKsC29KyoMfHbypayg==
  
  prebuild-install@^7.1.0, prebuild-install@^7.1.1:
    version "7.1.1"
    resolved "https://registry.npmmirror.com/prebuild-install/-/prebuild-install-7.1.1.tgz"
    integrity sha512-jAXscXWMcCK8GgCoHOfIr0ODh5ai8mj63L2nWrjuAgXE6tDyYGnx4/8o/rCgU+B4JSyZBKbeZqzhtwtC3ovxjw==
    dependencies:
      detect-libc "^2.0.0"
      expand-template "^2.0.3"
      github-from-package "0.0.0"
      minimist "^1.2.3"
      mkdirp-classic "^0.5.3"
      napi-build-utils "^1.0.1"
      node-abi "^3.3.0"
      pump "^3.0.0"
      rc "^1.2.7"
      simple-get "^4.0.0"
      tar-fs "^2.0.0"
      tunnel-agent "^0.6.0"
  
  prelude-ls@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.1.2.tgz"
    integrity sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==
  
  prepend-http@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/prepend-http/-/prepend-http-2.0.0.tgz"
    integrity sha512-ravE6m9Atw9Z/jjttRUZ+clIXogdghyZAuWJ3qEzjT+jI/dL1ifAqhZeC5VHzQp1MSt1+jxKkFNemj/iO7tVUA==
  
  prettier-linter-helpers@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
    integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
    dependencies:
      fast-diff "^1.1.2"
  
  printj@~1.1.0:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/printj/-/printj-1.1.2.tgz"
    integrity sha512-zA2SmoLaxZyArQTOPj5LXecR+RagfPSU5Kw1qP+jkWeNlrq+eJZyY2oS68SU1Z/7/myXM4lo9716laOFAVStCQ==
  
  process-nextick-args@~2.0.0:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
    integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==
  
  process@0.11.10, process@^0.11.10:
    version "0.11.10"
    resolved "https://registry.npmmirror.com/process/-/process-0.11.10.tgz"
    integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==
  
  progress@^2.0.0, progress@^2.0.3:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz"
    integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==
  
  promise-inflight@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/promise-inflight/-/promise-inflight-1.0.1.tgz"
    integrity sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==
  
  promise-retry@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/promise-retry/-/promise-retry-2.0.1.tgz"
    integrity sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==
    dependencies:
      err-code "^2.0.2"
      retry "^0.12.0"
  
  promise@^7.0.1:
    version "7.3.1"
    resolved "https://registry.npmmirror.com/promise/-/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
    integrity sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==
    dependencies:
      asap "~2.0.3"
  
  proto-list@~1.2.1:
    version "1.2.4"
    resolved "https://registry.npmmirror.com/proto-list/-/proto-list-1.2.4.tgz"
    integrity sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA==
  
  proxy-addr@~2.0.7:
    version "2.0.7"
    resolved "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
    integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
    dependencies:
      forwarded "0.2.0"
      ipaddr.js "1.9.1"
  
  proxy-agent@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/proxy-agent/-/proxy-agent-5.0.0.tgz"
    integrity sha512-gkH7BkvLVkSfX9Dk27W6TyNOWWZWRilRfk1XxGNWOYJ2TuedAv1yFpCaU9QSBmBe716XOTNpYNOzhysyw8xn7g==
    dependencies:
      agent-base "^6.0.0"
      debug "4"
      http-proxy-agent "^4.0.0"
      https-proxy-agent "^5.0.0"
      lru-cache "^5.1.1"
      pac-proxy-agent "^5.0.0"
      proxy-from-env "^1.0.0"
      socks-proxy-agent "^5.0.0"
  
  proxy-from-env@^1.0.0, proxy-from-env@^1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
    integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==
  
  psl@^1.1.28:
    version "1.9.0"
    resolved "https://registry.npmmirror.com/psl/-/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
    integrity sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==
  
  pstree.remy@^1.1.8:
    version "1.1.8"
    resolved "https://registry.npmmirror.com/pstree.remy/-/pstree.remy-1.1.8.tgz"
    integrity sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==
  
  pug-attrs@^2.0.4:
    version "2.0.4"
    resolved "https://registry.npmmirror.com/pug-attrs/-/pug-attrs-2.0.4.tgz#b2f44c439e4eb4ad5d4ef25cac20d18ad28cc336"
    integrity sha512-TaZ4Z2TWUPDJcV3wjU3RtUXMrd3kM4Wzjbe3EWnSsZPsJ3LDI0F3yCnf2/W7PPFF+edUFQ0HgDL1IoxSz5K8EQ==
    dependencies:
      constantinople "^3.0.1"
      js-stringify "^1.0.1"
      pug-runtime "^2.0.5"
  
  pug-code-gen@^2.0.2:
    version "2.0.3"
    resolved "https://registry.npmmirror.com/pug-code-gen/-/pug-code-gen-2.0.3.tgz#122eb9ada9b5bf601705fe15aaa0a7d26bc134ab"
    integrity sha512-r9sezXdDuZJfW9J91TN/2LFbiqDhmltTFmGpHTsGdrNGp3p4SxAjjXEfnuK2e4ywYsRIVP0NeLbSAMHUcaX1EA==
    dependencies:
      constantinople "^3.1.2"
      doctypes "^1.1.0"
      js-stringify "^1.0.1"
      pug-attrs "^2.0.4"
      pug-error "^1.3.3"
      pug-runtime "^2.0.5"
      void-elements "^2.0.1"
      with "^5.0.0"
  
  pug-error@^1.3.3:
    version "1.3.3"
    resolved "https://registry.npmmirror.com/pug-error/-/pug-error-1.3.3.tgz#f342fb008752d58034c185de03602dd9ffe15fa6"
    integrity sha512-qE3YhESP2mRAWMFJgKdtT5D7ckThRScXRwkfo+Erqga7dyJdY3ZquspprMCj/9sJ2ijm5hXFWQE/A3l4poMWiQ==
  
  pug-filters@^3.1.1:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/pug-filters/-/pug-filters-3.1.1.tgz#ab2cc82db9eeccf578bda89130e252a0db026aa7"
    integrity sha512-lFfjNyGEyVWC4BwX0WyvkoWLapI5xHSM3xZJFUhx4JM4XyyRdO8Aucc6pCygnqV2uSgJFaJWW3Ft1wCWSoQkQg==
    dependencies:
      clean-css "^4.1.11"
      constantinople "^3.0.1"
      jstransformer "1.0.0"
      pug-error "^1.3.3"
      pug-walk "^1.1.8"
      resolve "^1.1.6"
      uglify-js "^2.6.1"
  
  pug-lexer@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/pug-lexer/-/pug-lexer-4.1.0.tgz#531cde48c7c0b1fcbbc2b85485c8665e31489cfd"
    integrity sha512-i55yzEBtjm0mlplW4LoANq7k3S8gDdfC6+LThGEvsK4FuobcKfDAwt6V4jKPH9RtiE3a2Akfg5UpafZ1OksaPA==
    dependencies:
      character-parser "^2.1.1"
      is-expression "^3.0.0"
      pug-error "^1.3.3"
  
  pug-linker@^3.0.6:
    version "3.0.6"
    resolved "https://registry.npmmirror.com/pug-linker/-/pug-linker-3.0.6.tgz#f5bf218b0efd65ce6670f7afc51658d0f82989fb"
    integrity sha512-bagfuHttfQOpANGy1Y6NJ+0mNb7dD2MswFG2ZKj22s8g0wVsojpRlqveEQHmgXXcfROB2RT6oqbPYr9EN2ZWzg==
    dependencies:
      pug-error "^1.3.3"
      pug-walk "^1.1.8"
  
  pug-load@^2.0.12:
    version "2.0.12"
    resolved "https://registry.npmmirror.com/pug-load/-/pug-load-2.0.12.tgz#d38c85eb85f6e2f704dea14dcca94144d35d3e7b"
    integrity sha512-UqpgGpyyXRYgJs/X60sE6SIf8UBsmcHYKNaOccyVLEuT6OPBIMo6xMPhoJnqtB3Q3BbO4Z3Bjz5qDsUWh4rXsg==
    dependencies:
      object-assign "^4.1.0"
      pug-walk "^1.1.8"
  
  pug-parser@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/pug-parser/-/pug-parser-5.0.1.tgz#03e7ada48b6840bd3822f867d7d90f842d0ffdc9"
    integrity sha512-nGHqK+w07p5/PsPIyzkTQfzlYfuqoiGjaoqHv1LjOv2ZLXmGX1O+4Vcvps+P4LhxZ3drYSljjq4b+Naid126wA==
    dependencies:
      pug-error "^1.3.3"
      token-stream "0.0.1"
  
  pug-runtime@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmmirror.com/pug-runtime/-/pug-runtime-2.0.5.tgz#6da7976c36bf22f68e733c359240d8ae7a32953a"
    integrity sha512-P+rXKn9un4fQY77wtpcuFyvFaBww7/91f3jHa154qU26qFAnOe6SW1CbIDcxiG5lLK9HazYrMCCuDvNgDQNptw==
  
  pug-strip-comments@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/pug-strip-comments/-/pug-strip-comments-1.0.4.tgz#cc1b6de1f6e8f5931cf02ec66cdffd3f50eaf8a8"
    integrity sha512-i5j/9CS4yFhSxHp5iKPHwigaig/VV9g+FgReLJWWHEHbvKsbqL0oP/K5ubuLco6Wu3Kan5p7u7qk8A4oLLh6vw==
    dependencies:
      pug-error "^1.3.3"
  
  pug-walk@^1.1.8:
    version "1.1.8"
    resolved "https://registry.npmmirror.com/pug-walk/-/pug-walk-1.1.8.tgz#b408f67f27912f8c21da2f45b7230c4bd2a5ea7a"
    integrity sha512-GMu3M5nUL3fju4/egXwZO0XLi6fW/K3T3VTgFQ14GxNi8btlxgT5qZL//JwZFm/2Fa64J/PNS8AZeys3wiMkVA==
  
  pug@^2.0.0-beta6:
    version "2.0.4"
    resolved "https://registry.npmmirror.com/pug/-/pug-2.0.4.tgz#ee7682ec0a60494b38d48a88f05f3b0ac931377d"
    integrity sha512-XhoaDlvi6NIzL49nu094R2NA6P37ijtgMDuWE+ofekDChvfKnzFal60bhSdiy8y2PBO6fmz3oMEIcfpBVRUdvw==
    dependencies:
      pug-code-gen "^2.0.2"
      pug-filters "^3.1.1"
      pug-lexer "^4.1.0"
      pug-linker "^3.0.6"
      pug-load "^2.0.12"
      pug-parser "^5.0.1"
      pug-runtime "^2.0.5"
      pug-strip-comments "^1.0.4"
  
  pump@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/pump/-/pump-3.0.0.tgz"
    integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
    dependencies:
      end-of-stream "^1.1.0"
      once "^1.3.1"
  
  punycode@^2.1.0, punycode@^2.1.1:
    version "2.3.0"
    resolved "https://registry.npmmirror.com/punycode/-/punycode-2.3.0.tgz"
    integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==
  
  qrcode-npm@0.0.3:
    version "0.0.3"
    resolved "https://registry.npmmirror.com/qrcode-npm/-/qrcode-npm-0.0.3.tgz#77ee6fbefa9c0f29fa09d4d1520807c6a6042b9a"
    integrity sha512-5LyJEyLimmjDeIBiI8omZEtWrlXj6TCjzJdNTucmUMUUy+F/mF4ZA1rmGz0xNOh6w+L9dZQRRRhoOsmfTWsFYQ==
  
  qs@6.11.0:
    version "6.11.0"
    resolved "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz#fd0d963446f7a65e1367e01abd85429453f0c37a"
    integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
    dependencies:
      side-channel "^1.0.4"
  
  qs@^6.11.0, qs@^6.4.0:
    version "6.11.1"
    resolved "https://registry.npmmirror.com/qs/-/qs-6.11.1.tgz"
    integrity sha512-0wsrzgTz/kAVIeuxSjnpGC56rzYtr6JT/2BwEvMaPhFIoYa1aGO8LbzuU1R0uUYQkLpWBTOj0l/CLAJB64J6nQ==
    dependencies:
      side-channel "^1.0.4"
  
  qs@~6.5.2:
    version "6.5.3"
    resolved "https://registry.npmmirror.com/qs/-/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
    integrity sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==
  
  queue-microtask@^1.2.2:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
    integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==
  
  quick-lru@^5.1.1:
    version "5.1.1"
    resolved "https://registry.npmmirror.com/quick-lru/-/quick-lru-5.1.1.tgz"
    integrity sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==
  
  range-parser@~1.2.1:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
    integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==
  
  raw-body@2.5.1:
    version "2.5.1"
    resolved "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
    integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
    dependencies:
      bytes "3.1.2"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      unpipe "1.0.0"
  
  raw-body@2.5.2, raw-body@^2.2.0:
    version "2.5.2"
    resolved "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.2.tgz"
    integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
    dependencies:
      bytes "3.1.2"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      unpipe "1.0.0"
  
  rc@^1.2.7:
    version "1.2.8"
    resolved "https://registry.npmmirror.com/rc/-/rc-1.2.8.tgz"
    integrity sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==
    dependencies:
      deep-extend "^0.6.0"
      ini "~1.3.0"
      minimist "^1.2.0"
      strip-json-comments "~2.0.1"
  
  read-config-file@6.2.0:
    version "6.2.0"
    resolved "https://registry.npmmirror.com/read-config-file/-/read-config-file-6.2.0.tgz"
    integrity sha512-gx7Pgr5I56JtYz+WuqEbQHj/xWo+5Vwua2jhb1VwM4Wid5PqYmZ4i00ZB0YEGIfkVBsCv9UrjgyqCiQfS/Oosg==
    dependencies:
      dotenv "^9.0.2"
      dotenv-expand "^5.1.0"
      js-yaml "^4.1.0"
      json5 "^2.2.0"
      lazy-val "^1.0.4"
  
  readable-stream@1.1.x:
    version "1.1.14"
    resolved "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.1.14.tgz"
    integrity sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.1"
      isarray "0.0.1"
      string_decoder "~0.10.x"
  
  readable-stream@^2.2.2:
    version "2.3.8"
    resolved "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
    integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
    dependencies:
      core-util-is "~1.0.0"
      inherits "~2.0.3"
      isarray "~1.0.0"
      process-nextick-args "~2.0.0"
      safe-buffer "~5.1.1"
      string_decoder "~1.1.1"
      util-deprecate "~1.0.1"
  
  readable-stream@^3.1.1, readable-stream@^3.4.0, readable-stream@^3.6.0:
    version "3.6.2"
    resolved "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
    integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
    dependencies:
      inherits "^2.0.3"
      string_decoder "^1.1.1"
      util-deprecate "^1.0.1"
  
  readable-web-to-node-stream@^3.0.0:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/readable-web-to-node-stream/-/readable-web-to-node-stream-3.0.2.tgz#5d52bb5df7b54861fd48d015e93a2cb87b3ee0bb"
    integrity sha512-ePeK6cc1EcKLEhJFt/AebMCLL+GgSKhuygrZ/GLaKZYEecIgIECf4UaUuaByiGtzckwR4ain9VzUh95T1exYGw==
    dependencies:
      readable-stream "^3.6.0"
  
  readdirp@~3.6.0:
    version "3.6.0"
    resolved "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
    integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
    dependencies:
      picomatch "^2.2.1"
  
  reflect-metadata@0.1.13:
    version "0.1.13"
    resolved "https://registry.npmmirror.com/reflect-metadata/-/reflect-metadata-0.1.13.tgz"
    integrity sha512-Ts1Y/anZELhSsjMcU605fU9RE4Oi3p5ORujwbIKXfWa+0Zxs510Qrmrce5/Jowq3cHSZSJqBjypxmHarc+vEWg==
  
  regenerator-runtime@^0.11.0:
    version "0.11.1"
    resolved "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
    integrity sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==
  
  regenerator-runtime@^0.13.3:
    version "0.13.11"
    resolved "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
    integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==
  
  regexpp@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/regexpp/-/regexpp-2.0.1.tgz"
    integrity sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==
  
  repeat-string@^1.5.2:
    version "1.6.1"
    resolved "https://registry.npmmirror.com/repeat-string/-/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
    integrity sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==
  
  request@^2.74.0:
    version "2.88.2"
    resolved "https://registry.npmmirror.com/request/-/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
    integrity sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==
    dependencies:
      aws-sign2 "~0.7.0"
      aws4 "^1.8.0"
      caseless "~0.12.0"
      combined-stream "~1.0.6"
      extend "~3.0.2"
      forever-agent "~0.6.1"
      form-data "~2.3.2"
      har-validator "~5.1.3"
      http-signature "~1.2.0"
      is-typedarray "~1.0.0"
      isstream "~0.1.2"
      json-stringify-safe "~5.0.1"
      mime-types "~2.1.19"
      oauth-sign "~0.9.0"
      performance-now "^2.1.0"
      qs "~6.5.2"
      safe-buffer "^5.1.2"
      tough-cookie "~2.5.0"
      tunnel-agent "^0.6.0"
      uuid "^3.3.2"
  
  require-directory@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz"
    integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==
  
  resolve-alpn@^1.0.0:
    version "1.2.1"
    resolved "https://registry.npmmirror.com/resolve-alpn/-/resolve-alpn-1.2.1.tgz"
    integrity sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==
  
  resolve-from@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
    integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==
  
  resolve-path@^1.4.0:
    version "1.4.0"
    resolved "https://registry.npmmirror.com/resolve-path/-/resolve-path-1.4.0.tgz"
    integrity sha512-i1xevIst/Qa+nA9olDxLWnLk8YZbi8R/7JPbCMcgyWaFR6bKWaexgJgEB5oc2PKMjYdrHynyz0NY+if+H98t1w==
    dependencies:
      http-errors "~1.6.2"
      path-is-absolute "1.0.1"
  
  resolve@^1.1.6:
    version "1.22.2"
    resolved "https://registry.npmmirror.com/resolve/-/resolve-1.22.2.tgz#0ed0943d4e301867955766c9f3e1ae6d01c6845f"
    integrity sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==
    dependencies:
      is-core-module "^2.11.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  responselike@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/responselike/-/responselike-1.0.2.tgz"
    integrity sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==
    dependencies:
      lowercase-keys "^1.0.0"
  
  responselike@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/responselike/-/responselike-2.0.1.tgz"
    integrity sha512-4gl03wn3hj1HP3yzgdI7d3lCkF95F21Pz4BPGvKHinyQzALR5CapwC8yIi0Rh58DEMQ/SguC03wFj2k0M/mHhw==
    dependencies:
      lowercase-keys "^2.0.0"
  
  restore-cursor@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-2.0.0.tgz"
    integrity sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==
    dependencies:
      onetime "^2.0.0"
      signal-exit "^3.0.2"
  
  restore-cursor@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz"
    integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
    dependencies:
      onetime "^5.1.0"
      signal-exit "^3.0.2"
  
  retry@^0.12.0:
    version "0.12.0"
    resolved "https://registry.npmmirror.com/retry/-/retry-0.12.0.tgz"
    integrity sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==
  
  reusify@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz"
    integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==
  
  right-align@^0.1.1:
    version "0.1.3"
    resolved "https://registry.npmmirror.com/right-align/-/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
    integrity sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg==
    dependencies:
      align-text "^0.1.1"
  
  rimraf@2.6.3:
    version "2.6.3"
    resolved "https://registry.npmmirror.com/rimraf/-/rimraf-2.6.3.tgz"
    integrity sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==
    dependencies:
      glob "^7.1.3"
  
  rimraf@^3.0.0, rimraf@^3.0.2:
    version "3.0.2"
    resolved "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
    integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
    dependencies:
      glob "^7.1.3"
  
  roarr@^2.15.3:
    version "2.15.4"
    resolved "https://registry.npmmirror.com/roarr/-/roarr-2.15.4.tgz"
    integrity sha512-CHhPh+UNHD2GTXNYhPWLnU8ONHdI+5DI+4EYIAOaiD63rHeYlZvyh8P+in5999TTSFgUYuKUAjzRI4mdh/p+2A==
    dependencies:
      boolean "^3.0.1"
      detect-node "^2.0.4"
      globalthis "^1.0.1"
      json-stringify-safe "^5.0.1"
      semver-compare "^1.0.0"
      sprintf-js "^1.1.2"
  
  run-async@^2.2.0:
    version "2.4.1"
    resolved "https://registry.npmmirror.com/run-async/-/run-async-2.4.1.tgz"
    integrity sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==
  
  run-parallel@^1.1.9:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz"
    integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
    dependencies:
      queue-microtask "^1.2.2"
  
  rxjs@^5.5.2:
    version "5.5.12"
    resolved "https://registry.npmmirror.com/rxjs/-/rxjs-5.5.12.tgz#6fa61b8a77c3d793dbaf270bee2f43f652d741cc"
    integrity sha512-xx2itnL5sBbqeeiVgNPVuQQ1nC8Jp2WfNJhXWHmElW9YmrpS9UVnNzhP3EH3HFqexO5Tlp8GhYY+WEcqcVMvGw==
    dependencies:
      symbol-observable "1.0.1"
  
  rxjs@^6.4.0:
    version "6.6.7"
    resolved "https://registry.npmmirror.com/rxjs/-/rxjs-6.6.7.tgz"
    integrity sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==
    dependencies:
      tslib "^1.9.0"
  
  rxjs@^7.5.2:
    version "7.8.1"
    resolved "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
    integrity sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==
    dependencies:
      tslib "^2.1.0"
  
  safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
    version "5.1.2"
    resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
    integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==
  
  safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
    version "5.2.1"
    resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
    integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==
  
  "safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz"
    integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==
  
  sanitize-filename@^1.6.3:
    version "1.6.3"
    resolved "https://registry.npmmirror.com/sanitize-filename/-/sanitize-filename-1.6.3.tgz"
    integrity sha512-y/52Mcy7aw3gRm7IrcGDFx/bCk4AhRh2eI9luHOQM86nZsqwiRkkq2GekHXBBD+SmPidc8i2PqtYZl+pWJ8Oeg==
    dependencies:
      truncate-utf8-bytes "^1.0.0"
  
  sax@>=0.6.0, sax@^1.2.4:
    version "1.2.4"
    resolved "https://registry.npmmirror.com/sax/-/sax-1.2.4.tgz"
    integrity sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==
  
  semver-compare@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/semver-compare/-/semver-compare-1.0.0.tgz"
    integrity sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==
  
  semver@^5.0.1, semver@^5.5.0, semver@^5.5.1, semver@^5.7.1:
    version "5.7.1"
    resolved "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz"
    integrity sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==
  
  semver@^6.2.0:
    version "6.3.0"
    resolved "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz"
    integrity sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==
  
  semver@^7.3.2, semver@^7.3.5, semver@^7.3.7:
    version "7.4.0"
    resolved "https://registry.npmmirror.com/semver/-/semver-7.4.0.tgz"
    integrity sha512-RgOxM8Mw+7Zus0+zcLEUn8+JfoLpj/huFTItQy2hsM4khuC1HYRDp0cU482Ewn/Fcy6bCjufD8vAj7voC66KQw==
    dependencies:
      lru-cache "^6.0.0"
  
  semver@^7.5.0:
    version "7.5.4"
    resolved "https://registry.npmmirror.com/semver/-/semver-7.5.4.tgz#483986ec4ed38e1c6c48c34894a9182dbff68a6e"
    integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
    dependencies:
      lru-cache "^6.0.0"
  
  semver@~7.0.0:
    version "7.0.0"
    resolved "https://registry.npmmirror.com/semver/-/semver-7.0.0.tgz"
    integrity sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==
  
  send@0.18.0:
    version "0.18.0"
    resolved "https://registry.npmmirror.com/send/-/send-0.18.0.tgz#670167cc654b05f5aa4a767f9113bb371bc706be"
    integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
    dependencies:
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      fresh "0.5.2"
      http-errors "2.0.0"
      mime "1.6.0"
      ms "2.1.3"
      on-finished "2.4.1"
      range-parser "~1.2.1"
      statuses "2.0.1"
  
  serialize-error@^7.0.1:
    version "7.0.1"
    resolved "https://registry.npmmirror.com/serialize-error/-/serialize-error-7.0.1.tgz"
    integrity sha512-8I8TjW5KMOKsZQTvoxjuSIa7foAwPWGOts+6o7sgjz41/qMD9VQHEDxi6PBvK2l0MXUmqZyNpUK+T2tQaaElvw==
    dependencies:
      type-fest "^0.13.1"
  
  serve-static@1.15.0:
    version "1.15.0"
    resolved "https://registry.npmmirror.com/serve-static/-/serve-static-1.15.0.tgz#faaef08cffe0a1a62f60cad0c4e513cff0ac9540"
    integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
    dependencies:
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      parseurl "~1.3.3"
      send "0.18.0"
  
  set-blocking@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/set-blocking/-/set-blocking-2.0.0.tgz"
    integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==
  
  setprototypeof@1.1.0:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.1.0.tgz"
    integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==
  
  setprototypeof@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz"
    integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==
  
  sharp@^0.32.1:
    version "0.32.1"
    resolved "https://registry.npmmirror.com/sharp/-/sharp-0.32.1.tgz#41aa0d0b2048b2e0ee453d9fcb14ec1f408390fe"
    integrity sha512-kQTFtj7ldpUqSe8kDxoGLZc1rnMFU0AO2pqbX6pLy3b7Oj8ivJIdoKNwxHVQG2HN6XpHPJqCSM2nsma2gOXvOg==
    dependencies:
      color "^4.2.3"
      detect-libc "^2.0.1"
      node-addon-api "^6.1.0"
      prebuild-install "^7.1.1"
      semver "^7.5.0"
      simple-get "^4.0.1"
      tar-fs "^2.1.1"
      tunnel-agent "^0.6.0"
  
  shebang-command@^1.2.0:
    version "1.2.0"
    resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-1.2.0.tgz"
    integrity sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==
    dependencies:
      shebang-regex "^1.0.0"
  
  shebang-command@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
    integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
    dependencies:
      shebang-regex "^3.0.0"
  
  shebang-regex@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-1.0.0.tgz"
    integrity sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==
  
  shebang-regex@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
    integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==
  
  side-channel@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz"
    integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
    dependencies:
      call-bind "^1.0.0"
      get-intrinsic "^1.0.2"
      object-inspect "^1.9.0"
  
  signal-exit@^3.0.2, signal-exit@^3.0.7:
    version "3.0.7"
    resolved "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"
    integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==
  
  simple-concat@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/simple-concat/-/simple-concat-1.0.1.tgz"
    integrity sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==
  
  simple-get@^4.0.0, simple-get@^4.0.1:
    version "4.0.1"
    resolved "https://registry.npmmirror.com/simple-get/-/simple-get-4.0.1.tgz"
    integrity sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==
    dependencies:
      decompress-response "^6.0.0"
      once "^1.3.1"
      simple-concat "^1.0.0"
  
  simple-swizzle@^0.2.2:
    version "0.2.2"
    resolved "https://registry.npmmirror.com/simple-swizzle/-/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
    integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
    dependencies:
      is-arrayish "^0.3.1"
  
  simple-update-notifier@^1.0.7:
    version "1.1.0"
    resolved "https://registry.npmmirror.com/simple-update-notifier/-/simple-update-notifier-1.1.0.tgz"
    integrity sha512-VpsrsJSUcJEseSbMHkrsrAVSdvVS5I96Qo1QAQ4FxQ9wXFcB+pjj7FB7/us9+GcgfW4ziHtYMc1J0PLczb55mg==
    dependencies:
      semver "~7.0.0"
  
  slash@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"
    integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==
  
  slice-ansi@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-2.1.0.tgz"
    integrity sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==
    dependencies:
      ansi-styles "^3.2.0"
      astral-regex "^1.0.0"
      is-fullwidth-code-point "^2.0.0"
  
  slice-ansi@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
    integrity sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==
    dependencies:
      ansi-styles "^4.0.0"
      astral-regex "^2.0.0"
      is-fullwidth-code-point "^3.0.0"
  
  smart-buffer@^4.0.2, smart-buffer@^4.2.0:
    version "4.2.0"
    resolved "https://registry.npmmirror.com/smart-buffer/-/smart-buffer-4.2.0.tgz"
    integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==
  
  socket.io-adapter@~2.5.2:
    version "2.5.2"
    resolved "https://registry.npmmirror.com/socket.io-adapter/-/socket.io-adapter-2.5.2.tgz"
    integrity sha512-87C3LO/NOMc+eMcpcxUBebGjkpMDkNBS9tf7KJqcDsmL936EChtVva71Dw2q4tQcuVC+hAUy4an2NO/sYXmwRA==
    dependencies:
      ws "~8.11.0"
  
  socket.io-client@^4.4.1:
    version "4.6.1"
    resolved "https://registry.npmmirror.com/socket.io-client/-/socket.io-client-4.6.1.tgz"
    integrity sha512-5UswCV6hpaRsNg5kkEHVcbBIXEYoVbMQaHJBXJCyEQ+CiFPV1NIOY0XOFWG4XR4GZcB8Kn6AsRs/9cy9TbqVMQ==
    dependencies:
      "@socket.io/component-emitter" "~3.1.0"
      debug "~4.3.2"
      engine.io-client "~6.4.0"
      socket.io-parser "~4.2.1"
  
  socket.io-parser@~4.2.1:
    version "4.2.2"
    resolved "https://registry.npmmirror.com/socket.io-parser/-/socket.io-parser-4.2.2.tgz"
    integrity sha512-DJtziuKypFkMMHCm2uIshOYC7QaylbtzQwiMYDuCKy3OPkjLzu4B2vAhTlqipRHHzrI0NJeBAizTK7X+6m1jVw==
    dependencies:
      "@socket.io/component-emitter" "~3.1.0"
      debug "~4.3.1"
  
  socket.io@^4.4.1:
    version "4.6.1"
    resolved "https://registry.npmmirror.com/socket.io/-/socket.io-4.6.1.tgz"
    integrity sha512-KMcaAi4l/8+xEjkRICl6ak8ySoxsYG+gG6/XfRCPJPQ/haCRIJBTL4wIl8YCsmtaBovcAXGLOShyVWQ/FG8GZA==
    dependencies:
      accepts "~1.3.4"
      base64id "~2.0.0"
      debug "~4.3.2"
      engine.io "~6.4.1"
      socket.io-adapter "~2.5.2"
      socket.io-parser "~4.2.1"
  
  socks-proxy-agent@5, socks-proxy-agent@^5.0.0:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/socks-proxy-agent/-/socks-proxy-agent-5.0.1.tgz"
    integrity sha512-vZdmnjb9a2Tz6WEQVIurybSwElwPxMZaIc7PzqbJTrezcKNznv6giT7J7tZDZ1BojVaa1jvO/UiUdhDVB0ACoQ==
    dependencies:
      agent-base "^6.0.2"
      debug "4"
      socks "^2.3.3"
  
  socks-proxy-agent@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmmirror.com/socks-proxy-agent/-/socks-proxy-agent-7.0.0.tgz"
    integrity sha512-Fgl0YPZ902wEsAyiQ+idGd1A7rSFx/ayC1CQVMw5P+EQx2V0SgpGtf6OKFhVjPflPUl9YMmEOnmfjCdMUsygww==
    dependencies:
      agent-base "^6.0.2"
      debug "^4.3.3"
      socks "^2.6.2"
  
  socks@^2.3.3, socks@^2.6.2:
    version "2.7.1"
    resolved "https://registry.npmmirror.com/socks/-/socks-2.7.1.tgz"
    integrity sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==
    dependencies:
      ip "^2.0.0"
      smart-buffer "^4.2.0"
  
  source-map-support@0.5.21, source-map-support@^0.5.19:
    version "0.5.21"
    resolved "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz"
    integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
    dependencies:
      buffer-from "^1.0.0"
      source-map "^0.6.0"
  
  source-map@^0.6.0, source-map@~0.6.0, source-map@~0.6.1:
    version "0.6.1"
    resolved "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz"
    integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==
  
  source-map@~0.5.1:
    version "0.5.7"
    resolved "https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
    integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==
  
  sprintf-js@^1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.1.2.tgz"
    integrity sha512-VE0SOVEHCk7Qc8ulkWw3ntAzXuqf7S2lvwQaDLRnUeIEaKNQJzV6BwmLKhOqT61aGhfUMrXeaBk+oDGCzvhcug==
  
  sprintf-js@~1.0.2:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz"
    integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==
  
  ssf@~0.11.2:
    version "0.11.2"
    resolved "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz"
    integrity sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==
    dependencies:
      frac "~1.1.2"
  
  sshpk@^1.7.0:
    version "1.17.0"
    resolved "https://registry.npmmirror.com/sshpk/-/sshpk-1.17.0.tgz#578082d92d4fe612b13007496e543fa0fbcbe4c5"
    integrity sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==
    dependencies:
      asn1 "~0.2.3"
      assert-plus "^1.0.0"
      bcrypt-pbkdf "^1.0.0"
      dashdash "^1.12.0"
      ecc-jsbn "~0.1.1"
      getpass "^0.1.1"
      jsbn "~0.1.0"
      safer-buffer "^2.0.2"
      tweetnacl "~0.14.0"
  
  ssri@^9.0.0:
    version "9.0.1"
    resolved "https://registry.npmmirror.com/ssri/-/ssri-9.0.1.tgz"
    integrity sha512-o57Wcn66jMQvfHG1FlYbWeZWW/dHZhJXjpIcTfXldXEk5nz5lStPo3mK0OJQfGR3RbZUlbISexbljkJzuEj/8Q==
    dependencies:
      minipass "^3.1.1"
  
  stat-mode@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/stat-mode/-/stat-mode-1.0.0.tgz"
    integrity sha512-jH9EhtKIjuXZ2cWxmXS8ZP80XyC3iasQxMDV8jzhNJpfDb7VbQLVW4Wvsxz9QZvzV+G4YoSfBUVKDOyxLzi/sg==
  
  statuses@2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz"
    integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==
  
  "statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.3.1, statuses@^1.5.0:
    version "1.5.0"
    resolved "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz"
    integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==
  
  steno@^0.4.1:
    version "0.4.4"
    resolved "https://registry.npmmirror.com/steno/-/steno-0.4.4.tgz"
    integrity sha512-EEHMVYHNXFHfGtgjNITnka0aHhiAlo93F7z2/Pwd+g0teG9CnM3JIINM7hVVB5/rhw9voufD7Wukwgtw2uqh6w==
    dependencies:
      graceful-fs "^4.1.3"
  
  stream-throttle@^0.1.3:
    version "0.1.3"
    resolved "https://registry.npmmirror.com/stream-throttle/-/stream-throttle-0.1.3.tgz#add57c8d7cc73a81630d31cd55d3961cfafba9c3"
    integrity sha512-889+B9vN9dq7/vLbGyuHeZ6/ctf5sNuGWsDy89uNxkFTAgzy0eK7+w5fL3KLNRTkLle7EgZGvHUphZW0Q26MnQ==
    dependencies:
      commander "^2.2.0"
      limiter "^1.0.5"
  
  string-template@1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/string-template/-/string-template-1.0.0.tgz"
    integrity sha512-SLqR3GBUXuoPP5MmYtD7ompvXiG87QjT6lzOszyXjTM86Uu7At7vNnt2xgyTLq5o9T4IxTYFyGxcULqpsmsfdg==
  
  "string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
    version "4.2.3"
    resolved "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
    integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
    dependencies:
      emoji-regex "^8.0.0"
      is-fullwidth-code-point "^3.0.0"
      strip-ansi "^6.0.1"
  
  string-width@^2.1.0:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/string-width/-/string-width-2.1.1.tgz"
    integrity sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==
    dependencies:
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^4.0.0"
  
  string-width@^3.0.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/string-width/-/string-width-3.1.0.tgz"
    integrity sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==
    dependencies:
      emoji-regex "^7.0.1"
      is-fullwidth-code-point "^2.0.0"
      strip-ansi "^5.1.0"
  
  string_decoder@^1.1.1:
    version "1.3.0"
    resolved "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz"
    integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
    dependencies:
      safe-buffer "~5.2.0"
  
  string_decoder@~0.10.x:
    version "0.10.31"
    resolved "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz"
    integrity sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==
  
  string_decoder@~1.1.1:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz"
    integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
    dependencies:
      safe-buffer "~5.1.0"
  
  stringz@2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/stringz/-/stringz-2.1.0.tgz"
    integrity sha512-KlywLT+MZ+v0IRepfMxRtnSvDCMc3nR1qqCs3m/qIbSOWkNZYT8XHQA31rS3TnKp0c5xjZu3M4GY/2aRKSi/6A==
    dependencies:
      char-regex "^1.0.2"
  
  strip-ansi@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-4.0.0.tgz"
    integrity sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==
    dependencies:
      ansi-regex "^3.0.0"
  
  strip-ansi@^5.1.0:
    version "5.2.0"
    resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-5.2.0.tgz"
    integrity sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==
    dependencies:
      ansi-regex "^4.1.0"
  
  strip-ansi@^6.0.0, strip-ansi@^6.0.1:
    version "6.0.1"
    resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
    integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
    dependencies:
      ansi-regex "^5.0.1"
  
  strip-json-comments@^2.0.1, strip-json-comments@~2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
    integrity sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==
  
  strtok3@^6.2.4:
    version "6.3.0"
    resolved "https://registry.npmmirror.com/strtok3/-/strtok3-6.3.0.tgz#358b80ffe6d5d5620e19a073aa78ce947a90f9a0"
    integrity sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==
    dependencies:
      "@tokenizer/token" "^0.3.0"
      peek-readable "^4.1.0"
  
  sumchecker@^3.0.1:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/sumchecker/-/sumchecker-3.0.1.tgz"
    integrity sha512-MvjXzkz/BOfyVDkG0oFOtBxHX2u3gKbMHIF/dXblZsgD3BWOFLmHovIpZY7BykJdAjcqRCBi1WYBNdEC9yI7vg==
    dependencies:
      debug "^4.1.0"
  
  supports-color@^5.3.0, supports-color@^5.5.0:
    version "5.5.0"
    resolved "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
    integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
    dependencies:
      has-flag "^3.0.0"
  
  supports-color@^7.1.0:
    version "7.2.0"
    resolved "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
    integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
    dependencies:
      has-flag "^4.0.0"
  
  supports-preserve-symlinks-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
    integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
  
  svg-inline-react@^1.0.2:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/svg-inline-react/-/svg-inline-react-1.0.3.tgz#68d15bf88f99f64daa52821ed5441612d8ad9041"
    integrity sha512-qGwiEKfKiaqiP5YJ0Fa0e6CN74Gma0XDJZ5LYMYcPdfMM+I6sPVZMbYRJ2sEkAY7B7jJzUphoimEl4LnqzBZ5g==
  
  symbol-observable@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/symbol-observable/-/symbol-observable-1.0.1.tgz#8340fc4702c3122df5d22288f88283f513d3fdd4"
    integrity sha512-Kb3PrPYz4HanVF1LVGuAdW6LoVgIwjUYJGzFe7NDrBLCN4lsV/5J0MFurV+ygS4bRVwrCEt2c7MQ1R2a72oJDw==
  
  table@^5.2.3:
    version "5.4.6"
    resolved "https://registry.npmmirror.com/table/-/table-5.4.6.tgz"
    integrity sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==
    dependencies:
      ajv "^6.10.2"
      lodash "^4.17.14"
      slice-ansi "^2.1.0"
      string-width "^3.0.0"
  
  tar-fs@^2.0.0, tar-fs@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmmirror.com/tar-fs/-/tar-fs-2.1.1.tgz"
    integrity sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==
    dependencies:
      chownr "^1.1.1"
      mkdirp-classic "^0.5.2"
      pump "^3.0.0"
      tar-stream "^2.1.4"
  
  tar-stream@^2.1.4:
    version "2.2.0"
    resolved "https://registry.npmmirror.com/tar-stream/-/tar-stream-2.2.0.tgz"
    integrity sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==
    dependencies:
      bl "^4.0.3"
      end-of-stream "^1.4.1"
      fs-constants "^1.0.0"
      inherits "^2.0.3"
      readable-stream "^3.1.1"
  
  tar@^6.0.5, tar@^6.1.11, tar@^6.1.2:
    version "6.1.13"
    resolved "https://registry.npmmirror.com/tar/-/tar-6.1.13.tgz"
    integrity sha512-jdIBIN6LTIe2jqzay/2vtYLlBHa3JF42ot3h1dW8Q0PaAG4v8rm0cvpVePtau5C6OKXGGcgO9q2AMNSWxiLqKw==
    dependencies:
      chownr "^2.0.0"
      fs-minipass "^2.0.0"
      minipass "^4.0.0"
      minizlib "^2.1.1"
      mkdirp "^1.0.3"
      yallist "^4.0.0"
  
  temp-file@^3.4.0:
    version "3.4.0"
    resolved "https://registry.npmmirror.com/temp-file/-/temp-file-3.4.0.tgz"
    integrity sha512-C5tjlC/HCtVUOi3KWVokd4vHVViOmGjtLwIh4MuzPo/nMYTV/p1urt3RnMz2IWXDdKEGJH3k5+KPxtqRsUYGtg==
    dependencies:
      async-exit-hook "^2.0.1"
      fs-extra "^10.0.0"
  
  text-table@^0.2.0:
    version "0.2.0"
    resolved "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz"
    integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==
  
  thenify-all@^1.0.0:
    version "1.6.0"
    resolved "https://registry.npmmirror.com/thenify-all/-/thenify-all-1.6.0.tgz"
    integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
    dependencies:
      thenify ">= 3.1.0 < 4"
  
  "thenify@>= 3.1.0 < 4":
    version "3.3.1"
    resolved "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz"
    integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
    dependencies:
      any-promise "^1.0.0"
  
  through@^2.3.6, through@~2.3:
    version "2.3.8"
    resolved "https://registry.npmmirror.com/through/-/through-2.3.8.tgz"
    integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==
  
  thunkify@^2.1.2:
    version "2.1.2"
    resolved "https://registry.npmmirror.com/thunkify/-/thunkify-2.1.2.tgz#faa0e9d230c51acc95ca13a361ac05ca7e04553d"
    integrity sha512-w9foI80XcGImrhMQ19pxunaEC5Rp2uzxZZg4XBAFRfiLOplk3F0l7wo+bO16vC2/nlQfR/mXZxcduo0MF2GWLg==
  
  timm@^1.6.1:
    version "1.7.1"
    resolved "https://registry.npmmirror.com/timm/-/timm-1.7.1.tgz#96bab60c7d45b5a10a8a4d0f0117c6b7e5aff76f"
    integrity sha512-IjZc9KIotudix8bMaBW6QvMuq64BrJWFs1+4V0lXwWGQZwH+LnX87doAYhem4caOEusRP9/g6jVDQmZ8XOk1nw==
  
  tinycolor2@^1.6.0:
    version "1.6.0"
    resolved "https://registry.npmmirror.com/tinycolor2/-/tinycolor2-1.6.0.tgz#f98007460169b0263b97072c5ae92484ce02d09e"
    integrity sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==
  
  tmp-promise@^3.0.2:
    version "3.0.3"
    resolved "https://registry.npmmirror.com/tmp-promise/-/tmp-promise-3.0.3.tgz"
    integrity sha512-RwM7MoPojPxsOBYnyd2hy0bxtIlVrihNs9pj5SUvY8Zz1sQcQG2tG1hSr8PDxfgEB8RNKDhqbIlroIarSNDNsQ==
    dependencies:
      tmp "^0.2.0"
  
  tmp@^0.0.33:
    version "0.0.33"
    resolved "https://registry.npmmirror.com/tmp/-/tmp-0.0.33.tgz"
    integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
    dependencies:
      os-tmpdir "~1.0.2"
  
  tmp@^0.2.0:
    version "0.2.1"
    resolved "https://registry.npmmirror.com/tmp/-/tmp-0.2.1.tgz"
    integrity sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ==
    dependencies:
      rimraf "^3.0.0"
  
  to-fast-properties@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"
    integrity sha512-lxrWP8ejsq+7E3nNjwYmUBMAgjMTZoTI+sdBOpvNyijeDLa29LUn9QaoXAHv4+Z578hbmHHJKZknzxVtvo77og==
  
  to-readable-stream@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/to-readable-stream/-/to-readable-stream-1.0.0.tgz"
    integrity sha512-Iq25XBt6zD5npPhlLVXGFN3/gyR2/qODcKNNyTMd4vbm39HUaOiAM4PMq0eMVC/Tkxz+Zjdsc55g9yyz+Yq00Q==
  
  to-regex-range@^5.0.1:
    version "5.0.1"
    resolved "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
    integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
    dependencies:
      is-number "^7.0.0"
  
  toidentifier@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz"
    integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==
  
  token-stream@0.0.1:
    version "0.0.1"
    resolved "https://registry.npmmirror.com/token-stream/-/token-stream-0.0.1.tgz#ceeefc717a76c4316f126d0b9dbaa55d7e7df01a"
    integrity sha512-nfjOAu/zAWmX9tgwi5NRp7O7zTDUD1miHiB40klUnAh9qnL1iXdgzcz/i5dMaL5jahcBAaSfmNOBBJBLJW8TEg==
  
  token-types@^4.1.1:
    version "4.2.1"
    resolved "https://registry.npmmirror.com/token-types/-/token-types-4.2.1.tgz#0f897f03665846982806e138977dbe72d44df753"
    integrity sha512-6udB24Q737UD/SDsKAHI9FCRP7Bqc9D/MQUV02ORQg5iskjtLJlZJNdN4kKtcdtwCeWIwIHDGaUsTsCCAa8sFQ==
    dependencies:
      "@tokenizer/token" "^0.3.0"
      ieee754 "^1.2.1"
  
  touch@^3.1.0:
    version "3.1.0"
    resolved "https://registry.npmmirror.com/touch/-/touch-3.1.0.tgz"
    integrity sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==
    dependencies:
      nopt "~1.0.10"
  
  tough-cookie@~2.5.0:
    version "2.5.0"
    resolved "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
    integrity sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==
    dependencies:
      psl "^1.1.28"
      punycode "^2.1.1"
  
  tr46@~0.0.3:
    version "0.0.3"
    resolved "https://registry.npmmirror.com/tr46/-/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
    integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==
  
  truncate-utf8-bytes@^1.0.0:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/truncate-utf8-bytes/-/truncate-utf8-bytes-1.0.2.tgz"
    integrity sha512-95Pu1QXQvruGEhv62XCMO3Mm90GscOCClvrIUwCM0PYOXK3kaF3l3sIHxx71ThJfcbM2O5Au6SO3AWCSEfW4mQ==
    dependencies:
      utf8-byte-length "^1.0.1"
  
  tslib@2.5.0, tslib@^2.0.1, tslib@^2.1.0:
    version "2.5.0"
    resolved "https://registry.npmmirror.com/tslib/-/tslib-2.5.0.tgz"
    integrity sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==
  
  tslib@^1.9.0:
    version "1.14.1"
    resolved "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz"
    integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==
  
  tsscmp@1.0.6:
    version "1.0.6"
    resolved "https://registry.npmmirror.com/tsscmp/-/tsscmp-1.0.6.tgz"
    integrity sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==
  
  tunnel-agent@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmmirror.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
    integrity sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==
    dependencies:
      safe-buffer "^5.0.1"
  
  tunnel@^0.0.6:
    version "0.0.6"
    resolved "https://registry.npmmirror.com/tunnel/-/tunnel-0.0.6.tgz"
    integrity sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg==
  
  tweetnacl@^0.14.3, tweetnacl@~0.14.0:
    version "0.14.5"
    resolved "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
    integrity sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==
  
  type-check@~0.3.2:
    version "0.3.2"
    resolved "https://registry.npmmirror.com/type-check/-/type-check-0.3.2.tgz"
    integrity sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==
    dependencies:
      prelude-ls "~1.1.2"
  
  type-fest@^0.13.1:
    version "0.13.1"
    resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.13.1.tgz"
    integrity sha512-34R7HTnG0XIJcBSn5XhDd7nNFPRcXYRZrBB2O2jdKqYODldSzBAqzsWoZYYvduky73toYS/ESqxPvkDf/F0XMg==
  
  type-is@^1.6.14, type-is@^1.6.16, type-is@~1.6.18:
    version "1.6.18"
    resolved "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz"
    integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
    dependencies:
      media-typer "0.3.0"
      mime-types "~2.1.24"
  
  typed-emitter@^2.1.0:
    version "2.1.0"
    resolved "https://registry.npmmirror.com/typed-emitter/-/typed-emitter-2.1.0.tgz"
    integrity sha512-g/KzbYKbH5C2vPkaXGu8DJlHrGKHLsM25Zg9WuC9pMGfuvT+X25tZQWo5fK1BjBm8+UrVE9LDCvaY0CQk+fXDA==
    optionalDependencies:
      rxjs "^7.5.2"
  
  typedarray@^0.0.6:
    version "0.0.6"
    resolved "https://registry.npmmirror.com/typedarray/-/typedarray-0.0.6.tgz"
    integrity sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==
  
  uglify-js@^2.6.1:
    version "2.8.29"
    resolved "https://registry.npmmirror.com/uglify-js/-/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
    integrity sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w==
    dependencies:
      source-map "~0.5.1"
      yargs "~3.10.0"
    optionalDependencies:
      uglify-to-browserify "~1.0.0"
  
  uglify-js@~1.2:
    version "1.2.6"
    resolved "https://registry.npmmirror.com/uglify-js/-/uglify-js-1.2.6.tgz#d354b2d3c1cf10ebc18fa78c11a28bdd9ce1580d"
    integrity sha512-bMAZaFjLe07fmPbfUPoXzyZaB60kpC5EP63Xcqf9/Kt00fgNtQ3q+wAJt9aJh1iimi9vKkyIYgvXghdHb//IEg==
  
  uglify-to-browserify@~1.0.0:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
    integrity sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q==
  
  undefsafe@^2.0.5:
    version "2.0.5"
    resolved "https://registry.npmmirror.com/undefsafe/-/undefsafe-2.0.5.tgz"
    integrity sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==
  
  underscore@~1.4.4:
    version "1.4.4"
    resolved "https://registry.npmmirror.com/underscore/-/underscore-1.4.4.tgz#61a6a32010622afa07963bf325203cf12239d604"
    integrity sha512-ZqGrAgaqqZM7LGRzNjLnw5elevWb5M8LEoDMadxIW3OWbcv72wMMgKdwOKpd5Fqxe8choLD8HN3iSj3TUh/giQ==
  
  unescape@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/unescape/-/unescape-1.0.1.tgz"
    integrity sha512-O0+af1Gs50lyH1nUu3ZyYS1cRh01Q/kUKatTOkSs7jukXE6/NebucDVxyiDsA9AQ4JC1V1jUH9EO8JX2nMDgGQ==
    dependencies:
      extend-shallow "^2.0.1"
  
  unique-filename@^2.0.0:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/unique-filename/-/unique-filename-2.0.1.tgz"
    integrity sha512-ODWHtkkdx3IAR+veKxFV+VBkUMcN+FaqzUUd7IZzt+0zhDZFPFxhlqwPF3YQvMHx1TD0tdgYl+kuPnJ8E6ql7A==
    dependencies:
      unique-slug "^3.0.0"
  
  unique-slug@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/unique-slug/-/unique-slug-3.0.0.tgz"
    integrity sha512-8EyMynh679x/0gqE9fT9oilG+qEt+ibFyqjuVTsZn1+CMxH+XLlpvr2UZx4nVcCwTpx81nICr2JQFkM+HPLq4w==
    dependencies:
      imurmurhash "^0.1.4"
  
  universalify@^0.1.0:
    version "0.1.2"
    resolved "https://registry.npmmirror.com/universalify/-/universalify-0.1.2.tgz"
    integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==
  
  universalify@^2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz"
    integrity sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ==
  
  unpipe@1.0.0, unpipe@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz"
    integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  url-parse-lax@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmmirror.com/url-parse-lax/-/url-parse-lax-3.0.0.tgz"
    integrity sha512-NjFKA0DidqPa5ciFcSrXnAltTtzz84ogy+NebPvfEgAck0+TNg4UJ4IN+fB7zRZfbgUf0syOo9MDxFkDSMuFaQ==
    dependencies:
      prepend-http "^2.0.0"
  
  urllib@^2.38.0:
    version "2.40.0"
    resolved "https://registry.npmmirror.com/urllib/-/urllib-2.40.0.tgz"
    integrity sha512-XDZjoijtzsbkXTXgM+A/sJM002nwoYsc46YOYr6MNH2jUUw1nCBf2ywT1WaPsVEWJX4Yr+9isGmYj4+yofFn9g==
    dependencies:
      any-promise "^1.3.0"
      content-type "^1.0.2"
      debug "^2.6.9"
      default-user-agent "^1.0.0"
      digest-header "^1.0.0"
      ee-first "~1.1.1"
      formstream "^1.1.0"
      humanize-ms "^1.2.0"
      iconv-lite "^0.4.15"
      ip "^1.1.5"
      proxy-agent "^5.0.0"
      pump "^3.0.0"
      qs "^6.4.0"
      statuses "^1.3.1"
      utility "^1.16.1"
  
  utf8-byte-length@^1.0.1:
    version "1.0.4"
    resolved "https://registry.npmmirror.com/utf8-byte-length/-/utf8-byte-length-1.0.4.tgz"
    integrity sha512-4+wkEYLBbWxqTahEsWrhxepcoVOJ+1z5PGIjPZxRkytcdSUaNjIjBM7Xn8E+pdSuV7SzvWovBFA54FO0JSoqhA==
  
  utif2@^4.0.1:
    version "4.1.0"
    resolved "https://registry.npmmirror.com/utif2/-/utif2-4.1.0.tgz#e768d37bd619b995d56d9780b5d2b4611a3d932b"
    integrity sha512-+oknB9FHrJ7oW7A2WZYajOcv4FcDR4CfoGB0dPNfxbi4GO05RRnFmt5oa23+9w32EanrYcSJWspUiJkLMs+37w==
    dependencies:
      pako "^1.0.11"
  
  util-deprecate@^1.0.1, util-deprecate@~1.0.1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
    integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==
  
  util@^0.12.0:
    version "0.12.5"
    resolved "https://registry.npmmirror.com/util/-/util-0.12.5.tgz"
    integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
    dependencies:
      inherits "^2.0.3"
      is-arguments "^1.0.4"
      is-generator-function "^1.0.7"
      is-typed-array "^1.1.3"
      which-typed-array "^1.1.2"
  
  utility@^1.15.0, utility@^1.16.1, utility@^1.17.0:
    version "1.17.0"
    resolved "https://registry.npmmirror.com/utility/-/utility-1.17.0.tgz"
    integrity sha512-KdVkF9An/0239BJ4+dqOa7NPrPIOeQE9AGfx0XS16O9DBiHNHRJMoeU5nL6pRGAkgJOqdOu8R4gBRcXnAocJKw==
    dependencies:
      copy-to "^2.0.1"
      escape-html "^1.0.3"
      mkdirp "^0.5.1"
      mz "^2.7.0"
      unescape "^1.0.1"
  
  utils-merge@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
    integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==
  
  uuid@^3.3.2:
    version "3.4.0"
    resolved "https://registry.npmmirror.com/uuid/-/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
    integrity sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==
  
  uuid@^9.0.0:
    version "9.0.0"
    resolved "https://registry.npmmirror.com/uuid/-/uuid-9.0.0.tgz"
    integrity sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==
  
  validator@^13.7.0:
    version "13.9.0"
    resolved "https://registry.npmmirror.com/validator/-/validator-13.9.0.tgz"
    integrity sha512-B+dGG8U3fdtM0/aNK4/X8CXq/EcxU2WPrPEkJGslb47qyHsxmbggTWK0yEA4qnYVNF+nxNlN88o14hIcPmSIEA==
  
  vary@^1, vary@^1.1.2, vary@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz"
    integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==
  
  verror@1.10.0:
    version "1.10.0"
    resolved "https://registry.npmmirror.com/verror/-/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
    integrity sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==
    dependencies:
      assert-plus "^1.0.0"
      core-util-is "1.0.2"
      extsprintf "^1.2.0"
  
  verror@^1.10.0:
    version "1.10.1"
    resolved "https://registry.npmmirror.com/verror/-/verror-1.10.1.tgz#4bf09eeccf4563b109ed4b3d458380c972b0cdeb"
    integrity sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==
    dependencies:
      assert-plus "^1.0.0"
      core-util-is "1.0.2"
      extsprintf "^1.2.0"
  
  vm2@^3.9.11:
    version "3.9.16"
    resolved "https://registry.npmmirror.com/vm2/-/vm2-3.9.16.tgz"
    integrity sha512-3T9LscojNTxdOyG+e8gFeyBXkMlOBYDoF6dqZbj+MPVHi9x10UfiTAJIobuchRCp3QvC+inybTbMJIUrLsig0w==
    dependencies:
      acorn "^8.7.0"
      acorn-walk "^8.2.0"
  
  void-elements@^2.0.1:
    version "2.0.1"
    resolved "https://registry.npmmirror.com/void-elements/-/void-elements-2.0.1.tgz#c066afb582bb1cb4128d60ea92392e94d5e9dbec"
    integrity sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==
  
  wcwidth@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz"
    integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
    dependencies:
      defaults "^1.0.3"
  
  webidl-conversions@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
    integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==
  
  whatwg-fetch@^1.0.0:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/whatwg-fetch/-/whatwg-fetch-1.1.1.tgz#ac3c9d39f320c6dce5339969d054ef43dd333319"
    integrity sha512-MVDSiH8wkh6qdk+zxNlUas0pmuKVp8H5RwQZM2tGQhenUC+/nUBmJerAg/lFd3DPYrF2e6ArdaD2JpbGjM9oww==
  
  whatwg-fetch@^3.4.1:
    version "3.6.2"
    resolved "https://registry.npmmirror.com/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz#dced24f37f2624ed0281725d51d0e2e3fe677f8c"
    integrity sha512-bJlen0FcuU/0EMLrdbJ7zOnW6ITZLrZMIarMUVmdKtsGvZna8vxKYaexICWPfZ8qwf9fzNq+UEIZrnSaApt6RA==
  
  whatwg-url@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
    integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
    dependencies:
      tr46 "~0.0.3"
      webidl-conversions "^3.0.0"
  
  which-typed-array@^1.1.2:
    version "1.1.9"
    resolved "https://registry.npmmirror.com/which-typed-array/-/which-typed-array-1.1.9.tgz"
    integrity sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
      is-typed-array "^1.1.10"
  
  which@^1.1.1, which@^1.2.9:
    version "1.3.1"
    resolved "https://registry.npmmirror.com/which/-/which-1.3.1.tgz"
    integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
    dependencies:
      isexe "^2.0.0"
  
  which@^2.0.1, which@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
    integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
    dependencies:
      isexe "^2.0.0"
  
  wide-align@^1.1.5:
    version "1.1.5"
    resolved "https://registry.npmmirror.com/wide-align/-/wide-align-1.1.5.tgz"
    integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
    dependencies:
      string-width "^1.0.2 || 2 || 3 || 4"
  
  win-release@^1.0.0:
    version "1.1.1"
    resolved "https://registry.npmmirror.com/win-release/-/win-release-1.1.1.tgz"
    integrity sha512-iCRnKVvGxOQdsKhcQId2PXV1vV3J/sDPXKA4Oe9+Eti2nb2ESEsYHRYls/UjoUW3bIc5ZDO8dTH50A/5iVN+bw==
    dependencies:
      semver "^5.0.1"
  
  window-size@0.1.0:
    version "0.1.0"
    resolved "https://registry.npmmirror.com/window-size/-/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
    integrity sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg==
  
  with@^5.0.0:
    version "5.1.1"
    resolved "https://registry.npmmirror.com/with/-/with-5.1.1.tgz#fa4daa92daf32c4ea94ed453c81f04686b575dfe"
    integrity sha512-uAnSsFGfSpF6DNhBXStvlZILfHJfJu4eUkfbRGk94kGO1Ta7bg6FwfvoOhhyHAJuFbCw+0xk4uJ3u57jLvlCJg==
    dependencies:
      acorn "^3.1.0"
      acorn-globals "^3.0.0"
  
  wmf@~1.0.1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz"
    integrity sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==
  
  word-wrap@~1.2.3:
    version "1.2.3"
    resolved "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.3.tgz"
    integrity sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==
  
  word@~0.3.0:
    version "0.3.0"
    resolved "https://registry.npmmirror.com/word/-/word-0.3.0.tgz"
    integrity sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==
  
  wordwrap@0.0.2:
    version "0.0.2"
    resolved "https://registry.npmmirror.com/wordwrap/-/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
    integrity sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==
  
  wordwrap@~0.0.2:
    version "0.0.3"
    resolved "https://registry.npmmirror.com/wordwrap/-/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"
    integrity sha512-1tMA907+V4QmxV7dbRvb4/8MaRALK6q9Abid3ndMYnbyo8piisCmeONVqVSXqQA3KaP4SLt5b7ud6E2sqP8TFw==
  
  wrap-ansi@^7.0.0:
    version "7.0.0"
    resolved "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
    integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
    dependencies:
      ansi-styles "^4.0.0"
      string-width "^4.1.0"
      strip-ansi "^6.0.0"
  
  wrappy@1:
    version "1.0.2"
    resolved "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz"
    integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==
  
  write@1.0.3:
    version "1.0.3"
    resolved "https://registry.npmmirror.com/write/-/write-1.0.3.tgz"
    integrity sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==
    dependencies:
      mkdirp "^0.5.1"
  
  ws@^5.1.0:
    version "5.2.3"
    resolved "https://registry.npmmirror.com/ws/-/ws-5.2.3.tgz#05541053414921bc29c63bee14b8b0dd50b07b3d"
    integrity sha512-jZArVERrMsKUatIdnLzqvcfydI85dvd/Fp1u/VOpfdDWQ4c9qWXe+VIeAbQ5FrDwciAkr+lzofXLz3Kuf26AOA==
    dependencies:
      async-limiter "~1.0.0"
  
  ws@~8.11.0:
    version "8.11.0"
    resolved "https://registry.npmmirror.com/ws/-/ws-8.11.0.tgz"
    integrity sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg==
  
  xhr@^2.0.1:
    version "2.6.0"
    resolved "https://registry.npmmirror.com/xhr/-/xhr-2.6.0.tgz#b69d4395e792b4173d6b7df077f0fc5e4e2b249d"
    integrity sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA==
    dependencies:
      global "~4.4.0"
      is-function "^1.0.1"
      parse-headers "^2.0.0"
      xtend "^4.0.0"
  
  xlsx@^0.17.4:
    version "0.17.5"
    resolved "https://registry.npmmirror.com/xlsx/-/xlsx-0.17.5.tgz"
    integrity sha512-lXNU0TuYsvElzvtI6O7WIVb9Zar1XYw7Xb3VAx2wn8N/n0whBYrCnHMxtFyIiUU1Wjf09WzmLALDfBO5PqTb1g==
    dependencies:
      adler-32 "~1.2.0"
      cfb "^1.1.4"
      codepage "~1.15.0"
      crc-32 "~1.2.0"
      ssf "~0.11.2"
      wmf "~1.0.1"
      word "~0.3.0"
  
  xml-parse-from-string@^1.0.0:
    version "1.0.1"
    resolved "https://registry.npmmirror.com/xml-parse-from-string/-/xml-parse-from-string-1.0.1.tgz#a9029e929d3dbcded169f3c6e28238d95a5d5a28"
    integrity sha512-ErcKwJTF54uRzzNMXq2X5sMIy88zJvfN2DmdoQvy7PAFJ+tPRU6ydWuOKNMyfmOjdyBQTFREi60s0Y0SyI0G0g==
  
  xml2js@^0.4.5:
    version "0.4.23"
    resolved "https://registry.npmmirror.com/xml2js/-/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
    integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
    dependencies:
      sax ">=0.6.0"
      xmlbuilder "~11.0.0"
  
  xmlbuilder@>=11.0.1, xmlbuilder@^15.1.1:
    version "15.1.1"
    resolved "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-15.1.1.tgz"
    integrity sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==
  
  xmlbuilder@~11.0.0:
    version "11.0.1"
    resolved "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
    integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==
  
  xmlhttprequest-ssl@~2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.0.0.tgz"
    integrity sha512-QKxVRxiRACQcVuQEYFsI1hhkrMlrXHPegbbd1yn9UHOmRxY+si12nQYzri3vbzt8VdTTRviqcKxcyllFas5z2A==
  
  xregexp@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmmirror.com/xregexp/-/xregexp-2.0.0.tgz"
    integrity sha512-xl/50/Cf32VsGq/1R8jJE5ajH1yMCQkpmoS10QbFZWl2Oor4H0Me64Pu2yxvsRWK3m6soJbmGfzSR7BYmDcWAA==
  
  xtend@^4.0.0:
    version "4.0.2"
    resolved "https://registry.npmmirror.com/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
    integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==
  
  y18n@^5.0.5:
    version "5.0.8"
    resolved "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"
    integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==
  
  yallist@^3.0.2:
    version "3.1.1"
    resolved "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz"
    integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==
  
  yallist@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
    integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
  
  yargs-parser@^21.1.1:
    version "21.1.1"
    resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz"
    integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==
  
  yargs@^17.0.1, yargs@^17.5.1:
    version "17.7.1"
    resolved "https://registry.npmmirror.com/yargs/-/yargs-17.7.1.tgz"
    integrity sha512-cwiTb08Xuv5fqF4AovYacTFNxk62th7LKJ6BL9IGUpTJrWoU7/7WdQGTP2SjKf1dUNBGzDd28p/Yfs/GI6JrLw==
    dependencies:
      cliui "^8.0.1"
      escalade "^3.1.1"
      get-caller-file "^2.0.5"
      require-directory "^2.1.1"
      string-width "^4.2.3"
      y18n "^5.0.5"
      yargs-parser "^21.1.1"
  
  yargs@~3.10.0:
    version "3.10.0"
    resolved "https://registry.npmmirror.com/yargs/-/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
    integrity sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A==
    dependencies:
      camelcase "^1.0.2"
      cliui "^2.1.0"
      decamelize "^1.0.0"
      window-size "0.1.0"
  
  yauzl@^2.10.0:
    version "2.10.0"
    resolved "https://registry.npmmirror.com/yauzl/-/yauzl-2.10.0.tgz"
    integrity sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==
    dependencies:
      buffer-crc32 "~0.2.3"
      fd-slicer "~1.1.0"
  
  ylru@^1.2.0:
    version "1.3.2"
    resolved "https://registry.npmmirror.com/ylru/-/ylru-1.3.2.tgz"
    integrity sha512-RXRJzMiK6U2ye0BlGGZnmpwJDPgakn6aNQ0A7gHRbD4I0uvK4TW6UqkK1V0pp9jskjJBAXd3dRrbzWkqJ+6cxA==
