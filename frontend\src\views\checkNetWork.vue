<template>
    <teleport to='body'>
        <div class="check-net-work" v-if="!isPassNetWorkCheck">
            <div class="drag-enable error-message" v-if="isErrorMessageShow">
                <el-button type="primary" @click="isErrorMessageShow = false">关闭</el-button>
                <p v-for="str in errors">{{ str }}</p>
            </div>
            <div class="wrapper drag-enable primary" v-show="!isErrorMessageShow">

                <template v-if="checking">
                    <p>
                        <el-icon :size="30" class="is-loading">
                            <Loading class="loading" />
                        </el-icon>
                    </p>
                    <p style="margin-top: 16px;margin-bottom: 5px;">正在初始化{{msg}}</p>
                    <!-- <p class="txt">{{ msg }}</p> -->
                    <p class="m-t-16">
                        <el-button plain type="danger" @click="appStore.closeApp(false)">退出</el-button>
                    </p>
                </template>
                <template v-else>
                    <h3 class="danger m-b-16">网络错误</h3>
                    <p>
                        <!-- <el-button @click="changeCheckUrl">关闭</el-button> -->
                        <!-- <el-button @click="isErrorMessageShow = true">查看错误信息</el-button> -->
                        <el-button type="danger" @click="appStore.closeApp(false)">退出</el-button>
                        <!-- <el-button @click="check" type="primary">重新检测</el-button> -->
                    </p>
                </template>
            </div>
        </div>
    </teleport>
</template>
<script lang='ts' setup>
    import { ref } from 'vue';
    import { useAppStore } from '../stores/app'
    import { anyRequest } from '../apis/page';
    import { Loading } from '@element-plus/icons-vue'
    import { ElMessage, ElMessageBox } from 'element-plus';
    import { decrypt } from '../utils/common';
    import { ipc, fs } from '../apis/config';
    import { AxiosRequestConfig } from 'axios';

    const isPassNetWorkCheck = ref(false)
    const checking = ref(false)
    const appStore = useAppStore()
    const msg = ref('-')


    // try {
    //     fs.readFile('ip.txt', 'utf8', (err: any, data: any) => {
    //         if (err) {
    //             console.error(err);
    //             ElMessage.warning({
    //                 message: "请配置IP地址文件"
    //             })
    //             return;
    //         }
    //         console.log(data, '读取文件');
    //         check(data)
    //     });
    // } catch (error) {

    // }


    // let checkUrl = 'https://kh-1326811756.cos.ap-guangzhou.myqcloud.com/app.json'
    // const apiUrl = 'http://************:7798/'
    // let checkUrl = `http://admin-dev.yaohuo.site:8080/api/sim/serviceInfos`
    //let checkUrl = `http://***********:18636/api/sim/serviceInfos`
    //let checkUrl ='http://127.0.0.1:18636/api/sim/serviceInfos'
    //let checkUrl = 'http://************:8301/api/sim/serviceInfos'
    let checkUrl = 'http://************:2636/api/sim/serviceInfos'
    //let checkUrl = 'http://a.rtmaet.cn/api/sim/serviceInfos'

    // async function changeCheckUrl() {
    //     let { value } = await ElMessageBox.prompt('新的检测地址', '更换地址')
    //     value = value.trim()
    //     if (value.startsWith('http')) {
    //         checkUrl = value
    //         ElMessage.success('已更换为' + checkUrl)
    //     } else {
    //         ElMessage.warning({
    //             message: "无效地址"
    //         })
    //     }
    // }
    const isErrorMessageShow = ref(false)
    const errors = ref < string[] > ([])
    async function check () {
        errors.value = []
        // await checkPddNet()
        checking.value = true
        msg.value = '正在获取配置信息'

        anyRequest({
            url: checkUrl,
            method: "get",
        })
            .then(async res => {
                console.log(res, 'res');
                if (res.code == 0) {
                    // ipUrl = 'http://' + res.data + ':' + '7798'
                    //1.2.8版本更新，需要获取ip地址解开下面注释，注释下面IP地址

                    const urls = [res.data]
                    //const urls =['http://*************:2636']
                    //const urls =['http://***********:18636']
                    //const urls =['http://127.0.0.1:18636']
                    checUrls(urls)
                } else {
                    ElMessage.error('获取IP失败,请联系管理员')
                }
                // const { host_url, appid } = res
                // await ipc.invoke('controller.page.setAppId', appid)
                // await ipc.invoke('controller.page.setAppId', 66)
                // if (host_url && typeof host_url === 'string') {
                //     // const urls = decrypt(host_url)
                //     const urls = [ipUrl]

                //     if (urls && Array.isArray(urls)) {
                //         checUrls(urls)
                //     } else {
                //         return Promise.reject()
                //     }
                // } else {
                //     return Promise.reject()
                // }

            })
            .catch(() => {
                checUrls()
            })

    }
    // async function checkPddNet() {
    //     checking.value = true
    //     const res = await ipc.invoke("controller.page.anyRequest",{
    //         url:'https://mms.pinduoduo.com/login/',
    //         method:'get'
    //     })
    //     checking.value = false
    //     if(res && typeof res === 'string' && res.includes('拼多多商家后台')){
    //         return true
    //     }
    //     ElMessage.error('当前网络环境异常，请检查网络');
    //     return Promise.reject(false)
    // }

    check()
    async function checUrls (urls: string[] = ['http://************:8301/']) {
        console.log(urls, 'ip地址');
        // msg.value = `${String.fromCharCode(58 +　urls.length)}`
        // console.log(urls)
        msg.value = '.'.repeat(urls.length)
        // appStore.urls = urls
        // urls = ['http://**************:86']
        let _index = -1
        const result = urls.map((url, index) => {
            return new Promise((resolve, reject) => {
                const config: AxiosRequestConfig = {
                    url: url + '/api/Index/getAppInfo',
                    method: 'post',
                    // @ts-ignore
                    time_diff: appStore.time_diff
                }

                let current = 1
                const max = 2
                const fn = () => {
                    ipc.invoke("controller.page.request", config)
                        .then(res => {
                            console.log(res, '!');

                            // console.log(res,config,123)
                            if (typeof res === 'object' && typeof res.data === 'object' && _index === -1 && res.code === 0) {
                                _index = index
                                appStore.currentUrl = url
                                appStore.urlIndex = _index
                                appStore.urls = urls
                                finish(res.data)
                                console.log(JSON.parse(res.data.system.value), '???!');

                                resolve(true)
                            } else {

                                return Promise.reject(res)
                            }
                        })
                        .catch((res) => {
                            current++
                            if (_index === -1 && current <= max) {
                                fn()
                            } else {
                                try {
                                    errors.value.push(`${config.url?.split('.')[0] || '-'}-${res.code}-${res.msg}`)
                                } catch (e) {
                                    errors.value.push(`${config.url?.split('.')[0] || '-'}-${String(e)}`)
                                }
                                reject()
                            }
                        })
                }
                fn()
            })
        })
        await Promise.allSettled(result)
        if (_index === -1) {
            finish()
        }
    }

    function finish (appInfo?: typeof appStore.info) {
        if (!appInfo) {
            checking.value = false
        } else {
            isPassNetWorkCheck.value = true
            appStore.info = appInfo
            appStore.getAppInfoRepeat()
        }
    }
</script>
<style lang='scss' rel="stylesheet/scsss" scoped>
    .check-net-work {
        position: fixed;
        width: 100vw;
        height: 100vh;
        background-color: rgba(#000, .2);
        left: 0;
        top: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        .error-message {
            width: 400px;
            height: 300px;
            overflow-y: auto;
            box-shadow: var(--el-box-shadow);
            background-color: #fff;
            border-radius: 4px;
            padding: 12px;
            box-sizing: border-box;
            position: relative;

            .el-button {
                position: absolute;
                right: 12px;
                bottom: 12px;
            }

            p {
                font-size: 12px;
                margin-bottom: 5px;
            }
        }

        .wrapper {
            border-radius: 5px;
            width: 260px;
            height: 160px;
            box-shadow: var(--el-box-shadow);
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 24px;
            font-size: 12px;
        }

        p.txt {
            color: var(--el-color-info);
        }
    }
</style>