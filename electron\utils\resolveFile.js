const jschardet = require('jschardet')
const iconv = require('iconv-lite');
const fs = require('fs')
function resolveTXT(path){
    return new Promise((resolve,reject)=>{
        fs.readFile(path, { encoding:'binary' }, (error, data) => {
            if (error) {
              reject(error);
            } else {
              // const buffer = new Buffer(data,'binary')
              if(!data){
                resolve('')
                return
              }
              const buffer = Buffer.from(data,'binary')
              let {encoding} = jschardet.detect(buffer)
              // console.log('encoding',encoding)
              if(!encoding || encoding.startsWith('window') || encoding.includes('KOI8')){
                encoding = 'GBK'
              }
              var str = iconv.decode(buffer,encoding);
              resolve(str)
            }
          });
    })
}

function deleteFolderHandle(dirPath) {
  let files = [];
  if( fs.existsSync(dirPath) ) {
      files = fs.readdirSync(dirPath);
      files.forEach(function(file,index){
          let curPath = dirPath + "/" + file;
          if(fs.statSync(curPath).isDirectory()) {
            deleteFolderHandle(curPath);
          } else {
              fs.unlinkSync(curPath);
          }
      });
      fs.rmdirSync(dirPath);
  }
}

module.exports =  {
    resolveTXT,
    deleteFolderHandle
}