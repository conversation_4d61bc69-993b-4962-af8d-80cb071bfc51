type anyObj = {
  [key: string]: any;
};
type EChartsOption = echarts.EChartsOption;

type Pagination = {
  page: number;
  limit: number;
  total: number;
  pageSizes?: number[];
};

type goodsReadRecord = GoodsInfo & {
  id: number;
  add_time: number | string;
};
type goodsReadRecordInDateBase = Omit<
  goodsReadRecord,
  "skus" | "group_order_ids" | "group_id"
> & {
  skus: string;
  group_order_ids: string;
  group_id: string;
};

type AddressItem = {
  name: string;
  phone: string;
  province: string;
  city: string;
  district: string;
  address: string;
};

type GoodsInfo = {
  goods_name: string;
  goods_id: string | number;
  goods_img: string;

  activity_id: number;
  mallId: string | number;
  mallName: string;
  groupPrice: number;
  normalPrice: number;

  skus: Array<{
    skuId: number;
    spec: string;
    skuImg: string;
    groupPrice: number;
    normalPrice: number;
  }>;
  group_order_ids: number[];
  group_id: {
    alone?: number;
    multiple: number[];
  };
  coupon_code: number | string;
};

type Store = {
  id: number;
  mallId: number;
  mallName: string;
  cookieJSON: string;
  infoJSON: string;
  update_time: number;
  status: number;
};
type responseData<T = any> = {
  code: number;
  data: T;
  msg: string;
  _requestCount?:number
  _error_his?:responseData[]
};

type exportExcelRule = {
  label: string;
  value: (row: anyObj) => string;
  "!cols"?: anyObj;
};

type LogItem = {
  time?: string;
  type?: "danger" | "info" | "primary" | "warning" | "success";
  msg: string;
  [key: string]: any;
};