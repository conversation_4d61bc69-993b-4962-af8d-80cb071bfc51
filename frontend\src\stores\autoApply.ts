import { ElMessage } from "element-plus";
import { defineStore } from "pinia";
import { resolveApply, updateOrder } from "../apis/page";
import { useSetting } from "./setting";
import { random } from "../utils/common";
import { ipc } from "../apis/config";

type ApplyTask = {
  url: string;
  order_sn: string;
  id: number | string;
  type: "auto" | "code";
};
export const useAutoApply = defineStore("auto-apply", {
  state: () => {
    const state: {
      pending: ApplyTask[];
      applyTid?: NodeJS.Timer;
      lastExecTime: number;
      lastCycleTime: number;
    } = {
      pending: [],
      // loading: [],
      lastExecTime: 0,
      lastCycleTime: 0,
    };
    return state;
  },
  actions: {
    addToPendding(list: ApplyTask[]) {
      // console.log(list)
      // return
      if (!list.length) {
        return;
      }
      const set = new Set<ApplyTask["id"]>();
      this.pending.forEach((item) => {
        set.add(item.id);
      });
      let repeatFlag = false;
      list.forEach((item) => {
        if (set.has(item.id)) {
          repeatFlag = true;
        } else {
          set.add(item.id);
          this.pending.push(item);
        }
      });
      repeatFlag &&
        ElMessage.warning({
          message: "检测到重复订单，已过滤",
          grouping: true,
        });

      this.startExec();
    },
    startExec() {
      if (this.applyTid) {
        return;
      }
      // console.log(this.lastCycleTime, this.lastExecTime);
      this.applyTid = setTimeout(() => {
        const settingStore = useSetting();
        const { max, min, active } = settingStore.visit.payDelay;
        this.applyTid = void 0;
        this.lastCycleTime = Date.now();
        if (!this.pending.length) {
          return;
        }
        if (active) {
          if (Date.now() - this.lastExecTime > random(min, max) * 1000) {
            this.pendingToLoading(this.pending.shift()!);
          }
        } else {
          while (this.pending.length) {
            this.pendingToLoading(this.pending.shift()!);
          }
        }
        this.startExec();
      }, 1000);
    },
    pendingToLoading(item: ApplyTask) {
      this.lastExecTime = Date.now();
      if (item) {
        const settingStore = useSetting();
        const data = {
          // type: "auto",
          item: { ...item },
          win: {
            pass: settingStore.pay.zfb_pass,
            passArr: [...settingStore.zfbPassArr],
          },
        };
        if (settingStore.pay.isNew) {
          ipc.invoke("controller.autoApply.autoApply", data);
        } else {
          resolveApply(data);
        }
      }
    },
    stop() {
      this.pending = [];
      this.applyTid = void 0;
      this.applyTid && clearTimeout(this.applyTid);
    },
    resolveResult(data: Omit<ApplyTask, "url" | "type">) {
      if (data.id) {
        updateOrder(
          {
            ids: String(data.order_sn),
          },
          { showErrorMsg: false }
        );
      }
      this.startExec();
    },
  },
});
