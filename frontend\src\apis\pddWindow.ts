import { ElMessage } from "element-plus";
import { ipc } from "./config";
import { useSetting } from "../stores/setting";
import { anyRequest } from "./page";

const ipcRoute = {
  open: "controller.pddWindow.open",
  logout: "controller.pddWindow.logout",
  resize: "controller.pddWindow.resize",
  changeUrl: "controller.pddWindow.changeUrl",
  goodsInfo: "controller.pddWindow.goodsInfo",
  loginPddStorePage: "controller.pddWindow.loginPddStorePage",
  getGoodsDetails: "controller.pddWindow.getGoodsDetails",
};

type pddOptions = {
  x: number;
  y: number;
  width: number;
  height: number;
  scale: number;
};
export function pddWindowController(fn: string, data?: any) {
  return ipc.invoke('controller.pddWindow.' + fn, data)
}
export function openPddWindow(data?: pddOptions) {
  const { pddHeight, pddWidth, pddx, pddy, scale } = useSetting().appWindow;
  let _data = data || {
    width: pddWidth,
    height: pddHeight,
    x: pddx,
    y: pddy,
    scale,
  };
  return ipc.invoke(ipcRoute.open, { ..._data }).catch((res) => {
    ElMessage.warning({
      message: "打开拼多多窗口出错,请检查网络是否正常！",
      grouping: true,
    });
  });
}

export function resize(data?: pddOptions) {
  const { pddHeight, pddWidth, pddx, pddy, scale } = useSetting().appWindow;
  let _data = data || {
    width: pddWidth,
    height: pddHeight,
    x: pddx,
    y: pddy,
    scale,
  };
  return ipc.invoke(ipcRoute.resize, _data);
}

/**
 *
 * @param url  'back'后退 'next'前进 'home'首页 'reload'刷新 'reloadIgnoringCache'强制刷洗
 * @returns
 */
export function changePddWindowUrl(url: string, clear = false) {
  return ipc.invoke(ipcRoute.changeUrl, { url, clear });
}

export function getGoodsInfo() {
  return ipc.invoke(ipcRoute.goodsInfo);
}

export function loginPddStorePage(v: Store) {
  return ipc.invoke(ipcRoute.loginPddStorePage, { ...v });
}

export function getGoodsDetails(data: {
  goodsId: number | string;
  tk: string;
}): Promise<string> {
  return ipc.invoke(ipcRoute.getGoodsDetails, { ...data }).then((res) => {
    if (res) {
      return res;
    } else {
      return Promise.reject(res);
    }
  });
}

export function logout() {
  return ipc.invoke(ipcRoute.logout)
}

export function uploadImageByBase64(data: {
  "upload_sign": string
  image: string
}) {
  return anyRequest({
    url: "https://file.pinduoduo.com/v2/store_image",
    method: "post",
    headers: {
      accept: "*/*",
      "accept-language": 'zh-CN,zh;q=0.9',
      "content-type": "application/json;charset=UTF-8",
      "cookie": "_bee=YLyAxX6fC80AB3BmqEKVa2iKnSkSfVlq; _f77=c56a3e0b-ea91-4e7e-8e25-710467e79367; _a42=cf5be628-c258-47cc-99b9-c1f9df72e215",
      "origin": "https://mobile.yangkeduo.com",
      "referer": "https://mobile.yangkeduo.com/",
    },
    data
  })
}