import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import {
  createStyleImportPlugin,
  VxeTableResolve,
} from "vite-plugin-style-import";
import { resolve } from "path";
const pathResolve = (dir: string) => {
  return resolve(__dirname, ".", dir);
};
// https://vitejs.dev/config/
export default defineConfig({
  base: "./",
  build: {
    outDir: resolve("./dist"),
    // assetsDir:'assets'
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    createStyleImportPlugin({
      resolves: [VxeTableResolve()],
    }),
  ],

  resolve: {
    alias: {
      "/@": pathResolve("./src/"),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "/src/assets/css/varsScss.scss";',
      },
    },
  },

  server: {
    port: 2341,
    open: false,
    host: "0.0.0.0",
    hmr:true
  },
});
