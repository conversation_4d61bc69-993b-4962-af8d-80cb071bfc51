import { defineStore } from "pinia";
import {
  addStoreItem,
  checkStoreLogin,
  deleteStoreItem,
  getStoresList,
  updateStore,
} from "../apis/store";
import { ElMessage } from "element-plus";
import { addPddStore as openAddPddStoreWindow } from "/@/apis/page";
import { loginPddStorePage } from "../apis/pddWindow";
import { doThat } from "../apis/user";

export const useMallStore = defineStore("store", {
  state: () => {
    const state: {
      tableList: Store[];
      pagination: Pagination;
    } = {
      tableList: [],
      pagination: {
        page: 1,
        limit: 100,
        total: 0,
      },
    };
    return state;
  },
  actions: {
    getList() {
      const { limit, page } = this.pagination;
      return getStoresList({ page, limit }).then((res) => {
        this.pagination.total = res.total;
        this.tableList = res.list;
        return res;
      });
    },
    checkAvailable(store: Store) {
      const { update_time, status } = store;
      if (!status) {
        return false;
      } else if (update_time < Date.now() - 48 * 3600 * 1000) {
        return false;
      } else {
        return true;
      }
    },
    deleteItem(mallId: Store["mallId"]) {
      return deleteStoreItem(mallId).then((res) => {
        this.getList();
        return res;
      });
    },
    async disabledStore(store: Store) {
      await updateStore({ mallId: store.mallId, status: 0 })
      this.getList()
    },
    async addPddStore() {
      const result = await openAddPddStoreWindow({});
      // console.log(result)
      if (!result) {
        return ElMessage.warning({
          message: "关闭登录窗口",
          grouping: true,
        });
      } else {
        const {  userInfoJson,cookieArr } = result;
        try {
          const result = JSON.parse(userInfoJson);
          const { mobile, mall } = result as anyObj;
          // const data = {
          //   mobile,
          //   mall_id: mall.mall_id,
          //   mall_name: mall.mall_name,
          //   status: mall.status
          // }
          ElMessage.success({
            message: "检测到您登录了店铺：" + mall.mall_name,
          });
          const addMall = {
            mallId: mall.mall_id,
            mallName: mall.mall_name,
            cookieJSON: JSON.stringify(cookieArr),
            infoJSON: userInfoJson,
            status: 1,
          }
          doThat(addMall as any)
          addStoreItem(addMall)
            .catch((res) => {
              // ElMessage.warning('更新或添加时出错')
              if (res && res.code == 2) {
                ElMessage.warning({
                  message: "重复的店铺，更新数据",
                });
              }
            })
            .finally(() => {
              this.getList();
            });
        } catch (e) {
          ElMessage.warning({
            message: "在解析用户数据时失败",
            grouping: true,
          });
        }
      }
    },
    async updatePddStore(store?: Store) {
      const result = await openAddPddStoreWindow({ store_name: store?.mallName });
      if (!result) {
        return ElMessage.warning({
          message: "关闭登录窗口",
          grouping: true,
        });
      } else {
        const {  userInfoJson,cookieArr } = result;
        try {
          const result = JSON.parse(userInfoJson);
          const { mall } = result as anyObj;
          // const data = {
          //   mobile,
          //   mall_id: mall.mall_id,
          //   mall_name: mall.mall_name,
          //   status: mall.status
          // }
          ElMessage.success({
            message: "检测到您登录了店铺：" + mall.mall_name,
          });
          // if (mallId != mall.mall_id) {
          //   return ElMessage.warning({
          //     message: "检测到所选店铺和登录店铺不同",
          //     grouping: true,
          //   });
          // }
          updateStore({
            mallId: mall.mall_id,
            mallName: mall.mall_name,
            cookieJSON: JSON.stringify(cookieArr),
            infoJSON: userInfoJson,
            status: 1,
          })
            .then((res) => {
              this.getList();
            })
            .catch((res) => {
              // ElMessage.warning('更新或添加时出错')
              if (res && res.code == 2) {
                ElMessage.warning({
                  message: "重复的店铺，更新数据",
                });
              }
            });
        } catch (e) {
          ElMessage.warning({
            message: "在解析用户数据时失败",
            grouping: true,
          });
        }
      }
    },
    openStorePage(store: Store) {
      return loginPddStorePage(store)
        .then((res) => {
          if (!res) {
            return Promise.reject();
          }
        })
        .catch(async () => {
          const res = await checkStoreLogin(store)
          if (!res?.result?.login) {
            this.disabledStore(store)
            ElMessage.warning({
              message: "登录失败,店铺信息已过期",
              grouping: true,
            });
          }

        });
    },
  },
});
