<template>
  <div class="screen-video" @click="openDialog" @mouseenter="state.controlsShow = true"
    @mouseleave="state.controlsShow = false">
    <video class="page" :src="src" :poster="poster" :controls="false" v-bind="$attrs"></video>
    <Transition name="fade">
      <div class="controls" v-show="state.controlsShow" @click.stop="emits('action','delete')">
        <span>删</span>
        <span>除</span>
      </div>
    </Transition>
    <el-dialog :width="886" v-model="state.dialog" class="screen-video-dialog" :append-to-body="true">
      <div class="video-container">
        <video ref="video" :src="src" :poster="poster" :controls="true" v-bind="$attrs"></video>
        <div class="close" @click="state.dialog = false">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { Close } from "@element-plus/icons-vue";
const props = defineProps<{
  src: string;
  poster?: string;
}>();

type Action = 'delete'
const emits = defineEmits<{
  (e: 'action',data:Action): void
}>()

const state = reactive({
  dialog: false,
  controlsShow: false,
});

const video = ref<HTMLVideoElement>();

function openDialog() {
  state.dialog = true;
}
watch(
  () => state.dialog,
  (value) => {
    if (!value) {
      video.value?.pause();
    }
  }
);

defineExpose({
  openVideo: openDialog
})
</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
.screen-video {
  width: 100%;
  height: 100%;
  position: relative;

  .controls {
    width: 20%;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-color: rgba($color: #000000, $alpha: .5);
    color: #fff;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

video.page {
  width: 100%;
  height: 100%;
  object-fit: fill;
  cursor: pointer;
}
</style>

<style lang="scss">
.el-dialog.screen-video-dialog {
  background: transparent;
  // margin-left: 550px !important;
  .video-container {
    position: relative;
    background: transparent;
    border: var(--el-color-primary) 1px dotted;
    // max-height: 500px;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 640px;
  }

  .close {
    position: absolute;
    right: 36px;
    top: 36px;
    width: 36px;
    height: 36px;
    box-sizing: border-box;
    border-radius: 50%;
    transform: translate(50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #ffffff;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.47);

    .el-icon {
      font-size: 16px;
      color: #fff;
    }
  }

  video {
    max-width: 886px;
    max-height: 640px;
    border-radius: 5px;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__header {
    padding: 0;
    display: none;
  }
}
</style>
