<template>
  <div class="change-sales c-s-gift no-padding">
    <el-config-provider size="default">
      <div class="settings">
        <div class="item account">
          <header>
            <h3>1.账号配置</h3>
          </header>
          <div class="content">
            <el-form label-position="top" size="small">
              <el-form-item label="店铺:">
                <el-select v-model="config.mallId" @change="storeChange" :disabled="loading.start">
                  <el-option v-for="store in mallStore.tableList" :label="store.mallName" :value="store.mallId">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否旗舰店">
                <el-switch v-model="config.isFlagshipStore" active-text="是" inactive-text="否"
                  @change="(e) => { e || (config.recover = false) }"></el-switch>
              </el-form-item>
              <el-form-item label="小号位置:">
                <el-input-number v-model="start_site" :controls="false" style="flex-grow: 1;"></el-input-number>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="item buy">
          <header>
            <h3>2.购买配置</h3>
          </header>
          <div class="content">
            <el-form label-position="top" :inline="false" size="small" style="justify-content: flex-start;">

              <el-form-item label="购买数量" style="flex-shrink: 0;">
                <el-radio-group v-model="config.num_type" style="margin-bottom:10px ;">
                  <el-radio-button label="random">随机</el-radio-button>
                  <el-radio-button label="random-range">范围随机</el-radio-button>
                  <el-radio-button label="appoint">指定</el-radio-button>

                </el-radio-group>
                <el-space v-show="config.num_type === 'appoint'">
                  <el-input-number style="width: 77px;" :controls="false" v-model="config.num_appoint" :precision="0"
                    :min="101"></el-input-number>
                  <span>件</span>
                </el-space>
                <el-space v-show="config.num_type === 'random'">
                  <el-input-number style="width: 58px;" :controls="false" v-model="config.num_times" :precision="0"
                    :min="3" :max="5"></el-input-number>
                  <span>位数</span>
                </el-space>
                <el-space v-show="config.num_type === 'random-range'" :size="2">
                  <el-input-number style="width: 77px;" :controls="false" v-model="config.randomRage.v1" :precision="0"
                    :min="101"></el-input-number>
                  <span>-</span>
                  <el-input-number style="width: 77px;" :controls="false" v-model="config.randomRage.v2" :precision="0"
                    :min="101"></el-input-number>
                </el-space>
                <el-tooltip :show-after="500" placement="right">
                  <el-space style="margin-top: 5px;">
                    <span>同步老商品信息</span>
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-space>
                  <template #content>
                    <p>将炮灰链接信息同步至老商品信息</p>
                    <p>只有旗舰店下单方式才可同步</p>
                    <!-- <p>旗舰店方式下单一次最多购买数量9505</p>
                    <p>非旗舰店也可以按照旗舰店方式下单</p> -->
                  </template>
                </el-tooltip>
                <el-switch :disabled="!config.isFlagshipStore" v-model="config.recover" active-text="同步"
                  inactive-text="不同步"></el-switch>
                <el-space>
                  <div>每单最大销量</div>
                  <el-select v-model="config.order_per_max_count" style="width: 80px;">
                    <el-option label="200001(仅工商注册分类)" :value="200001" v-if="!config.isFlagshipStore"></el-option>
                    <el-option label="9505" :value="9505"></el-option>
                    <el-option label="3851" :value="3851"></el-option>
                    <el-option label="359" :value="359"></el-option>
                  </el-select>
                </el-space>
              </el-form-item>
              <el-form-item label="其他">
                <p>
                  <el-button style="width: 150px;" type="success" plain @click="getTempGoodsList">获取炮灰商品</el-button>
                </p>
                <p class="m-t-10">
                  <el-button :loading="tableProps.loading" :disabled="!tableState.selection.length"
                    style="width: 150px;" type="danger" plain @click="offSale">下架选中</el-button>
                </p>
                <p style="margin-top: 10px">

                  <el-checkbox title="【炮灰商品的主图sku图详情图使用内置图片】。如果主链接主图，详情图有问题(例如带价格)，导致改销量链接被驳回，可以使用软件内置图片发布新商品"
                    v-model="config.useDefaultImage" label="创建商品使用内置图片"></el-checkbox>

                </p>
                <p>
                  <!-- <el-space>
                    <span>下单方式：</span>
                    <el-select v-model="config.order_type" style="width: 84px;" :disabled="loading.start">
                      <el-option label="多多果园" value="guoyuan"></el-option>
                      <el-option label="ID拍单" value="gift"></el-option>
                    </el-select>
                  </el-space> -->
                  <el-checkbox label="下单使用线路2" v-model="config.line2"></el-checkbox>
                </p>
                <p>
                  <el-select v-model="config.cat_id" style="width: 120px">
                    <el-option label="商品分类：默认" :value="0"></el-option>
                    <el-option v-for="item in catData" :label="item.cats.join('-')" :value="item.cat_id"></el-option>
                  </el-select>
                  <el-tooltip placement="top">
                    <el-icon style="margin-left: 5px;">
                      <QuestionFilled />
                    </el-icon>
                    <template #content>
                      <p>默认：旗舰店为选中商品分类，其他为默认下的第一个分类</p>
                      <p>如果提示【商品发布失败】【商品检测失败】【商品价格检测失败】等商品操作相关的错误，请切换到其他或默认分类</p>
                    </template>
                  </el-tooltip>
                </p>
              </el-form-item>
              <el-form-item label="炮灰循环">
                <p>
                  <el-switch active-text="开启" v-model="config.isTempLoop" inactive-text="关闭"></el-switch>
                </p>
                <div v-show="config.isTempLoop">
                  <p class="between-flex" style="margin: 10px 0;">
                    <el-radio-group v-model="config.tempLoopType">
                      <el-radio-button label="random">随机生成</el-radio-button>
                      <el-radio-button label="appoint">指定炮灰({{ appointTempGoodsIds.length }})</el-radio-button>
                    </el-radio-group>
                    <el-tooltip>
                      <el-icon style="margin-left: 5px;">
                        <QuestionFilled />
                      </el-icon>
                      <template #content>
                        <p>随机生成：系统自动在店铺生成炮灰链接</p>
                        <p>指定炮灰：用户自行选择店铺【已上架】的商品作为炮灰链接</p>
                        <p>注意：请不要将正常出售的商品作为炮灰链接。</p>
                        <p>炮灰商品要求:商品价格大于4000</p>
                        <p>sku数量为1</p>
                      </template>
                    </el-tooltip>
                  </p>
                  <div v-show="config.tempLoopType === 'appoint'">
                    <el-input type="textarea" :rows="2" resize="none" placeholder="请输入炮灰商品ID，多个用逗号隔开,建议至少保证2个可用"
                      v-model="config.appointTempGoodsId"></el-input>
                  </div>
                  <div v-show="config.tempLoopType === 'random'">
                    <el-button :disabled="tempGoodsCache.length === 0" @click="tempGoodsCache = []">清空炮灰商品缓存({{
                      tempGoodsCache.length
                    }})</el-button>
                  </div>
                </div>
                <p>
                  <el-tooltip>
                    <template #content>
                      <p>启动后每个订单sku价格5.5元,单个订单最多拍359销量</p>
                      <p>如果销量需求太高会造成下单量过多</p>
                    </template>
                    <el-space>
                      <el-checkbox v-model="config.auto_increase_price" :disabled="loading.start"
                        label='规避低价限制'></el-checkbox>
                      <el-icon>
                        <QuestionFilled />
                      </el-icon>
                    </el-space>
                  </el-tooltip>
                </p>


                <!-- <p v-show="config.isTempLoop">
                  <el-space>
                    <span>每个炮灰循环间隔</span>
                    <el-input-number :controls="false" v-model="config.tempLoopDelay" :precision="0" :min="50"
                      :max="120" style="width: 50px;"></el-input-number>
                    <span>秒</span>
                  </el-space> 
                </p>
                <p v-show="config.isTempLoop">
                  <el-button :disabled="tempGoodsCache.length === 0" @click="tempGoodsCache = []">清空炮灰商品缓存({{
                  tempGoodsCache.length
                }})</el-button>
                </p> -->
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="item ctrl">
          <header>
            <h3>3.控制台</h3>
          </header>
          <div class="content">

            <p>
              <!-- <span>催付金额：自动运算最低</span>
              <el-checkbox v-model="config.lowPriceSku" label="获取最低价SKU"></el-checkbox> -->
            </p>
            <p><el-button type="success" @click="getGoodsList()" :loading="tableProps.loading">获取商品</el-button></p>
            <p><el-button type="primary" @click="start" :loading="loading.start">开始</el-button></p>
            <p><el-button type="primary" :disabled="loading.stop || !loading.start"
                @click="addToLog({ msg: '已发送停止指令' }); loading.stop = true">停止</el-button></p>

            <p>
              <el-space>
                <el-checkbox @change="e => e && (defenseDialogShow = true)"
                  :label="`防薅羊毛(${defenseConfig.appointAddrList.length})`" v-model="defenseConfig.active"></el-checkbox>
                <el-link :underline="false" @click="defenseDialogShow = true">打开设置</el-link>
              </el-space>
            </p>
          </div>
        </div>
      </div>
      <div class="table-container">
        <!-- <div class="table-header">
          <el-input>商品ID</el-input>
        </div> -->
        <VxeTable ref="tableRef" :="{ ...tableProps, ...tableEvents }">
          <VxeColumn type='checkbox' :width="50"></VxeColumn>
          <VxeColumn type='seq' title="序号" :width="50"></VxeColumn>
          <VxeColumn title="商品ID" field="id" :width="130"></VxeColumn>
          <VxeColumn title="商品名称" field="goods_name"></VxeColumn>
          <VxeColumn title="SKU">

            <template #default='scope'>
              <el-select v-model="scope.row._skuSelect" @click="scope.row._sku_list = scope.row.sku_list || []">
                <el-option v-for="item in scope.row._sku_list || []" :label="item.spec || scope.row.goods_name"
                  :value="item.skuId"></el-option>
                <el-option label="系统自己选" :value="0"></el-option>
              </el-select>
            </template>
          </VxeColumn>
          <VxeColumn title="SKUID" :width="140">

            <template #default='scope'>
              {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.skuId }}
            </template>
          </VxeColumn>
          <VxeColumn title="SKU拼团价格">

            <template #default='scope'>
              {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.groupPrice / 100 }}
            </template>
          </VxeColumn>
          <VxeColumn title="创建时间" field="created_at" :formatter="({ row }) => dateFormat(row.created_at)" sortable>
          </VxeColumn>
          <VxeColumn title="库存">

            <template #default='scope'>
              {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.skuQuantity }}
            </template>
          </VxeColumn>
          <VxeColumn title="商品销量" field="sold_quantity" sortable :width="80"></VxeColumn>
          <VxeColumn title="状态" field="" :width="150" sortable
            :sort-by="({ row }) => rowStatusMap.get(row.id)?.msg || ''">
            <template #default='scope'>
              <span :class="rowStatusMap.get(scope.row.id)?.type">{{ rowStatusMap.get(scope.row.id)?.msg }}</span>
            </template>
          </VxeColumn>
        </VxeTable>
        <footer class="table-footer">
          <p>

            <el-space>
              <span></span>
            </el-space>
            <el-space>
              <el-link :underline="false" @click="tableAction('check-all')">全选</el-link>
              <el-link :underline="false" @click="tableAction('reverse')">反选</el-link>
              <el-link :underline="false" @click="tableAction('area-select')">区域选择</el-link>
              <!-- <el-link :underline="false" @click="tableAction('id-check')">商品id选择</el-link> -->
              <el-link :underline="false" @click="tableAction('clear')">取消选择</el-link>
              <el-input @keyup.enter="getGoodsList()" placeholder="ID搜索,多个逗号隔开" v-model="state.goods_id"></el-input>
              <el-link :underline="false" @click="inputGoodsIdMultiple">大量输入</el-link>
            </el-space>
          </p>
          <p>
          <div>
            <!-- 当前总计 <span class="primary">{{ `${msgState.current}/${msgState.max}` }}</span>,
            已完成 <span class="success">{{ msgState.success }}</span>,
            获取拼团ID失败 <span class="danger">{{ msgState.get_group_id_false }}</span> -->
          </div>
          <paginationVue @size-change="() => {
            pagination.page = 1
            getGoodsList()
          }" @current-change="getGoodsList()" :total="pagination.total" :page-sizes="pagination.pageSizes"
            v-model:current-page="pagination.page" v-model:page-size="pagination.limit" />
          </p>
        </footer>

      </div>
      <LogVue v-model:list="state.logList" height="90px" />
      <el-dialog :width="1000" v-model="checkTempGoodsErrorState.show" title="检测结果"
        @close="checkTempGoodsErrorState.errors = []" :close-on-press-escape="false">

        <div class="contaier" style="height: 520px;overflow-y: auto">
          <div class="card" style="margin-bottom: 12px;border: var(--el-border);border-radius: 5px;padding: 12px"
            v-for="item in checkTempGoodsErrorState.errors">
            <h4>{{ item.goodsInfo.id }}-{{ item.goodsInfo.goods_name }}</h4>
            <div class="msg" style="padding: 12px">
              {{ item.msg }}
            </div>
          </div>
        </div>
      </el-dialog>
      <el-dialog v-model="defenseDialogShow" title="防薅羊毛设置" append-to-body :close-on-press-escape="false">
        <el-alert type="error" :closable="false">
          <p>请选择 <strong>【收货地址】</strong>所在省，【没选择】的地区将无法下单</p>
          <p>注意：如果是自定义地址下单，请确保你的自定义地址所在省份被勾选</p>
          <p>注意：【全选和不选】都相当于【不开启】此功能</p>
          <p>注意：开启此功能后，下单设置中的【过滤省份】【指定省份】会【失效】，指定省份以此处勾选的为主</p>
        </el-alert>
        <el-space style="margin: 12px 0">
          <el-link :underline="false" type="primary" @click="defenseConfigAddrAction('all')">全选</el-link>
          <el-link :underline="false" type="primary" @click="defenseConfigAddrAction('reverse')">反选</el-link>
          <el-link :underline="false" type="primary" @click="defenseConfigAddrAction('clear')">取消</el-link>
        </el-space>
        <p class="success">请选择 收货地址 所在省份</p>
        <el-checkbox-group v-model="defenseConfig.appointAddrList">
          <el-checkbox v-for=" item in provinceList " :label="item.id">{{ item.regionName }}</el-checkbox>
        </el-checkbox-group>
        <template #footer>
          <el-button @click="defenseDialogShow = false">关闭弹窗</el-button>
        </template>
      </el-dialog>

    </el-config-provider>
  </div>
</template>

<script lang='ts' setup>
import { computed, h, reactive, ref, watch } from 'vue'
import { useMallStore } from '/@/stores/store';
import { useSetting } from '/@/stores/setting';
import { usePersistenceRef } from '/@/hooks/ref';
import { useTable } from '/@/hooks/useTable';
import { VxeTableInstance, VxeTextarea } from 'vxe-table';
import { ElMessage, ElMessageBox } from 'element-plus';
import { addAddress, antiContentFromWeb, batchOffSale, changeSkuPrice, closeMerchantCoupone, createGoodsId, createGoodsInfo, createMerchantGoods, getCouponList, getMerchantCouponV2, getUpLoadData, goodsSubmit, newGoodsCommitId, pddStoreGoodsDetails, quickOrder, storeGoodsList, transFromData } from '/@/apis/changeSales';
import paginationVue from '/@/components/pagination/pagination.vue';
import LogVue from '/@/components/Log/log.vue'
import { batchRequest, delayPromise, random, retry, pddResErrorMsg, pageStepRequest, dateFormat } from '/@/utils/common';
import { useSubAccount } from '/@/stores/pageData';
import { changeSkuPirceList, createPddGoodsInfo, tempGoodsNames, } from '/@/utils/porpery';
import { cancelGiftGoodsApi, createGiftGoodsApi, createMateria, giftGoodsListApi, giftMainGoodsSku, materialInfo, rejectGoodsListApi, storeCreateFile, storeGoodsListApi, uploadImageToStore } from '/@/apis/store';
import { autoSupply, getApplyInfo, getPifaGroupId } from '/@/apis/page';
import { getRandomItem } from '/@/apis/address';
import { urlToBase64 } from '/@/utils/file';
import { cloneDeep } from 'lodash';
import { useAutoApply } from '/@/stores/autoApply';
import { QuestionFilled } from '@element-plus/icons-vue'
import { RequestData, TempGoodsCacheItem } from '.';
import dayjs from 'dayjs'
import { isUserCanUse } from '../../utils';
import { catData, provinceList } from '/@/utils/pddTempGoodsData';
import { costTemplateListApi, createCostTemplateApi, query_filtered_mall_goods_list_api } from '/@/apis/pddStore';

const mallStore = useMallStore()

const rowStatusMap = ref<Map<number, { msg: string, type?: string }>>(new Map())


function resetRecycleConfig() {
  config.tempLoopType = 'random'
  config.isTempLoop = false
  config.appointTempGoodsId = ''
  tempGoodsCache.value = []
}

const storeChange = () => {
  const { mallId } = config
  const mall = mallStore.tableList.find(item => item.mallId === mallId)
  if (mall) {
    const result = /(旗舰|专卖|专营)/.test(mall.mallName)
    config.isFlagshipStore = result
    if (!result) {
      config.recover = false
    }
  }
  resetRecycleConfig()
}

async function offSale() {
  const list = tableState.selection.map(item => item.id)
  if (!list.length) {
    addToLog({ msg: '没有可下架商品', type: 'danger' })
    return
  }
  const mall = mallStore.tableList.find(item => item.mallId === config.mallId)
  if (!mall) {
    addToLog({ msg: '没有找到店铺信息', type: 'danger' })
    return
  }
  addToLog({ msg: '执行批量下架商品', type: 'info' })

  const res = await batchOffSale(mall, list)
  if (res.success) {
    addToLog({ msg: '下架成功', type: 'success' })
  } else {
    addToLog({ msg: '下架失败', type: 'danger' })
  }
  pagination.page = 1
  pagination.limit = 100
  await delayPromise(1500)
  getGoodsList()

}

const defenseDialogShow = ref(false)
const defenseConfig = usePersistenceRef({
  active: false,
  appointAddrList: [] as number[]
}, 'cs-gift-defenseConfig',)

function defenseConfigAddrAction(type: 'all' | 'reverse' | 'clear') {
  switch (type) {
    case 'all':
      defenseConfig.value.appointAddrList = provinceList.map(item => item.id)
      break;
    case 'reverse':
      const selectedSet = new Set<number>([...defenseConfig.value.appointAddrList])
      defenseConfig.value.appointAddrList = []
      provinceList.forEach(item => {
        if (!selectedSet.has(item.id)) {
          defenseConfig.value.appointAddrList.push(item.id)
        }
      })
      break;
    case 'clear':
      defenseConfig.value.appointAddrList = []
  }
}

const config: {
  mallId?: Store['mallId']
  num_type: 'random' | 'appoint' | 'random-range',
  /**随机多少位 */
  num_times: number,
  /**指定多少件 */
  num_appoint: number,
  /**范围随机件 */
  randomRage: {
    v1: number,
    v2: number
  },

  /**一次执行多少个商品 */
  goodsBatch: number
  recover: boolean

  /** 是否是旗舰店 */
  isFlagshipStore: boolean
  /**使用内置主图 */
  useDefaultImage: boolean
  /**是否开启炮灰商品循环 */
  isTempLoop: boolean
  /** 炮灰循环间隔 */
  tempLoopDelay: number,
  /**炮灰循环类型 */
  tempLoopType: 'random' | 'appoint'
  /**执行炮灰商品 循环时 指定的商品ID */
  appointTempGoodsId: string

  auto_increase_price: boolean
  /**true 果园下单 false 赠品下单 */
  line2: boolean
  // order_type: 'guoyuan' | 'gift'

  cat_id: number

  order_per_max_count: number,
} = reactive({
  goodsBatch: 1,
  num_type: 'appoint',
  randomRage: { v1: 101, v2: 2000 },
  num_times: 3,
  num_appoint: 7663,
  recover: false,
  isFlagshipStore: false,
  useDefaultImage: true,
  isTempLoop: false,
  tempLoopDelay: 100,
  tempLoopType: 'random',
  appointTempGoodsId: '',
  auto_increase_price: false,
  order_type: 'gift',
  line2: false,
  order_per_max_count: 9505,
  //order_per_max_count: 200001,
  //cat_id: 0
  cat_id: 8932
})

watch(() => config.isFlagshipStore, (isFlagshipStore) => {
  if (isFlagshipStore) {
    if (config.order_per_max_count > 9505) {
      config.order_per_max_count = 9505
    }
    if (config.cat_id == 8932) {
      config.cat_id = 0
    }
  } else {
    config.order_per_max_count = 200001
    config.cat_id = 8932
  }
})

const appointTempGoodsIds = computed(() => {
  const { appointTempGoodsId } = config
  const list = appointTempGoodsId.split(/[,，\n]/)
  const result = new Set<number>()
  list.forEach(item => {
    if (item.length < 6) {
      return
    }
    const num = Number(item.trim())
    if (num && Number.isInteger(num)) {
      result.add(num)
    }
  })
  return [...result]
})

const state = reactive<{
  logList: LogItem[]
  goods_id: string
}>({
  logList: [],
  goods_id: ''
})
const addToLog = (data: Partial<LogItem>, row?: anyObj) => {
  state.logList.push({
    date: Date.now(),
    msg: '',
    ...data,
  })

  if (row) {
    const msg = data.msg || ''
    if (rowStatusMap.value.has(row.id)) {
      rowStatusMap.value.get(row.id)!.msg = msg
      rowStatusMap.value.get(row.id)!.type = data.type
    } else {
      rowStatusMap.value.set(row.id, { msg, type: data.type })
    }
  }
}


const log = addToLog

const start_site = usePersistenceRef(1, 'start_site')
const tableRef = ref<VxeTableInstance>()
const { tableProps, tableState, tableEvents, pagination, tableAction } = useTable(310,
  { id: 'change-sales-gift', columnConfig: { resizable: true } },
  { tableRef, pagination: { pageSizes: [100, 1000, 2000] } })

async function getTempGoodsList() {
  const { mallId } = config
  const store = mallStore.tableList.find(item => item.mallId == mallId)
  if (!store) {
    return ElMessage.warning({
      message: '请选择店铺',
      grouping: true
    })
  }
  tableProps.loading = true
  const goodsNameList = [...new Set(config.isFlagshipStore ? tempGoodsNames.flagshipStore : tempGoodsNames.common)]
  const allList: any[] = []
  for (let i = 0; i < goodsNameList.length; i++) {
    const goodsTitle = goodsNameList[i]
    let list = await pageStepRequest({
      request: async (page) => {
        const res = await storeGoodsList(store!, page, void 0, goodsTitle)
        // console.log(res, goodsTitle)
        if (!res.success) {
          return Promise.reject()
        }
        const { goods_list, total } = res.result;
        goods_list.forEach((item: any) => {
          // isOnsale
          item.sku_list = (item.sku_list as anyObj[]).filter(item => item.isOnsale)
          if (!item.sku_list.length) {
            return
          }
          item._skuSelect = (item.sku_list as anyObj[]).sort((a, b) => a.groupPrice - b.groupPrice)[0].skuId
          // state.tableList.push(item)
          item._sku_list = [item.sku_list[0]]

        });
        return { list: goods_list, total }
      }
    })
    allList.push(...list)
  }
  pagination.total = allList.length
  pagination.page = 1
  tableProps.data = allList
  tableProps.loading = false
}

const checkTempGoodsErrorState = ref({
  show: false,
  errors: [] as Array<{ goodsInfo: anyObj, msg: string }>
})

/**检测填写的指定炮灰商品是否可用 */
async function resolveAppointTempGoods(mall: Store, opt: {
  isFlagshipStore?: boolean
}) {
  if (!appointTempGoodsIds.value.length) {
    return []
  }
  loading.start = true
  const list = await pageStepRequest({
    request: async (page) => {
      const res = await storeGoodsList(mall!, page, appointTempGoodsIds.value.join(','))
      if (!res.success) {
        return Promise.reject()
      }
      const { goods_list, total } = res.result;
      return { list: goods_list, total }
    }
  })
  let errorMsg: typeof checkTempGoodsErrorState.value.errors = []
  const filterList = list.filter(tempGoodsInfo => {
    let flag = true
    let msg: string[] = []
    const { sku_list = [], } = tempGoodsInfo
    if (sku_list.length !== 1) {
      msg.push('当前临时商品的sku数量不为1')
      flag = false
    }
    const skuItem = sku_list[0] || { groupPrice: 0, skuQuantity: 0 }
    if (skuItem.groupPrice < 4000 * 100) {
      msg.push('当前临时商品的价格低于4000元')
      flag = false
    }
    // if (skuItem.skuQuantity < opt.num) {
    //   msg.push(`当前临时商品的库存数量低于${opt.num}件`)
    //   flag = false
    // }
    // if (!opt.isFlagshipStore) {
    //   if (cat_id !== 10018 && cat_id !== 10018) {
    //     msg.push('当前临时商品的分类不符合要求,不是【全屋定制-地暖/暖气片/散热器-暖气片/散热器】')
    //     flag = false
    //   }
    // }
    if (!flag) {
      errorMsg.push({ goodsInfo: tempGoodsInfo, msg: msg.join(';') })
    }
    return flag
  })
  if (errorMsg.length) {
    checkTempGoodsErrorState.value.errors = errorMsg
    checkTempGoodsErrorState.value.show = true
  }
  loading.start = false
  addToLog({ msg: `检测到${filterList.length}个符合要求的临时商品,${errorMsg.length}个不符合要求的商品` })
  return filterList as anyObj[]
}

async function getGoodsList(goodsTitle = '') {
  const { mallId } = config
  const store = mallStore.tableList.find(item => item.mallId == mallId)
  if (!store) {
    return ElMessage.warning({
      message: '请选择店铺',
      grouping: true
    })
  }
  if (!mallStore.checkAvailable(store)) {
    return ElMessage.warning({
      message: '当前店铺已过期，请重新登录',
      grouping: true
    })
  }
  const { goods_id } = state
  if (goods_id) {
    pagination.page = 1
  }

  tableProps.loading = true
  /**一页对应真实页数多少页 */
  const times = Math.round(pagination.limit / 100)
  let basePage = (pagination.page - 1) * times
  let page = 1
  const max = pagination.page * times
  const list: any[] = []

  /**已经请求到多少条数据,用于计算后续是否还有数据 */
  let requestGoodsCount = 0
  await new Promise((resolve) => {

    function request() {
      log({
        msg: `正在获取第${pagination.page}-${page}页商品`
      })
      storeGoodsList(store!, basePage + page, goods_id, goodsTitle)
        .then(res => {
          if (res.success) {
            // state.tableList = [];
            const goodsList: anyObj[] = res.result.goods_list
            requestGoodsCount += goodsList.length
            // state.tableList.push(...)
            goodsList.forEach(item => {
              // isOnsale
              item.sku_list = (item.sku_list as anyObj[]).filter(item => item.isOnsale)
              if (!item.sku_list.length) {
                return
              }
              item._skuSelect = (item.sku_list as anyObj[]).sort((a, b) => a.groupPrice - b.groupPrice)[0].skuId
              // state.tableList.push(item)
              item._sku_list = [item.sku_list[0]]
              list.push(item)
            });
            pagination.total = res.result.total
            log({
              // msg: `获取商品第${page}页成功,当前已获取${state.tableList.length}/${res.result.total}条`,
              msg: `获取商品第${pagination.page}-${page}页成功`,
              type: 'success'
            })
            if (res.result.total > requestGoodsCount && basePage + page < max) {
              if (tableProps.loading) {
                page++
                request()
              } else {
                log({
                  msg: '停止获取',
                  type: 'warning'
                })
                resolve(true)
              }

            } else {
              resolve(true)
            }
          } else {
            // console.log(res)
            let errorMsg = res.errorMsg || res.error_msg || ''
            if (errorMsg == '会话已过期') {
              errorMsg += ',请重新登录店铺'
            }
            log({
              msg: '获取失败：' + errorMsg,
              type: 'danger'
            })
            resolve(true)
          }
        })
        .catch((res) => {
          // console.log(res) 
          log({
            msg: '意外的错误,停止获取',
            type: 'danger'
          })
          resolve(true)
        })
    }
    request()
  })
  tableProps.data = list
  tableAction('clear')
  log({
    msg: `获取商品列表结束`
  })
  tableProps.loading = false
}
async function inputGoodsIdMultiple() {
  let goodsIdStr = state.goods_id
  await ElMessageBox({
    title: '修改商品ID筛选',
    showCancelButton: true,
    message: h('div', {}, [
      h('p', { class: 'm-b-5 primary' }, '请输入商品ID,多个用逗号或换行隔开！'),
      h(VxeTextarea, {
        modelValue: state.goods_id,
        onChange: ({ value }) => { goodsIdStr = value },
        resize: 'none',
        rows: 10,
        placeholder: '请输入商品ID,多个用逗号或换行隔开！'
      })
    ])
  })
  state.goods_id = goodsIdStr.split(/[,，\n]/).filter(item => item.trim()).join(',')

}
function resolveSingleBuyCount(target: number, isFlagshipStore: boolean) {
  let obj = {
    num_per_order: 0,
    price_per_order: 0,
    max_num_per_order: 9505
  }
  const order_per_max_count = Number(config.order_per_max_count)

  if (config.auto_increase_price || order_per_max_count == 359) {
    obj.max_num_per_order = 359
    obj.price_per_order = 5.5
  } else if (order_per_max_count === 9505) {
    obj.price_per_order = 0.21
    obj.max_num_per_order = 9505
    // obj.max_num_per_order = 3851
    // obj.price_per_order = 0.51
  } else if (order_per_max_count === 3851) {
    obj.price_per_order = 0.51
    obj.max_num_per_order = 3851
  } else if (order_per_max_count === 200001) {
    obj.price_per_order = 0.01
    obj.max_num_per_order = 200001
  }
  if (target <= 100) {
    obj.num_per_order = 101
  }
  else if (target >= obj.max_num_per_order) {
    obj.num_per_order = obj.max_num_per_order
  } else {
    obj.num_per_order = target
  }
  return obj
}
function resolveBuyCount(isFlagshipStore: boolean) {
  const { num_appoint, num_type, num_times = 3, } = config
  let num = 1
  switch (num_type) {
    case 'appoint': {
      num = num_appoint || 1;
      break
    }
    case 'random': {
      num = random(Math.pow(10, num_times - 1), Math.pow(10, num_times))
      break
    }
    case 'random-range': {
      const { v1, v2 } = config.randomRage
      num = random(v1, v2)
      break
    }
  }
  if (num <= 0) {
    num = 1
  }
  if (num < 101) {
    num = 101
    addToLog({
      msg: `商品数量不能小于101,已自动调整到101`,
      type: 'primary'
    })
  }
  addToLog({ msg: `商品数量:${num}` })
  return num
}
const loading = reactive({
  start: false,
  stop: false
})
async function checkStop() {
  if (loading.stop) {
    addToLog({
      msg: '检测到停止执行',
      type: 'warning'
    })
    return Promise.reject()
  }
}
/**缓存 */
const tempGoodsCache = ref<TempGoodsCacheItem[]>([])
function getTempGoodsFromCache() {
  let { tempLoopDelay, isTempLoop } = config
  tempLoopDelay = 60
  if (!isTempLoop) {
    return void 0
  }
  return tempGoodsCache.value.find(item => item.lastUsedTime + tempLoopDelay * 1000 <= Date.now())
}
let isNoticeAccount = false
async function start() {
  if (!isUserCanUse()) {
    return
  }
  if(tempGoodsCache.value.length){
    addToLog({ msg: '清空缓存' })
    tempGoodsCache.value = []
  }
  const { mallId, tempLoopType } = config
  const mall = mallStore.tableList.find(item => item.mallId == mallId)
  if (!mall) {
    ElMessage.warning({
      message: '请选择店铺',
      grouping: true
    })
    return
  }
  if (!mallStore.checkAvailable(mall)) {
    return ElMessage.warning({
      message: '当前店铺已过期，请重新登录',
      grouping: true
    })
  }
  const allList = [...tableState.selection]
  if (!allList.length) {
    ElMessage.warning({
      message: '请选择商品',
      grouping: true
    })
    return
  }
  state.logList = []
  loading.start = true
  const costTemplateInfo = await resolveDefense(mall)
  loading.start = false

  if (config.auto_increase_price && !isNoticeAccount) {
    await ElMessageBox({
      title: '警告',
      // type: 'warning',
      message: h('div', { style: 'padding:5px' }, [
        h('h4', '检测到开启规避低价限制'),
        h('p', { class: 'danger' }, '开启后，每个订单SKU价格为5.5元，如果需购买数量过大，则需要【大量小号次数】'),
        h('p', { calss: 'warning' }, '当购买数量大于359,则需要 （购买数量÷359）小号次数'),
        h('p', '确认继续吗？(点击确定后，本次运行【不再提示】！)')
      ]),
      showCancelButton: true
    })
    isNoticeAccount = true
  }

  let tempGoodsInfos = [] as anyObj[]
  if (config.isTempLoop && config.tempLoopType === 'appoint') {
    tempGoodsInfos = await resolveAppointTempGoods(mall, { isFlagshipStore: config.isFlagshipStore })
    if (!tempGoodsInfos.length) {
      ElMessage.warning({
        message: '检测后没有可用的炮灰商品ID',
      })
      return
    }
    config.appointTempGoodsId = tempGoodsInfos.map(item => item.id).join(',')

  }

  rowStatusMap.value.clear()
  const _start = async () => {
    if (loading.stop) {
      addToLog({
        msg: '停止执行',
        type: 'warning'
      })
      loading.start = false
      return
    }
    const row = allList.shift()
    if (!row) {
      log({
        msg: '结束执行'
      })
      loading.start = false
      return
    }
    const target = resolveBuyCount(config.isFlagshipStore)
    let requestData: RequestData = {
      row,
      num: target,
      // num_per_order: ,
      ...resolveSingleBuyCount(target, config.isFlagshipStore),
      mall,
      coupon_code: '',
      isFlagshipStore: config.isFlagshipStore,
      costTemplateInfo
    }

    try {
      addToLog({ msg: '初始化', row })
      // console.log(requestData)
      // await Promise.reject('打断')
      await getCloneGoodsDetails(requestData)
      // console.log('requestData', requestData)
      // await Promise.reject('打断')
      await checkStop()
      await resolveAccount(requestData)
      // await Promise.reject('打断')
      await checkStop()
      // if (requestData.tempGoodsInfo && requestData.goodsInfo) {
      // } else {

      if (config.isTempLoop && tempLoopType === 'appoint') {
        const tempGoodsInfo = tempGoodsInfos.shift()
        if (!tempGoodsInfo) {
          addToLog({ msg: '没有可用炮灰商品' })
          loading.start = false
          return Promise.reject('')
        }
        requestData.goods_id = tempGoodsInfo.id
        requestData.tempGoodsInfo = tempGoodsInfo
        requestData.isCacheTempGoods = true
        await checkStop()
      } else {
        const cacheGoods = getTempGoodsFromCache()
        if (!cacheGoods) {
          await createTempGoods(requestData)
          // await Promise.reject('打断')
          await checkStop()
          await checkTempGoods(requestData)
          await checkStop()
          console.log('requestData', requestData)

          config.recover && config.isFlagshipStore && resolveMateriaInfo(requestData)
        } else {
          addToLog({ msg: '从缓存中获取临时数据' })
          requestData.isCacheTempGoods = true
          requestData.tempGoodsInfo = cacheGoods.tempGoodsInfo
          requestData.goodsInfo = cacheGoods.goodsInfo
          cacheGoods.lastUsedTime = Date.now()
          requestData.goods_id = requestData.goodsInfo.goods_id as number
        }
      }


      // return Promise.reject('')
      addToLog({ msg: '准备创建优惠券，大约15秒' })
      await delayPromise(10000)
      await checkStop()
      await createCoupon(requestData)
      // }
      await getGorupId(requestData)
      await createOrder(requestData)
      if (config.recover) {
        await editGoods(requestData)
      }
      _start()
    } catch (e) {
      console.log(e)
      addToLog({ msg: requestData.row.goods_name + '处理失败', type: 'danger' })
      // await closeMerchantCoupon(requestData)
      _start()
    }
    const goodsId = requestData.goods_id
    if (goodsId && config.isTempLoop && config.tempLoopType === 'appoint') {
      tempGoodsInfos.push(requestData.tempGoodsInfo!)
    }
    console.log('useAppointTempGoodsIds', tempGoodsInfos)
  }
  loading.stop = false
  loading.start = true

  _start()
}

/**检测防薅羊毛 */
async function resolveDefense(mall: Store) {
  const { active, appointAddrList } = defenseConfig.value
  if (!active || !appointAddrList.length || appointAddrList.length === provinceList.length) {
    addToLog({ msg: '未启动防薅羊毛' })
    return void 0
  }
  const map = new Map<number, typeof provinceList[number]>()
  for (const item of provinceList) {
    map.set(item.id, item)
  }
  let templateId: number | undefined = await new Promise(resolve => {
    let page = 1
    const checkFn = async () => {
      const res = await costTemplateListApi(mall, { pageNo: page++ })
      if (res.success) {
        const { list, total } = res.result;

        const costTempItem = (list as anyObj[]).find(item => {
          const areaList: anyObj[] = item.dispatchFree.areaList
          if (areaList.length !== appointAddrList.length) {
            return false
          }
          const checkedSet = new Set<number>([...appointAddrList]);
          areaList.forEach(({ province }) => {
            checkedSet.delete(province)
          })
          if (checkedSet.size) {
            return false
          }
          return true
        })
        if (costTempItem) {
          addToLog({ msg: '检测到可用运费模板' + costTempItem.costTemplateName })
          return resolve(costTempItem.costTemplateId)
        }
        if (total >= page * 10) {
          checkFn()
        } else {
          resolve(void 0)
        }
      } else {
        resolve(void 0)
      }
    }
    checkFn()
  })
  if (!templateId) {
    // 创建模板
    const res = await createCostTemplateApi(mall, {
      dispatchFree: {
        sfFreeType: 0,
        areaList: appointAddrList.map(item => ({ province: item }))
      },
      noShipConfig: provinceList.filter(item => !appointAddrList.includes(item.id)).map(item => ({ province: item.id, noShipReasonType: 2 })),
    })
    if (res.success) {
      templateId = res.result.costTemplateId
      addToLog({ msg: '创建运费模板成功', type: 'success' })
    } else {
      addToLog({ msg: '创建运费模板失败' + pddResErrorMsg(res), type: 'danger' })
    }
  }
  if (!templateId) {
    return void 0
  }
  return {
    id: templateId,
    province: appointAddrList.map(id => ({ id, name: map.get(id)?.regionName ?? '' }))
  }
}

async function resolveAccount(requestData: RequestData) {
  log({ msg: '正在分配小号', }, requestData.row)
  const subAccount = useSubAccount()
  // const res = subAccount.getAccount(start_site.value - 1)
  // if (res.status) {
  //   requestData.account = res.data
  //   const ind = (res.ind + 2) % subAccount.list.length
  //   start_site.value = ind === 0 ? subAccount.list.length : ind
  // } else {
  //   log({
  //     type: 'danger',
  //     msg: '获取小号出错' + res.msg
  //   })
  //   return Promise.reject()
  // }
  const target = requestData.num
  let singleMax = requestData.max_num_per_order
  const accountCount = Math.ceil(target / singleMax)

  const accounts: RequestData['accounts'] = []

  addToLog({
    msg: `需要可用小号次数：${accountCount},一单${singleMax},目标${target}`
  })
  for (let i = 0; i < accountCount; i++) {
    const res = subAccount.getAccount(start_site.value - 1)
    if (res.status) {
      accounts.push(res.data)
      const ind = (res.ind + 2) % subAccount.list.length
      start_site.value = ind === 0 ? subAccount.list.length : ind
    } else {
      addToLog({
        type: 'danger',
        msg: '获取小号出错' + res.msg
      }, requestData.row)
      break
    }
  }

  if (accounts.length < accountCount) {
    addToLog({ type: 'danger', msg: '没有获取到足够小号' })
    accounts.forEach(item => {
      subAccount.recover(String(item))
    })
    return Promise.reject()
  }

  requestData.accounts = accounts
}
async function getCloneGoodsDetails(requestData: RequestData) {
  const { mall, row } = requestData
  addToLog({ type: 'info', msg: '正在获取原商品详情' }, row)
  const res = await pddStoreGoodsDetails(mall, row.id)
  if (!res.success) {
    addToLog({
      msg: '获取商品详情失败：' + (res.error_msg || res.errorMsg),
      type: 'danger'
    }, row)
    return Promise.reject()
  }

  requestData.cloneGoodsDetails = res.result
  // console.log(requestData)
  return res.result
}
/**创建临时商品 */
async function createTempGoods(requestData: RequestData) {
  const { mall, isFlagshipStore, row } = requestData
  const createIdRes = await createGoodsId(mall)
  if (!createIdRes.success) {
    addToLog({ msg: `获取商品ID失败${createIdRes.error_msg || ''}`, type: 'danger' }, row)
    return Promise.reject()
  }


  const { goods_commit_id, goods_id } = createIdRes.result
  requestData.goods_commit_id = goods_commit_id
  requestData.goods_id = goods_id

  const { detail_gallery, cat_id, } = requestData.cloneGoodsDetails!
  const otherData: anyObj = {

  }
  if (requestData.costTemplateInfo) {
    otherData.cost_template_id = requestData.costTemplateInfo.id
  }
  if (isFlagshipStore) {
    const goodsName = (function () {
      const list = tempGoodsNames.flagshipStore
      return list[random(0, list.length)] || list[0]
    })()
    otherData.goods_name = goodsName
    otherData.goods_desc = goodsName
  }
  let config_cat_id = config.cat_id
  if (requestData.price_per_order == 0.01 && config_cat_id != 8932) {
    addToLog({ msg: '当前每单最大销量为200001,仅能使用固定分类《工商注册》,自动调整', type: 'warning' })
    config_cat_id = 8932
  }
  const parmas = {
    mall,
    goods_commit_id, goods_id, gallery: detail_gallery, detailsInfo: requestData.cloneGoodsDetails!,
    cat_id: config_cat_id || (isFlagshipStore ? cat_id : void 0),
    // cat_id,
    otherData,
    only_sku_property: !!config_cat_id || !isFlagshipStore,
    useDefaultImage: config.useDefaultImage
  }
  if (!isFlagshipStore && !config.cat_id) {
    parmas.useDefaultImage = true
  }
  if(config_cat_id == 8932){
    parmas.useDefaultImage = true
  }


  const info = await createPddGoodsInfo(parmas)

  console.log('info', info, requestData)
  // return Promise.reject('')
  const createGoodsRes = await createGoodsInfo(mall, info)
  if (!createGoodsRes.success) {
    addToLog({ msg: `创建商品失败${createGoodsRes.error_msg || ''}`, type: 'danger' }, row)
    return Promise.reject()
  }

  const anti_content = await new Promise<string[]>((resolve, reject) => {
    antiContentFromWeb()
      .then(res => {
        const { anti_content1, anti_content2 } = res.data
        resolve([anti_content1, anti_content2])
      })
      .catch(res => {
        addToLog({ msg: '获取anticontent失败' + res.msg })
        reject()
      })
  })
  // return Promise.reject('中断')
  const submitRes = await goodsSubmit(mall, { goods_commit_id, goods_id }, anti_content)
  console.log('submitRes', submitRes, requestData)
  if (!submitRes.success) {
    addToLog({ msg: `提交商品失败${submitRes.error_msg || ''}`, type: 'danger' }, row)
    return Promise.reject()
  }
  addToLog({ msg: '已成功提交创建商品' + goods_id, type: 'success' }, row)
  // return Promise.reject('')
  // editGoods(requestData)
}
/**检测创建的临时商品是否成功 */
async function checkTempGoods(requestData: RequestData) {
  const { row } = requestData
  addToLog({ msg: '准备检测临时商品创建状态' }, row)
  await delayPromise(5000)
  const { goods_id, mall } = requestData
  await retry(async (index) => {
    addToLog({ msg: `正在检测创建的临时商品状态，第${index}次` }, row)
    // @ts-ignore
    const res = await storeGoodsListApi(mall, { goods_id_list: [String(goods_id)], pre_sale_type: 4 })
    console.log('checkTempGoods', res)
    if (!res.success) {
      addToLog({ msg: `检测创建的临时商品失败${res.error_msg || ''}`, type: 'danger' })
      return Promise.reject(res)
    }
    if (res && res.result && res.result.total) {
      const tempGoodsInfo = res.result.goods_list[0]
      if (!tempGoodsInfo.sku_list) {
        return Promise.reject()
      }
      requestData.tempGoodsInfo = tempGoodsInfo
      addToLog({ msg: '临时商品已上架', type: 'success' }, row)
      return res
    }
    await delayPromise(1000)
    const rejectGoodsRes = await rejectGoodsListApi(mall, [String(goods_id)])
    if (rejectGoodsRes && rejectGoodsRes.success && rejectGoodsRes.result?.total) {
      addToLog({ msg: '检测到临时商品被驳回', type: 'danger' }, row)
      return false
    }
    if (index >= 10) {
      addToLog({ msg: '检测创建的临时商品状态超过10次', type: 'danger' })
    }
    return Promise.reject(res)
  }, 10, 3000)


}
// async function checkTempGoodsPrice(requestData: RequestData) {
//   const { row, mall, goods_id } = requestData
//   await retry(async (index) => {
//     addToLog({ msg: `正在检测临时商品价格状态，第${index}次` }, row)

//     // const rejectGoodsRes = await rejectGoodsListApi(mall, [String(goods_id)])
//     // if (rejectGoodsRes && rejectGoodsRes.success && rejectGoodsRes.result?.total) {
//     //   const rejectGoodsList = rejectGoodsRes.result.list as anyObj[]
//     //   addToLog({ msg: '检测到临时商品修改价格被驳回' + rejectGoodsList[0]?.reject_comment, type: 'danger' }, row)
//     //   return false
//     // }
//     // await delayPromise(1000)
//     // @ts-ignore
//     const res = await storeGoodsListApi(mall, { goods_id_list: [String(goods_id)], pre_sale_type: 4 })
//     // console.log('checkTempGoods', res)
//     if (!res.success) {
//       addToLog({ msg: `检测临时商品价格状态失败${res.error_msg || ''}`, type: 'danger' })
//       return Promise.reject(res)
//     }
//     if (res && res.result && res.result.total) {
//       const tempGoodsInfo = res.result.goods_list[0]
//       if (!tempGoodsInfo.sku_list) {
//         return Promise.reject()
//       }
//       const minPrice = tempGoodsInfo.sku_group_price?.[0]
//       if (minPrice > 550) {
//         return Promise.reject({ error_msg: '未改价' })
//       }
//       // requestData.tempGoodsInfo = tempGoodsInfo
//       addToLog({ msg: '已改价' + (minPrice / 100).toFixed(2), type: 'success' }, row)
//       return res
//     }


//     if (index >= 10) {
//       addToLog({ msg: '检测创建的临时商品状态超过10次', type: 'danger' })
//     }
//     return Promise.reject(res)
//   }, 10, 3000)
//     .then(res => {
//     })
// }
/**创建优惠券 */
async function createCoupon(requestData: RequestData) {
  const { mall, isFlagshipStore, row,  price_per_order } = requestData
  function resolveTime(time = Date.now()) {
    const date = new Date(time);
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    date.setMilliseconds(0);
    return date.valueOf();
  }
  const ruleRes = await query_filtered_mall_goods_list_api({goods_id:requestData.goods_id!.toString()},mall)
  if(ruleRes.success ){
    const item =  (ruleRes.result.goods_list as anyObj[]).find(item => item.id == requestData.goods_id)
    if(item && item.coupon_value_limit){
      const minDiscountPrice = item.coupon_value_limit[0] as number
      if(minDiscountPrice){
        const minBuyNum = Math.ceil(minDiscountPrice / (price_per_order*100))
        if(minBuyNum > requestData.num_per_order){
          addToLog({ msg: `当前每单购买数量${requestData.num_per_order},商品立减券不满足最低购买数量${minBuyNum}，已自动调整`, type: 'warning' }, row)
          requestData.num_per_order = minBuyNum
        }
      }
    }
  }
  const batch_desc = '商品立减券' + Date.now()
  const _discount = requestData.num_per_order * price_per_order
  if (Number.isInteger(_discount)) {
    requestData.num_per_order++
  }
  let discount = Math.floor(_discount) * 100

  if (discount > 200000) {
    discount = 200000
  }
  // requestData.coupon_discount = discount
  const reqData = [
    {
      goods_id: requestData.goods_id!,
      batch_desc,
      batch_start_time: resolveTime(),
      batch_end_time: resolveTime(Date.now() + 3600 * 1000 * 48) - 1,
      discount,
      init_quantity: 100000,
      user_limit: 10
    }
  ]
  const res = await createMerchantGoods(mall, reqData)
  console.log('createCoupon', res, reqData)

  if (!res.success) {
    const msg = res.error_msg || res.errorMsg || ''
    addToLog({ msg: `创建优惠券失败:${msg}`, type: 'danger' }, row)
    return Promise.reject()
  } else if (res.result.has_error_msg) {
    let failedFlag = false
    res.result.error_msg_list.forEach((item: anyObj) => {
      addToLog({ msg: `创建优惠券失败:${item.error_msg}`, type: 'danger' }, row)
      failedFlag = true
    })
    if (failedFlag) {
      return Promise.reject()
    }
  } else {
    addToLog({ msg: '提交创建优惠券成功', type: 'success' }, row)
  }
  addToLog({ msg: '准备获取优惠券信息', type: 'info' })
  await delayPromise(15 * 1000)
  await retry(async (index) => {
    addToLog({ msg: `获取优惠券列表第${index}次--` })
    const listRes = await getCouponList(mall, { goods_list: [String(requestData.goods_id || '')], batch_status: 1 })
    const list = listRes.result.data_list as anyObj[]
    // console.log('getCouponList', listRes)
    const item = list.find(item => item.batch_desc === batch_desc)
    if (item) {
      requestData.coupon_code = item.batch_sn
      requestData.coupon_batch_id = item.batch_id
      addToLog({ msg: '成功获取到优惠券码' + requestData.coupon_code, type: 'success' })
      // bindGift(requestData)
    } else {
      if (index >= 30) addToLog({ msg: '没有获取到优惠券码', type: 'danger' })
      return Promise.reject()
    }
  }, 30, 10 * 1000)



}
async function getGorupId(requestData: RequestData) {
  if (requestData.goodsInfo) {
    addToLog({ msg: '已从缓存中获取下单商品信息', type: 'success' })
    return
  }
  const { tempGoodsInfo, mall, row } = requestData
  const { id = 0, sku_list = [], goods_name, mall_id, thumb_url } = tempGoodsInfo || {}

  // const getPifaGroupId
  const supplyRes = await autoSupply({ mallId: mall.mallId, goods_id: id })
  if (supplyRes.code) {
    addToLog({
      msg: '自动供货失败' + supplyRes.msg,
      type: 'danger'
    }, row)
    return Promise.reject()
  }
  addToLog({
    msg: '自动供货成功,将获取拼团ID',
  })

  await delayPromise(3000)
  await retry(async (index) => {
    addToLog({
      msg: `正在获取拼团ID第${index}次`,
    }, row)
    return getPifaGroupId({ goods_id: id, sku_id: sku_list[0].skuId }, { showErrorMsg: false })
  }, 3)
    .then(async res => {
      const groupId = res.data
      if (groupId) {
        const goodsInfo: GoodsInfo = {
          goods_name,
          goods_id: id,
          skus: sku_list.map((item: anyObj) => {
            const sku: GoodsInfo['skus'][number] = {
              skuId: item.skuId,
              groupPrice: item.groupPrice / 100,
              normalPrice: item.normalPrice / 100,
              skuImg: item.skuThumbUrl,
              spec: item.spec
            }
            return sku
          }),
          group_id: {
            alone: groupId,
            multiple: [groupId]
          },
          mallId: mall_id,
          mallName: mall.mallName,
          normalPrice: 0,
          group_order_ids: [],
          groupPrice: 0,
          goods_img: thumb_url,
          activity_id: 0,
          coupon_code: ''
        }
        requestData.goodsInfo = goodsInfo
        return res
      } else {

        return Promise.reject()
      }

    })
    .catch(async res => {
      addToLog({
        msg: `获取拼团ID失败${res.msg}`,
        type: 'danger'
      }, row)
      await closeMerchantCoupon(requestData)
      return Promise.reject(res)
    })


}

async function closeMerchantCoupon(requestData: RequestData) {
  const { mall, coupon_batch_id } = requestData
  if (mall && coupon_batch_id) {
    return closeMerchantCoupone(mall, { batch_id: coupon_batch_id })
      .then(res => {
        if (res.success) {
          addToLog({
            msg: '成功关闭优惠券领取',
            type: 'success'
          })
        } else {
          addToLog({
            msg: `关闭优惠券领取失败${res.error_msg || res.errorMsg || '-'}`,
            type: 'danger'
          })
        }
      })
  }
}
async function createOrder(requestData: RequestData) {
  const goodsInfo = requestData.goodsInfo
  if (!goodsInfo) {
    await closeMerchantCoupon(requestData)
    return Promise.reject({ msg: '没有商品信息' })
  }
  try {
    const { accounts = [], row } = requestData
    if (!accounts.length) {
      await closeMerchantCoupon(requestData)
      return Promise.reject({ msg: '没有小号' })
    }
    addToLog({ msg: '正在强制绑定商品' }, row)
    await Promise.allSettled([bindGift(requestData)])
    const { coupon_code, mall, promotion_event_sn, giftInfo, } = requestData
    if (!promotion_event_sn) {
      addToLog({
        msg: '没有成功绑定商品,',
        type: 'danger'
      }, row)
      await closeMerchantCoupon(requestData)
      return
    }
    const { skus, goods_id, goods_name, group_id } = goodsInfo
    const accountGroup = new Map<string, { account: string | number, count: number }>()
    accounts.forEach(item => {
      const a = String(item)
      if (!accountGroup.has(a)) {
        accountGroup.set(a, { account: item, count: 1 })
      } else {
        accountGroup.get(a)!.count++
      }
    })
    await skuPriceChange({ goodsInfo, requestData })

    addToLog({ msg: '准备执行领取优惠券并下单' }, row)
    // await delayPromise(20000)
    let successCount = 0
    await batchRequest([...accountGroup.values()], {
      batch: 25,

      request: async (batchList) => {
        // console.log(batchList, 'batchList')
        await Promise.allSettled(batchList.map(async item => {
          const list: string[] = new Array(item.count).fill(item.account)
          console.log('accountList', list)
          await batchRequest(list, {
            batch: 1,
            request: async (list) => {
              const item = list[0]
              const addressData = await resolveAddress(item, requestData)
              const createOrderRequestData: any = {
                account: item,
                sku: skus[0].skuId,
                sku_spec: skus[0].spec,
                shop_id: mall.mallId,
                shop_name: mall.mallName,
                goods_id,
                num: requestData.num_per_order,
                // num: 50001,
                mode: 'open_group',
                // activity_id: coupon_code,
                goods_name,
                group_id: group_id.multiple[0] || group_id.alone!,
                type: 'gift',
                line: config.line2 ? 'guoyuan' : void 0,
                use_coupon: true,
                ...addressData,
              }
              await retry(async () => {
                return getMerchantCouponV2({ coupon_code, account: item, }, { showErrorMsg: false })
              }, 5, 2000)
                .then(res => {
                  const promotion_identity_vo = res.data && res.data.promotion_identity_vo
                  if (config.line2) {
                    addToLog({ msg: `${item}领取优惠券成功`, type: 'success' })
                    return
                  }
                  if (!promotion_identity_vo) {
                    return Promise.reject({ msg: 'promotion_identity_vo不存在' })
                  }
                  const { promotion_id, promotion_type, extension } = promotion_identity_vo
                  createOrderRequestData.single_promotion_list = [
                    {
                      // ...promotion_identity_vo,
                      promotion_id,
                      promotion_type,
                      sku_ids: [giftInfo!.mainGoodsSkuId],
                      mall_id: mall.mallId
                    }
                  ]

                  addToLog({ msg: `${item}领取优惠券成功`, type: 'success' }, row)
                })
                .catch(res => {
                  console.log(res, 'getMerchantCouponV2 -error')
                  addToLog({ msg: `${item}领取优惠券失败${res.msg}`, type: 'danger' }, row)
                  // closeMerchantCoupon(requestData)
                  return Promise.reject()
                })

              if (promotion_event_sn) {
                createOrderRequestData.goods_promotion_list = [
                  {
                    "promotion_type": 28,
                    "promotion_id": promotion_event_sn,
                    "sku_ids": [
                      giftInfo!.mainGoodsSkuId
                    ],
                    "extension": {
                      gift_use_list: JSON.stringify([
                        {
                          mallId: mall.mallId,
                          goodsId: giftInfo?.giftGoodsId,
                          skuId: giftInfo?.giftGoodsSkuId,
                          goodsNumber: requestData.num_per_order
                        }
                      ])
                      // "gift_use_list": "[{\"mallId\":790140,\"goodsId\":************,\"skuId\":1504485111324,\"goodsNumber\":50100}]"
                    }
                  }
                ]
              }
              console.log('createOrderRequestData', createOrderRequestData)
              return retry(async () => {
                return quickOrder(createOrderRequestData, { showErrorMsg: false })
                  .then(res => {
                    if (config.isTempLoop && !requestData.isCacheTempGoods) {
                      tempGoodsCache.value.push({
                        goodsInfo,
                        mallId: mall.mallId,
                        tempGoodsInfo: requestData.tempGoodsInfo!,
                        status: 'pending',
                        lastUsedTime: Date.now()
                      })
                    }
                    successCount++
                    addToLog({
                      msg: `${createOrderRequestData.goods_name}:下单成功。订单号:${res.data.order_sn},已成功${successCount}次`,
                      type: 'success'
                    })
                    autoApply(res.data)
                  })
              }, 3)
                .catch(res => {
                  addToLog({
                    msg: `${createOrderRequestData.goods_name}:下单失败:${res.msg}`,
                    type: 'danger'
                  })
                })

            }
          })
        }))
      }
    })
    addToLog({ msg: `下单结束，成功下单数量${successCount}`, type: successCount > 0 ? 'success' : 'danger' }, row)
    // updateStatus(row, `下单结束，成功下单数量${successCount}`)



    await closeMerchantCoupon(requestData)


    await skuPriceChange({ goodsInfo, requestData, type: 'recover' })

    await cancelGiftGoods(requestData)
    // await delayPromise(5000)

  } catch (e) {
    console.log('orderCreate - catch', e)

  }

}
async function resolveAddress(account: string, requestData: RequestData): Promise<anyObj> {
  const settingStore = useSetting()
  const {
    nameCode_active,
    nameCode_position,
    nameCode_str,
    addr_active,
    addr_position,
    addr_str,
    filterStr,
    type,
    appoint_address,
  } = settingStore.address;
  let addressData: anyObj = {
    appoint_address: appoint_address || void 0,
  }
  if (requestData.costTemplateInfo) {
    const list = requestData.costTemplateInfo.province
    addressData.appoint_address = list.reduce((pre, cur, index) => {
      return pre += (cur.name + (index === list.length - 1 ? '' : ','))
    }, '')
  }
  if (type == 'diy') {
    const result = await getRandomItem();
    Reflect.deleteProperty(result, "id");
    addressData = result;
  } else {
    addressData = {
      ...addressData,
      filter_address: filterStr.replace("，", ","),
      address_cipher: addr_active ? addr_str : void 0,
      address_site: addr_active ? addr_position : void 0,
      name_cipher: nameCode_active ? nameCode_str : void 0,
      name_site: nameCode_active ? nameCode_position : void 0,
    }

  }


  return addAddress({ account: account, ...addressData }, { showErrorMsg: false })
    .then(res => {
      return res.data
    })
    .catch(res => {
      // console.log('添加地址返回值-error：', res)

      useSubAccount().recover(account)
      addToLog({
        msg: '添加地址出错:' + res.msg,
        type: 'danger'
      }, requestData.row)
      return Promise.reject()
    })
}

// async function skuPriceChange(goodsInfo: GoodsInfo, requestData: RequestData, type = 'reduce' as 'reduce' | 'recover') {
async function skuPriceChange(opt: {
  goodsInfo: Pick<GoodsInfo, 'skus'>,
  requestData: RequestData,
  type?: 'reduce' | 'recover'
}) {
  const { goodsInfo, requestData, type = 'reduce' } = opt
  const { mall, price_per_order } = requestData
  const { skus } = goodsInfo
  let changePriceRes: any = { success: false, error_msg: '--' }
  if (type === 'recover') {

    let data = {
      goods_id: requestData.goods_id!,
      "market_price_in_yuan": "5002.00",
      // "market_price_in_yuan": type === 'reduce' ? (isFlagshipStore ? "3" : "5002.00") : '5002.00',
      "market_price": 500200,
      // "market_price": type === 'reduce' ? (isFlagshipStore ? 300 : 500200) : 500200,
      sku_prices: skus.map(item => {
        const obj = {
          sku_id: item.skuId,
          "multi_price_in_yuan": '4000',
          // "multi_price_in_yuan": type === 'reduce' ? (isFlagshipStore ? "0.21" : "0.01") : '4000',
          "single_price_in_yuan": "5001.00",
          // "single_price_in_yuan": type === 'reduce' ? (isFlagshipStore ? "3" : "5001") : '5001',
          "multi_price": 400000,
          // "multi_price": type === 'reduce' ? (isFlagshipStore ? 21 : 1) : 400000,
          "single_price": 500100
          // "single_price": type === 'reduce' ? (isFlagshipStore ? 3 : 500100) : 500100
        }
        return obj
      })
    }
    let data2 = cloneDeep(data)
    if (false && config.isFlagshipStore) {
      let { max_group_price } = requestData.cloneGoodsDetails!

      const multi_price = max_group_price
      // data.market_price = Math.max(multi_price,market_price)
      data.market_price = max_group_price + 300
      data.market_price_in_yuan = (data.market_price / 100).toFixed(2)
      data.sku_prices.forEach(item => {
        item.multi_price = multi_price
        item.multi_price_in_yuan = (item.multi_price / 100).toFixed(2)
        item.single_price = multi_price + 200
        item.single_price_in_yuan = (item.single_price / 100).toFixed(2)
      })
    }
    else {
      data.market_price = 50200
      data.market_price_in_yuan = '502.00'
      data.sku_prices.forEach(item => {
        item.multi_price = 50000
        item.multi_price_in_yuan = '500.00'
        item.single_price = 50100
        item.single_price_in_yuan = '501.00'
      })
    }
    // console.log('changeSkuPrice', data)
    changePriceRes = await changeSkuPrice(mall, data)
    if (changePriceRes.success) {
      // await delayPromise(15000)
      setTimeout(() => {
        changeSkuPrice(mall, data2)
      }, 25 * 1000);

    }
  } else {
    const priceItem = changeSkuPirceList.find(item => item.multi_price === price_per_order * 100)
    if (!priceItem) {
      addToLog({ msg: `--修改价格失败，没有找到修改价格参数`, type: 'danger' })
      closeMerchantCoupon(requestData)
      return Promise.reject()
    }
    const data = {
      goods_id: requestData.goods_id!,
      "market_price_in_yuan": priceItem.market_price_in_yuan,
      "market_price": priceItem.market_price,
      sku_prices: skus.map(item => {
        const obj = {
          sku_id: item.skuId,
          "multi_price_in_yuan": priceItem.multi_price_in_yuan,
          "single_price_in_yuan": priceItem.single_price_in_yuan,
          "multi_price": priceItem.multi_price,
          "single_price": priceItem.single_price
        }
        return obj
      })
    }
    // console.log('changeSkuPrice', data)
    changePriceRes = await changeSkuPrice(mall, data)
    if (changePriceRes.success) {
      await new Promise(resolve => {
        checkSkuPrice(requestData)
          .then(resolve)
          .catch(() => {
            changePriceRes = { success: false, error_msg: '价格检测未通过' }
            resolve(true)
          })
      })
    }
  }


  // console.log('changePriceRes', changePriceRes)
  if (changePriceRes.success) {
    addToLog({
      msg: '修改价格成功',
      type: 'success'
    })
  } else {
    addToLog({
      msg: '修改价格失败' + pddResErrorMsg(changePriceRes),
      type: 'danger'
    }, requestData.row)

    await closeMerchantCoupon(requestData)
    await cancelGiftGoods(requestData)

    return Promise.reject()
  }
}
async function checkSkuPrice(requestData: RequestData) {
  const { mall, row, goods_id } = requestData
  const retryMax = 20
  addToLog({ msg: '检查商品sku价格', type: 'info' }, row)
  await delayPromise(2000)
  const res = await retry<Boolean>(async (index) => {
    addToLog({ msg: `检查商品价格第${index}次`, type: 'info' })
    const res = await storeGoodsListApi(mall, { goods_id_list: [String(goods_id)], pre_sale_type: 4 })

    if (!res.success) {
      addToLog({ msg: `获取商品信息失败${res.error_msg || ''}`, type: 'danger' })
      if (index >= retryMax) {
        return false
      }
      return Promise.reject(false)
    }
    if (res && res.result && res.result.total) {
      const tempGoodsInfo = res.result.goods_list[0]
      if (!tempGoodsInfo.sku_list) {
        if (index >= retryMax) {
          return false
        }
        return Promise.reject(false)
      }
      const groupPrice = tempGoodsInfo.sku_list?.[0]?.groupPrice
      if (groupPrice < 550) {
        addToLog({ msg: '商品价格已更改', type: 'success' }, row)
        return true
      }
    }
    if (loading.stop) {
      addToLog({ msg: `检查商品价格失败-用户停止`, type: 'danger' })
      return false
    }
    if (index >= retryMax) {
      return false
    }
    return Promise.reject(false)
  }, retryMax, 3000)
  if (res) {
    return {
      success: true
    }
  }
  await delayPromise(1000)
  const rejectGoodsRes = await rejectGoodsListApi(mall, [String(goods_id)])
  if (rejectGoodsRes && rejectGoodsRes.success && rejectGoodsRes.result?.total) {
    const goodsInfo = rejectGoodsRes.result.list[0]
    const { reject_comment } = goodsInfo
    if (reject_comment.includes('商品SKU价格异常')) {
      return Promise.reject({
        success: false,
        error_msg: '商品sku价格异常'
      })
    }
    return Promise.reject(rejectGoodsRes)
  } else {
    return {
      success: true
    }
  }
}

async function bindGift(requestData: RequestData) {
  // const mall = useMallStore().tableList[0]
  const { mall, tempGoodsInfo, row } = requestData
  // const mainGoodsId = tempGoodsInfo?.id
  // const mainGoodsSkuId = tempGoodsInfo?.sku_list[0]?.skuId
  // const giftGoodsId = cloneGoodsDetails?.goods_id
  // const giftGoodsSkuId = cloneGoodsDetails?.skus[0]?.sku_id
  let mainGoodsId = tempGoodsInfo?.id
  let mainGoodsSkuId = ''
  const giftGoodsId = row.id
  let giftGoodsSkuId = row._skuSelect
  if (!giftGoodsId) {
    const giftGoodsRes = await giftGoodsListApi(mall, { goods_name: giftGoodsId })
    if (!giftGoodsRes.success) {
      addToLog({ msg: '获取赠品列表失败' + giftGoodsRes.error_msg || giftGoodsRes.errorMsg, type: 'danger' }, row)
      return Promise.reject(giftGoodsRes)
    }
    giftGoodsSkuId = giftGoodsRes.result.gift_display_info_list[0].sku_id
  }
  // if(!goods){
  //     addToLog({msg:'未找到赠品' ,type:'danger'})
  //     return Promise.reject('未找到赠品')
  // }
  // const giftMainGoodsRes = await giftMainGoodsListApi(mall,{goods_name:mainGoodsId,gift_goods_id:Number(giftGoodsId)})
  // console.log('giftMainGoodsRes',giftMainGoodsRes)
  // if(!giftMainGoodsRes.success){
  //     addToLog({msg:'获取主商品列表失败' + giftMainGoodsRes.error_msg || giftMainGoodsRes.errorMsg,type:'danger'})
  //     return Promise.reject(giftGoodsRes)
  // }
  // mainGoodsSkuId = giftMainGoodsRes.result.goods_display_info_list[0].sku_id
  // const mainGoods = giftMainGoodsRes.result.gift_display_info_list[0]
  // if(!mainGoods){
  //     addToLog({msg:'未找到主商品' ,type:'danger'})
  //     return Promise.reject('未找到主商品')
  // }
  const mainGoodsSkuListRes = await giftMainGoodsSku(mall, { goods_id: Number(mainGoodsId), gift_goods_id: Number(giftGoodsId) })

  mainGoodsSkuId = mainGoodsSkuListRes.result.sku_display_info_list[0].sku_id
  const data = {
    "event_name": "送赠品", //活动名称
    // "start_time": Date.now() - 1000 * 60 + useAppStore().time_diff,  //当前时间
    "start_time": dayjs(Date.now()).startOf('day').valueOf(),
    "gift_goods_id": giftGoodsId, //活动赠品ID
    "gift_sku_id": giftGoodsSkuId, //活动赠品SKU ID
    "effective_time_type": 1,
    "main_goods_enroll_list": [ //活动主商品
      {
        "goods_id": mainGoodsId,   //活动主商品ID
        "enroll_sku_ids": [
          mainGoodsSkuId   //活动主商品SKU ID
        ],
        "quantity": "2000000"
      }
    ]
  }


  if (!mainGoodsId || !mainGoodsSkuId || !giftGoodsId || !giftGoodsSkuId) {
    addToLog({ msg: '信息不全-无法操作', type: 'danger' })
    return Promise.reject()
  }
  requestData.giftInfo = {
    mainGoodsId: Number(mainGoodsId),
    mainGoodsSkuId: Number(mainGoodsSkuId),
    giftGoodsId: Number(giftGoodsId),
    giftGoodsSkuId: Number(giftGoodsSkuId)
  }
  await retry(async (index) => {
    addToLog({ msg: `第${index}次尝试绑定商品` })
    const res = await createGiftGoodsApi(mall, data)
    console.log(res, 'createGiftGoodsApi', index)
    if (!res.success) {
      return Promise.reject(res)
    }
    const { success_event_info_list } = res.result
    const item = (success_event_info_list as any[]).find(item => {
      return item.goods_id == mainGoodsId
    })
    if (item && item.promotion_event_sn) {
      addToLog({ msg: '绑定成功' + item.promotion_event_sn })
      requestData.promotion_event_sn = item.promotion_event_sn
      return true
    } else {
      return Promise.reject('绑定失败')
    }
  }, 6, 2000)
    .catch(err => {
      addToLog({ msg: '绑定失败', type: 'danger' })
      return Promise.reject(err)
    })




}
async function cancelGiftGoods(requestData: RequestData) {
  const { mall, promotion_event_sn } = requestData
  if (!promotion_event_sn) {
    // addToLog({ msg: '未找到活动管理ID,请前往后台《下单赠礼品》模块手动取消', type: 'danger' })
    return
  }
  const res = await cancelGiftGoodsApi(mall, { promotion_event_sn })
  if (res.success) {
    addToLog({ msg: '取消绑定成功' })
  } else {
    addToLog({ msg: '取消绑定失败' + res.error_msg || res.errorMsg, type: 'danger' })
  }
}

async function editGoods(requestData: RequestData) {
  if (requestData.isCacheTempGoods) {
    addToLog({ msg: '循环使用的炮灰商品,无法复原', type: 'warning' })
    return
  }
  if (!requestData.isFlagshipStore) {
    addToLog({ msg: '非旗舰店下单方式商品,无法复原', type: 'warning' })
    return
  }
  try {
    addToLog({ msg: '正在复原商品信息' })
    await delayPromise(15 * 1000)
    const { mall } = requestData
    const { goods_commit_id, goods_id, detail_gallery } = requestData.cloneGoodsDetails!
    const uploadRes = await getUpLoadData(mall, {
      goods_commit_id,
      goods_id,
      cat_id: requestData.cloneGoodsDetails!.cat_id
    })
    addToLog({ msg: '正在复原商品信息-' })
    const newGoodsCommitRes = await retry(async (index) => {
      addToLog({ msg: `正在申请编辑商品-第${index}次` })
      const res = await newGoodsCommitId(mall, { goods_id: requestData.goods_id! })
      if (index >= 5) {
        return res
      }
      if (!res.success) {
        return Promise.reject(res)
      }
      return res
    }, 5, 10 * 1000)
    if (!newGoodsCommitRes.success) {
      addToLog({ msg: '申请编辑商品失败' + newGoodsCommitRes.error_msg, type: 'danger' })
      return Promise.reject()
    }
    const new_goods_commit_id = newGoodsCommitRes.result.goods_commit_id
    addToLog({ msg: '正在复原商品信息--' })
    const transformRes = await transFromData({
      ...uploadRes,
      detail: requestData.cloneGoodsDetails!,
      goods_commit_id,
      goods_id,
      new_goods_id: requestData.goods_id!,
      new_goods_commit_id,
      decoration: {
        floor_list: (detail_gallery as anyObj[]).filter(item => item.type == 2).map(item => {
          return {
            content_list: [{
              img_url: item.url
            }]
          }
        })
      }
    })
    requestData.newGoodsCloneInfo = transformRes.data
    addToLog({ msg: '正在复原商品信息---' })


    const createRes = await createGoodsInfo(mall, cloneDeep(requestData.newGoodsCloneInfo!))
    console.log('editGoods - createRes', createRes)
    const anti_content = await new Promise<string[]>((resolve, reject) => {
      antiContentFromWeb()
        .then(res => {
          const { anti_content1, anti_content2 } = res.data

          resolve([anti_content1, anti_content2])
        })
        .catch(res => {
          addToLog({ msg: '获取anticontent失败' + res.msg })
          reject()
        })
    })
    addToLog({ msg: '正在复原商品信息----' })
    const submitRes = await goodsSubmit(mall, { goods_commit_id: requestData.newGoodsCloneInfo!.goods_commit_id, goods_id: requestData.newGoodsCloneInfo!.goods_id }, anti_content)
    console.log('editGoods - submitRes', submitRes)
    if (!submitRes.success) {
      addToLog({ msg: '商品复原失败' + (submitRes.error_msg || submitRes.errorMsg), type: 'danger' })

    } else {
      addToLog({ msg: '商品信息已复原', type: 'success' })
      // addToLog({ msg: '请前往后台查询复原的商品是否符合预期,修改商品信息前请支付订单，否则订单将失效', type: 'primary' })
    }


  } catch (e: any) {
    let msg = ''
    if (typeof e === 'object') {
      if (e.msg) {
        msg = e.msg
      }
    } else {
      msg = String(e)
    }
    addToLog({ msg: '商品信息复原失败:' + String(msg), type: 'danger' })
  }
}

async function resolveMateriaInfo(requestData: RequestData) {
  const { row, mall, goods_id, cloneGoodsDetails } = requestData
  const cloneGoodsId = row.id
  const types = [1, 3]
  const { cat_id_1, cat_id_2, cat_id_3, cat_id_4 } = cloneGoodsDetails!
  await Promise.allSettled(types.map(async type => {
    const title = type === 1 ? '白底图' : '长图'
    const res = await materialInfo(mall, { type, goods_id: cloneGoodsId })
    if (!res.success) {
      addToLog({ msg: `获取${title}失败，${res.error_msg || res.errorMsg}` })
      return
    }
    const { check_status, latest_material } = res.result || {}
    if (check_status == 2 && latest_material) {
      addToLog({ msg: `正在处理${title}` })
      const { base64 } = await urlToBase64(latest_material)
      addToLog({ msg: `正在上传${title}` })

      const uploadRes = await uploadImageToStore(mall, base64)
      console.log('uploadRes', uploadRes)
      if (!uploadRes.url) {
        addToLog({ msg: `上传${title}失败，${res.error_msg || res.errorMsg}` })
        return
      }
      const createRes = await storeCreateFile(mall, { url: uploadRes.url })
      console.log('createRes', createRes)
      if (createRes.error_msg) {
        addToLog({ msg: `创建${title}失败，${createRes.error_msg}` })
        return
      }
      const mall_file_id = createRes.result
      /**创建 */
      const create2Res = await createMateria(mall, {
        type,
        mall_file_id,
        goods_id: goods_id!,
        cat_id_1,
        cat_id_2,
        cat_id_3,
        cat_id_4,
        content: uploadRes.url
      })
      console.log(create2Res)
      addToLog({ msg: `结束${title}操作` })
    } else {
      addToLog({ msg: `该商品${title}不存在或没有有效的${title}` })
    }
  }))
}

async function autoApply(orderInfo: anyObj) {
  const { order_sn } = orderInfo
  const settingStore = useSetting()
  const { chance, zfb_pass } = settingStore.pay
  if (chance === 'immediate') {
    if (!zfb_pass) {
      log({
        msg: "没有设置支付密码，不会开启自动支付",
        type: "warning",
      });
      return;
    }
    addToLog({ msg: '获取支付链接' })
    getApplyInfo({
      order_sn,
    })
      .then((res) => {
        const autoApply = useAutoApply();
        // console.log('获取支付链接成功',res.data)
        log({
          msg: `订单${order_sn},已进入自动支付队列`,
        });
        autoApply.addToPendding([
          {
            ...res.data,
            type: "auto",
          },
        ]);
      })
      .catch((e) => {
        log({
          msg: `订单${order_sn},获取支付链接失败，不会自动支付`,
        });
      });
  }
}


</script>

<style lang='scss' src="../../css.scss" scoped></style>

<style lang='scss' rel="stylesheet/scsss" scoped>
.table-container {
  .vxe-table {
    @include commonTableHeader();
  }
}
</style>
