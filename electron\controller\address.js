const { Controller } = require("ee-core");
const Storage = require("ee-core").Storage;

class addressController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  async getList({page,limit}){
    limit = limit || 20
    page = page || 1
    return this.service.addrStorage.getList({limit,page})
  }

  async addItem(data){
    return this.service.addrStorage.addAddrItem(data)
  }

  async multipleAdd(data){
    return this.service.addrStorage.multipleAdd(data)
  }

  async delItem({id}){
    return this.service.addrStorage.delAddrItem(id)
  }
  async delSelected({ids}){
    return this.service.addrStorage.delSelected(ids)
  }
  async delAddrAll(){
    return this.service.addrStorage.delAddrAll()
  }
  async getRandomItem(){
    return this.service.addrStorage.getRandomItem()
  }


}

addressController.toString = () => "[class addressController]";
module.exports = addressController;
