import { ElMessage, ElMessageBox } from "element-plus";
import { defineStore } from "pinia";
import request, { exchangeCami, appInfo } from "../apis/page";
import { appInfo as appInfoSystem } from "../apis/mainWindow";
import { closeWindow, setWindowSize, minimizeWindow } from "/@/apis/mainWindow";
import { useUserStore } from "./user";
import { captchaCollect, getVerifyImage } from "../apis/store";
import { ipc } from "../apis/config";
type PddVerify = {
  show: boolean,
  verify_auth_token: string,
  imgs: string[],
  type: number,
  clickPoint: Array<{ x: number, y: number }>,
  slider: number,
  cb: null | ((success: boolean) => any)
  captcha_collect: string
  tips: string
  pointCount: number,
  initing: boolean
}
export const useAppStore = defineStore("app", {
  state: () => {
    return {
      info: {
        app: {
          notice: "",
          version: "",
          ID: 0,
          logo: "",
          update_url: "",
          software_url: "",
          app_name: "",
          free: 0,
        },
        system: {
          ID: 2,
          name: "",
          value: "",
        
        },
        app_setting:{
          is_open:1,
          notice:''
        }
      },
      isRisk: false,

      /**某些接口需要的特殊地址，请求失败则清除，使用默认地址 */
      specialApiUrl: "http://***********:81",

      /**系统应用信息 */
      appInfo: {
        appPath: "",
        appVersion: "",
        userAgent: ""
      },
      tids: {
        appInfo: void 0 as undefined | NodeJS.Timer,
      },
      /**请求地址 */
      currentUrl: "",
      urls: [] as string[],
      urlIndex: 0,
      /**网络频繁次数 */
      urlErrorCount: 0,
      /**网络频繁记录的时间 */
      urlErrorTime: 0,
      /**服务器时间 与 本机时间的差值 */
      time_diff: 0,
      pddVerify: {
        show: false,
        verify_auth_token: '',
        imgs: [] as string[],
        type: 11,
        /**点击验证点击的点 */
        clickPoint: [],
        /**滑块验证的滑动距离 */
        slider: 0,
        cb: null as null | ((success: boolean) => any),
        captcha_collect: '',
        tips: '',
        pointCount: 0,
        initing: false
      } as PddVerify,
      loadings:{
        renewal:false
      }
    };
  },
  getters: {
    appName(): string {
      return this.info.app.app_name;
    },
    systemValue(): anyObj | null {
      try {
        return JSON.parse(this.info.system.value);
      } catch (e) {
        return null;
      }
    },
    // commentOrderPath(): string {
    //   return this.appInfo.appPath + "/评论文件夹";
    // },
    tempPath(): string {
      return this.appInfo.appPath + "/temp";
    },
  },
  actions: {
    getAppInfo() {
      return appInfo().then((res) => {
        console.log(res, "appInfo");
        if (!res || !res.data) {
          return Promise.reject({});
        }
        if (!res.code) {
          this.info = res.data;
          console.log(res)
          return res.data;
        } else {
          if (res.msg !== "网络频繁") {
            ElMessage.error(res.msg);
          }
          return Promise.reject(res);
        }
      });
    },
    getAppInfoRepeat() {
      clearTimeout(this.tids.appInfo);
      this.getAppInfo().finally(async () => {
        this.tids.appInfo = setTimeout(() => {
          this.getAppInfoRepeat();
        }, 5 * 60 * 1000);
      });
    },
    closeApp(notice = false) {
      new Promise((resolve, reject) => {
        if (!notice) {
          resolve(true);
        } else {
          ElMessageBox({
            title: "警告",
            type: "warning",
            message: "窗口关闭后，将结束本次运行",
            showCancelButton: true,
          }).then(resolve, reject);
        }
      })
        .then(() => {
          return closeWindow();
        })
        .catch(() => { });
    },
    setSize(data = { x: 0, y: 0 }) {
      return setWindowSize(data);
    },
    minimize() {
      return minimizeWindow();
    },
    /**续费 */
    async renewal() {
      if(this.loadings.renewal){
        ElMessage.warning("上一次请求执行中")
        return
      }
      const { value } = await ElMessageBox.prompt("请输入卡密", "提示", {
        showCancelButton: true,
        inputPattern: /[^\s]+/,
        inputErrorMessage: "请输入卡密",
      });
      if (!value) {
        return;
      }
      // ElMessage.success(value.trim())
      const userStore = useUserStore();
      // console.log(userStore.user_info);
      this.loadings.renewal = true
      exchangeCami({
        account: userStore.user_info.username,
        card: value.trim(),
      }).then((res) => {
        // console.log(res)
        ElMessage.success("充值成功");
        userStore.refreshUserInfo();
      }).finally(() => {
        this.loadings.renewal = false
      })
    },
    getAppInfo_System() {
      appInfoSystem().then((res) => {
        this.appInfo = res;
      });
    },
    selectUseableUrl() {
      if (!this.urls.length) {
        return Promise.reject();
      }
      //1分钟内累计3次
      if (this.urlErrorTime + 60 * 1000 < Date.now()) {
        this.urlErrorCount = 0;
      }
      this.urlErrorCount++;
      this.urlErrorTime = Date.now();
      if (this.urlErrorCount >= 3) {
        this.urlIndex++;
        if (this.urlIndex >= this.urls.length) {
          this.urlIndex = 0;
        }
        const res = this.urls[this.urlIndex] || "";
        this.currentUrl = res;
        this.urlErrorCount = 0;
      }
    },
    async openPddVerify(data: Pick<PddVerify, 'cb' | 'verify_auth_token'>) {
      let errorCount = 0
      this.pddVerify.initing = true
      const fn = async () => {
        try {
          const { verify_auth_token, cb } = data
          const res = await getVerifyImage(verify_auth_token)
          const type = res.type
          let captcha_collect = this.pddVerify.captcha_collect
          if (data.verify_auth_token !== this.pddVerify.verify_auth_token) {
            const res2 = await captchaCollect(verify_auth_token)
            captcha_collect = window.get_captcha_collect(res2.salt)
          }
          if (!res.code) {
            const pictures = res.pictures
            if (pictures && Array.isArray(pictures)) {
              if (type == 22) {
                this.pddVerify.imgs = [window.image_decryption(pictures[0]), window.image_decryption(pictures[1])]
              } else {
                this.pddVerify.imgs = [window.image_decryption(pictures[0])]
              }
              const resolveTips = (semantics?: string[]) => {
                let tips = ''
                let pointCount = 0
                if (semantics && Array.isArray(semantics)) {
                  tips = window.title_decode(window.image_decryption(semantics[0]))
                  if (semantics[1]) {
                    pointCount = Number(window.title_decode(window.image_decryption(semantics[1])))
                  }
                }
                this.pddVerify.tips = tips
                this.pddVerify.pointCount = pointCount
              }
              this.pddVerify.captcha_collect = captcha_collect
              this.pddVerify.type = type
              this.pddVerify.clickPoint = []
              this.pddVerify.slider = 0
              this.pddVerify.show = true
              this.pddVerify.cb = cb
              this.pddVerify.initing = false
              this.pddVerify.verify_auth_token = verify_auth_token
              resolveTips(res.semantics)
            }
          }
        } catch (e) {
          errorCount++
          if (errorCount < 3) {
            fn()
          } else {
            data.cb && data.cb(false)
            this.pddVerify.show = false
          }
        }
      }
      fn()
    },
    async openPddVerify_outwin(data: {verify_auth_token:PddVerify['verify_auth_token'] ,cb?:PddVerify['cb']}) {
      const { verify_auth_token,cb } = data
      const res = await ipc.invoke('controller.user.openPddVerify', { verify_auth_token })
      cb?.(res as boolean)
      return res as boolean
    }
  },
});
