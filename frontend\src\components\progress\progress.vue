<template>
  <div class="ddhy-progress" :class="{success:percent >= 100}">
      <div class="progress-bar" :style="`width:${percent + '%'}`"></div>
      <div class="splice" v-for="item in 10"></div>
  </div>
</template>
<script lang='ts' setup>
const props = defineProps<{
    percent:number
}>()
</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>

.ddhy-progress{
    --splice-bg-color:var(--el-color-white);
    --progress-bar-bg-color:var(--el-color-danger);   
    --progress-bg-color: var(--el-fill-color-light);
    width: 100%;
    height: 100%;
    min-height: 20px;
    border-radius: 6px;
    position: relative;
    display: grid;
    grid-template-columns: repeat(10,1fr);
    overflow: hidden;
    background-color: var(--progress-bg-color);
    &.success{
        --progress-bar-bg-color:var(--el-color-success);  
    }
    .splice{
        width: 2px;
        height: 100%;
        background:var(--splice-bg-color);
        position: relative;
        z-index: 100;
        &:nth-child(2){
            visibility: hidden;
        }
    }
    .progress-bar{
        position: absolute;
        left: 0;
        height: 100%;
        background-color: var(--progress-bar-bg-color);
        transition: width .2s linear;
        // z-index: 1;
    }
}
</style>
