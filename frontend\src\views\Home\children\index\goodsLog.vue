<template>
    <div class="goods-log">
        <el-space class="table-header">
            <span>商品ID：</span>
            <el-input v-model="searchForm.goodsId"></el-input>
            <el-button type="primary" @click="getGoodsLogList">搜索</el-button>
        </el-space>
        <el-table :border="true" height="450" :data="state.goodsLogList" ref='goodsLogTableRef'
            @selection-change="(data: typeof state.selection) => { state.selection = data }">
            <template #empty>
                <el-empty description="暂无记录"></el-empty>
            </template>
            <el-table-column type="selection" width="45"></el-table-column>
            <el-table-column label="商品信息" prop="" width="500">
                <template #default="scope">
                    <div class="goods-info-box">
                        <el-image :src="scope.row.goods_img"></el-image>
                        <div class="right">
                            <p class="title">
                                {{ scope.row.goods_name }}
                            </p>
                            <p class="desc">
                                <el-space>
                                    <span>ID：{{ scope.row.goods_id }} </span>
                                    <span>店铺ID：{{ scope.row.mallId }} </span>
                                </el-space>
                            </p>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="最近更新时间" prop="add_time" width="160" sortable>
                <template #default="scope"> {{ dayjs(Number(scope.row.add_time)).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template #default="scope">
                    <el-space>
                        <el-link :underline="false" type="primary"
                            @click="emits('selectGoods', scope.row.goods_id)">选择</el-link>
                        <el-link :underline="false" type="danger" @click="deleteGoodsReadItem(scope.row)">删除</el-link>
                    </el-space>
                    <p>
                        <el-link :underline="false" type="primary" @click="selectGoodsRecord(scope.row)">本地解析</el-link>
                    </p>
                </template>
            </el-table-column>
        </el-table>
        <footer class="table-foolter">
            <el-button type="danger" plain :disabled="!state.selection.length"
                @click="deleteGoodsRead()">删除选中</el-button>
            <el-button type="danger" plain @click="deleteGoodsReadAll()">删除全部</el-button>
        </footer>
        <ds-pagination :total="goodsLogPagination.total" v-model:current-page="goodsLogPagination.page"
            v-model:page-size="goodsLogPagination.limit" @current-change="getGoodsLogList()"
            @size-change="getGoodsLogList()"></ds-pagination>
    </div>
</template>
<script lang='ts' setup>
import { ElLoading, ElMessageBox, TableInstance } from 'element-plus';
import { delGoodsRecord, getReadList } from '/@/apis/goods';
import { reactive, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { ipc } from '/@/apis/config';
const props = defineProps<{
    selectGoodsRecord: Function,
    visible: boolean
}>()




const emits = defineEmits(['selectGoods'])
const searchForm = reactive({
    goodsId: ''
})
const state = reactive({
    goodsLogList: [] as goodsReadRecordInDateBase[],
    selection: [] as goodsReadRecordInDateBase[],
})

const goodsLogPagination: Pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
})
const goodsLogTableRef = ref<TableInstance>()
function getGoodsLogList() {
    const { goodsId } = searchForm
    const { page, limit } = goodsLogPagination
    const loading = ElLoading.service({ text: "加载中--", target: goodsLogTableRef.value?.$el })

    let key = String(goodsId);
    if (key.startsWith("https://")) {
        const reg = /goods_id=(\d+)/;
        const res = key.match(reg);
        res && (key = res[1]);
    }

    getReadList({ page, limit, goods_id: key ? Number(key) : void 0 })
        .then(res => {
            console.log(res)
            state.goodsLogList = res.list
            goodsLogPagination.total = res.total
        })
        .finally(() => {
            loading.close()
        })
}



// 删除一条商品读取记录
function deleteGoodsReadItem(row: goodsReadRecordInDateBase) {
    delGoodsRecord(row.goods_id)
        .then(res => {
            getGoodsLogList()
        })
}

watch(() => props.visible, (val) => {
    if (val) {
        getGoodsLogList()
    }
}, { immediate: true })

function deleteGoodsRead(list = state.selection) {
    delGoodsRecord(list.map(item => item.goods_id).join(','))
        .then(res => {
            getGoodsLogList()
        })
}
async function deleteGoodsReadAll() {
    await ElMessageBox({
        type: 'warning',
        title: '提示',
        message: '确定要删除所有吗？',
        showCancelButton: true
    })
    await ipc.invoke('controller.goods.deleteAll')
    getGoodsLogList()

}

</script>
<style lang='scss' rel="stylesheet/scsss" scoped>
.table-header {
    margin-bottom: 10px
}

.el-table {
    @include commonTableHeader();
}

.table-foolter {
    margin-top: 10px;
    height: 30px;
    display: flex;
    align-items: center;
}
</style>
