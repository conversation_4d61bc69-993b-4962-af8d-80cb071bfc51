<template>
  <div class="order-manage no-padding" @click="() => {
    tableMenuState.show = false
  }">
    <exportOrderVue ref="exportOrderEl" :columns="tableColumns" />
    <el-config-provider size="default">
      <el-form class="search-form" :inline="true">
        <el-form-item label="时间：">
          <el-date-picker v-model="searchForm.time" type="datetimerange" range-separator="至" start-placeholder="开始时间"
            value-format="x" end-placeholder="结束时间" :shortcuts="shortcuts"
            :default-time="[new Date(currentDate.year, currentDate.month, currentDate.day, 0, 0, 0), new Date(currentDate.year, currentDate.month, currentDate.day, 23, 59, 59)]"
            :disabled-date="(date: Date) => date.getTime() < Date.now() - 1000 * 60 * 60 * 24 * 30"
            popper-class="date-range-select ordermanage-timerange-select-popper" />
        </el-form-item>
        <!-- :disabled-date="(date: Date) => date.getTime() < Date.now() - 1000 * 60 * 60 * 24 * 30 || date.getTime() > Date.now()" -->
        <el-divider direction="vertical"></el-divider>
        <el-form-item label="下单类型：">
          <el-select v-model="searchForm.type" popper-class="ordermanage-ordertype-select-popper">
            <el-option label="全部" value=""></el-option>
            <template v-for="item in orderTypes">
              <el-option :value="item.value" v-if="!item.disabled" :label="item.label">{{ item.label }}</el-option>
            </template>
          </el-select>
        </el-form-item>
        <el-divider direction="vertical"></el-divider>
        <el-form-item>
          <el-input placeholder="请输入" v-model="searchForm.commonSearch.value"
            @keyup.enter="orderCateClick(searchForm.statusList.find(item => item.value === searchForm.status)!)" @click="() => {
              paste()
                .then(res => {
                  searchForm.commonSearch.value = res
                })
            }">

            <template #prepend>
              <el-select v-model="searchForm.commonSearch.key" placeholder="选择搜索类型" style="width: 85px"
                popper-class="ordermanage-searchkey-select-popper">
                <el-option v-for="item in searchForm.commonSearch.list" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </template>

            <template #append>
              <el-button @click="getList()" :disabled="buttonLoading.getList">搜索</el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-config-provider>

    <div class="table-header">
      <el-radio-group size="default" v-model="searchForm.status" :disabled="buttonLoading.getList">
        <el-radio-button @click.native.prevent="orderCateClick(item)" v-for="item in searchForm.statusList"
          :key="item.value" :label="item.value">{{
          `${item.label}` }}</el-radio-button>
        <!-- <el-radio-button>全部订单(115)</el-radio-button>
        <el-radio-button> 待支付(10)</el-radio-button>
        <el-radio-button>待发货待分享(23)</el-radio-button>
        <el-radio-button>已签收待收货(12)</el-radio-button>
        <el-radio-button>已收待评价(15)</el-radio-button>
        <el-radio-button>已完成(15)</el-radio-button>
        <el-radio-button>已取消(40)</el-radio-button> -->
      </el-radio-group>
      <div class="btns">
        <el-button size='default' type="primary" v-blur :loading="buttonLoading.isOrderSync"
          :disabled="buttonLoading.isOrderSync" @click="orderSync()">同步订单状态</el-button>
      </div>
    </div>

    <el-dialog title="正在同步最新订单状态..." :append-to-body="true" v-model="orderSyncState.progressShow" :width="480"
      class="dialog-480 order-sync-dialog" top="300px">
      <el-progress :percentage="orderSyncPercentage"></el-progress>
    </el-dialog>

    <div class="table-contaienr">
      <!-- <el-auto-resizer v-if="isTableShow">
        <template #default='{ width, height }'>
          <el-table-v2 :header-height="38" border :row-height="38" @scroll="() => { tableMenuState.show = false }"
            @contextmenu="tablecontextmenu" :row-class="tableRowClass" :stripe="true" :data="state.orderList"
            ref="tableRef" :height="520" fixed :columns="tableColumns" :width="width">
          </el-table-v2>
        </template>
      </el-auto-resizer> -->
      <vxe-table ref="tableRef" :border="true" stripe @contextmenu="tablecontextmenu" :row-class-name="tableRowClass"
        :loading="buttonLoading.getList" :checkbox-config="{ checkField: 'checkField' }" :height="540"
        :row-config="{ height: 50, keyField: 'order_sn' }" :scroll-y="{ enabled: true }"
        :column-config="{ resizable: true }" :data="state.orderList" @checkbox-all="() => { initTableSelection() }"
        @checkbox-change="() => { initTableSelection() }" show-header-overflow="tooltip" show-overflow="tooltip">
        <vxe-column type='checkbox' :width="40" fixed="left" :resizable='false'> </vxe-column>
        <vxe-column field="sort" :width="50" fixed="left" title="序号" :resizable='false'>

          <template #default='scope'>
            {{ scope._rowIndex + pagination.limit * (pagination.page - 1) + 1 }}
          </template>
        </vxe-column>
        <vxe-column :width="120" field="shop_name" title="所属店铺">

          <template #default='scope'>
            <el-link :underline="false" @click="copyStr(scope.row.shop_name)">{{ scope.row.shop_name }}</el-link>
          </template>
        </vxe-column>
        <vxe-column :width="140" field="add_time" title="下单时间" :sortable="true">

          <template #default='scope'>
            {{ dayjs(scope.row.add_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </vxe-column>
        <vxe-column :width="100" field="type" title="下单类型" :sortable="true">

          <template #default='scope'>
            {{ orderTypes.find(item => item.value == scope.row.type)?.label || '' }}
          </template>
        </vxe-column>
        <vxe-column :width="70" field="order_status" title="订单状态">

          <template #default='scope'>
            {{ scope.row.order_status_prompt }}
          </template>
        </vxe-column>
        <vxe-column :width="170" field="order_sn" title="订单编号">

          <template #default='scope'>
            <el-link :underline="false" @click="copyStr(scope.row.order_sn)">{{ scope.row.order_sn }}</el-link>
          </template>
        </vxe-column>
        <vxe-column :width="70" field="order_amount" title="价格" :sortable="true">

          <template #default='scope'>
            {{ Number(scope.row.order_amount) / 100 }}
          </template>
        </vxe-column>
        <vxe-column :width="70" field="goods_number" title="购买数量"></vxe-column>
        <vxe-column :width="110" field="goods_id" title="商品ID" :sortable="true">

          <template #default='scope'>
            <el-link :underline="false" @click="copyStr(scope.row.goods_id)">{{ scope.row.goods_id }}</el-link>
          </template>
        </vxe-column>
        <vxe-column :width="110" field="account" title="小号">

          <template #default='scope'>
            <el-link :underline="false" @click="copyStr(scope.row.account)">{{ scope.row.account }}</el-link>
          </template>
        </vxe-column>
        <vxe-column :width="200" field="goods_name" title="商品名称">

          <template #default='scope'>
            {{ scope.row.goods_name }}
          </template>
        </vxe-column>
        <vxe-column :width="200" field="sku_spec" title="购买规格" :sortable="true">

          <template #default='scope'>
            <el-link :underline="false" @click="copyStr(scope.row.sku_spec)">{{ scope.row.sku_spec }}</el-link>
          </template>
        </vxe-column>
        <vxe-column :width="200" field="recipient" title="收货信息">

          <template #default='{ row }'>
            {{ row.province + row.city + row.district + row.address }}
          </template>
        </vxe-column>
        <vxe-column :width="100" field="name" title="收件人">

          <template #default='{ row }'>
            {{ row.name }}
          </template>
        </vxe-column>
        <vxe-column :width="100" field="phone" title="手机号">

          <template #default='{ row }'>
            {{ row.phone }}
          </template>
        </vxe-column>
        <vxe-column :width="120" field="express" title="快递公司">

          <template #default='{ row }'>
            {{ row.express }}
          </template>
        </vxe-column>
        <vxe-column :width="120" field="tracking_number" title="快递单号">

          <template #default='{ row }'>
            <el-link :underline="false" @click="copyStr(row.tracking_number)">{{ row.tracking_number }}</el-link>
          </template>
        </vxe-column>
        <vxe-column :width="130" field="logistics" title="物流信息">

          <template #default='{ row }'>
            {{ row.logistics }}
          </template>
        </vxe-column>
        <vxe-column :width="100" field="is_collect" title="店铺收藏">

          <template #default='{ row }'>
            {{ row.is_collect ? '是' : '否' }}
          </template>
        </vxe-column>
        <vxe-column :width="100" field="is_attention" title="店铺关注">

          <template #default='{ row }'>
            {{ row.is_attention ? '是' : '否' }}
          </template>
        </vxe-column>
        <!-- <vxe-column :width="60" fiexd='' field="action" title="操作">
          <template #default='{ row }'>
            <el-popover popper-class="table-action-popover" placement="bottom-end" :showArrow="false" :showAfter="100">
              <template #reference>
                <span> <el-icon>
                    <MoreFilled />
                  </el-icon> </span>
              </template>

              <div class="action-list">
                <p @click="openCommentDialog(row)">手工评价</p>
                <p @click="openCommentDialog(row)">追加评价</p>
                <p @click="openTableAction('chat_dialog', row)">与此商聊天</p>
                <p @click="openTableAction('change_addr_dialog', row)">修改地址</p>
              </div>
            </el-popover>
          </template>
        </vxe-column> -->
      </vxe-table>
      <!-- 表格菜单 -->
      <div class="table-row-menu" v-show="tableMenuState.show">

        <template v-if="tableMenuState.row">
          <div class="row">
            <p @click="openCommentDialog([tableMenuState.row!])">手工评价</p>
            <p @click="openCommentDialog([tableMenuState.row!])">追加评价</p>
            <p @click="openTableAction('chat_dialog', tableMenuState.row!)">与此商聊天</p>
            <p @click="openTableAction('change_addr_dialog', tableMenuState.row!)">修改地址</p>
          </div>
          <div class="row">
            <p @click="copyStr(tableMenuState.row!.goods_id)">复制宝贝ID</p>
            <p @click="copyStr(tableMenuState.row!.shop_name)">复制店铺名</p>
            <p @click="copyStr(tableMenuState.row!.order_sn)">复制订单号</p>
            <p @click="() => copyStr(selectionList.map(item => item.order_sn).join('\n'))"
              v-show="selectionList.length">
              复制选中订单号</p>
            <p @click="() => copyStr(state.orderList.map(item => item.order_sn).join('\n'))">复制当前页订单号</p>
          </div>
          <div class="row">
            <!-- <p @click="tableMenuAction('create-comFolder')">创建评价文件夹</p> -->
            <p class="title">↓预设商品评价↓</p>
            <p @click="tableMenuAction('open-comFolder')">打开评价文件夹</p>
            <p @click="tableMenuAction('set-comFolder')">设定总文件夹</p>

          </div>
        </template>

        <template v-else>
          <div class="row">
            <p @click="getList()">刷新列表</p>
          </div>
        </template>
      </div>
      <footer class="table-footer">
        <div class="left">
          <el-checkbox label="全选订单" v-model="state.checkAll" :indeterminate="state.indeterminate" @change="(val) => {
            if (val) {
              tableSelect('check-all')
            } else {
              tableSelect('remove-all')
            }
          }"></el-checkbox>
          <el-divider direction="vertical"></el-divider>
          <el-link :underline="false" @click="tableSelect('reverse')">反选订单</el-link>
          <el-divider direction="vertical"></el-divider>
          <el-link :underline="false" @click="tableSelect('area-select')">区域选择</el-link>
          <el-divider direction="vertical"></el-divider>
          <el-link :underline="false" @click="tableSelect('remove-all')">取消选择</el-link>
          <el-divider direction="vertical"></el-divider>
          <el-link :underline="false" @click="tableFunc('Statistics')">统计金额</el-link>
        </div>
        <ds-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.limit"
          :page-sizes="[50, 500, 1000, 2000]" :total="pagination.total" :small="true" @current-change="getList"
          @size-change="getList" />
      </footer>

    </div>

    <!-- 与商家聊天 -->
    <el-dialog :width="480" title="与此商家聊天" v-model="tableAction.chat_dialog" class="dialog-480">
      <el-form label-width="80">
        <el-form-item label="开启假聊：">
          <el-radio-group v-model="tableAction.chat_type">
            <el-radio-button label="random">随机发送</el-radio-button>
            <el-radio-button label="order">固定发送</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发送数量：">
          <el-input-number v-model="tableAction.chat_num" :controls="false" :precision="0" :min="1"></el-input-number>
          <span class="m-l-5">条</span>
        </el-form-item>

        <el-input type="textarea" :rows="5" placeholder="换行隔开" v-model="tableAction.chat_content"></el-input>
        <p class="m-t-8 f-s-12" style="color: var(--el-text-color-secondary);">
          提示：也可以在订单管理，【与此商家聊天】进行手工聊天
        </p>
      </el-form>

      <template #footer>
        <el-button @click="tableAction.chat_dialog = false">取消</el-button>
        <el-button type="primary" @click="chatWidthShop" :loading="buttonLoading.chatWidthShop"
          :disabled="buttonLoading.chatWidthShop">确定</el-button>
      </template>
    </el-dialog>

    <!-- 修改地址 -->
    <el-dialog :width="480" title="修改地址" v-model="tableAction.change_addr_dialog" class="change-addr-dialog dialog-480">
      <el-form label-width="80" :model="tableAction.change_addr" ref="changeAddrForm">
        <el-form-item label="收件人" prop="name" :rules="[{ required: true, message: '请输入收件人' }]">
          <el-input v-model="tableAction.change_addr.name" :placeholder="`收货人(原：${tableAction.row?.name})`"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone"
          :rules="[{ required: true, message: '请输入手机号' }, { pattern: /^1[^0-2]\d{9}$/, message: '请输入合法的手机号' }]">
          <el-input v-model="tableAction.change_addr.phone"
            :placeholder="`手机号(原：${tableAction.row?.phone})`"></el-input>
        </el-form-item>
        <el-form-item label="省市区" prop="p_c_d" :rules="[{ required: true, message: '请选择省市区' }, {
          validator: (rule: any, value: string[], cb: Function) => {
            value.filter(item => item).length >= 3 ? cb() : cb('请选择正确的省市区')
          }
        }]">
          <el-cascader ref="addressCascader" style="flex-grow: 1;" v-model="tableAction.change_addr.p_c_d"
            :props="props" :options="tableAction.change_addr.options" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address" :rules="[{ required: true, message: '请输入详细地址' }]">
          <el-input placeholder="具体地址" v-model="tableAction.change_addr.address"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="tableAction.change_addr_dialog = false">取消</el-button>
        <el-button type="primary" @click="changeAddressSubmit" :loading="buttonLoading.changeAddr"
          :disabled="buttonLoading.changeAddr">确定</el-button>
      </template>
    </el-dialog>

    <el-config-provider :size="'default'">
      <footer class="controls" v-show="searchForm.status !== 99">

        <p>
          <span>已选中 <strong>{{ tableSelectState.selectionMap.size }}</strong> 项</span>
        <div class="btns">


          <el-button v-blur type="success" @click="operateLogState.dialog = true">操作日志</el-button>
          <el-button v-blur @click="exportOrder()">导出订单</el-button>
          <el-button v-blur @click="exportCard()" :loading="buttonLoading.exportCard"
            :disabled="buttonLoading.exportCard">导出卡券</el-button>
          <el-button v-blur @click="exportQrcode()" :loading="buttonLoading.exportPayCode"
            :disabled="buttonLoading.exportPayCode">导出二维码</el-button>
          <el-button v-blur @click="autoSell()" :loading="buttonLoading.autoSell"
            :disabled="buttonLoading.autoSell">券自动核销</el-button>
          <el-button v-blur @click="autoSpell()" :loading="buttonLoading.autoSpell"
            :disabled="buttonLoading.autoSpell">一键免拼</el-button>


          <el-button v-blur @click="change_checkOrders()" :loading="buttonLoading.changePrice"
            :disabled="buttonLoading.changePrice">一键改价格
          </el-button>
          <el-button v-blur :loading="buttonLoading.isRemark" :disabled="buttonLoading.isRemark"
            @click="orderRemarkState.dialog = true">订单备注</el-button>
          <el-button :loading="buttonLoading.isDelete" :disabled="buttonLoading.isDelete" v-blur type="danger" plain
            @click="deleteOrder()">删除订单</el-button>
        </div>
        </p>
        <p class="btns">
          <el-space>
            <div class="link-button-group success">
              <el-button type="success" @click="orderApply('wechat')" :loading="buttonLoading.apply_wechat"
                :disabled="buttonLoading.apply_wechat">
                <!-- <Icon href="icon-ri_wechat-pay-fill"></Icon>  -->
                微信支付
              </el-button>
              <el-divider :direction="'vertical'"></el-divider>
              <el-button type="success" @click="orderApply('code')" :loading="buttonLoading.apply_code"
                :disabled="buttonLoading.apply_code">
                <!-- <Icon href="icon-ri_alipay-fill"></Icon>  -->
                扫码支付
              </el-button>
              <el-divider :direction="'vertical'"></el-divider>
              <el-button type="success" @click="orderApply('auto')" :loading="buttonLoading.apply_auto"
                :disabled="buttonLoading.apply_auto">
                <!-- <Icon href="icon-ri_alipay-fill"></Icon>  -->
                自动支付
              </el-button>
              <el-divider :direction="'vertical'"></el-divider>
              <el-button type="success" @click="stopAutoApply">停止自动支付</el-button>
            </div>

            <div class="link-button-group primary">
              <el-button type="primary" @click="confirmOrder()" :loading="buttonLoading.isConfirm"
                :disabled="buttonLoading.isConfirm">确认收货</el-button>

              <el-divider :direction="'vertical'"></el-divider>
              <el-button type="primary" @click="openCommentDialog()">评价与采集</el-button>
              <!-- <el-divider :direction="'vertical'"></el-divider>
            <a>收货并好评</a> -->
              <el-divider :direction="'vertical'"></el-divider>
              <el-button type="primary" @click="openCommentDialog()">追加评价</el-button>
            </div>



            <el-button :loading="buttonLoading.isCancel" :disabled="buttonLoading.isCancel" v-blur
              @click="cancelOrder()">取消订单</el-button>
            <el-button :loading="buttonLoading.isRefund" :disabled="buttonLoading.isRefund" v-blur
              @click="refundOrder()">订单退款</el-button>
            <el-button :loading="buttonLoading.cancelRefund" :disabled="buttonLoading.cancelRefund" v-blur
              @click="cancelRefund()">取消退款</el-button>
            <!-- <el-button :loading="buttonLoading.intervene" @click="orderInterveneState.dialog = true">平台介入</el-button> -->

          </el-space>
        </p>
      </footer>
      <footer class="controls" v-show="searchForm.status === 99">

        <p>
          <span>已选中 <strong>{{ tableSelectState.selectionMap.size }}</strong> 项</span>
        <div class="btns">
          <el-button v-blur type="success" @click="operateLogState.dialog = true">操作日志</el-button>
          <el-button v-blur type="success" :disabled="buttonLoading.orderRecover" :loading="buttonLoading.orderRecover"
            @click="recoverOrder()">恢复订单</el-button>

        </div>
        </p>
        <p class="btns">
        </p>
      </footer>
    </el-config-provider>
    <!-- 一键改价 -->
    <el-dialog v-model="changeOrderPriceState.dialog" title="修改订单价格" top="70px" :width="1028"
      class="change-price-dialog" :append-to-body="true" center>
      <el-form label-position="top" :inline="true">
        <el-form-item label="修改方式">
          <el-radio-group v-model="changeOrderPriceState.type" size='default'>
            <el-radio-button label="price">按目标价格修改</el-radio-button>
            <el-radio-button label="cut">按折扣修改</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="修改目标价格" v-show="changeOrderPriceState.type === 'price'">
          <el-input-number size="default" :precision="2" :step="0.1" :min="0.01"
            v-model="changeOrderPriceState.price"></el-input-number>
        </el-form-item>
        <el-form-item label="修改目标折扣" v-show="changeOrderPriceState.type === 'cut'">
          <el-input-number size="default" :precision="1" :step="0.1" :min="0.1" :max="10"
            v-model="changeOrderPriceState.cut"></el-input-number>
        </el-form-item>
      </el-form>
      <el-alert type="success">
        您可以从下面店铺中选择一个或多个店铺进行改价操作。 <strong>默认全选</strong>
      </el-alert>
      <el-scrollbar height="500">

        <div class="box" v-for="[key, value] in changeOrderPriceState.map"
          :class="{ 'is_checked': changeOrderPriceState.checkedSet.has(key) }">
          <h4 @click="() => {
            changeOrderPriceState.checkedSet.has(key) ? changeOrderPriceState.checkedSet.delete(key) : changeOrderPriceState.checkedSet.add(key)
          }">
            <span class="store-id">店铺ID：{{ key }}</span>
            <span class="store-name">店铺名称：{{ value[0]?.shop_name }}</span>
            <span class="">改价订单数量：{{ value.length }}</span>
          </h4>
        </div>

      </el-scrollbar>

      <template #footer>
        <el-button @click="changeOrderPriceState.dialog = false">取消</el-button>
        <el-button @click="changeOrderPriceSubmit()" :disabled="!changeOrderPriceState.checkedSet.size">提交</el-button>
      </template>
    </el-dialog>
    <!-- 退款理由 -->
    <el-dialog :append-to-body="true" v-model="refundState.dialog" title="申请退款" :width="480"
      class="dialog-480 refund-dialog">
      <el-form label-position="top" :model="refundState" ref="refundFormEl">
        <el-form-item label="申请类型" prop="after_sales_type" :rules="[{ required: true, message: '请选择申请类型' }]">
          <el-select v-model="refundState.after_sales_type">
            <el-option label="仅退款" :value="1"></el-option>
            <el-option label="退款退货" :value="2"></el-option>
            <el-option label="换货" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收货状态" prop="user_ship_status" :rules="[{ required: true, message: '请选择收货状态' }]">
          <el-select v-model="refundState.user_ship_status">
            <el-option label="未收到货" :value="1"></el-option>
            <el-option label="已收到货" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="手机号码" prop="user_phone" :rules="[{ required: true, message: '请输入手机账号' }, {
          pattern: /^1[^0-2]\d{9}$/, message: '请输入合法手机号'
        }]">
          <el-input v-model="refundState.user_phone"></el-input>
        </el-form-item>
        <el-form-item label="申请原因" prop="question_type" :rules="[{ required: true, message: '请选择申请原因' }]">
          <el-select v-model="refundState.question_type">
            <el-option label="质量问题" :value="81"></el-option>
            <el-option label="不喜欢、效果不好" :value="87"></el-option>
            <el-option label="不想要了" :value="88"></el-option>
            <el-option label="货物与描述不符" :value="288"></el-option>
            <el-option label="缺货" :value="290"></el-option>
            <!-- <el-option label="大小/重量与商品描述不符" value="大小/重量与商品描述不符"></el-option>
            <el-option label="生产日期/保质期与商品描述不符" value="生产日期/保质期与商品描述不符"></el-option>
            <el-option label="标签批次包装成分等与商品描述不符" value="标签批次包装成分等与商品描述不符"></el-option>
            <el-option label="商品变质发霉有异物" value="商品变质发霉有异物"></el-option>
            <el-option label="质量问题" value="质量问题"></el-option>
            <el-option label="收到商品少件（含少配件）" value="收到商品少件（含少配件）"></el-option>
            <el-option label="商品破损或污渍" value="商品破损或污渍"></el-option>
            <el-option label="商家发错货" value="商家发错货"></el-option>
            <el-option label="假冒品牌" value="假冒品牌"></el-option> -->
            <el-option label="其他原因" :value="95"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退款原因" prop="question_desc" :rules="[{ required: true, message: '请输入退款原因' }]">
          <el-input type="textarea" v-model="refundState.question_desc" :rows="5" resize="none"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="refundState.dialog = false">取消</el-button>
        <el-button type="primary" v-blur @click="refundSubmit()">确定</el-button>
      </template>
    </el-dialog>
    <!-- 评价与采集 -->
    <el-dialog v-model="state.isChildShow" :append-to-body="false" :show-close="false" class="sub-operate" :width="1028"
      :close-on-press-escape="false" :close-on-click-modal="false" top="65px" :modal="false" :draggable="false">
      <div class="container">
        <h3>
          <span>评价与采集</span>
          <div class="close" @click="state.isChildShow = false">
            <Icon href="icon-close" />
          </div>
        </h3>

        <div class="content">
          <EVALandCOLL ref="EVALandCOLLRef" :order-list="state.commentOrderList" @finish-comment="getList()" />
        </div>
      </div>
    </el-dialog>

    <!-- 订单日志 -->
    <el-dialog v-model="operateLogState.dialog" title="订单操作日志" top="60px" :append-to-body="true" class="log-dialog"
      :width="1028">
      <header>
        <el-radio-group size="default" v-model="operateLogState.active">
          <el-radio-button v-for="item in operateLogState.list" :key="item.value" :label="item.value">{{ item.label
            }}</el-radio-button>
        </el-radio-group>
      </header>
      <div class="btns">
        <el-button type="danger" plain size="default"
          :disabled="!operateLogState.map.get(operateLogState.active)?.length"
          @click="clearItemLog(operateLogState.active)">清除当前日志</el-button>
        <el-button type='danger' size="default" plain :disabled="!buttonLoading[operateLogActive.buttonLoading!]"
          @click="stopRequestItem(operateLogActive.buttonLoading!)">停止{{ operateLogActive.label }}</el-button>
        <el-button type="danger" size="default" @click="stopRequest">停止全部</el-button>
      </div>
      <el-scrollbar ref="logScrollEl">
        <div class="log-list able-select">
          <p v-for="item in operateLogState.map.get(operateLogState.active)" :class="item.type">
            <span class="time">{{ item.time }}</span>
            <span class="msg">{{ item.msg }}</span>
          </p>
        </div>
      </el-scrollbar>
    </el-dialog>

    <!-- 扫码支付 -->
    <el-dialog v-model="orderApplyState.dialog" title="支付" :width="480" class="dialog-480 apply"
      :close-on-click-modal="false" :close-on-press-escape="false" :before-close="async (done: Function) => {
        await ElMessageBox({
          title: '提示',
          type: 'warning',
          message: '确定关闭吗?',
          showCancelButton: true
        })
        done()
      }">
      <div class="pay-container"
        @keyup.right="() => { orderApplyState.index = (orderApplyState.codePaylist.length + orderApplyState.index + 1) % orderApplyState.codePaylist.length }"
        @keyup.left="() => { orderApplyState.index = (orderApplyState.codePaylist.length + orderApplyState.index - 1) % orderApplyState.codePaylist.length }">
        <h3>
          <span :class="codeApplyCurrent.type === 'wechat' ? 'success' : 'primary'">{{ codeApplyCurrent.type ===
            'wechat' ? '微信' : '支付宝' }}</span>
          扫码 ({{ orderApplyState.index + 1 }}/{{ orderApplyState.codePaylist.length }})

          <el-tag type="success" v-show="codeApplyCurrent.pay">已支付</el-tag>
          <el-tag type="info" v-show="!codeApplyCurrent.pay">待支付</el-tag>
        </h3>
        <p> 当前已支付订单({{ orderApplyState.codePaylist.filter(item => item.pay).length }}/{{
          orderApplyState.codePaylist.length }}) </p>
        <div class="img-container">
          <p v-if="!codeApplyCurrent.payInfo">
            等待获取支付链接
          </p>
          <img v-else-if="!codeApplyCurrent.payInfo.code" :src="codeApplyCurrent.payInfo.imgBase64">
          <p v-else class="danger">
            <span>获取支付链接错误 </span>
            <br>
            <span>{{ codeApplyCurrent.payInfo?.msg }}</span>
          </p>
        </div>
        <p>订单金额：<span class="danger f-s-20">{{ codeApplyCurrent?.order.order_amount / 100 }}</span></p>
        <p>订单编号：{{ codeApplyCurrent?.order.order_sn }}
          <el-icon @click="copyStr(codeApplyCurrent?.order.order_sn)" class="primary">
            <DocumentCopy />
          </el-icon>
        </p>
        <p class="btn">
          <el-button size="large" @click="() => {
            orderApplyState.index = (orderApplyState.codePaylist.length + orderApplyState.index - 1) % orderApplyState.codePaylist.length
          }">上一单</el-button>

          <el-button size="large" type="success" @click="() => {
            orderApplyState.index = (orderApplyState.codePaylist.length + orderApplyState.index + 1) % orderApplyState.codePaylist.length
          }">下一单</el-button>
        </p>
      </div>
    </el-dialog>
    <el-dialog v-model="orderRemarkState.dialog" title="订单备注(可在商家后台看到 )" :width="480" class="dialog-480">
      <el-form label-position="top">
        <el-form-item label="旗帜颜色" prop="tag">
          <el-radio-group v-model="orderRemarkState.tag">
            <el-radio label=""> <span>无</span> </el-radio>
            <el-radio v-for="item in orderRemarkState.tagList" :label="item.value">
              <span :style="{ color: item.color || item.value }">{{ item.label }}</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" resize="none" :rows="10" v-model="orderRemarkState.remark"
            placeholder="仅平台客服和商家可见，需在店铺管理受录店铺才能备注"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="orderRemarkState.dialog = false">取消</el-button>
        <el-button @click="orderRemarkSubmit()" type="primary">确定</el-button>
      </template>
    </el-dialog>

    <!-- 平台介入 -->
    <el-dialog v-model="orderInterveneState.dialog" title="平台介入" :width="480" class="dialog-480">
      <el-form label-position="top">
        <el-form-item label="图片(只处理前6张)">
          <div>
            <div class="imgs" style="display: flex;flex-wrap: wrap;">
              <div class="img-box" v-for="(item, index) in orderInterveneState.images.slice(0, 6)" :key="item">
                <el-image :src="safeFileUrl(item)" style="width: 60px; height: 60px;margin-right: 5px;"></el-image>
                <p> <el-link :underline="false" type="danger"
                    @click="orderInterveneState.images.splice(index, 1)">删除</el-link> </p>
              </div>


            </div>
            <p>
              <el-button type="danger" @click="orderInterveneState.images = []">清空</el-button>
              <el-button v-if="orderInterveneState.images.length < 6" @click="addInterveneImg">添加</el-button>
            </p>
          </div>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" resize="none" :rows="4" v-model="orderInterveneState.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="orderInterveneState.dialog = false">取消</el-button>
        <el-button type="primary" @click="orderInterveneSubmit()" :loading="buttonLoading.intervene">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { h, computed, nextTick, onMounted, reactive, ref, watch, onActivated } from "vue";
  import { shortcuts } from "/@/utils/porpery";
  import EVALandCOLL from './eval&coll.vue'
  import { orderTypes } from "../../commdata";
  import { orderRecover, orderList, updateOrderAddress, orderRemark, wechatPay, sendMessageToShop,updateOrderV2, updateOrder, changeOrderPrice, exportCardRequest, getApplyInfo, autoSpellRequest, cancelOrderRequest, orderRefund, orderConfirm, getAutoSellPassId, addressList, delOrderRequest, clearApplyList, cancelRefundRequest, autoSell as autoSellRequest, updateOrderInfo, getSignature, applyPlatform } from "/@/apis/page";
  import { CascaderProps, ElCheckbox, ElIcon, ElLink, ElMessage, ElMessageBox, ElPopover, ElSpace, ElTooltip, FormInstance, ScrollbarInstance } from "element-plus";
  import type { Column, CheckboxValueType } from 'element-plus'
  import dayjs from 'dayjs'
  import { useAutoApply } from "/@/stores/autoApply";
  import { batchRequest, copyStr, delayPromise, paste, random, safeFileUrl } from "/@/utils/common";
  import QRCode from 'qrcode'
  import { DocumentCopy, List, MoreFilled } from '@element-plus/icons-vue'
  import { selectFolder, saveByBase64, openFileWindow, writeFile, writeExcel, makeDir, exists, selectFile, imgToBase64 } from '/@/apis/mainWindow'
  import { useSetting } from "/@/stores/setting";
  import { FixedDir } from "element-plus/es/components/table-v2/src/constants";
  import { useMallStore } from "/@/stores/store";
  import exportOrderVue from "./exportOrder.vue";
  import { VxeTableInstance, VxeTablePropTypes } from "vxe-table";
  import { ipc } from "/@/apis/config";
  import { stop_wechat } from "/@/apis/analyze";
  import { uploadImageByBase64 } from "/@/apis/pddWindow";
  const pagination = reactive({
    limit: 1000,
    total: 0,
    page: 1,
  });
  const currentDate = (function () {
    const date = new Date()
    return {
      year: date.getFullYear(),
      month: date.getMonth(),
      day: date.getDate()
    }
  })()
  const orderStatus = {
    'pendingPay': 0,
    'pendingShare': 1,
    'payed': 2,
    'pendingReceive': 3,
    'pendingComment': 4,
    'canceled': 5,
    'refunded': 7,
    // 已风控
    'risked': 8
  }

  const searchForm = reactive({
    status: -1,
    statusList: [
      {
        label: '全部', value: -1, count: 0,
      },
      {

        label: '待支付', value: 0, count: 0
      },
      // {
      //   label: '待分享', value: 1, count: 0
      // },
      {
        //原 已支付
        label: '待发货', value: 2, count: 0
      },
      {
        // 原 待签收
        label: '待收货', value: 3, count: 0
      },
      {
        // 原 待收货
        label: '已签收', value: -3, count: 0
      },
      {
        label: '待评价', value: 4, count: 0
      },
      {
        label: '已评价', value: -4, count: 0
      },

      {
        label: '已退款', value: 7, count: 0
      },
      {
        label: '交易已取消', value: 5, count: 0
      },
      {
        label: '风控订单', value: 8, count: 0
      },
      {
        label: '回收站', value: 99, count: 0
      },
    ],
    type: '',
    time: [new Date(currentDate.year, currentDate.month, currentDate.day, 0, 0, 0).valueOf(), new Date(currentDate.year, currentDate.month, currentDate.day, 23, 59, 59).valueOf()] as undefined | [number, number],

    commonSearch: {
      key: 'goods_id',
      list: [
        { label: '商品ID', value: 'goods_id' },
        { label: "店铺名", value: 'shop_name' },
        { label: "订单编号", value: "order_sn" },
        { label: "商品价格", value: "price" },
        { label: "规格", value: "sku" },
      ],
      value: ''
    }
  })

  watch(() => searchForm.commonSearch.value, (val) => {
    if (!val) {
      return
    }
    if (String(val).length > 5 && /^\d+$/.test(val)) {
      searchForm.commonSearch.key = 'goods_id'
    } else if (/^[0-9\.]+$/.test(val)) {
      searchForm.commonSearch.key = 'price'
    }
    else if (/^\d{6}-\d+/.test(val)) {
      searchForm.commonSearch.key = 'order_sn'
    } else if (String(val).length > 15) {
      searchForm.commonSearch.key = 'sku'
    } else {
      searchForm.commonSearch.key = 'shop_name'
    }
  })





  type Row = anyObj
  const tableRef = ref < VxeTableInstance < Row >> ()
  const state: {
    isChildShow: boolean
    orderList: Row[]

    checkAll: boolean
    indeterminate: boolean
    // 需要评价的订单
    commentOrderList: Row[]

    requestTids: Set<NodeJS.Timer>
  } = reactive({
    isChildShow: false,
    commentOrderList: [],
    orderList: [],

    checkAll: false,
    indeterminate: false,

    requestTids: new Set()
  })

  const isTableShow = ref(true)
  onActivated(() => {
    isTableShow.value = false
    nextTick(() => {
      isTableShow.value = true
    })
  })


  function rowCheckChange (rowData: anyObj, val: CheckboxValueType) {
    // console.log(rowData, val)
    if (val) {
      tableSelectState.selectionMap.set(rowData.order_sn, rowData)
    } else {
      tableSelectState.selectionMap.delete(rowData.order_sn)
    }
  }



  const tableSelectState: {
    selectionMap: Map<string, Row>
  } = reactive({
    selectionMap: new Map()
  })



  function initTableSelection () {
    nextTick(() => {
      const $table = tableRef.value
      if ($table) {
        const records = $table.getCheckboxRecords()
        tableSelectState.selectionMap.clear()
        records.forEach(item => {
          tableSelectState.selectionMap.set(item.order_sn, item)
        })
      }
    })
  }

  function createLinkRow (value: string) {
    return h(ElLink, { class: 'text-overflow', underline: false, onClick: () => { copyStr(value) } }, () => value)
  }

  function createTooltipRow (value: string, content: string, link = false) {
    return h(ElTooltip, { popperClass: 'table-tooltip', content, 'showAfter': 200 }, { default: () => link ? createLinkRow(value) : h('div', { class: 'text-overflow pointer' }, { default: () => value }) })
  }

  //el-table-v2的column 已弃用 但导出订单信息需要
  const tableColumns: Array<Column<any> & { excelRule?: Partial<exportExcelRule> }> = [
    {
      key: "selection",
      width: 35,
      cellRenderer: ({ rowData }) => {
        return h(ElCheckbox, { onChange: (val) => rowCheckChange(rowData, val), modelValue: rowData.checked })
      },
      headerCellRenderer: () => {
        return h(ElCheckbox, {
          onChange: (val) => {
            if (val) {
              tableSelect('check-all')
            } else {
              tableSelect('remove-all')
            }
          }, indeterminate: state.indeterminate, modelValue: state.checkAll
        })
      },
      fixed: FixedDir.LEFT,

    },
    {
      title: '序号',
      key: 'index',
      width: 50,
      cellRenderer: (scope) => {
        const { limit, page } = pagination
        return h('div', {}, scope.rowIndex + 1 + (page - 1) * limit)
      },
      fixed: FixedDir.LEFT
    },
    {
      title: '所属店铺',
      key: 'shop_name',
      dataKey: "shop_name",
      width: 120,
      cellRenderer: (scope) => createLinkRow(scope.rowData.shop_name),
      excelRule: {
        value: (row) => row.shop_name,
        '!cols': { wch: 25 }
      }
    },
    {
      title: '下单时间',
      key: 'add_time',
      dataKey: "add_time",
      width: 140,
      cellRenderer: (scope) => createTooltipRow(dayjs(scope.rowData.add_time * 1000).format('YYYY-MM-DD HH:mm:ss'), dayjs(scope.rowData.add_time * 1000).format('YYYY-MM-DD HH:mm:ss')),
      excelRule: {
        value: (row) => dayjs(row.add_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        '!cols': {
          wch: 50
        }
      }
    },
    {
      title: '下单类型',
      key: 'type',
      dataKey: "type",
      width: 70,
      cellRenderer: (scope) => h('div', {}, orderTypes.find(item => item.value == scope.rowData.type)?.label),
      excelRule: {
        value: (row) => orderTypes.find(item => item.value == row.type)?.label || '',
        '!cols': { wch: 20 }
      }
    },
    {
      title: '订单状态',
      key: 'order_status',
      dataKey: "order_status",
      width: 70,

      // cellRenderer: (scope) => h('div', {}, scope.rowData.order_status_prompt || searchForm.statusList.find(item => item.value == Number(scope.rowData.combined_order_status))?.label)
      cellRenderer: (scope) => {
        const str = scope.rowData.order_status_prompt || searchForm.statusList.find(item => item.value == Number(scope.rowData.combined_order_status))?.label || ''
        return createTooltipRow(str, str)
      },
      excelRule: {
        value: (row) => row.order_status_prompt,
        '!cols': { wch: 25 }
      }
    },
    {
      title: '订单编号',
      key: 'order_sn',
      dataKey: "order_sn",
      width: 170,
      // cellRenderer: (scope) => h('div', {}, scope.rowData.order_sn)
      cellRenderer: (scope) => createLinkRow(scope.rowData.order_sn),
      excelRule: {
        value: (row) => row.order_sn, '!cols': { wch: 25 }
      }
    },
    {
      title: '价格(元)',
      key: 'order_amount',
      dataKey: "order_amount",
      width: 70,
      cellRenderer: (scope) => h('div', {}, Number(scope.rowData.order_amount) / 100),
      excelRule: {
        value: (row) => Number(row.order_amount) / 100,
        '!cols': {
          wch: 15
        }
      }
    },
    {
      title: '购买数量',
      key: 'goods_number',
      dataKey: "goods_number",
      width: 70,
      cellRenderer: (scope) => h('div', {}, scope.rowData.goods_number),
      excelRule: {
        value: row => String(row.goods_number),
        '!cols': { wch: 10 }
      }
    },
    {
      title: '商品ID',
      key: 'goods_id',
      dataKey: "goods_id",
      width: 110,
      // cellRenderer: (scope) => h('div', {}, scope.rowData.goods_id)
      cellRenderer: (scope) => createLinkRow(scope.rowData.goods_id),
      excelRule: {
        value: row => String(row.goods_id),
        '!cols': {
          wch: 15
        }
      }
    },
    {
      title: '小号',
      key: 'account',
      dataKey: "account",
      width: 110,
      cellRenderer: (scope) => createLinkRow(scope.rowData.account),
      excelRule: {
        value: (row) => String(row.account),
        '!cols': { wch: 30 }
      }
    },
    {
      title: '商品名称',
      key: 'goods_name',
      dataKey: "goods_name",
      width: 200,
      cellRenderer: (scope) => createTooltipRow(scope.rowData.goods_name, scope.rowData.goods_name),
      excelRule: {
        value: (row) => row.goods_name, '!cols': { wch: 50 }
      }
    },
    {
      title: '购买规格',
      key: 'sku_spec',
      dataKey: "sku_spec",
      width: 200,
      maxWidth: 600,
      minWidth: 200,
      sortable: true,
      // cellRenderer: (scope) => h('div', {}, scope.rowData.sku_spec)
      headerCellRenderer: () => h(ElSpace, {}, {
        default: () => [
          h('span', {}, '规格'),
          h(ElLink, {
            underline: false, type: 'primary', onClick: () => {
              const map = new Map < string, Row[]> ()
              state.orderList.forEach(item => {
                const { sku_spec } = item
                if (map.has(sku_spec)) {
                  map.get(sku_spec!)?.push(item)
                } else {
                  map.set(sku_spec, [item])
                }
              })
              const data = [...map.values()]
              state.orderList = data.flat(1)
            }
          }, () => '排序')
        ]
      }),
      cellRenderer: (scope) => createTooltipRow(scope.rowData.sku_spec, scope.rowData.sku_spec, true),
      excelRule: {
        value: (row) => row.sku_spec, '!cols': { wch: 50 }
      }
    },
    // {
    //   title: '收件人',
    //   key: 'name',
    //   dataKey: "name",
    //   width: 80,
    //   cellRenderer: (scope) => h('div', {}, scope.rowData.name)
    // },
    // {
    //   title: '手机号',
    //   key: 'phone',
    //   dataKey: "phone",
    //   width: 120,
    //   cellRenderer: (scope) => h('div', {}, scope.rowData.phone)
    // },
    // {
    //   title: '省',
    //   key: 'province',
    //   dataKey: "province",
    //   width: 120,
    //   cellRenderer: (scope) => h('div', {}, scope.rowData.province)
    // },
    // {
    //   title: '市',
    //   key: 'city',
    //   dataKey: "city",
    //   width: 140,
    //   cellRenderer: (scope) => h('div', {}, scope.rowData.city)
    // },
    // {
    //   title: '区',
    //   key: 'district',
    //   dataKey: "district",
    //   width: 120,
    //   cellRenderer: (scope) => h('div', {}, scope.rowData.district)
    // },
    // {
    //   title: '详细地址',
    //   key: 'address',
    //   dataKey: "address",
    //   width: 120,
    //   // cellRenderer: (scope) => h('div', {}, scope.rowData.address)
    //   cellRenderer: (scope) => createTooltipRow(scope.rowData.address, scope.rowData.address)
    // },
    {
      title: "收货信息",
      width: 200,
      dataKey: 'recipient',
      cellRenderer: (scope) => {
        const { name, phone, province, city, district, address } = scope.rowData
        const info = `${name} ${phone} \n ${province} ${city} ${district} \n ${address}`
        return createTooltipRow(info, info)
      },

      excelRule: {
        label: '订单地址',
        value: row => row.province + row.city + row.district + row.address,
        '!cols': { wch: 100 }
      }
    },
    {
      title: '快递公司',
      key: 'express',
      dataKey: "express",
      width: 120,
      // cellRenderer: (scope) => h('div', {}, scope.rowData.express)
      cellRenderer: (scope) => createTooltipRow(scope.rowData.express, scope.rowData.express),
      excelRule: {
        value: row => row.express || ''
      }
    },
    {
      title: '快递单号',
      key: 'tracking_number',
      dataKey: "tracking_number",
      width: 120,
      // cellRenderer: (scope) => h('div', {}, scope.rowData.tracking_number)
      cellRenderer: (scope) => createLinkRow(scope.rowData.tracking_number),
      excelRule: {
        value: (row) => String(row.tracking_number), '!cols': { wch: 45 }
      }
    },
    {
      title: '物流信息',
      key: 'logistics',
      dataKey: "logistics",
      width: 130,
      // cellRenderer: (scope) => h('div', {}, scope.rowData.logistics)
      cellRenderer: (scope) => createTooltipRow(scope.rowData.logistics, scope.rowData.logistics),
      excelRule: {
        value: (row) => row.logistics,
        '!cols': { wch: 80 }
      }
    },
    {
      title: '店铺收藏',
      key: 'is_collect',
      dataKey: "is_collect",
      width: 100,
      cellRenderer: (scope) => h('div', {}, scope.rowData.is_collect ? '是' : '否'),
      // excelRule: {
      //   value: row => row.is_collect ? '是' : '否',
      //   '!cols': { wch: 10 }
      // }
    },
    {
      title: '店铺关注',
      key: 'is_attention',
      dataKey: "is_attention",
      width: 100,
      cellRenderer: (scope) => h('div', {}, scope.rowData.is_attention ? '是' : '否'),
      // excelRule: {
      //   value: row => row.is_attention ? '是' : '否',
      //   '!cols': { wch: 10 }
      // }
    },
    {
      title: '操作',
      key: 'action',
      dataKey: "action",
      width: 60,
      fixed: FixedDir.RIGHT,
      cellRenderer: (scope) => h(ElPopover, { popperClass: 'table-action-popover', placement: 'bottom-end', showArrow: false, showAfter: 100 },
        {
          reference: () => h('span', { class: 'pointer' }, h(ElIcon, {}, { default: () => h(MoreFilled) })),
          default: () => h('div', { class: 'action-list' }, [
            h('p', { onClick: () => openCommentDialog([scope.rowData]) }, '手工评价'),
            h('p', { onClick: () => openCommentDialog([scope.rowData]) }, '追加评价'),
            h('p', { onClick: () => openTableAction('chat_dialog', scope.rowData) }, '与此商聊天'),
            h('p', { onClick: () => openTableAction('change_addr_dialog', scope.rowData) }, '修改地址'),
            // h('p',{onClick:()=>openCommentDialog([scope.rowData])},'复制宝贝ID'),
          ])
        }
      )
    },
  ]

  const EVALandCOLLRef = ref < InstanceType < typeof EVALandCOLL >> ()
  function openCommentDialog (list = selectionList.value) {
    if (EVALandCOLLRef.value?.getButtonLoading().comment) {
      ElMessage.info({
        message: "当前有评价正在执行",
        grouping: true
      })
      state.isChildShow = true
      return
    }
    const filterList = list.filter(item => {
      return item.combined_order_status === orderStatus.pendingComment
    })
    if (filterList.length) {
      // return ElMessage.warning({
      //   message: list.length ? '选择的订单中没有可以评价的订单' : "请选择需要评价的订单",
      //   grouping: true
      // })
      state.commentOrderList = filterList
    }
    state.isChildShow = true
  }

  const buttonLoading = reactive({
    //获取列表
    getList: false,
    //  正在进行订单同步
    isOrderSync: false,
    // 删除订单
    isDelete: false,
    // 取消订单
    isCancel: false,
    /**改价 */
    changePrice: false,
    /**订单备注 */
    isRemark: false,
    // 退款
    isRefund: false,
    // 取消退款
    cancelRefund: false,
    // 訂單確認
    isConfirm: false,
    // 获取核销卡券
    exportCard: false,
    // 导出支付二维码
    exportPayCode: false,
    // 自动核销卡券
    autoSell: false,
    // 一件免拼
    autoSpell: false,
    // 与商家聊天
    chatWidthShop: false,
    // 修改订单地址
    changeAddr: false,
    /**扫码支付 */
    apply_code: false,
    /**自动支付 */
    apply_auto: false,
    /** 微信支付*/
    apply_wechat: false,
    /**恢复订单 */
    orderRecover: false,
    /**平台接入 */
    intervene: false
  })

  const logScrollEl = ref < ScrollbarInstance > ()
  type LogItem = 'cancel' | 'changeOrderPrice' | 'delete' | 'refund' | 'confirm' | 'autoSell' | 'autoSpell' | 'cancelRefund' | 'remark' | 'intervene'
  type LogMsgItem = { msg: string, type?: 'danger' | 'success' | 'warning', time: string }
  const operateLogState: {
    dialog: boolean
    list: Array<{ label: string, value: LogItem, buttonLoading?: keyof typeof buttonLoading }>
    active: LogItem
    map: Map<LogItem, Array<LogMsgItem>>
  } = reactive({
    dialog: false,
    list: [
      { label: "取消订单", value: 'cancel', buttonLoading: 'isCancel' },
      { label: '订单改价', value: "changeOrderPrice", buttonLoading: 'changePrice' },
      { label: '订单删除', value: "delete", buttonLoading: "isDelete" },
      { label: "订单退款", value: 'refund', buttonLoading: "isRefund" },
      { label: "取消退款", value: 'cancelRefund', buttonLoading: "cancelRefund" },
      { label: "订单确认", value: 'confirm', buttonLoading: "isConfirm" },
      { label: "券自动核销", value: 'autoSell', buttonLoading: 'autoSell' },
      { label: "订单免拼", value: 'autoSpell', buttonLoading: 'autoSpell' },
      { label: "订单备注", value: 'remark', buttonLoading: 'isRemark' },
      // { label: "平台介入", value: 'intervene', buttonLoading: 'intervene' },
    ],
    active: 'cancel',
    map: new Map()
  })
  const operateLogActive = computed(() => {
    const { list, active } = operateLogState
    return list.find(item => item.value === active)!
  })

  watch([() => operateLogState.map, () => operateLogState.active], (value) => {
    nextTick(() => {
      let top = logScrollEl.value?.wrapRef?.scrollHeight || 0
      logScrollEl.value?.setScrollTop(top < 0 ? 0 : top)
    })
  }, { deep: true })



  function log (type: LogItem, data: Omit<LogMsgItem, 'time'>) {
    if (!operateLogState.map.get(type)) {
      operateLogState.map.set(type, [])
    }
    operateLogState.map.get(type)?.push({
      ...data,
      time: dayjs().format('YYYY-MM-DD HH:mm:ss')
    })
  }

  const selectionList = computed(() => {
    return [...tableSelectState.selectionMap.values()]
  })

  watch([() => selectionList.value.length, () => state.orderList.length], () => {
    const listLength = state.orderList.length
    const selectionLength = selectionList.value.length
    if (!selectionLength) {
      state.indeterminate = false
      state.checkAll = false
    }
    else if (selectionLength === listLength) {
      state.indeterminate = false
      state.checkAll = true
    } else {
      state.indeterminate = true
      state.checkAll = false
    }
  }, { immediate: true })

  // watch(() => tableSelectState.selectionMap, (val) => {
  //   // console.log(123213, val.size)
  //   state.orderList.forEach(item => {
  //     if (val.has(item.order_sn)) {
  //       item.checked = true
  //     } else {
  //       item.checked = false
  //     }
  //   })
  // }, { immediate: true, deep: true })

  async function tableSelect (action: 'check-all' | 'remove-all' | 'reverse' | 'area-select') {

    switch (action) {
      case 'check-all': {
        // tableRef.value?.toggleAllSelection()
        // state.orderList.forEach(item => {
        //   tableSelectState.selectionMap.set(item.order_sn, item)
        // })
        tableRef.value?.setAllCheckboxRow(true)
        break
      }
      case 'remove-all': {
        // tableRef.value?.clearSelection()
        // tableSelectState.selectionMap.clear()
        tableRef.value?.clearCheckboxRow()
        break
      }
      case 'reverse': {
        // tableRef.value?.toggleAllCheckboxRow()
        // const idSet = new Set(selectionList.value.map(item => item.id))
        state.orderList.forEach(item => {

          tableRef.value?.toggleCheckboxRow(item)
          // if (tableSelectState.selectionMap.has(item.order_sn)) {
          //   // tableSelectState.selectionMap.delete(item.order_sn)

          // } else {
          //   // tableSelectState.selectionMap.set(item.order_sn, item)
          // }

        })
        break
      }
      case 'area-select': {
        await ElMessageBox.prompt('请输入需要选中的订单(用-隔开),留空表示起始值或末尾值\n(例：20-30 ; -30 ; 20- ; -)', '区域选择', {
          // inputType: 'textarea',
          showCancelButton: true,
          // customClass: 'limit-input-textarea'
          inputPlaceholder: "请输入范围"
        })
          .then(res => {
            const { value } = res
            let [min, max] = value.split('-').map(item => Number(item.trim()))
            min = min - 1 < 0 ? 0 : min - 1
            max = max || state.orderList.length
            if (min > max) {
              return ElMessage.warning({
                message: '最小值大于最大值',
                grouping: true
              })
            }
            tableSelect('remove-all')
            const list = tableRef.value?.getTableData().visibleData.slice(min, max) || []
            // const list = state.orderList.slice(min, max)
            // tableRef.value?.clearSelection()
            // tableSelectState.selectionMap.clear()
            const order_snSet = new Set(list.map(item => item.order_sn))
            state.orderList.find(item => {
              if (order_snSet.has(item.order_sn)) {
                tableRef.value?.setCheckboxRow(item, true)
                order_snSet.delete(item.order_sn)
              }
              return !order_snSet.size
            })
            // console.log(res)
            // const orderSns = new Set(value.split('\n').filter(item => item).map(item => item.trim()))
            // state.orderList.forEach(item => {
            //   tableRef.value?.toggleRowSelection(item, orderSns.has(item.order_sn))
            // })
          })
      }

    }

    // console.log(tableSelectState.selectionMap.size)
    initTableSelection()
  }



  async function tableFunc (type: 'Statistics') {
    switch (type) {
      case 'Statistics': {
        const total = {
          all: 0,
          selection: 0
        }
        const selection = tableSelectState.selectionMap
        state.orderList.forEach(item => {
          const order_amount = Number(item.order_amount / 100)
          if (selection.has(item.order_sn)) {
            total.selection += order_amount
          }
          total.all += order_amount
        })
        return ElMessageBox({
          title: '订单金额统计',
          message: h('div', {}, [
            h('p', { class: 'success ', }, `选中订单：${total.selection.toFixed(2)}元`),
            h('p', { class: 'danger m-t-10' }, `本页订单：${total.all.toFixed(2)}元`)
          ])
        })
        break
      }
    }
  }

  function orderCateClick (item: typeof searchForm.statusList[number]) {
    if (buttonLoading.getList) {
      return
    }
    // console.log('orderCateClick')
    searchForm.status = item.value
    getList()

  }

  function getList (resetSelection = true) {
    // console.log('getList')
    const { limit, page } = pagination
    const { status, time, type, commonSearch } = searchForm

    const start = Date.now()

    // status 已评价和待评价都是4 但已评价的comment_status 为1  
    // status 待收货和已发货(已签收)都是3 但已发货的shipping_status 为1  
    const data: Parameters<typeof orderList>[0] = {
      limit,
      page,
      status: (status === -1 || status === 99) ? void 0 : Math.abs(status),
      time: time && time.map(item => Math.floor(item / 1000)).join(' - '),
      type,
      comment_status: status === -4 ? 1 : void 0,
      shipping_status: status === -3 ? 1 : void 0,
      is_delete: (status === 99) ? 1 : void 0
    }
    Reflect.set(data, commonSearch.key, commonSearch.value.trim())
    buttonLoading.getList = true
    console.log(data)
    orderList(data)
      .then(res => {
        console.log('getList - success', res)
        // return
        state.orderList = res.data.data
        // state.orderList = shuffle(state.orderList)
        pagination.total = res.data.total
        // if (resetSelection) {
        nextTick(() => {
          // const map: typeof tableSelectState.selectionMap = new Map()
          // tableSelect('check-all')
          let size = tableSelectState.selectionMap.size
          state.orderList.find(item => {
            if (tableSelectState.selectionMap.has(item.order_sn)) {
              size--
              tableRef.value?.setCheckboxRow(item, true)
              // map.set(item.order_sn, item)
            }
            return size <= 0
          })
          // tableSelectState.selectionMap = map
          initTableSelection()
        })
        // }

        // nextTick(() => {
        //   tableRef.value?.doLayout()
        // })
      })
      .catch(res => {
        console.log('getList - error', res)
      })
      .finally(() => {
        console.log('用时:', Date.now() - start)
        buttonLoading.getList = false
      })
  }

  const changeOrderPriceState = reactive({
    map: new Map < string | number, Array<Row>> (),
    checkedSet: new Set < string | number > (),
    dialog: false,
    type: 'price' as 'price' | 'cut',
    price: 0.01,
    cut: 1,
})



  function change_checkOrders (list = selectionList.value) {
    list = list.filter(item => Number(item.combined_order_status) === orderStatus.pendingPay)
    if (!list.length) {
      return ElMessage.warning({
        message: '选择的订单中没有符合条件的订单'
      })
    }
    const { map } = changeOrderPriceState
    map.clear()
    list.forEach(item => {

      const _list = map.get(item.shop_id)
      if (_list) {
        _list.push(item)
      } else {
        map.set(item.shop_id, [item])
      }
    })
    changeOrderPriceState.checkedSet = new Set([...map.keys()])
    changeOrderPriceState.dialog = true
  }

  /**提交修改订单价格 */
  async function changeOrderPriceSubmit () {
    buttonLoading.changePrice = true
    changeOrderPriceState.dialog = false
    const logActive: LogItem = 'changeOrderPrice'
    // operateLogState.dialog = true
    operateLogState.active = logActive
    const { map, type, cut, checkedSet } = changeOrderPriceState
    const price = changeOrderPriceState.price * 100

    const storePromiseArr: Promise<any>[] = [];
    checkedSet.forEach(shop_id => {
      if (map.has(shop_id)) {
        storePromiseArr.push(new Promise(async (resolve, reject) => {

          const list = map.get(shop_id)!
          const max = list.length
          let current = 0
          function request (limit = 7) {
            if (!list.length) {
              reject()
              return
            }
            const items: typeof list = []
            while (items.length < limit && list.length) {
              items.push(list.shift()!)
            }

            Promise.allSettled(items.map((item, index) => {
              // const item = list.shift()!
              const { order_amount, order_sn, shop_name, id, shop_id } = item
              let goodsDiscount: number
              switch (type) {
                case 'cut': {
                  goodsDiscount = order_amount * (10 - cut) / 10
                  break
                }
                case 'price': {
                  goodsDiscount = order_amount - price
                  break
                }
              }
              return changeOrderPrice({
                store_id: shop_id,
                goodsDiscount,
                order_sn,
                shop_name
              })
                .then(res => {
                  // 同步订单

                  log(logActive, {
                    type: "success",
                    msg: `【${shop_name}】:${order_sn}修改成功,(${++current}/${max})`
                  })
                })
                .catch(res => {
                  console.log(res)
                  if (res.code == 2) {
                    // 需要重新登录
                    log(logActive, {
                      type: 'danger',
                      msg: `【${shop_name}】:${order_sn}修改失败：会话已过期,请重新登录店铺,(${++current}/${max})`
                    })
                    list.length = 0
                  } else {
                    // 普通错误
                    log(logActive, {
                      type: 'danger',
                      msg: `【${shop_name}】:${order_sn}修改失败：${res.msg},(${++current}/${max})`
                    })
                  }
                })
            }))
              .then(async () => {
                await Promise.allSettled([
                  updateOrder({ ids: items.map(item => item.order_sn).join(',') }, { showErrorMsg: false })
                ])
                if (!buttonLoading.changePrice) {
                  log(logActive, {
                    msg: "检测到暂停改价触发，已停止后续操作"
                  })
                  await delayPromise(1000)
                  reject()
                }
                else if (list.length) {
                  await delayPromise(500)
                  request()
                } else {
                  await delayPromise(1000)
                  resolve(true)
                }
              })
          }
          request(1)
        }))
      }
    })
    await Promise.allSettled(storePromiseArr)
    buttonLoading.changePrice = false
    taskEndLog(logActive, 1000)

  }

  const orderRemarkState = reactive({
    dialog: false,
    remark: "",
    tag: '',
    tagList: [
      {
        label: "红色",
        value: "red"
      },
      {
        label: "紫色",
        value: "purple"
      },
      {
        label: "绿色",
        value: "green"
      },
      {
        label: "蓝色",
        value: "blue"
      },
      {
        label: "黄色",
        value: "yellow",
        color: 'orange'
      },
    ]
  })
  function orderRemarkSubmit (list = selectionList.value) {
    orderRemarkState.dialog = false
    if (!list.length) {
      ElMessage.warning({
        message: '请选择订单',
        grouping: true
      })
      return
    }
    buttonLoading.isRemark = true
    // orderRemark()
    const logActive: LogItem = 'remark'
    operateLogState.active = logActive
    const { remark, tag, tagList } = orderRemarkState
    const tagActive = tagList.find(item => item.value == tag)
    const max = list.length
    let current = 1
    const requestList = [...list]
    const fn = async (limit = 10) => {
      if (requestList.length) {
        await Promise.allSettled(requestList.splice(0, limit).map(order => {
          const { order_sn, shop_id, shop_name } = order
          return orderRemark({
            orderSn: order_sn,
            store_id: shop_id,
            remark,
            remarkTagName: tagActive?.label || null,
            remarkTag: tagActive?.value.toUpperCase() || null,
          })
            .then(res => {
              log(logActive, {
                type: 'success',
                msg: `【${shop_name}】:${order_sn}备注成功,(${current}/${max})`
              })
            })
            .catch(res => {
              if (res.code == 2) {
                // 需要重新登录
                log(logActive, {
                  type: 'danger',
                  msg: `【${shop_name}】:${order_sn}备注失败：会话已过期,请重新登录店铺,(${current}/${max})`
                })
                const mallStore = useMallStore()
                const store = mallStore.tableList.find(item => item.mallId === Number(shop_id))
                store && mallStore.disabledStore(store)
                requestList.length = 0
              } else {
                // 普通错误
                log(logActive, {
                  type: 'danger',
                  msg: `【${shop_name}】:${order_sn}备注失败：${res.msg},(${current}/${max})`
                })
              }
            })
            .finally(() => {
              current++
            })
        }))
        if (!buttonLoading.isRemark) {
          // 暂停
          log(logActive, {
            msg: "检测到暂停备注触发，已停止后续操作"
          })
        } else {
          fn()
        }
      } else {
        buttonLoading.isRemark = false
        taskEndLog(logActive)
      }
    }
    fn(1)
  }


  const orderSyncState = reactive({
    progressShow: false,
    max: 0,
    current: 0,
    stopFlag: false
  })
  const orderSyncPercentage = computed(() => {
    const { max, current } = orderSyncState
    if (max) {
      const result = Math.ceil(current / max * 100)
      return result >= 100 ? 100 : result
    } else {
      return 0
    }
  })

  function orderSync (list = selectionList.value) {
    if (!list.length) {
      return ElMessage.warning({
        message: '请选择需要同步的订单',
        grouping: true
      })
    }
    const requestList = [...list]
    orderSyncState.current = 0
    orderSyncState.max = requestList.length
    orderSyncState.stopFlag = false
    orderSyncState.progressShow = true
    const groupMax = 20
    buttonLoading.isOrderSync = true
    function request () {
      const requestOrders: anyObj[] = []
      // while (requestList.length && order_ids.length < groupMax) {
      //   order_ids.push(requestList.shift()!.id)
      // }
      for (let index = 0; index < groupMax; index++) {
        // const element = array[index];
        const item = requestList.shift()
        if (!item) {
          continue
        }
        // if (!requestOrders.find((_item)=>item.account === _item.account)) {
        requestOrders.push(item)
        // } else {
        // requestList.push(item)
        // }

      }

      return updateOrder({ ids: requestOrders.map(item => item.order_sn).join(',') }, { loading: false, showErrorMsg: false })
        .finally(async () => {
          orderSyncState.current += requestOrders.length

          if (requestList.length) {
            request()
          } else {
            // ElMessage.success('同步完成')
            buttonLoading.isOrderSync = false
            orderSyncState.progressShow = false
            // await ElMessageBox({
            //   type: "success",
            //   title: "同步成功",
            //   message: "订单同步成功，是否重新获取列表?",
            //   showCancelButton: true
            // })
            getList(false)
          }
        })
    }
    request()

  }

  onMounted(() => {
    getList()
  })


  const tableAction: {
    row: Row | null
    chat_dialog: boolean
    chat_type: "order" | 'random'
    chat_num: number
    chat_content: string

    change_addr_dialog: boolean
    change_addr: {
      address: string
      name: string,
      phone: string
      p_c_d: string[]
      options: anyObj[]
    }
  } = reactive({
    row: null,

    // 与商家聊天
    chat_dialog: false,
    chat_type: 'order',
    chat_num: 3,
    chat_content: "",
    // 修改地址
    change_addr_dialog: false,
    change_addr: {
      phone: "",
      name: "",
      /**省市区 */
      p_c_d: [],
      address: '',
      options: []
    }
  })
  function openTableAction (dialog: 'change_addr_dialog' | 'chat_dialog', row: Row) {
    tableAction[dialog] = true
    tableAction.row = row
    switch (dialog) {
      case 'change_addr_dialog': {
        tableAction.change_addr.name = row.name || ''
        tableAction.change_addr.phone = row.phone || ''
        tableAction.change_addr.address = row.address || ''
        tableAction.change_addr.p_c_d = [row.province, row.city, row.district]
        break
      }
    }
  }
  addressList({ pid: 0 })
    .then(res => {
      console.log('addressList', res)
      tableAction.change_addr.options = res.data
    })

  const addressCascader = ref < any > ()
  const changeAddrForm = ref < FormInstance > ()
  async function changeAddressSubmit () {
    // const {phone,name,p_c_d,address,options} = tableAction.change_addr
    // console.log(addressCascader.value?.getCheckedNodes(false))
    await changeAddrForm.value?.validate()
    const { row } = tableAction
    const { name, p_c_d, phone, address } = tableAction.change_addr
    buttonLoading.changeAddr = true
    updateOrderAddress({
      order_sn: row!.order_sn,
      name,
      phone,
      address,
      province: p_c_d[0],
      city: p_c_d[1],
      district: p_c_d[2],
    })
      .then(res => {
        ElMessage.success('修改成功')
        tableAction.change_addr_dialog = false
        getList()
      })
      .finally(() => {
        buttonLoading.changeAddr = false
      })
  }

  function chatWidthShop () {
    const { row, chat_num, chat_type, chat_content } = tableAction
    if (!row || !chat_content) {
      ElMessage.warning({
        message: '请填写聊天内容',
        grouping: true
      })
    } else {
      const contentArr = chat_content.split('\n').filter(item => item)
      if (!contentArr.length) {
        return ElMessage.warning({
          message: '聊天内容列表为空',
          grouping: true
        })
      }
      let index = 0
      const getIndex = () => {
        switch (chat_type) {
          case 'order': {
            return (contentArr.length + index++) % contentArr.length
          }
          case 'random': {
            return random(0, contentArr.length)
          }
        }
      }
      const requestArr: Promise<any>[] = []
      buttonLoading.chatWidthShop = true
      for (let i = 0; i < chat_num; i++) {
        requestArr.push(new Promise((resolve, reject) => {
          setTimeout(() => {
            sendMessageToShop({
              shop_id: row.shop_id,
              account: row.account,
              content: contentArr[getIndex()]
            })
              .then(resolve)
              .catch(reject)
          }, i * 1000)
        }))
      }
      Promise.allSettled(requestArr)
        .then(res => {
          // console.log(res)
          buttonLoading.chatWidthShop = false
          tableAction.chat_dialog = false
          ElMessage.success('消息发送完成')
        })
    }
  }

  const props: CascaderProps = {
    lazy: true,
    lazyLoad (node, resolve) {
      const { data, level } = node
      addressList({
        pid: data!.id as number
      })
        .then(res => {
          resolve(res.data.map((item: anyObj) => {
            return {
              ...item,
              leaf: level >= 2
            }
          }))
        })
    },
    label: 'name',
    value: 'name'
  }

  const orderApplyState: {
    dialog: boolean
    codePaylist: Array<{
      order: typeof state.orderList[number],
      payInfo?: { data: { url: string, order_sn: string, id: number }, code: number, msg: string, imgBase64?: string },
      type: string,
      pay?: boolean
    }>,
    index: number
  } = reactive({
    dialog: false,
    codePaylist: [],
    index: 0
  })

  /**显示扫码支付的哪一张 */
  const codeApplyCurrent = computed(() => {
    return orderApplyState.codePaylist[orderApplyState.index]
  })


  // 支付订单
  function orderApply (type: 'wechat' | 'code' | 'auto', list = selectionList.value) {
    const filterList = list.filter(item => item.combined_order_status === orderStatus.pendingPay)
    if (!filterList.length) {
      return ElMessage.warning({
        message: !list.length ? '请选择订单' : '选择的订单没有未支付的订单',
        grouping: true
      })
    }


    if (type === 'code') {
      applyByCode(list)
      return
    } else if (type === 'wechat') {
      applyByWechat(list)
      return
    }

    const settingStore = useSetting()
    if (!settingStore.pay.zfb_pass) {
      return ElMessage.warning({
        message: "没有设置支付密码，不能自动支付",
        grouping: true
      })
    }
    const batchMax = 10
    let count = 0
    const autoApply = useAutoApply()
    buttonLoading.apply_auto = true
    function getPayUrlRepeat () {
      const requestList = filterList.slice(count, count + batchMax)
      count += requestList.length
      const rquestPromiseArr = requestList.map(item => {
        return getApplyInfo({ order_sn: item.order_sn }, { showErrorMsg: false, repeatWhenFailed: true })
          .then(res => {

            if (!res.code && res.data.url) {
              const { id, order_sn } = item
              autoApply.addToPendding([{
                url: res.data.url,
                order_sn,
                id: id || order_sn,
                type: 'auto'
              }])
            } else {

            }
          })
          .catch(res => {
            ElMessage.error({
              message: '获取支付链接失败',
              grouping: true
            })
          })

      })
      Promise.allSettled(rquestPromiseArr)
        .then(() => {
          if (count >= filterList.length) {
            buttonLoading.apply_auto = false
          } else {
            if (buttonLoading.apply_auto) {
              getPayUrlRepeat()
            }
          }

        })
    }
    getPayUrlRepeat()
  }

  //停止自动支付
  async function stopAutoApply () {
    await ElMessageBox({
      title: "提示",
      type: 'warning',
      message: "确定要停止自动支付吗？",
      showCancelButton: true
    })
    // 清除后续已加载支付链接的订单
    clearApplyList()
    const applyStore = useAutoApply()
    applyStore.stop()
    // 清除后续未加载支付链接的订单
    buttonLoading.apply_auto = false
  }

  /**自动支付结果监听 start */
  async function autoApplyRes (event: Electron.IpcRendererEvent, data: { id: number, order_sn: string, type: 'success' | 'error' | 'close', msg?: string, left: number }) {
    const applyStore = useAutoApply()
    switch (data.type) {
      case 'success': {
        applyStore.resolveResult(data)
        if (data.left === 0) {
          await delayPromise(1500)
          getList()
        }
        break
      }
      case 'close': {
        await delayPromise(1500)
        getList()
        break
      }
      case 'error': {
        data.msg && ElMessage.error({
          grouping: true,
          message: `${data.order_sn}:${data.msg}`
        })
      }
    }
  }
  ipc.off('autoapply-complete', autoApplyRes)
  ipc.on('autoapply-complete', autoApplyRes)
  /**自动支付 end */

  /**扫码支付时，检测订单支付状态 */
  async function checkPayStatus () {
    if (!orderApplyState.dialog) {
      return
    }
    await new Promise(async (resolve) => {
      if (!codeApplyCurrent.value || codeApplyCurrent.value.pay) {
        await delayPromise(1000)
        resolve(true)
      } else {
        checkPayStatus_order(codeApplyCurrent.value)
          .finally(async () => {
            await delayPromise(800)
            resolve(true)
          })
      }
    })
    checkPayStatus()
  }

  /**检测待支付(扫码支付)订单信息 */
  function checkPayStatus_order (item: typeof orderApplyState.codePaylist[number]) {
    return updateOrderInfo({ order_sn: item.order.order_sn })
      .then(async res => {
        if (res.data[0]) {
          const { order_status, order_sn } = res.data[0]
          if (order_status == 1) {
            const item = orderApplyState.codePaylist.find(item => item.order.order_sn == order_sn)
            if (item) {
              item.pay = true
            }
            if (!orderApplyState.codePaylist.find(item => !item.pay)) {
              await delayPromise(1000)
              orderApplyState.dialog = false
              getList()
            } else {
              orderApplyState.index = (orderApplyState.codePaylist.length + orderApplyState.index + 1) % orderApplyState.codePaylist.length
            }
          }

        }
        return res
      })
  }

  /** 自动支付 支付宝 二维码 */
  function applyByCode (list = selectionList.value) {
    buttonLoading.apply_code = true
    orderApplyState.codePaylist = list.map(item => ({
      order: item,
      type: "code"
    }))
    orderApplyState.index = 0
    orderApplyState.dialog = true
    try {
      stop_wechat()
    } catch (e) {
      buttonLoading.apply_code = true
    }
    /**一批请求多少个 */
    const batchMax = 10;
    /** 已请求多少次 */
    let count = 0;
    function requestRepeat () {
      const requestList = orderApplyState.codePaylist.slice(count, count + batchMax)
      count += requestList.length
      const rquestPromiseArr = requestList.map(item => {
        return getApplyInfo({ order_sn: item.order.order_sn }, { showErrorMsg: false })
          .then(res => {
            item.payInfo = res
            if (!res.code && res.data.url) {
              QRCode.toDataURL(res.data.url)
                .then(imgBase64 => {
                  // payInfo.imgBase64 = imgBase64
                  item.payInfo!.imgBase64 = imgBase64
                })
            } else {
              item.payInfo!.msg = '没有获取到支付链接'
            }
          })
          .catch(res => {
            item.payInfo = res
          })

      })
      Promise.allSettled(rquestPromiseArr)
        .then(() => {
          if (!orderApplyState.dialog) {
            buttonLoading.apply_code = false
          }
          else if (count >= orderApplyState.codePaylist.length) {
            buttonLoading.apply_code = false
          } else {
            requestRepeat()
          }

        })
    }
    requestRepeat()
    checkPayStatus()
  }

  function applyByWechat (list = selectionList.value) {
    buttonLoading.apply_wechat = true
    orderApplyState.index = 0
    orderApplyState.dialog = true
    orderApplyState.codePaylist = list.map(item => {
      return {
        order: item,
        type: 'wechat'
      }
    })
    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0
    function requestRepeat () {
      const requestList = orderApplyState.codePaylist.slice(count, count + batchMax)
      count += requestList.length
      const rquestPromiseArr = requestList.map(item => {
        return wechatPay({ order_sn: item.order.order_sn }, { showErrorMsg: false })
          .then(res => {
            item.payInfo = res
            if (!res.code && res.data) {
              QRCode.toDataURL(res.data)
                .then(imgBase64 => {
                  // payInfo.imgBase64 = imgBase64
                  item.payInfo!.imgBase64 = imgBase64
                })
            } else {
              item.payInfo!.msg = '没有获取到支付链接'
            }
          })
          .catch(res => {
            item.payInfo = res
          })

      })
      Promise.allSettled(rquestPromiseArr)
        .then(() => {
          if (count >= orderApplyState.codePaylist.length) {

            buttonLoading.apply_wechat = false
          } else {
            requestRepeat()
          }

        })
    }
    requestRepeat()
    checkPayStatus()
  }

  // 导出支付二维码
  async function exportQrcode (list = selectionList.value) {
    if (!list.length) {
      return ElMessage.warning({
        message: "请选择需要导出的订单",
        grouping: true
      })
    }

    buttonLoading.exportPayCode = true
    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0

    const imgUrlRes: anyObj[] = []

    async function finishRequest () {
      buttonLoading.exportPayCode = false
      if (!imgUrlRes.length) {
        ElMessage.warning({
          message: '没有获取到支付链接',
          grouping: true
        })
        return
      }
      const path = await selectFolder()
      if (!path) {
        ElMessage.warning({
          message: "取消选择存放位置"
        })
        return
      }
      ElMessage.success('开始保存，完成后自动打开文件夹')
      await Promise.allSettled(imgUrlRes.map(res => {
        return new Promise((resolve, reject) => {
          QRCode.toDataURL(res.data.url)
            .then(base64 => {
              return saveByBase64({
                dest: `${path}/${res.data.order_sn}.png`,
                data: base64
              })
            })
            .then(() => {
              resolve(true)
            })
            .catch(() => {
              reject()
            })
        })
      }))

      openFileWindow(path)
    }

    function requestRepeat () {
      const requestList = list.slice(count, count + batchMax)
      count += requestList.length
      const requestArr = requestList.map(item => {
        return getApplyInfo({
          order_sn: item.order_sn
        }, { showErrorMsg: false })
          .then(res => {
            if (!res.code && res.data.url) {
              imgUrlRes.push(res)
            }
          })
      })

      Promise.allSettled(requestArr)
        .then(res => {
          if (!buttonLoading.exportPayCode) {
            finishRequest()
          }
          else if (count >= list.length) {
            finishRequest()
          } else {
            requestRepeat()
          }
        })
    }
    requestRepeat()
  }

  // 导出卡券
  async function exportCard (list = selectionList.value) {
    const filterList = list.filter(item => item.order_status_prompt == '电子券码待使用')
    if (!filterList.length) {
      return ElMessage.warning({
        message: list.length ? '没有可以导出的订单' : '请选择订单'
      })
    }
    buttonLoading.exportCard = true

    exportCardRequest({
      order_sns: filterList.map(item => item.order_sn).join(',')
    })
      .then(async (res) => {
        const path = await selectFolder()
        if (path) {
          await writeFile({
            dest: `${path}/导出卡券${dayjs().format('YYYYMMDDhhmmss')}.txt`,
            data: res.data.map((item: anyObj) => `订单：${item.order_sn}\t卡券核销码：${item.card}`).join('\n')
          })
          return path
        } else {
          return Promise.reject()
        }
      })
      .then(res => {
        openFileWindow(res)
      })
      .finally(() => {
        buttonLoading.exportCard = false
      })
  }

  const exportOrderEl = ref < InstanceType < typeof exportOrderVue >> ()
  // 导出订单信息
  async function exportOrder (list = selectionList.value) {
    if (!list.length) {
      return ElMessage.warning({
        message: "请选择需要导出的订单!",
        grouping: true
      })
    }
    const result = list.find(item => item.status === orderStatus.pendingPay)
    if (result) {
      await ElMessageBox({
        title: "提示",
        message: "检测到选中的订单中有 待支付 订单,是否继续导出？",
        showCancelButton: true,
        type: "warning"
      })
    }
    exportOrderEl.value?.openConfig(list)
    return

  }

  // 取消订单
  async function cancelOrder (list = selectionList.value) {
    const filterList = list.filter(item => item.combined_order_status === orderStatus.pendingPay)
    if (!filterList.length) {
      return ElMessage.warning({
        message: !list.length ? '请选择需要取消的订单' : '选择的订单中没有可以取消的订单',
        grouping: true
      })
    }

    await ElMessageBox({
      title: "提示",
      message: "确定取消选中订单吗?",
      showCancelButton: true
    })
    buttonLoading.isCancel = true
    const logType = 'cancel'
    log(logType, {
      msg: '*'.repeat(99)
    })

    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0
    /**订单次数 */
    let orderCount = 0
    const max = filterList.length
    operateLogState.dialog = true
    operateLogState.active = logType
    function requestRepeat () {
      const requestList = filterList.slice(count, count + batchMax)
      count += requestList.length
      const requestArr = requestList.map(item => {
        return cancelOrderRequest({
          order_sn: item.order_sn
        }, { showErrorMsg: false })
          .then(res => {
            orderCount++
            log(logType, {
              type: 'success',
              msg: `${item.order_sn},取消成功,(${orderCount}/${max})`
            })
          })
          .catch(res => {
            orderCount++
            // console.log(res)
            log(logType, {
              type: 'danger',
              msg: `${item.order_sn},取消失败,${res.msg},(${orderCount}/${max})`
            })
          })
      })
      Promise.allSettled(requestArr)
        .then(async () => {
          await Promise.allSettled([
            updateOrder({ ids: requestList.map(item => item.order_sn).join(',') }, { showErrorMsg: false })
          ])
          if (!buttonLoading.isCancel) {
            log(logType, {
              type: 'warning',
              msg: '检测到任务已被暂停，停止后续请求'
            })
          }
          else if (count >= filterList.length) {
            taskEndLog(logType)
            buttonLoading.isCancel = false
          } else {
            requestRepeat()
          }
        })

    }
    requestRepeat()
  }
  // 删除订单
  async function deleteOrder (list = selectionList.value) {
    const filterList =
      [...list]
    // .filter(item => item.combined_order_status === orderStatus.canceled || item.combined_order_status == orderStatus.refunded || (item.combined_order_status === 4 && item.comment_status) || item.order_status_prompt.includes('退款成功') || item.combined_order_status == orderStatus.risked)
    if (!filterList.length) {
      return ElMessage.warning({
        message: !list.length ? '请选择需要删除的订单' : '选择的订单中没有可以删除的订单',
        grouping: true
      })
    }

    await ElMessageBox({
      title: "提示",
      message: "是否确定删除选中并（永久删除）小号内订单吗?",
      showCancelButton: true
    })
    buttonLoading.isDelete = true
    const logType = 'delete'
    log(logType, {
      msg: '*'.repeat(99)
    })
    log(logType, {
      msg: `本次已成功筛选出${filterList.length}单`,
      type: 'success'
    })
    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0
    operateLogState.dialog = true
    operateLogState.active = logType
    function requestRepeat () {
      const requestList = filterList.slice(count, count + batchMax)
      count += requestList.length
      const requestArr = requestList.map((item, index) => {
        return delOrderRequest({
          order_sn: item.order_sn
        }, { showErrorMsg: false })
          .then(res => {
            // console.log(res)
            log(logType, {
              type: 'success',
              msg: `${item.order_sn},删除成功(${count - requestArr.length + index + 1}),${res.data}`
            })
          })
          .catch(res => {
            log(logType, {
              type: 'danger',
              msg: `${item.order_sn},删除失败(${count - requestArr.length + index + 1}),${res.msg}`
            })
          })
          .finally(() => {


          })
      })
      Promise.allSettled(requestArr)
        .then(() => {
          log(logType, {
            msg: `${count} -- ${filterList.length}`
          })
          if (!buttonLoading.isDelete) {
            log(logType, {
              msg: "检测到暂停指令，已停止后续操作"
            })
          }
          else if (count >= filterList.length) {
            taskEndLog(logType)
            buttonLoading.isDelete = false
          } else {
            requestRepeat()
          }

        })
    }
    requestRepeat()

  }

  // 券自动核销
  async function autoSell (list = selectionList.value) {
    // /api/order/autoSell
    const filterList = list.filter(item => item.order_status_prompt == '电子券码待使用')
    if (!filterList.length) {
      return ElMessage.warning({
        message: list.length ? '没有可以核销的订单' : '请选择订单'
      })
    }
    const map = new Map < string, { shopInfo: anyObj, orders: Array<typeof state.orderList[number] > }> ()
  filterList.forEach(item => {
    const store = map.get(item.shop_id)
    if (store) {
      store.orders.push(item)
    } else {
      const { shop_name, shop_id } = item
      map.set(item.shop_id, {
        shopInfo: {
          shop_id,
          shop_name
        },
        orders: [item]
      })
    }
  })
  buttonLoading.autoSell = true
  let loadingCount = 0
  function resolveLoading () {
    loadingCount++
    if (loadingCount >= map.size) {
      buttonLoading.autoSell = false
      taskEndLog(logItem)
    }
  }
  const logItem = 'autoSell'
  log(logItem, {
    msg: "*".repeat(100)
  })
  operateLogState.active = logItem
  operateLogState.dialog = true
  // console.log(map.size)
  const mallStore = useMallStore()
  for (const [key, value] of map) {
    const { shop_id, shop_name } = value.shopInfo
    const mall = mallStore.tableList.find(item => item.mallId == shop_id)
    if (!mall) {
      log(logItem, {
        msg: shop_name + '未找到店铺信息',
        type: 'warning'
      })
      resolveLoading()
      return
    }
    if (!mallStore.checkAvailable(mall)) {
      log(logItem, {
        msg: shop_name + '店铺已过期',
        type: 'warning'
      })
      resolveLoading()
      return
    }
    const allList = value.orders

    function start () {
      const requestList = allList.splice(0, 10)
      log(logItem, {
        msg: `正在获取店铺${shop_name}当前批次的核销码-----`,
      })
      getAutoSellPassId({
        order_sns: requestList.map(item => item.order_sn).join(',')
      }, { showErrorMsg: false })
        .then(async res => {
          const list: Array<{ order_sn: string, code: number, password: number, id: string }> = res.data
          await Promise.allSettled(list.map(async (item, index) => {
            await delayPromise(index * 300)
            if (item.code) {
              log(logItem, {
                msg: `店铺${shop_name}的订单${item.order_sn}获取核销码失败`,
                type: "danger"
              })
              return Promise.reject()
            } else {
              return autoSellRequest(mall!, item)
                .then(res => {
                  log(logItem, {
                    msg: `店铺${shop_name}的订单${item.order_sn}操作成功`,
                    type: "success"
                  })
                  return updateOrder({ ids: item.order_sn }, { showErrorMsg: false })
                })
                .catch((res) => {
                  log(logItem, {
                    msg: `店铺${shop_name}的订单${item.order_sn}操作失败:${res.msg}`,
                    type: "danger"
                  })
                })
            }
          }))
            .then(() => {
              getList()
            })
          // log(logItem, {
          //   msg: `店铺${shop_name}的订单操作完成`,
          //   type: "success"
          // })

        })
        .catch(res => {
          // resolveLoading()
          log(logItem, {
            msg: `店铺${shop_name}的订单操作失败：${res.msg}`,
            type: "danger"
          })
        })
        .finally(() => {
          if (!buttonLoading.autoSell) {
            log(logItem, {
              msg: `检测到暂停指令,停止后续操作`,
              type: 'warning'
            })
            resolveLoading()
            return
          }
          if (!allList.length) {
            resolveLoading()
          } else {
            start()
          }
        })
    }
    start()
  }
}

  // 一键免拼
  async function autoSpell (list = selectionList.value) {
    const filterList = list.filter(item => item.order_status_prompt === '待分享')
    if (!filterList.length) {
      return ElMessage.warning({
        message: list.length ? '请选择订单' : '没有可以免拼的订单',
        grouping: true
      })
    }
    buttonLoading.autoSpell = true

    const logItem = 'autoSpell'
    operateLogState.active = logItem
    operateLogState.dialog = true
    log(logItem, {
      msg: "*".repeat(100)
    })

    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0

    function requestRepeat () {
      const requestList = filterList.slice(count, count + batchMax)
      count += requestList.length
      const requestArr = requestList.map(order => {
        return autoSpellRequest({
          order_sn: order.order_sn
        }, { showErrorMsg: false })
          .then(res => {
            log(logItem, {
              msg: `${order.order_sn}` + '免拼成功',
              type: 'success'
            })
          })
          .catch(res => {
            log(logItem, {
              msg: `${order.order_sn}` + `免拼失败，${res.msg}`,
              type: 'danger'
            })
          })
      })
      Promise.allSettled(requestArr)
        .then(() => {
          if (!buttonLoading.autoSpell) {
            log(logItem, {
              type: "warning",
              msg: "检测到暂停指令，已停止后续操作"
            })
          }
          else if (count >= filterList.length) {
            taskEndLog(logItem)
            buttonLoading.autoSpell = false
          } else {
            requestRepeat()
          }

        })
    }
    requestRepeat()


  }

  async function recoverOrder (list = selectionList.value) {
    const filterList = list.filter(item => item.is_delete)
    if (!filterList.length) {
      return ElMessage.warning({
        message: list.length ? '没有可恢复的订单' : '请选择订单',
        grouping: true
      })
    }
    buttonLoading.orderRecover = true
    // orderRecover({
    //   order_sns: filterList.map(item => item.order_sn).join(',')
    // })
    //   .then(res => {
    //     getList()
    //   })
    //   .finally(() => {
    //     buttonLoading.orderRecover = false
    //   })
    await batchRequest(filterList, {
      request: async (list) => {
        return orderRecover({
          order_sns: list.map(item => item.order_sn).join(',')
        })
      },
      isStop: () => !buttonLoading.orderRecover
    })
    getList()
    buttonLoading.orderRecover = false
  }

  // 订单退款
  const refundState = reactive({
    dialog: false,
    after_sales_type: 1,
    user_ship_status: 1,
    // question_type: 79,
    question_type: 95,
    question_desc: "",
    user_phone: '',
    list: [] as typeof selectionList.value
  })

  const refundFormEl = ref < FormInstance > ()

  function openRefundDialog () {
    refundState.dialog = true
  }

  async function refundOrder (list = selectionList.value) {
    const filterList = list.filter(item =>
      item.combined_order_status === orderStatus.payed ||
      item.combined_order_status == orderStatus.pendingReceive ||
      item.combined_order_status == orderStatus.pendingComment ||
      item.combined_order_status == orderStatus.pendingShare
    )
    if (!filterList.length) {
      return ElMessage.warning({
        message: !list.length ? '请选择需要退款的订单' : '选择的订单中没有可以退款的订单',
        grouping: true
      })
    }
    openRefundDialog()
    refundState.user_phone = filterList[0].phone
    refundState.question_desc = '不想要了'
    refundState.list = filterList
    // await ElMessageBox({
    //   title: "提示",
    //   message: "确定退款中订单吗?",
    //   showCancelButton: true
    // })



  }
  /**退款 */
  async function refundSubmit (filterList = refundState.list) {
    await refundFormEl.value?.validate()
    buttonLoading.isRefund = true
    const logType = 'refund'

    log(logType, {
      msg: '*'.repeat(99)
    })
    const { after_sales_type, user_ship_status, question_desc, question_type, user_phone } = refundState

    refundState.dialog = false
    operateLogState.dialog = true
    operateLogState.active = logType
    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0
    function requestRepeat () {
      const requestList = filterList.slice(count, count + batchMax)
      count += requestList.length
      const requestArr = requestList.map(item => {
        return orderRefund({
          order_sn: item.order_sn,
          after_sales_type,
          user_ship_status,
          question_desc,
          question_type,
          user_phone,
          apply_amount: item.order_amount / 100
        }, { showErrorMsg: false })
          .then(res => {
            log(logType, {
              type: 'success',
              msg: `${item.order_sn},退款成功`
            })
          })
          .catch(res => {
            log(logType, {
              type: 'danger',
              msg: `${item.order_sn},退款失败,${res.msg}`
            })
          })
          .finally(() => {

          })
      })
      Promise.allSettled(requestArr)
        .then(() => {
          if (!buttonLoading.isRefund) {
            log(logType, {
              type: "warning",
              msg: "检测到暂停指令，已停止后续操作"
            })
          }
          else if (count >= filterList.length) {
            taskEndLog(logType)
            buttonLoading.isRefund = false

          } else {
            requestRepeat()
          }
        })
    }
    requestRepeat()
  }

  /**取消退款 */
  async function cancelRefund (list = selectionList.value) {
    const filterList = [...list]
    buttonLoading.cancelRefund = true
    const logType = 'cancelRefund'
    log(logType, {
      msg: '*'.repeat(99)
    })
    operateLogState.dialog = true
    operateLogState.active = logType
    /**一批请求多少个 */
    const batchMax = 10
    /** 已请求多少次 */
    let count = 0
    function requestRepeat () {
      const requestList = filterList.slice(count, count + batchMax)
      count += requestList.length
      const requestArr = requestList.map(item => {
        return cancelRefundRequest({
          order_sn: item.order_sn,
        }, { showErrorMsg: false })
          .then(res => {
            log(logType, {
              type: 'success',
              msg: `${item.order_sn},取消退款成功`
            })
          })
          .catch(res => {
            log(logType, {
              type: 'danger',
              msg: `${item.order_sn},取消退款失败,${res.msg}`
            })
          })
          .finally(() => {

          })
      })
      Promise.allSettled(requestArr)
        .then(() => {
          if (!buttonLoading.cancelRefund) {
            log(logType, {
              type: "warning",
              msg: "检测到暂停指令，已停止后续操作"
            })
          }
          else if (count >= filterList.length) {
            taskEndLog(logType)
            buttonLoading.cancelRefund = false
          } else {
            requestRepeat()
          }
        })
    }
    requestRepeat()
  }

  async function taskEndLog (logItem: LogItem, delay = 500) {
    log(logItem, {
      msg: `本次请求任务已结束`
    })
    log(logItem, {
      msg: `*`.repeat(100)
    })

    await delayPromise(delay)
    getList()
  }

  // 订单确认
  async function confirmOrder (list = selectionList.value) {
    if (buttonLoading.isConfirm) {
      return ElMessage.warning({ message: '当前有订单正在请求', grouping: true })
    }
    const filterList = list.filter(item => item.combined_order_status === orderStatus.pendingReceive)
    if (!filterList.length) {
      return ElMessage.warning({
        message: !list.length ? '请选择需要确认的订单' : '选择的订单中没有可以确认的订单',
        grouping: true
      })
    }
    await ElMessageBox({
      title: "提示",
      message: "确定对选中的订单做确认操作吗?",
      showCancelButton: true
    })
    buttonLoading.isConfirm = true
    const logType = 'confirm'
    log(logType, {
      msg: '*'.repeat(99)
    })

    operateLogState.dialog = true
    operateLogState.active = logType

    const setting = useSetting()
    let delay = 300
    const { active, min, max } = setting.visit.confirm
    if (active) {
      delay = random(min, max) * 1000
    }else{
      delay = 300
    }
    const requestArr = filterList.map((item, index) => {
      return new Promise((resolve, reject) => {

        const tid = setTimeout(() => {
          orderConfirm({
            order_sn: item.order_sn
          }, { showErrorMsg: false })
            .then(res => {
              resolve(res)
              log(logType, {
                type: 'success',
                msg: `${item.order_sn},确认成功,还剩${state.requestTids.size - 1}单`
              })
              // updateOrder({
              //   ids: item.order_sn
              // })
            })
            .catch(res => {
              reject(res)
              log(logType, {
                type: 'danger',
                msg: `${item.order_sn},确认失败,${res.msg}，还剩${state.requestTids.size - 1}单`
              })
            })
            .finally(() => {
              state.requestTids.delete(tid)
            })
        }, delay * index);
        state.requestTids.add(tid)
      })
    })
    Promise.allSettled(requestArr)
      .then(() => {
        taskEndLog(logType)
        buttonLoading.isConfirm = false
      })
  }

  // 停止全部请求
  function stopRequest () {
    // 停止确认收货
    buttonLoading.isConfirm = false
    state.requestTids.forEach(tid => {
      clearTimeout(tid)
    })
    state.requestTids.clear()

    // 停止对应按钮
    buttonLoading.isCancel = false
    buttonLoading.changePrice = false
    buttonLoading.isDelete = false
    buttonLoading.isRefund = false
    buttonLoading.autoSpell = false
    buttonLoading.autoSell = false
  }

  /**停止某个按钮 */
  function stopRequestItem (type?: keyof typeof buttonLoading) {
    if (!type) {
      return
    }
    switch (type) {
      case 'isConfirm': {
        state.requestTids.forEach(tid => {
          clearTimeout(tid)
        })
        state.requestTids.clear()
        log('refund', {
          type: "warning",
          msg: "发出暂停指令,将取消后续请求"
        })
        break
      }
    }
    buttonLoading[type] = false
  }
  function clearItemLog (type?: LogItem) {
    if (!type) {
      return
    }
    const list = operateLogState.map.get(type)
    if (list) {
      list.length = 0
    }
  }

  const tableMenuState: {
    show: boolean
    x: string
    y: string
    row?: Row

  } = reactive({
    show: false,
    x: '0px',
    y: '0px',
    hover: []
  })
  watch(() => tableMenuState.show, (val) => {
    if (!val) {
      tableMenuState.row = void 0;

    }
  }, { immediate: true })

  /**右键表格 */
  function tablecontextmenu (e: MouseEvent) {
    e.stopPropagation()
    tableMenuState.row = void 0
    const { scale } = useSetting().appWindow
    let x = e.x / scale
    let y = (e.y - 70) / scale
    if (x > 1200) {
      x = 1200
    }
    tableMenuState.x = x + 'px'
    tableMenuState.y = y + 'px'
    tableMenuState.show = true
    // @ts-ignore
    const path = e.path as HTMLDivElement[]
    // const row = path?.find(item => item.classList?.contains('el-table-v2__row'))
    const row = path?.find(item => item.classList?.contains('vxe-body--row'))
    if (!row) {
      return
    }
    row.classList.forEach(item => {
      const result = item.match(/^rowIndex-(\d+)$/)
      if (!result) {
        return
      }
      const ind = result[1]

      const order = state.orderList[Number(ind)]
      if (order) {
        tableMenuState.row = order
      }

    })

    // console.log(row)
    // console.log(e)
    // e.stopPropagation()
  }
  type TableMenuAction = 'open-comFolder' | 'create-comFolder' | 'set-comFolder'
  async function tableMenuAction (action: TableMenuAction) {
    // const appStore = useAppStore()
    const setting = useSetting()
    const row = tableMenuState.row
    if (!row) {
      return
    }
    switch (action) {
      // case 'create-comFolder': {
      //   const path = `${appStore.commentOrderPath}/${row.goods_id}`
      //   const res = await makeDir({ path })
      //   // console.log('makeDir',res)
      //   ElMessage.success('创建成功')
      //   openFileWindow(path)
      //   break
      // }
      case 'open-comFolder': {
        // const path = `${appStore.commentOrderPath}/${row.goods_id}`

        if (!setting.orderManage.commentFolder) {
          ElMessage.warning({
            message: '检测到没有设定评论文件夹,请先选定文件夹',
            grouping: true
          })
          await tableMenuAction('set-comFolder')
        }
        const path = setting.orderManage.commentFolder + '/' + row.goods_id
        const res = await exists(path)
        if (!res) {
          await makeDir({ path })
        }
        openFileWindow(path)
        break
      }
      case 'set-comFolder': {
        await ElMessageBox({
          showCancelButton: true,
          title: '预设评论文件夹',
          type: 'warning',
          message: h('div', {}, [
            h('p', {}, '注意'),
            h('p', { class: 'danger' }, '请不要选择C盘文件夹，可能导致权限不足无法创建'),
            h('p', { class: 'danger' }, '请不要选择软件(.exe文件)所在目录的文件夹，否则更新时会被覆盖'),
          ]),
          confirmButtonText: '我已知晓'
        })
        const res = await selectFolder()
        if (res) {

          setting.orderManage.commentFolder = res
          setting.setOrderSetting('orderManage', { ...setting.orderManage })
          ElMessage.success('已成功设置评价文件夹')
          return res
        } else {
          return Promise.reject()
        }
        break
      }
    }
  }

  const tableRowClass: VxeTablePropTypes.RowClassName<Row> = ({ row, rowIndex }) => {
    let classStr = `rowIndex-${rowIndex}`
    if (tableMenuState.row?.order_sn === row.order_sn) {
      classStr += ` row-contextmenu-active`
    }
    return classStr
  }


  const orderInterveneState = reactive({
    dialog: false,
    remark: "",
    images: [] as string[]
  })
  async function addInterveneImg () {
    if (orderInterveneState.images.length > 6) {
      ElMessage.warning('最多只能上传6张图片')
      return
    }
    const res = await selectFile({
      title: "选择图片",
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png'] }
      ],
    })
    const imgs = res.data as string[]
    imgs.some(item => {
      if (orderInterveneState.images.length < 6) {
        orderInterveneState.images.push(item)
      }
      return orderInterveneState.images.length >= 6
    })

  }
  async function orderInterveneSubmit () {
    const { images } = orderInterveneState
    console.log(selectionList.value)
    const list = selectionList.value.filter(item => item.after_sales_id)
    if (list.length === 0) {
      ElMessage.warning('没有可申请的订单')
      return
    }
    const imgBase64s = await Promise.all(images.map(item => {
      return imgToBase64(item, true, 50)
    }))
    const logType = 'intervene'
    buttonLoading.intervene = true
    orderInterveneState.dialog = false
    await batchRequest(list, {
      batch: 10,
      isStop: () => !buttonLoading.intervene,
      request: async (reqList) => {
        await Promise.allSettled(reqList.map(async item => {
          const { account, order_sn } = item
          // getSignature
          const uploadImgs: string[] = []
          await batchRequest(imgBase64s, {
            batch: 1,
            request: async (base64List) => {
              await Promise.allSettled(base64List.map(async (base64) => {
                const signRes = await getSignature({ account })
                const signature = signRes.data.signature
                const res = await uploadImageByBase64({ image: base64, upload_sign: signature })
                // console.log(res)
                // log(logType, {
                //   msg: order_sn + '上传图片' + res.msg,
                //   type: res.code === 0 ? 'success' : 'danger'
                // })
                uploadImgs.push(res.url)
              }))
            }
          })
          if (uploadImgs.length !== imgBase64s.length) {
            log(logType, {
              msg: order_sn + '有图片上传失败,不会申请',
              type: 'danger'
            })
            return Promise.reject('有图片上传失败')
          }
          const data = { order_sn, images: JSON.stringify(uploadImgs), remark: orderInterveneState.remark }
          return applyPlatform(data, { showErrorMsg: false }).then(res => {
            log(logType, {
              msg: order_sn + '申请介入成功',
              type: 'success'
            })
          })
            .catch(res => {
              log(logType, {
                msg: order_sn + '申请介入失败' + res.msg,
                type: 'danger'
              })
            })
        }))
        await updateOrder({ ids: list.map(item => item.order_sn).join(',') })
      }
    })
    getList()
    buttonLoading.intervene = false
  }
</script>

<style lang="scss" rel="stylesheet/scsss" scoped>
  .order-manage {
    overflow: hidden;
    height: 787px;
    position: relative;

    :deep(.el-popper) {
      max-width: 400px;
    }
  }

  .el-form.search-form {
    padding: 0 16px 10px 16px;
    height: 45px;
    box-sizing: border-box;
    // background-color: var(--el-fill-color-light);

    // border: var(--el-border);
    // border-radius: 6px;
    display: flex;
    align-items: center;
    // margin: 0 16px;
    border-bottom: 1px solid var(--el-border-color-darker);

    .el-divider {
      margin: 0 16px;
    }

    .el-form-item {
      margin: 0;

      &:last-child {
        flex-grow: 1;

        .el-input {
          :deep(.el-input-group__prepend) {
            .el-input__wrapper {
              background-color: var(--el-fill-color-blank);
            }
          }

          :deep(.el-input-group__append) {
            background-color: var(--el-color-primary);
            box-shadow: none;
            color: var(--el-fill-color-blank);
          }
        }
      }

      :deep(.el-date-editor--datetimerange) {
        width: 280px;
      }

      .el-select {
        width: 105px;
      }
    }
  }

  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 8px 16px 1px 16px;
    border-top: 1px solid var(--el-color-white);
  }

  .table-contaienr {
    width: calc(100% - 32px);
    margin: 0 auto;
    // box-shadow: var(--diy-shadow);
    border: var(--el-border);

    .vxe-table,
    .el-table,
    .el-table-v2 {
      @include commonTableHeader();
      font-size: 12px;

      :deep(.el-table-v2__header-row) {
        height: 38px !important;
      }

      :deep(.el-link) {
        font-size: 12px;
      }

      :deep(.row-contextmenu-active) {
        background-color: var(--el-color-primary-light-9);
        box-shadow: 0 0 5px rgba($color: #000000, $alpha: .5)
      }
    }
  }

  .table-footer {
    height: 44px;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    justify-content: space-between;
    padding: 0 16px;
    background-color: #fff;
    border-top: var(--el-border);

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    :deep(.pagination) {
      padding: 0;
    }
  }

  footer.controls {
    margin-top: 12px;
    height: 106px;
    box-sizing: border-box;
    padding: 12px 20px 20px;
    background-color: var(--el-bg-color-page);
    border-top: 1px solid var(--el-color-white);
    filter: drop-shadow(0px -1px 0px #CDD0D6);

    p {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;

      span {
        font-size: 14px;

        strong {
          color: var(--el-color-primary);
        }
      }

      &:first-child {
        margin-top: 0;
      }
    }

    p.btns {
      justify-content: flex-end;

      .el-space {
        :deep(.el-space__item) {
          &:last-child {
            margin-right: 0 !important;
          }
        }
      }

      .link-button-group {
        display: flex;
        align-items: center;
        border-radius: 6px;
        overflow: hidden;

        // padding: 3px;
        .el-button {
          border-radius: 0;

          .ds-icon {
            font-size: 16px;
            margin-right: 6px;
          }

        }

        .el-divider {
          background-color: transparent;
          margin: 0;
          height: 12px;
        }

        &.success {
          background-color: var(--el-color-success);
        }

        &.primary {
          background-color: var(--el-color-primary);
        }
      }

      // a {
      //   font-size: 14px;
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      //   height: 20px;
      //   padding: 2px 16px;
      //   box-sizing: border-box;
      //   color: var(--el-color-white);
      //   cursor: pointer;
      //   border-radius: 5px;

      //   .ds-icon {
      //     font-size: 16px;
      //     margin-right: 6px;
      //   }

      //   &:hover {
      //     box-shadow: 0 0 4px #fff inset;
      //   }
      // }
    }
  }

  .table-row-menu {
    display: flex;
    justify-content: center;
    position: fixed;
    left: v-bind('tableMenuState.x');
    top: v-bind('tableMenuState.y');
    z-index: 99;
    box-shadow: var(--diy-shadow);
    border: var(--el-border);
    border-radius: 6px;
    background-color: var(--el-fill-color-blank);

    .row {
      width: 103px;

      p {
        height: 28px;
        line-height: 28px;
        text-align: center;
        cursor: pointer;
        font-size: 12px;

        &.title {
          // font-weight: bold;
          color: var(--el-color-danger);
        }

        &:not(.title):hover {
          color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }
      }
    }

  }
</style>

<style lang="scss">
  .el-dialog.sub-operate {
    // margin-left: 470px;
    margin-bottom: 0;

    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      padding: 0;

      &>.container {
        height: 840px;
        border-radius: 6px;

        overflow: hidden;
        // background: url('/src/assets/image/home-bg.png');
        // background: var(--el-color-primary-light-9);

        &>h3 {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 42px;
          padding: 10px 16px;
          box-sizing: border-box;
          font-size: 14px;
          // color: var(--el-color-white);

          .close {
            cursor: pointer;
            width: 42px;
            line-height: 42px;
            font-weight: 400;
            text-align: center;

          }
        }

        &>.content {
          padding: 0 16px;
          height: calc(100% - 42px);
          overflow: hidden;
          width: 100%;
          // background-color: var(--el-bg-color-page );
          box-sizing: border-box;
        }
      }
    }

  }

  .el-popover.table-action-popover,
  .el-popover.logistics-option {
    min-width: 0;
    width: 103px !important;
    padding: 0;

    p {
      height: 36px;
      line-height: 36px;
      text-align: center;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
    }
  }

  .el-dialog.change-addr-dialog {
    .el-select {
      width: 170px;
      flex-grow: 1;
    }
  }

  .el-dialog.change-price-dialog {
    // margin-left: 480px;

    // .el-radio-group {
    //   flex-direction: column;
    // }

    .box {
      margin-top: 20px;
      border: var(--el-border);
      border-radius: 4px;

      &.is_checked {
        border-color: var(--el-color-primary);
      }

      h4 {
        cursor: pointer;
        height: 40px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding-left: 20px;


        &>span {
          margin-left: 10px;
        }
      }
    }

    .el-table {
      @include commonTableHeader(false)
    }
  }

  .el-dialog.log-dialog {
    // margin-left: 470px;
    margin-bottom: 0;

    .btns {
      margin-top: 10px;
    }

    .el-scrollbar {
      height: 600px;
      margin-top: 10px;
      border: var(--el-border);
      border-radius: 5px;
      overflow: hidden;

      .el-scrollbar__wrap {
        background-color: var(--el-fill-color-light);
        min-height: 100%;
      }

      .log-list {
        box-sizing: border-box;
        padding: 0 10px;

        p {
          box-sizing: border-box;
          min-height: 24px;
          margin: 5px 0;
          font-size: 14px;
          display: flex;
          align-items: center;
          flex-wrap: wrap;

          span.time {
            margin-right: 10px;
          }
        }
      }
    }
  }


  .el-dialog.refund-dialog {
    .el-form {

      .el-input,
      .el-select {
        flex-grow: 1;
      }
    }
  }

  .el-dialog.apply {
    .pay-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      P,
      h3 {
        text-align: center;
      }

      .img-container {
        width: 404px;
        height: 404px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 5px 0;

        img {
          width: 100%;
          height: 100%;
        }
      }

      p.btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      p {
        margin: 3px 0;

        .el-icon {
          cursor: pointer;
        }
      }
    }
  }

  .date-range-select.el-picker__popper {

    // margin-left: 200px !important;

    .el-popper__arrow {
      display: none;
    }
  }

  .limit-input-textarea {
    .el-textarea {
      max-height: 400px;
      overflow: auto;
    }
  }

  .table-tooltip {
    max-width: 300px;
  }

  .el-dialog.order-sync-dialog {
    .el-dialog__header {
      background-color: var(--el-color-primary) !important;
      height: 40px;
      // box-sizing: border-box;
      padding: 10px;

      .el-dialog__title,
      .el-dialog__close {
        color: #fff !important;
        // font-size: 14px;
      }

      .el-dialog__headerbtn {
        height: 32px;
      }

    }

    .el-dialog__body {
      padding: 10px
    }
  }
</style>