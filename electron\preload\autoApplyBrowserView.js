contextBridge.exposeInMainWorld("electron", {
  verifyCodeBase64: () => {
    var imgNode = document.querySelector("img.J-check-code");
    var canvas = document.createElement("canvas");
    canvas.width = imgNode.width;
    canvas.height = imgNode.height;
    var ctx = canvas.getContext("2d");
    ctx.drawImage(imgNode, 0, 0, imgNode.width, imgNode.height);
    var ext = "png";
    var dataURL = canvas.toDataURL("image/" + ext);
    return dataURL
  },
});
