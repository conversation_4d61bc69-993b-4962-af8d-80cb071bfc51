const Appliaction = require("ee-core").Appliaction;
const { protocol } = require("electron");
const path = require("path");

class Main extends Appliaction {
  constructor() {
    super();
    // this === eeApp;
  }

  /**
   * core app have been loaded
   */
  async ready () {
    // do some things
  }

  /**
   * electron app ready
   */
  async electronAppReady () {
    // do some things
  }

  /**
   * main window have been loaded
   */
  async windowReady () {
    // do some things
    // 延迟加载，无白屏
    const winOpt = this.config.windowsOption;
    const win = this.electron.mainWindow;
    if (winOpt.show == false) {
      win.once("ready-to-show", () => {
        win.show();
      });
    }

    win.on("window-all-closed", (evt) => {
      console.log("window-all-closed");
      win.quit();
    });

    protocol.registerStreamProtocol("pinduoduo", function (request, callback) {
      const url = "https://mobile.pinduoduo.com/login.html";
      callback({
        statusCode: 302,
        headers: {
          Location: url,
        },
        data: null,
      });
    });

    protocol.registerFileProtocol("img", (request, callback) => {
      const url = request.url.substring(7);
      const result = path.normalize(url);

      // console.log(result);
      callback(decodeURI(result));
    });
  }

  /**
   * before app close
   */
  async beforeClose () {
    // do some things
  }
}

new Main();
