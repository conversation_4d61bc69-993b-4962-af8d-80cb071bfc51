# getReviewUid 接口签名验证修改说明

## 修改概述

已成功修改 `getReviewUid` 接口的签名验证逻辑，实现了您要求的签名算法：

```
SHA512(Base64_Encode(2025-8-10+num+key+(num*10+826)))
```

## 修改的文件

### 1. frontend/src/apis/page.ts

修改了 `getReviewUid` 函数，添加了签名验证逻辑：

- 添加了可选的 `key` 参数
- 实现了签名字符串构建：`2025-8-10${num}${key}${num * 10 + 826}`
- 使用 CryptoJS 进行 Base64 编码
- 使用 CryptoJS.SHA512 生成签名
- 将签名添加到请求数据中

### 2. frontend/src/stores/pageData.ts

更新了调用 `getReviewUid` 的地方：

- 添加了 `signatureKey` 参数，使用值 `"1672369827627767"`
- 确保调用时传递正确的 key 值

## 签名算法详解

### 步骤 1: 构建签名字符串
```javascript
const signatureString = `2025-8-10${num}${key}${num * 10 + 826}`;
```

### 步骤 2: Base64 编码
```javascript
const base64Encoded = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(signatureString));
```

### 步骤 3: SHA512 哈希
```javascript
const signature = CryptoJS.SHA512(base64Encoded).toString();
```

## 示例

假设参数：
- `num = 10`
- `key = "1672369827627767"`

计算过程：
1. 签名字符串：`2025-8-10` + `10` + `1672369827627767` + `926` = `2025-8-10101672369827627767926`
2. Base64 编码：`MjAyNS04LTEwMTAxNjcyMzY5ODI3NjI3NzY3OTI2`
3. SHA512 签名：`c7bd39b88cbd39c7a0b1b884e209231a35a30fbb978bbf6075df6a746aaf332511dbdc536223b3c1e0b1db380db9328b38e8ad67c1d33165232c171f2ccd8ba7`

## 使用方法

现在调用 `getReviewUid` 时会自动生成签名：

```javascript
// 在 pageData.ts 中
const signatureKey = "1672369827627767";
getReviewUid({ num, key: signatureKey })
```

签名会自动添加到请求数据中，后端可以验证 `signature` 字段。

## 注意事项

1. 签名使用的日期是固定的：`2025-8-10`
2. 数值计算公式：`num * 10 + 826`
3. 使用的 key 值：`"1672369827627767"`
4. 签名字段名：`signature`

修改已完成，接口现在会在每次请求时自动生成并包含签名验证。
