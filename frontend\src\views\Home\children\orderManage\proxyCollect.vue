<template>
  <div class="collect-comment">
    <el-dialog :width="700" title="采集评论" v-model="state.dialog" :append-to-body="true" class="collect-comment-proxy"
      :close-on-press-escape="false" :close-on-click-modal="false">
      <el-space>
        <el-button type="danger" @click="state.reviewsMap.clear()">清空数据</el-button>
      </el-space>
      <div class="config">
        <el-space>
          <el-checkbox v-model="config.fullDsr" label="仅5星评论"></el-checkbox>
          <el-checkbox v-model="config.widthImg" label="仅带图评论"></el-checkbox>
          <el-checkbox v-model="config.filterDefault" label="过滤默认评论"></el-checkbox>
          <el-input v-model="config.filterStr" placeholder="过滤关键词"></el-input>
        </el-space>
      </div>
      <el-table :height="400" :data="tableData">
        <el-table-column label="商品ID" props="goodsId">
          <template #default='scope'>
            {{ scope.row.goodsId }}
          </template>
        </el-table-column>
        <el-table-column label="评论数量" props="">
          <template #default='scope'>
            {{ scope.row.reviewMap.size }}
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="action" :width="120">
          <template #default='scope'>
            <el-link :underline="false" type="primary" @click="down([...scope.row.reviewMap.values()])">下载</el-link>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="state.dialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang='ts' setup>
import { computed, reactive } from 'vue';
import { ipc } from '/@/apis/config';
import { execJStoAnalyzeWindow, start_wechat } from '/@/apis/analyze';
import { ElMessage, ElMessageBox } from 'element-plus';
import { copyStr } from '/@/utils/common';

const emits = defineEmits<{
  (e: 'down', data: anyObj[]): any
}>()

const state: {
  dialog: boolean
  /** 
   * Map<商品ID,Map<评论ID,评论信息>>
   */
  reviewsMap: Map<string, Map<string, anyObj>>
} = reactive({
  dialog: false,
  reviewsMap: new Map()
})

const config = reactive({
  fullDsr: true,
  widthImg: false,
  filterDefault: true,
  filterStr: ''
})

const tableData = computed(() => {
  const list: anyObj[] = []
  state.reviewsMap.forEach((reviewMap, goodsId) => {
    list.push({ goodsId, reviewMap })
  })
  return list
})

function start(goods_id?: string | number) {
  return start_wechat()
    .then(res => {
      if (res.success) {
     
        ElMessage.success('已启动')
        state.dialog = true
        return Promise.resolve(res)
      } else {
        ElMessage.warning('启动失败' + res.msg.toString())
        return Promise.reject(res)
      }
    })

}

async function down(list: anyObj[]) {
  const { value } = await ElMessageBox.prompt('请输入下载数量', '下载评论', {
    showCancelButton: true
  })
  const _value = Number(value)
  const filterList = list.slice(0, _value || 0)
  if (filterList.length) {
    emits('down', filterList)
    // state.dialog = false
  } else {
    ElMessage.warning({
      message: '没有下载内容',
      grouping: true
    })
  }
}

function resolveReviews(_: Electron.IpcRendererEvent, data: { type: 'web' | 'wechat', bodyStr: string, goodsId: string }) {
  console.log(data)
  try {
    const { goodsId } = data
    const list: anyObj[] = JSON.parse(data.bodyStr).data
    const reviewMap = state.reviewsMap.get(goodsId) || new Map()
    let count = 0
    list.forEach(item => {
      const { review_id } = item
      if (reviewMap.has(review_id)) {
        return
      }
      const { fullDsr, widthImg, filterDefault, filterStr } = config
      if (fullDsr && item.desc_score < 5) {
        return
      }
      if (widthImg && !item.pictures?.length) {
        return
      }
      if (filterDefault) {
        const comment = item.comment
        if (comment.includes("该用户觉得商品较好") ||
          comment.includes(
            "该用户觉得商品很好，给出了5星好评"
          ) ||
          comment.includes("该用户未填写文字评价")) {
          return
        }
      }
      if (filterStr && item.comment.include(filterStr.trim())) {
        return
      }
      reviewMap.set(review_id, item)
      count++
    })
    state.reviewsMap.set(goodsId, reviewMap)
    if (count) {
      execJStoAnalyzeWindow(`addLog('${goodsId}-新增${count}条评论,总计共${reviewMap.size}条')`)
    }
  } catch (e) {

  }
}

ipc.off('analyzeController.collectRviews', resolveReviews)
ipc.on('analyzeController.collectRviews', resolveReviews)

defineExpose({
  start
})
</script>

<style lang='scss'  rel="stylesheet/scsss" >
.el-dialog.collect-comment-proxy {
  // margin-left: 600px !important;

  .config {
    margin: 10px 0;
  }

  .el-table {
    @include commonTableHeader(false)
  }
}
</style>
