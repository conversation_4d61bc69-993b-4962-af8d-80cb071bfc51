.change-sales {
    // padding: 0 16px;
    position: relative;
  
    .settings {
      display: flex;
      justify-content: space-between;
  
      .item {
        height: 211px;
        border: 1px solid var(--el-border-color-darker);
        border-radius: 6px;
        background-color: var(--el-fill-color-darker);
  
        header {
          height: 30px;
          box-sizing: border-box;
          padding: 4px 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid var(--el-border-color-darker);
  
          h3 {
            font-size: 14px;
          }
  
        }
  
        .content {
          height: calc(100% - 30px);
  
          .el-form {
            height: 100%;
          }
        }
      }
  
      .account {
        width: 160px;
  
        .content {
          box-sizing: border-box;
          padding: 0 10px;
  
          .el-form-item {
            :deep(.el-form-item__label) {
              margin-bottom: 2px;
            }
  
            margin-bottom: 0px;
          }
        }
      }
  
      .buy {
        width: 566px;
  
        .content {
          box-sizing: border-box;
          padding: 8px 12px;
  
          .el-form {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
  
            width: 100%;
  
            .el-form-item {
              margin-right: 16px;
              margin-bottom: 0;
  
              :deep(.el-form-item__content) {
                flex-direction: column;
                justify-content: space-between;
                align-items: flex-start;
                flex-grow: 1;
  
                .el-input-number {
                  width: 100%;
                }
              }
            }
          }
        }
      }
  
      .ctrl {
        width: 298px;
  
        .content {
          padding: 8px 12px;
          box-sizing: border-box;
  
          P {
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            line-height: 28px;
  
            &+p {
              margin-top: 8px;
            }
  
            .el-button {
              flex-grow: 1;
            }
          }
        }
      }
  
    }
  
    .table-container {
      margin-top: 12px;
      border: var(--el-border);
      background-color: #fff;
      .table-footer {
        height: 70px;
        box-sizing: border-box;
        padding: 5px 20px;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex-direction: column;
        p{
            font-size: 14px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        :deep(.pagination) {
          padding: 0px;
        }
      }
    }
  
    .log-container {
      margin-top: 5px;
  
      header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0 6px;
        font-size: 12px;
      }
  
      .el-scrollbar {
        border-radius: 5px;
        border: var(--el-border);
        overflow: hidden;
      }
  
      .log-list {
        min-height: 80px;
        background-color: #fff;
  
        p {
          min-height: 24px;
          box-sizing: border-box;
          padding: 0 5px;
          font-size: 14px;
        }
      }
    }
  
  }