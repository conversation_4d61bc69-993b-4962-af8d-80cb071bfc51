const axios = require("axios");
const map = new Map();
process.on("message", (requestList) => {
  const ids = new Set(requestList.map(item => item.item.id));
  [...map.keys()].forEach(id => {
    if(!ids.has(id)){
      cancel(id)
    }
  });
  requestList.forEach((data) => {
    const { item, config } = data;
    if (item.status) {
      attack(item, config);
    } else {
      cancel(item.id);
    }
  });
});
function attack(item, config) {
  if (map.has(item.id)) {
    return;
  }
  // console.log(config)
  const request = () => {
    // console.log('start',config.url)
    axios(config)
      // .then((res) => {
      //   // console.log(res.data,'request')
      // })
      .catch((e) => {
        // console.log(e)
      });
  };
  const sid = setInterval(request, item.delay * 1000);
  map.set(item.id, sid);
}
function cancel(id) {
  const sid = map.get(id);
  if (sid) {
    clearInterval(sid);
    map.delete(id);
  }
}
// function startAttack(list) {
//     list.forEach(item => {
//       if (!item.status || attackMap.has(item.id) || !item.url) {
//         return
//       }
//       const sid = setInterval(() => {
//         try {
//           const params = item.params ? JSON.parse(item.params) : {}
//           const headers = item.header ? JSON.parse(item.header) : {}
//           const config: AxiosRequestConfig = {
//             url: item.url,
//             method: item.method,
//             headers
//           }
//           if (config.url?.toLocaleLowerCase() === 'get') {
//             config.params = params
//           } else {
//             config.data = params
//           }
//           anyRequest(config)
//         } catch (e) {

//         }

//       }, item.delay * 1000)

//       attackMap.set(item.id, {
//         sid,
//         item
//       })
//     })
//   }
