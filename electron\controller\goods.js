const { Controller } = require("ee-core");
const Storage = require("ee-core").Storage;


class goodsController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  async getReadList({ page, limit, goods_id }) {
    limit = limit || 20
    page = page || 1
    const data = { limit, page }
    if (goods_id) {
      data.goods_id = goods_id
    }

    return this.service.goods.getGoodsList(data)
  }

  async addGoodsRecord(data) {
    return this.service.goods.addGoodsItem(data)
  }

  async delGoodsRecord(goods_id) {
    return this.service.goods.deleteGoodsItem(goods_id)
  }
  async updateGoods(data) {
    return this.service.goods.updateGoods(data)
  }
  async deleteAll() {
    return this.service.goods.deleteAll()
  }



}

goodsController.toString = () => "[class goodsController]";
module.exports = goodsController;
