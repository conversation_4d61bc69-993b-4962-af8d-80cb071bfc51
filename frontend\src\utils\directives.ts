import { App, DirectiveBinding } from "vue";

const directives = function (app: App) {
  blurDirective(app);
  enableDrop(app);
};

/**
 * 点击后自动失焦指令
 * @description v-blur
 */
function blurDirective(app: App) {
  app.directive("blur", {
    mounted(el) {
      el.addEventListener("focus", () => {
        el.blur();
      });
    },
  });
}

function enableDrop(app: App) {
  app.directive("drop", {
    mounted(el:HTMLElement, binding, vnode) {
      el.addEventListener('dragover',(e:Event)=>{
        e.preventDefault();
      })
    },
    
  });
}

export default directives;
