import { createApp } from "vue";
import App from "./App.vue";
import "element-plus/dist/index.css";
import router from "/@/router/index";

import 'vxe-table/lib/style.css'


import bindComponents from "./utils/components";
import directives from "/@/utils/directives";
import bindStore from "./stores/config";

Object.defineProperty(navigator, "userAgent", {
    get() {
      return  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 Edg/114.0.1823.82"
    }
  })
import "./assets/js/iconfont.js";
import "./assets/js/anti_content.js";
import "./assets/js/antiContent.js"

import './assets/js/captcha_collect.js'

import "./assets/css/index.scss";
import "./assets/css/resize.scss";
import './assets/css/vxe-table.scss'
import './assets/css/transition.scss'
/**主题 */
import "./assets/theme/default.css";





const app = createApp(App);
app.use(router);
bindComponents(app);
bindStore(app);
app.mount("#app");


// 注册指令
directives(app);


