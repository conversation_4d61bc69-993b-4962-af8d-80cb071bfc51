<template>
    <div class="task-log">
        <el-alert>
            有{{ taskStore.loading.size }}个任务在执行。请求池：{{ taskStore.requestPool.size }}。
            当前队列状态 <span class="m-l-5 primary">{{ taskStore.taskStatusDes }}</span>
            <span class="m-l-5" v-show="taskStore.pointExecTime"> 上一次计时器执行时间 {{ dateFormat(taskStore.pointExecTime) }}
            </span>
        </el-alert>
        <p class="m-t-10 m-b-10">
            <el-link :underline="false" type="primary" @click="taskStore.logList = []">清空日志</el-link>
        </p>
        <el-scrollbar height="383px" ref="taskLogScroll">
            <div class="log-list">
                <p v-for="log in taskStore.logList" :class="[log.type]">
                    <span class="time">{{ dayjs(log.time).format('MM-DD HH:mm:ss') }}： </span>
                    <span class="msg">
                        <el-link :underline="false" @click="copyStr(log.task_id!)" v-if="log.task_id">任务{{ log.task_id
                        }}</el-link>
                        {{ log.msg }}
                    </span>
                </p>
            </div>
        </el-scrollbar>
    </div>
</template>
<script lang='ts' setup>
import { ScrollbarInstance } from 'element-plus';
import { useTask } from '/@/stores/task';
import { copyStr, dateFormat } from '/@/utils/common';
import dayjs from 'dayjs';
import { nextTick, ref, watch } from 'vue';
const taskStore = useTask()
const taskLogScroll = ref<ScrollbarInstance>()
const props = defineProps<{
    visible:boolean
}>()
watch([() => taskStore.logList.length,()=>props.visible], () => {
    let top = taskLogScroll.value?.wrapRef?.scrollHeight || 0
    top && nextTick(() => {
        taskLogScroll.value?.setScrollTop(top < 0 ? 0 : top)
    })
}, { immediate: true })
</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
p.desc {
    justify-content: space-between;

    strong {
        color: var(--el-text-color-regular);
    }
}

.el-scrollbar {
    border: var(--el-border);
    border-radius: 6px;
    margin-top: 10px;
    background-color: var(--el-fill-color-light);

    .log-list {
        padding: 0 16px;
        box-sizing: border-box;

        // color: var(--el-color-danger);
        p {
            min-height: 22px;
            // line-height: 22px;
            display: flex;
            align-items: center;

            .time {
                flex-shrink: 0;
            }

            .msg {
                display: flex;
                align-items: center
            }
        }
    }
}
</style>
