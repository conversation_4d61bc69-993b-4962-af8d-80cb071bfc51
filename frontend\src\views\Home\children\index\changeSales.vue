<template>
    <el-button type="primary" @click="visible = true">0成本改百万销量</el-button>
    <el-dialog v-model="visible" title="0成本改百万销量" class="" :width="1050" top="60px" :show-close="false"
        :close-on-click-modal="false" :close-on-press-escape="false">
        <el-alert type="error" :closable="false" style="margin-bottom: 20px;">
            <p>会自动发布一个商品，然后手动解析或微信解析成功后开始自动下单</p>
            <!-- <p>旗舰店，专卖店等企业店铺无法使用</p> -->
        </el-alert>
        <el-form label-width="80">
            <el-form-item label="选择店铺:">
                <el-space>
                    <el-select v-model="state.mallId" @change="storeChange">
                        <el-option v-for="item in pddMall.tableList" :key="item.mallId" :label="item.mallName"
                            :value="item.mallId"></el-option>
                    </el-select>
                    <el-button type="primary" @click="pddMall.addPddStore()">登录</el-button>

                </el-space>
            </el-form-item>
            <el-form-item label="是否旗舰店">
                <el-switch v-model="state.isFlagshipStore" active-text="是" inactive-text="否"
                    @change="(e) => { e || (state.recover = false) }"></el-switch>
            </el-form-item>
            <el-form-item label="克隆商品ID:">
                <div>
                    <el-space>
                        <el-input v-model="state.cloneGoodsId"></el-input>
                        <!-- <el-checkbox v-model="state.changeOriginGoods">同时修改原商品销量</el-checkbox> -->
                        <el-checkbox v-model="state.recover"
                            :disabled="!state.isFlagshipStore">同步至老商品(仅旗舰店)</el-checkbox>
                    </el-space>
                    <p class="tips info">会通过这个商品复制一个新的商品</p>
                </div>
            </el-form-item>
            <el-form-item label="目标销量:">
                <el-space>
                    <div>
                        <p>
                            <el-select v-model="state.num">
                                <el-option v-for="item in 10" :label="item + '0万'" :value="item">

                                </el-option>
                            </el-select>
                        </p>
                        <p class="tips info">此销量是新商品的销量</p>
                    </div>
                    <div>
                        <p>
                            <el-select v-model="state.num_per_order">
                                <el-option label="一单9505销量" :value="9505">

                                </el-option>
                                <el-option label="一单3851销量" :value="3851">

                                </el-option>
                                <el-option label="一单359销量" :value="359">

                                </el-option>
                            </el-select>
                        </p>
                        <p class="tips info">一单多少销量</p>
                    </div>
                </el-space>
            </el-form-item>
            <el-form-item label="小号位置:">
                <el-space>
                    <el-input-number :min="1" :precision="0" v-model="start_site"></el-input-number>
                    <el-button type="success" :loading="loading.create" @click="create">执行任务</el-button>
                    <el-button type="success" plain :loading="loading.recover" @click="editGoods()">商品数据同步</el-button>
                </el-space>
            </el-form-item>
            <el-form-item label=" ">
                <el-checkbox v-model="state.useDefaultImage" label="创建商品使用内置主图"></el-checkbox>
                <span style="margin-left :16px;" class="warning">如果主链接主图有问题(例如主图带价格)，导致改销量链接被驳回，可以使用软件内置图片发布新商品</span>
            </el-form-item>
        </el-form>
        <LogVue v-model:list="state.logList" height="250px" />
        <template #footer>
            <el-button @click="visible = false">关闭页面</el-button>
            <el-button type="primary" :loading="loading.create" @click="create">执行任务</el-button>
            <el-button type="danger" @click="reset" :disabled="!loading.create">重置执行</el-button>
        </template>
    </el-dialog>
</template>
<script lang='ts' setup>
import { Ref, inject, reactive, ref, watch } from 'vue';
import { useMallStore } from '/@/stores/store';
import { ElMessage, ElMessageBox } from 'element-plus';
import LogVue from '/@/components/Log/log.vue'
import {
    antiContentFromWeb, addAddress, quickOrder,
    pddStoreGoodsDetails, changeSkuPrice, closeMerchantCoupone,
    createGoodsId, createGoodsInfo, getMerchantCouponV2, createMerchantGoods,
    goodsSubmit, getCouponList,
    getUpLoadData,
    transFromData,
    newGoodsCommitId,
} from '/@/apis/changeSales'
import {
    uploadImageToStore, storeCreateFile, materialInfo, createMateria, storeGoodsListApi, rejectGoodsListApi,
    giftMainGoodsListApi, giftMainGoodsSku, giftGoodsListApi, createGiftGoodsApi, cancelGiftGoodsApi
} from '/@/apis/store'
import { changeSkuPirceList, createPddGoodsInfo, tempGoodsNames } from "/@/utils/porpery";
import { delayPromise, retry, copyStr, batchRequest, random, pddResErrorMsg } from '/@/utils/common';
import { useSubAccount } from '/@/stores/pageData';
import { useSetting } from '/@/stores/setting';
import { getRandomItem } from '/@/apis/address';
import { urlToBase64 } from '/@/utils/file';
import { cloneDeep } from 'lodash';

import dayjs from 'dayjs'
import { autoSupply, getPifaGroupId } from '/@/apis/page';
const emits = defineEmits<{
    (e: 'goodsDetails', data: string | number): any
}>()

async function reset(){
    await ElMessageBox({
        title:'提示',
        message:'是否要重置状态？(请查看日志确保任务已结束或中断,以免出现错误)。【此功能主要用于意外错误，任务无法停止导致无法再次使用】',
        showCancelButton:true

    })
    loading.create = false
}

const props = defineProps<{
    gooodsInfo?: GoodsInfo,
    couponList: anyObj[]
}>()

const requestDataList: Array<RequestData> = []

const storeChange = () => {
    const { mallId } = state
    const mall = pddMall.tableList.find(item => item.mallId === Number(mallId))
    if (mall) {
        const result = /(旗舰|专卖|专营)/.test(mall.mallName)
        state.isFlagshipStore = result
        if (!state.isFlagshipStore) {
            state.recover = false
        }
    }
}
watch(() => props.gooodsInfo, (val) => {
    if (!val) return
    const { goods_id } = val
    const requestData = requestDataList.find(item => item.goods_id == goods_id)
    if (!requestData) {
        return
    }
    createOrder(val, requestData)
    requestDataList.splice(requestDataList.indexOf(requestData), 1)
})

const start_site = inject('start_site') as Ref<number>

let requestDataPendingToRecover: RequestData | undefined = undefined
const visible = ref(false)
const loading = reactive({
    create: false,
    /**恢复商品 */
    recover: false
})
const state = reactive({
    mallId: '',
    cloneGoodsId: '',
    /**单位10万 */
    num: 1,
    logList: [{ msg: '在使用此功能前，请确保店铺正常', type: 'danger' }, { msg: '【关闭页面不会中断执行】', type: 'info' }] as LogItem[],
    recover: false,
    isFlagshipStore: false,
    useDefaultImage: false,
    num_per_order: 3851
})

function getPricePerOrder(num_per_order: number) {
    if (num_per_order > 9000) {
        return 0.21
    } 
    else if(num_per_order > 3000){
        return 0.51
    }
    else {
        return 5.5
    }
}

function addToLog(data: Partial<LogItem>) {
    state.logList.push({
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        msg: '',
        ...data
    })
}

type RequestData = {
    goods_id?: number
    goods_commit_id?: number
    account: Array<string>
    mall: Store
    isFlagshipStore: boolean
    coupon_code: string,
    /**用来取消优惠券 */
    coupon_batch_id?: string
    /**购买数量 */
    num: number
    /**每单购买数量 */
    num_per_order: number
    /**每单价格 */
    price_per_order: number
    cloneGoodsId: number | string
    cloneGoodsDetails?: anyObj,
    gallery: anyObj[]
    newGoodsCloneInfo?: anyObj

    /**创建的临时商品数据 */
    tempGoodsInfo?: anyObj
    /**下单送赠礼活动管理sn 用于取消赠品 */
    promotion_event_sn?: string
    giftInfo?: {
        mainGoodsId: number
        mainGoodsSkuId: number
        giftGoodsId: number
        giftGoodsSkuId: number
    }
}
const pddMall = useMallStore()
async function create() {
    const mall = pddMall.tableList.find(item => item.mallId == Number(state.mallId))
    if (!mall) {
        return ElMessage.warning({ message: '请选择店铺', grouping: true })
    }
    if (!pddMall.checkAvailable(mall)) {
        return ElMessage.warning({ message: '当前店铺不可用', grouping: true })
    }
    const requestData: RequestData = {
        account: [],
        coupon_code: '',
        mall,
        isFlagshipStore: state.isFlagshipStore,
        num: state.num * 100000,
        num_per_order: state.num_per_order,
        price_per_order: getPricePerOrder(state.num_per_order),
        cloneGoodsId: state.cloneGoodsId.trim(),
        gallery: []
    }
    if (!requestData.cloneGoodsId) {
        return ElMessage.warning({ message: '请输入商品ID', grouping: true })
    }
    state.logList = []
    loading.create = true
    try {
        await getCloneGoodsDetails(requestData)
        await resolveAccount(requestData)
        await createTempGoods(requestData)
        await checkTempGoods(requestData)
        // console.log(requestData)
        resolveMateriaInfo(requestData)
        addToLog({ msg: '准备创建优惠券，大约15秒' })
        await delayPromise(10000)
        await createCoupon(requestData)
        getGorupId(requestData)

    } catch (e) {
        console.log(e)
        addToLog({ msg: '处理失败', type: 'danger' })
        loading.create = false
    }
    loading.create = false

}
async function closeMerchantCoupon(requestData: RequestData) {
    const { mall } = requestData
    if (requestData.coupon_batch_id) {
        closeMerchantCoupone(mall, { batch_id: requestData.coupon_batch_id })
            .then(res => {
                if (res.success) {
                    addToLog({
                        msg: '成功关闭优惠券领取',
                        type: 'success'
                    })
                } else {
                    addToLog({
                        msg: `关闭优惠券领取失败${res.error_msg || res.errorMsg || '-'}`,
                        type: 'danger'
                    })

                }
            })
    }
}
async function getGorupId(requestData: RequestData) {
    const { tempGoodsInfo, mall } = requestData
    const { id = 0, sku_list = [], goods_name, mall_id, thumb_url } = tempGoodsInfo || {}
    await new Promise(async (resolve, reject) => {
        // const getPifaGroupId
        const supplyRes = await autoSupply({ mallId: mall.mallId, goods_id: id })
        if (supplyRes.code) {
            addToLog({
                msg: '自动供货失败' + supplyRes.msg,
                type: 'danger'
            })
            return resolve(true)
        }
        addToLog({
            msg: '自动供货成功,将获取拼团ID',
        })
        await delayPromise(3000)
        retry(async (index) => {
            addToLog({
                msg: `正在获取拼团ID第${index}次`,
            })
            const res = await getPifaGroupId({ goods_id: id, sku_id: sku_list[0].skuId }, { showErrorMsg: false })
            console.log(res)
            return res
        }, 3)
            .then(res => {
                const groupId = res.data
                if (groupId) {
                    const goodsInfo: GoodsInfo = {
                        goods_name,
                        goods_id: id,
                        skus: sku_list.map((item: anyObj) => {
                            const sku: GoodsInfo['skus'][number] = {
                                skuId: item.skuId,
                                groupPrice: item.groupPrice / 100,
                                normalPrice: item.normalPrice / 100,
                                skuImg: item.skuThumbUrl,
                                spec: item.spec
                            }
                            return sku
                        }),
                        group_id: {
                            alone: groupId,
                            multiple: [groupId]
                        },
                        mallId: mall_id,
                        mallName: mall.mallName,
                        normalPrice: 0,
                        group_order_ids: [],
                        groupPrice: 0,
                        goods_img: thumb_url,
                        activity_id: 0,
                        coupon_code: ''
                    }
                    createOrder(goodsInfo, requestData)
                    reject()
                    return
                }
                return Promise.reject()
            })
            .catch(() => {
                resolve(true)
            })
    })


    requestDataList.push(requestData)
    emits('goodsDetails', requestData.goods_id!)
    addToLog({
        msg: '商品已就绪' + requestData.goods_id + ',已自动填充并尝试解析',
        type: 'success'
    })
    addToLog({ msg: '如果pdd窗口已登录且有效将自动解析执行', type: 'info' })
    addToLog({ msg: '如果未登录请使用微信解析解析新商品数据', type: 'warning' })
    addToLog({ msg: '【关闭页面不会中断执行】', type: 'info' })
}


async function getCloneGoodsDetails(requestData: RequestData) {
    const { mall, cloneGoodsId } = requestData
    const res = await pddStoreGoodsDetails(mall, cloneGoodsId)
    if (!res.success) {
        addToLog({
            msg: '获取商品详情失败：' + (res.error_msg || res.errorMsg),
            type: 'danger'
        })
        return Promise.reject()
    }
    requestData.cloneGoodsDetails = res.result
    // console.log(requestData)
    return res.result
}

/**分配小号 */
async function resolveAccount(requestData: RequestData) {
    addToLog({ msg: '正在分配小号' })
    const subAccount = useSubAccount()
    const { num, num_per_order } = requestData
    // if (subAccount.availableSet.size < requestData.num) {
    //     ElMessage.warning({ message: '没有足够小号', grouping: true })
    //     return Promise.reject()
    // }
    const accountMax = Math.ceil(num / num_per_order)
    addToLog({ msg: '当前需要分配小号次数: ' + accountMax + '次' })
    for (let i = 0; i < accountMax; i++) {
        const res = subAccount.getAccount(start_site.value - 1)
        if (res.status) {
            requestData.account.push(res.data)
            const ind = (res.ind + 2) % subAccount.list.length
            start_site.value = ind === 0 ? subAccount.list.length : ind
        } else {
            addToLog({
                type: 'danger',
                msg: '获取小号出错' + res.msg
            })
            break
        }
    }
    // console.log(requestData)
    if (requestData.account.length < accountMax) {
        addToLog({ type: 'danger', msg: '没有获取到足够小号' })
        requestData.account.forEach(item => {
            subAccount.recover(String(item))
        })
        return Promise.reject()
    }
    // return Promise.reject()
}
/**处理商品详情 */
async function resolveGoodsDetails(requestData: RequestData) {
    const { mall } = requestData
    const { goods_commit_id, goods_id, detail_gallery } = requestData.cloneGoodsDetails!
    addToLog({ msg: '正在获取商品详情' })
    // console.log(reanformRes,requestData)
    // // return reanformRes.data
    // const res = await pddStoreGoodsDetailsFloorList(mall, { goods_commit_id, goods_id })
    // if (!res.success || !res.result) {
    //     addToLog({
    //         msg: '获取详情失败---',
    //         type: 'danger'
    //     })
    //     return Promise.reject()
    // }

    // const floor_list = res.result.floor_list as anyObj[]
    // // console.log(floor_list)
    // // const url = floor_list[0].content_list[0].img_url
    // // const result = await urlToBase64(url)
    // // const uploadRes = await uploadImageToStore(mall,result.base64)
    // // console.log('uploadRes',uploadRes)
    // // const createRes = await storeCreateFile(mall,{url:uploadRes.url})
    // // console.log(createRes)
    const gallery: anyObj[] = []
    // floor_list.forEach(item => {
    //     (item.content_list || []).forEach((imgItem: anyObj) => {
    //         imgs.push(imgItem.img_url)
    //     })
    // })
    // console.log(floor_list, imgs)
    detail_gallery.forEach((item: anyObj) => {
        gallery.push(item)
    })
    requestData.gallery = gallery
}
/**创建临时商品 */
async function createTempGoods(requestData: RequestData) {
    const { mall, isFlagshipStore } = requestData

    const createIdRes = await createGoodsId(mall)
    if (!createIdRes.success) {
        addToLog({ msg: `获取商品ID失败${createIdRes.error_msg || ''}`, type: 'danger' })
        return Promise.reject()
    }


    const { goods_commit_id, goods_id } = createIdRes.result
    requestData.goods_commit_id = goods_commit_id
    requestData.goods_id = goods_id

    await resolveGoodsDetails(requestData)
    const { cat_id } = requestData.cloneGoodsDetails!
    const otherData: anyObj = {}

    if (isFlagshipStore) {
        const goodsName = (function () {
            const list = tempGoodsNames.flagshipStore
            return list[random(0, list.length)] || list[0]
        })()
        otherData.goods_name = goodsName
        otherData.goods_desc = goodsName
    }
    const info = await createPddGoodsInfo(
        {
            mall,
            goods_commit_id, goods_id, gallery: requestData.gallery, detailsInfo: requestData.cloneGoodsDetails!,
            cat_id: isFlagshipStore ? cat_id : void 0,
            otherData,
            only_sku_property: !isFlagshipStore,
            useDefaultImage: state.useDefaultImage
        }
    )
    // console.log(info, 'info')
    const createGoodsRes = await createGoodsInfo(mall, info)
    if (!createGoodsRes.success) {
        addToLog({ msg: `创建商品失败${createGoodsRes.error_msg || ''}`, type: 'danger' })
        return Promise.reject()
    }

    const anti_content = await new Promise<string[]>((resolve, reject) => {
        antiContentFromWeb()
            .then(res => {
                const { anti_content1, anti_content2 } = res.data
                resolve([anti_content1, anti_content2])
            })
            .catch(res => {
                addToLog({ msg: '获取anticontent失败' + res.msg })
                reject()
            })
    })

    const submitRes = await goodsSubmit(mall, { goods_commit_id, goods_id }, anti_content)
    if (!submitRes.success) {
        addToLog({ msg: `提交商品失败${submitRes.error_msg || ''}`, type: 'danger' })
        return Promise.reject()
    }
    addToLog({ msg: '已成功提交创建商品' + goods_id, type: 'success' })

    // editGoods(requestData)
}
/**检测创建的临时商品是否成功 */
async function checkTempGoods(requestData: RequestData) {
    addToLog({ msg: '准备检测临时商品创建状态' })
    await delayPromise(5000)
    const { goods_id, mall } = requestData
    await retry(async (index) => {
        addToLog({ msg: `正在检测创建的临时商品状态，第${index}次` })
        // @ts-ignore
        const res = await storeGoodsListApi(mall, { goods_id_list: [String(goods_id)], pre_sale_type: 4 })
        console.log('checkTempGoods', res)
        if (!res.success) {
            addToLog({ msg: `检测创建的临时商品失败${res.error_msg || ''}`, type: 'danger' })
            return Promise.reject(res)
        }
        if (res && res.result && res.result.total) {
            requestData.tempGoodsInfo = res.result.goods_list[0]
            addToLog({ msg: '临时商品已上架', type: 'success' })
            return res
        }
        await delayPromise(1000)
        const rejectGoodsRes = await rejectGoodsListApi(mall, [String(goods_id)])
        if (rejectGoodsRes && rejectGoodsRes.success && rejectGoodsRes.result?.total) {
            addToLog({ msg: '检测到临时商品被驳回', type: 'danger' })
            return false
        }
        return Promise.reject(res)
    }, 10, 3000)
        .then(res => {
            if (!res) {
                return Promise.reject(res)
            }
        })


}
/**创建优惠券 */
async function createCoupon(requestData: RequestData) {
    const { mall, isFlagshipStore, price_per_order, num_per_order } = requestData
    function resolveTime(time = Date.now()) {
        const date = new Date(time);
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        date.setMilliseconds(0);
        return date.valueOf();
    }
    const batch_desc = '商品立减券' + requestData.goods_id
    let discount = Math.floor((num_per_order * price_per_order)) * 100
    const res = await createMerchantGoods(mall, [
        {
            goods_id: requestData.goods_id!,
            batch_desc,
            batch_start_time: resolveTime(),
            batch_end_time: resolveTime(Date.now() + 3600 * 1000 * 48) - 1,
            // discount: 1000 * 100,
            discount,
            init_quantity: 100000,
            user_limit: 10
        }
    ])
    // console.log('createCoupon', res)

    if (!res.success) {
        const msg = res.error_msg || res.errorMsg || ''
        addToLog({ msg: `创建优惠券失败:${msg}`, type: 'danger' })
        return Promise.reject()
    } else if (res.result.has_error_msg) {
        let failedFlag = false
        res.result.error_msg_list.forEach((item: anyObj) => {
            addToLog({ msg: `创建优惠券失败:${item.error_msg}`, type: 'danger' })
            failedFlag = true
        })
        if (failedFlag) {
            return Promise.reject()
        }
    } else {
        addToLog({ msg: '提交创建优惠券成功', type: 'success' })
    }
    addToLog({ msg: '准备获取优惠券信息', type: 'info' })
    await delayPromise(15 * 1000)
    await retry(async (index) => {
        addToLog({ msg: `获取优惠券列表第${index}次--` })
        const listRes = await getCouponList(mall, { goods_list: [String(requestData.goods_id || '')], batch_status: 1 })
        const list = listRes.result.data_list as anyObj[]
        // console.log('getCouponList', listRes)
        const item = list.find(item => item.batch_desc === batch_desc)
        if (item && item.status_str === '领取中') {
            requestData.coupon_code = item.batch_sn
            requestData.coupon_batch_id = item.batch_id
            addToLog({ msg: '成功获取到优惠券码' + requestData.coupon_code, type: 'success' })
            // bindGift(requestData)
        } else {
            if (index >= 30) addToLog({ msg: '没有获取到优惠券码', type: 'danger' })
            return Promise.reject()
        }
    }, 30, 10 * 1000)



}


async function createOrder(goodsInfo: GoodsInfo, requestData: RequestData) {
    visible.value = true
    try {
        addToLog({ msg: '准备关联赠品' })
        await Promise.allSettled([bindGift(requestData)])
        const { promotion_event_sn } = requestData
        if (!promotion_event_sn) {
            addToLog({
                msg: '没有成功绑定商品,',
                type: 'danger'
            })
            await closeMerchantCoupon(requestData)
            return
        }
        const { account, coupon_code, mall, giftInfo, isFlagshipStore, num_per_order } = requestData
        const { skus, goods_id, goods_name, group_id } = goodsInfo
        const accountGroup = new Map<string, { account: string, count: number }>()
        account.forEach(item => {
            if (!accountGroup.has(item)) {
                accountGroup.set(item, { account: item, count: 1 })
            } else {
                accountGroup.get(item)!.count++
            }
        })
        await skuPriceChange(goodsInfo, requestData)
        addToLog({ msg: '准备执行领取优惠券并下单' + account.length })
        // await delayPromise(20000)
        await batchRequest([...accountGroup.values()], {
            batch: 25,
            request: async (batchList) => {
                await Promise.allSettled(batchList.map(async item => {
                    const list: string[] = new Array(item.count).fill(item.account)
                    await batchRequest(list, {
                        batch: 1,
                        request: async (list) => {
                            const item = list[0]
                            const addressData = await resolveAddress(item)
                            const createOrderRequestData: any = {
                                account: item,
                                sku: skus[0].skuId,
                                sku_spec: skus[0].spec,
                                shop_id: mall.mallId,
                                shop_name: mall.mallName,
                                goods_id,
                                num: num_per_order,
                                // num: 50001,
                                mode: 'open_group',
                                // activity_id: coupon_code,
                                goods_name,
                                type: 'gift',
                                group_id: group_id.multiple[0] || group_id.alone!,
                                use_coupon: true,
                                ...addressData,
                            }
                            await retry(() => {
                                return getMerchantCouponV2({ coupon_code, account: item, }, { showErrorMsg: false })
                            }, 3, 2000)
                                .then(res => {
                                    const promotion_identity_vo = res.data && res.data.promotion_identity_vo
                                    if (!promotion_identity_vo) {
                                        return Promise.reject({ msg: 'promotion_identity_vo不存在' })
                                    }
                                    createOrderRequestData.single_promotion_list = [
                                        {
                                            ...promotion_identity_vo,
                                            sku_ids: [giftInfo!.mainGoodsSkuId],
                                            mall_id: mall.mallId
                                        }
                                    ]
                                    addToLog({ msg: `${item}领取优惠券成功`, type: 'success' })
                                })
                                .catch(res => {
                                    addToLog({ msg: `${item}领取优惠券失败${res.msg}`, type: 'danger' })
                                    return Promise.reject()
                                })

                            if (promotion_event_sn) {
                                createOrderRequestData.goods_promotion_list = [
                                    {
                                        "promotion_type": 28,
                                        "promotion_id": promotion_event_sn,
                                        "sku_ids": [
                                            giftInfo!.mainGoodsSkuId
                                        ],
                                        "extension": {
                                            gift_use_list: JSON.stringify([
                                                {
                                                    mallId: mall.mallId,
                                                    goodsId: giftInfo?.giftGoodsId,
                                                    skuId: giftInfo?.giftGoodsSkuId,
                                                    goodsNumber: createOrderRequestData.num
                                                }
                                            ])
                                            // "gift_use_list": "[{\"mallId\":790140,\"goodsId\":************,\"skuId\":1504485111324,\"goodsNumber\":50100}]"
                                        }
                                    }
                                ]
                            }
                            return retry(async () => {
                                return quickOrder(createOrderRequestData, { showErrorMsg: false })
                                    .then(res => {

                                        addToLog({
                                            msg: `${createOrderRequestData.goods_name}:下单成功。订单号:${res.data.order_sn}`,
                                            type: 'success'
                                        })

                                    })
                            }, 3)
                                .catch(res => {
                                    addToLog({
                                        msg: `${createOrderRequestData.goods_name}:下单失败:${res.msg}`,
                                        type: 'danger'
                                    })
                                })

                        }
                    })
                }))
            }
        })
        // return
        closeMerchantCoupon(requestData)

        await skuPriceChange(goodsInfo, requestData, 'recover')
        requestDataPendingToRecover = requestData
        addToLog({
            msg: '下单完成,请前往订单管理界面支付订单',
            type: 'success'
        })
        addToLog({
            msg: '支付完成后,点击【商品数据同步】可同步商品信息',
            type: 'success'
        })
        cancelGiftGoods(requestData)
        // await delayPromise(5000)
        editGoods()
    } catch (e) {
        console.log('orderCreate - catch', e)
        loading.create = false
    }
    loading.create = false
}

async function resolveAddress(account: string): Promise<anyObj> {
    const settingStore = useSetting()
    const {
        nameCode_active,
        nameCode_position,
        nameCode_str,
        addr_active,
        addr_position,
        addr_str,
        filterStr,
        type,
        appoint_address,
    } = settingStore.address;
    let addressData: anyObj = {
        appoint_address: appoint_address || void 0,
    }
    if (type == 'diy') {
        const result = await getRandomItem();
        Reflect.deleteProperty(result, "id");
        addressData = result;
    }

    return addAddress({ account: account, ...addressData }, { showErrorMsg: false })
        .then(res => {
            addressData = {
                ...addressData,
                ...res.data,
                filter_address: filterStr.replace("，", ","),
                address_cipher: addr_active ? addr_str : void 0,
                address_site: addr_active ? addr_position : void 0,
                name_cipher: nameCode_active ? nameCode_str : void 0,
                name_site: nameCode_active ? nameCode_position : void 0,
            }

            return addressData
        })
        .catch(res => {
            // console.log('添加地址返回值-error：', res)

            useSubAccount().recover(account)
            addToLog({
                msg: '添加地址出错:' + res.msg,
                type: 'danger'
            })
            return Promise.reject()
        })
}

async function skuPriceChange(goodsInfo: GoodsInfo, requestData: RequestData, type = 'reduce' as 'reduce' | 'recover') {

    // const { goodsInfo, requestData, type = 'reduce' } = opt
    const { mall, isFlagshipStore, price_per_order } = requestData
    const { skus } = goodsInfo
    let changePriceRes: any = { success: false, error_msg: '--' }
    if (type === 'recover') {
        const { market_price, max_group_price } = requestData.cloneGoodsDetails!
        let data = {
            goods_id: requestData.goods_id!,
            "market_price_in_yuan": "5002.00",
            // "market_price_in_yuan": type === 'reduce' ? (isFlagshipStore ? "3" : "5002.00") : '5002.00',
            "market_price": 500200,
            // "market_price": type === 'reduce' ? (isFlagshipStore ? 300 : 500200) : 500200,
            sku_prices: skus.map(item => {
                const obj = {
                    sku_id: item.skuId,
                    "multi_price_in_yuan": '4000',
                    // "multi_price_in_yuan": type === 'reduce' ? (isFlagshipStore ? "0.21" : "0.01") : '4000',
                    "single_price_in_yuan": "5001.00",
                    // "single_price_in_yuan": type === 'reduce' ? (isFlagshipStore ? "3" : "5001") : '5001',
                    "multi_price": 400000,
                    // "multi_price": type === 'reduce' ? (isFlagshipStore ? 21 : 1) : 400000,
                    "single_price": 500100
                    // "single_price": type === 'reduce' ? (isFlagshipStore ? 3 : 500100) : 500100
                }
                return obj
            })
        }
        const data2 = cloneDeep(data)
        if (true || !requestData.isFlagshipStore) {
            // if (true) {
            data.market_price = 50200
            data.market_price_in_yuan = '502.00'
            data.sku_prices.forEach(item => {
                item.multi_price = 50000
                item.multi_price_in_yuan = '500.00'
                item.single_price = 50100
                item.single_price_in_yuan = '501.00'
            })
        } else {
            const multi_price = max_group_price
            // data.market_price = Math.max(multi_price,market_price)
            data.market_price = max_group_price + 300
            data.market_price_in_yuan = (data.market_price / 100).toFixed(2)
            data.sku_prices.forEach(item => {
                item.multi_price = multi_price
                item.multi_price_in_yuan = (item.multi_price / 100).toFixed(2)
                item.single_price = multi_price + 200
                item.single_price_in_yuan = (item.single_price / 100).toFixed(2)
            })
        }
        // console.log('changeSkuPrice', data)
        changePriceRes = await changeSkuPrice(mall, data)
        if (changePriceRes.success) {
            // await delayPromise(15000)
            setTimeout(() => {
                changeSkuPrice(mall, data2)
            }, 20000);

        }
    } else {
        const priceItem = changeSkuPirceList.find(item => item.multi_price === price_per_order * 100)
        if (!priceItem) {
            addToLog({ msg: `--修改价格失败，没有找到修改价格参数`, type: 'danger' })
            return Promise.reject()
        }
        const data = {
            goods_id: requestData.goods_id!,
            "market_price_in_yuan": priceItem.market_price_in_yuan,
            "market_price": priceItem.market_price,
            sku_prices: skus.map(item => {
                const obj = {
                    sku_id: item.skuId,
                    "multi_price_in_yuan": priceItem.multi_price_in_yuan,
                    "single_price_in_yuan": priceItem.single_price_in_yuan,
                    "multi_price": priceItem.multi_price,
                    "single_price": priceItem.single_price
                }
                return obj
            })
        }
        // console.log('changeSkuPrice', data)
        changePriceRes = await changeSkuPrice(mall, data)
        if (changePriceRes.success) {
            await new Promise(resolve => {
                checkSkuPrice(requestData)
                    .then(resolve)
                    .catch(() => {
                        changePriceRes = { success: false, error_msg: '价格检测未通过' }
                        resolve(true)
                    })
            })

        }
    }


    // console.log('changePriceRes', changePriceRes)
    if (changePriceRes.success) {
        addToLog({
            msg: '修改价格成功',
            type: 'success'
        })
    } else {
        addToLog({
            msg: '修改价格失败' + pddResErrorMsg(changePriceRes),
            type: 'danger'
        })

        await closeMerchantCoupon(requestData)
        await cancelGiftGoods(requestData)

        return Promise.reject()
    }
}
async function checkSkuPrice(requestData: RequestData) {
    const { mall, goods_id } = requestData
    const retryMax = 8
    addToLog({ msg: '检查商品sku价格', type: 'info' })
    await delayPromise(2000)
    const res = await retry<Boolean>(async (index) => {
        addToLog({ msg: `检查商品价格第${index}次`, type: 'info' })
        const res = await storeGoodsListApi(mall, { goods_id_list: [String(goods_id)], pre_sale_type: 4 })
        if (!res.success) {
            addToLog({ msg: `获取商品信息失败${res.error_msg || ''}`, type: 'danger' })
            if (index >= retryMax) {
                return false
            }
            return Promise.reject(false)
        }
        if (res && res.result && res.result.total) {
            const tempGoodsInfo = res.result.goods_list[0]
            if (!tempGoodsInfo.sku_list) {
                if (index >= retryMax) {
                    return false
                }
                return Promise.reject(false)
            }
            const groupPrice = tempGoodsInfo.sku_list?.[0]?.groupPrice
            if (groupPrice < 550) {
                addToLog({ msg: '商品价格已更改', type: 'success' })
                return true
            }
        }
        if (index >= retryMax) {
            return false
        }
        return Promise.reject(false)
    }, retryMax, 3000)
    if (res) {
        return {
            success: true
        }
    }
    await delayPromise(1000)
    const rejectGoodsRes = await rejectGoodsListApi(mall, [String(goods_id)])
    if (rejectGoodsRes && rejectGoodsRes.success && rejectGoodsRes.result?.total) {
        const goodsInfo = rejectGoodsRes.result.list[0]
        const { reject_comment } = goodsInfo
        if (reject_comment.includes('商品SKU价格异常')) {
            return Promise.reject({
                success: false,
                error_msg: '商品sku价格异常'
            })
        }
        return Promise.reject(rejectGoodsRes)
    } else {
        return {
            success: true
        }
    }
}

async function editGoods() {
    const requestData = requestDataPendingToRecover
    if (!requestData) {
        return
    }
    if (loading.recover) {
        return
    }
    if (!requestData.isFlagshipStore) {
        addToLog({ msg: '只有旗舰店方式下单才可同步商品', type: 'warning' })
        return
    }
    if (!state.recover) {
        addToLog({ msg: '未启用同步至老商品', type: 'warning' })
    }
    // await ElMessageBox({
    //     title: '提示',
    //     message: '是否同步指定商品信息数据。请确认订单已完成支付(同步数据后未支付的订单将失效)',
    //     type: 'warning',
    //     showCancelButton: true
    // })
    loading.recover = true
    try {
        addToLog({ msg: '正在复原商品信息' })
        await delayPromise(15 * 1000)
        const { mall } = requestData
        const { goods_commit_id, goods_id } = requestData.cloneGoodsDetails!
        const uploadRes = await getUpLoadData(mall, {
            goods_commit_id,
            goods_id,
            cat_id: requestData.cloneGoodsDetails!.cat_id
        })
        addToLog({ msg: '正在复原商品信息-' })
        const newGoodsCommitRes = await retry(async (index) => {
            addToLog({ msg: `正在申请编辑商品-第${index}次` })
            const res = await newGoodsCommitId(mall, { goods_id: requestData.goods_id! })
            if (index >= 5) {
                return res
            }
            if (!res.success) {
                return Promise.reject(res)
            }
            return res
        }, 5, 10 * 1000)
        if (!newGoodsCommitRes.success) {
            addToLog({ msg: '申请编辑商品失败' + newGoodsCommitRes.error_msg, type: 'danger' })
            return Promise.reject()
        }
        const new_goods_commit_id = newGoodsCommitRes.result.goods_commit_id
        addToLog({ msg: '正在复原商品信息--' })
        const transformRes = await transFromData({
            ...uploadRes,
            detail: requestData.cloneGoodsDetails!,
            goods_commit_id,
            goods_id,
            new_goods_id: requestData.goods_id!,
            new_goods_commit_id,
            decoration: {
                floor_list: requestData.gallery.filter(item => item.type == 2).map(item => {
                    return {
                        content_list: [{
                            img_url: item.url
                        }]
                    }
                    return
                })
            }
        })
        requestData.newGoodsCloneInfo = transformRes.data
        addToLog({ msg: '正在复原商品信息---' })


        const createRes = await createGoodsInfo(mall, cloneDeep(requestData.newGoodsCloneInfo!))
        console.log('editGoods - createRes', createRes)
        const anti_content = await new Promise<string[]>((resolve, reject) => {
            antiContentFromWeb()
                .then(res => {
                    const { anti_content1, anti_content2 } = res.data

                    resolve([anti_content1, anti_content2])
                })
                .catch(res => {
                    addToLog({ msg: '获取anticontent失败' + res.msg })
                    reject()
                })
        })
        addToLog({ msg: '正在复原商品信息----' })
        const submitRes = await goodsSubmit(mall, { goods_commit_id: requestData.newGoodsCloneInfo!.goods_commit_id, goods_id: requestData.newGoodsCloneInfo!.goods_id }, anti_content)
        console.log('editGoods - submitRes', submitRes)
        if (!submitRes.success) {
            addToLog({ msg: '商品复原失败' + (submitRes.error_msg || submitRes.errorMsg), type: 'danger' })

        } else {
            addToLog({ msg: '商品信息已复原', type: 'success' })
            // addToLog({ msg: '请前往后台查询复原的商品是否符合预期,修改商品信息前请支付订单，否则订单将失效', type: 'primary' })
            requestDataPendingToRecover = undefined
        }


    } catch (e: any) {
        let msg = ''
        if (typeof e === 'object') {
            if (e.msg) {
                msg = e.msg
            }
        } else {
            msg = String(e)
        }
        addToLog({ msg: '商品信息复原失败:' + String(msg), type: 'danger' })
    }
    loading.recover = false
}

async function resolveMateriaInfo(requestData: RequestData) {
    const { cloneGoodsId, mall, goods_id, cloneGoodsDetails } = requestData
    const types = [1, 3]
    const { cat_id_1, cat_id_2, cat_id_3, cat_id_4 } = cloneGoodsDetails!
    await Promise.allSettled(types.map(async type => {
        const title = type === 1 ? '白底图' : '长图'
        const res = await materialInfo(mall, { type, goods_id: cloneGoodsId })
        if (!res.success) {
            addToLog({ msg: `获取${title}失败，${res.error_msg || res.errorMsg}` })
            return
        }
        const { check_status, latest_material } = res.result || {}
        if (check_status == 2 && latest_material) {
            addToLog({ msg: `正在处理${title}` })
            const { base64 } = await urlToBase64(latest_material)
            addToLog({ msg: `正在上传${title}` })

            const uploadRes = await uploadImageToStore(mall, base64)
            console.log('uploadRes', uploadRes)
            if (!uploadRes.url) {
                addToLog({ msg: `上传${title}失败，${res.error_msg || res.errorMsg}` })
                return
            }
            const createRes = await storeCreateFile(mall, { url: uploadRes.url })
            console.log('createRes', createRes)
            if (createRes.error_msg) {
                addToLog({ msg: `创建${title}失败，${createRes.error_msg}` })
                return
            }
            const mall_file_id = createRes.result
            /**创建 */
            const create2Res = await createMateria(mall, {
                type,
                mall_file_id,
                goods_id: goods_id!,
                cat_id_1,
                cat_id_2,
                cat_id_3,
                cat_id_4,
                content: uploadRes.url
            })
            console.log(create2Res)
            addToLog({ msg: `结束${title}操作` })
        } else {
            addToLog({ msg: `该商品${title}不存在或没有有效的${title}` })
        }
    }))
}

async function bindGift(requestData: RequestData) {
    // const mall = useMallStore().tableList[0]
    const { mall, tempGoodsInfo, cloneGoodsDetails } = requestData
    // const mainGoodsId = tempGoodsInfo?.id
    // const mainGoodsSkuId = tempGoodsInfo?.sku_list[0]?.skuId
    // const giftGoodsId = cloneGoodsDetails?.goods_id
    // const giftGoodsSkuId = cloneGoodsDetails?.skus[0]?.sku_id
    let mainGoodsId = tempGoodsInfo?.id
    let mainGoodsSkuId = ''
    const giftGoodsId = state.cloneGoodsId
    let giftGoodsSkuId = ''
    const giftGoodsRes = await giftGoodsListApi(mall, { goods_name: giftGoodsId })
    console.log('giftGoodsRes', giftGoodsRes)
    if (!giftGoodsRes.success) {
        addToLog({ msg: '获取赠品列表失败' + giftGoodsRes.error_msg || giftGoodsRes.errorMsg, type: 'danger' })
        return Promise.reject(giftGoodsRes)
    }
    giftGoodsSkuId = giftGoodsRes.result.gift_display_info_list[0].sku_id
    // if(!goods){
    //     addToLog({msg:'未找到赠品' ,type:'danger'})
    //     return Promise.reject('未找到赠品')
    // }
    // const giftMainGoodsRes = await giftMainGoodsListApi(mall,{goods_name:mainGoodsId,gift_goods_id:Number(giftGoodsId)})
    // console.log('giftMainGoodsRes',giftMainGoodsRes)
    // if(!giftMainGoodsRes.success){
    //     addToLog({msg:'获取主商品列表失败' + giftMainGoodsRes.error_msg || giftMainGoodsRes.errorMsg,type:'danger'})
    //     return Promise.reject(giftGoodsRes)
    // }
    // mainGoodsSkuId = giftMainGoodsRes.result.goods_display_info_list[0].sku_id
    // const mainGoods = giftMainGoodsRes.result.gift_display_info_list[0]
    // if(!mainGoods){
    //     addToLog({msg:'未找到主商品' ,type:'danger'})
    //     return Promise.reject('未找到主商品')
    // }
    const mainGoodsSkuListRes = await giftMainGoodsSku(mall, { goods_id: Number(mainGoodsId), gift_goods_id: Number(giftGoodsId) })

    mainGoodsSkuId = mainGoodsSkuListRes.result.sku_display_info_list[0].sku_id
    const data = {
        "event_name": "送赠品", //活动名称
        // "start_time": Date.now() - 1000 * 60,  //当前时间
        "start_time": dayjs(Date.now()).startOf('day').valueOf(),
        "gift_goods_id": giftGoodsId, //活动赠品ID
        "gift_sku_id": giftGoodsSkuId, //活动赠品SKU ID
        "effective_time_type": 1,
        "main_goods_enroll_list": [ //活动主商品
            {
                "goods_id": mainGoodsId,   //活动主商品ID
                "enroll_sku_ids": [
                    mainGoodsSkuId   //活动主商品SKU ID
                ],
                // "quantity": "2000000" //库存 10w个最多
                "quantity": "2000000" //库存 10w个最多
            }
        ]
    }

    console.log(data, requestData)
    if (!mainGoodsId || !mainGoodsSkuId || !giftGoodsId || !giftGoodsSkuId) {
        addToLog({ msg: '信息不全-无法操作', type: 'danger' })
        return Promise.reject()
    }
    requestData.giftInfo = {
        mainGoodsId: Number(mainGoodsId),
        mainGoodsSkuId: Number(mainGoodsSkuId),
        giftGoodsId: Number(giftGoodsId),
        giftGoodsSkuId: Number(giftGoodsSkuId)
    }
    const createRes = await createGiftGoodsApi(mall, data)
    console.log('createRes', createRes)
    if (!createRes.success) {
        addToLog({ msg: '绑定赠品失败' + createRes.error_msg || createRes.errorMsg, type: 'danger' })
        return Promise.reject(createRes)
    }

    const { success_event_info_list } = createRes.result
    const item = (success_event_info_list as any[]).find(item => {
        return item.goods_id == mainGoodsId
    })
    if (item && item.promotion_event_sn) {
        addToLog({ msg: '绑定成功' })
        requestData.promotion_event_sn = item.promotion_event_sn
        return true
    } else {
        addToLog({ msg: '绑定失败' })
        return Promise.reject('绑定失败')
    }

}

async function cancelGiftGoods(requestData: RequestData) {
    const { mall, promotion_event_sn } = requestData
    if (!promotion_event_sn) {
        // addToLog({ msg: '未找到活动管理ID,请前往后台《下单赠礼品》模块手动取消', type: 'danger' })
        return
    }
    const res = await cancelGiftGoodsApi(mall, { promotion_event_sn })
    if (res.success) {
        addToLog({ msg: '取消成功' })
    } else {
        addToLog({ msg: '取消失败' + res.error_msg || res.errorMsg, type: 'danger' })
    }
}
</script>
<style lang='scss' rel="stylesheet/scsss" scoped></style>
