@mixin text-overflow {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-overflow {
  @include text-overflow()
}

@mixin commonTableHeader($is-deep: true) {
  // @if ($is-deep) {
  //   :deep(.el-table__header) {
  //     .el-table__cell {
  //       background-color: var(--el-fill-color-light);
  //       color: var(--el-text-color-secondary);
  //       font-weight: 500;
  //     }
  //   }

  //   :deep(.el-table-v2__header) {
  //     .el-table-v2__header-cell {
  //       background-color: var(--el-fill-color-light);
  //       color: var(--el-text-color-secondary);
  //       font-weight: 500;
  //     }
  //   }
  //   :deep(.vxe-header--row){
  //     .vxe-header--column{
  //       background-color: var(--el-fill-color-light);
  //       color: var(--el-text-color-secondary);
  //       font-weight: 500;
  //       .vxe-cell{
  //         display: flex;
  //         align-items: center;

  //       }
  //     }
  //   }

  //   // :deep(.el-table__cell) {
  //   //   padding-left: 12px;
  //   // }
  // }

  // @else {
  //   .el-table__header {
  //     .el-table__cell {
  //       background-color: var(--el-fill-color-light);
  //       color: var(--el-text-color-secondary);
  //       font-weight: 500;
  //     }
  //   }

  //   // .el-table__cell {
  //   //   padding-left: 12px;
  //   // }
  // }
}

@mixin icon-title($icon-width: 5px, $icon-height: 16px, $space: 10px, $font-size: 14px) {
  height: 26px;
  padding-left: $space;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  color: var(--el-text-color-primary);
  position: relative;
  font-size: $font-size;

  .text {
    margin: 0 8px;
  }

  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    width: $icon-width;
    height: $icon-height;
    background: var(--el-color-primary);
    top: 50%;
    transform: translateY(-50%);
    border-radius: 26px;
  }

  .el-icon {
    cursor: pointer;
  }
}

@mixin goods-info-box($imageWidth: 40px, $imageHeight: 40px) {
  display: flex;
  align-items: center;

  &>.el-image,
  &>img {
    width: $imageWidth;
    height: $imageHeight;
    margin-right: 8px;
    border-radius: 4px;
    flex-shrink: 0;
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;

    p {
      height: 22px;
      line-height: 22px;
    }

    p.title {
      font-size: 14px;
    }

    p.desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}
