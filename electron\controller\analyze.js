const { Controller }           = require( "ee-core" );
const AnyProxy                 = require( "anyproxy" );
const cp                       = require( "child_process" );
const path                     = require( "path" );
const fs                       = require( "fs" );
const os                       = require( "os" );
const { <PERSON>rowserWindow }        = require( "electron" );
const { isDev, resourcesRoot } = require( "../config/config.app" );
const ffi                      = require( "ffi-napi" );
// const ref = require("ref-napi");
// console.log('[AnyProxy.utils.certMgr.ifRootCAFileExists()]', AnyProxy.utils.certMgr.ifRootCAFileExists())
const process      = require( 'process' );
process.env.PATH   = `${ resourcesRoot + '/extraResources/dll/p' }${ path.delimiter }${ process.env.PATH }`;
const dllDir       = resourcesRoot + '/extraResources/dll/p';
const proxyLibrary = ffi.Library( dllDir + '/proxy.dll', {
    // const proxyLibrary = ffi.Library('proxy.dll', {
    'SetSystemProxy'     : ['int', ['string', 'string']],
    'EnableSystemProxy'  : ['int', []],
    'DisableSystemProxy' : ['int', []]
} );

/**
 * @type {ProxyOptions}
 */
const proxyServerConfig = {
    port : 8001,
    rule : {
        async beforeSendResponse ( requestDetail, responseDetail ) {
            console.log( "**************url:", requestDetail.url );
            if (
                requestDetail.url &&
                requestDetail.url.includes( "mobile.yangkeduo.com/goods" )
            ) {
                const str    = responseDetail.response.body.toString( "utf-8" );
                const reg    = /window.rawData=({.*});/;
                const result = str.match( reg );
                that.app.electron.mainWindow.send( "analyzeController.goodsDetails", {
                    type : "wechat-url",
                    str  : result && result[ 1 ],
                } );
            }
            if (
                requestDetail.url &&
                requestDetail.url.includes(
                    "https://api.pinduoduo.com/api/oak/integration/render?"
                )
            ) {
                const str = responseDetail.response.body.toString( "utf-8" );
                that.app.electron.mainWindow.send( "analyzeController.goodsDetails", {
                    type : "wechat-page",
                    str  : str,
                } );
            }
            // 评论
            if (
                requestDetail.url &&
                requestDetail.url.includes(
                    "https://mobile.yangkeduo.com/proxy/api/reviews/"
                )
            ) {
                const result = requestDetail.url.match( /reviews\/(\d+)\// );
                if ( result && result[ 1 ] ) {
                    const goodsId = result[ 1 ];
                    console.log( "[goods-id]:", goodsId );
                    const str = responseDetail.response.body.toString( "utf-8" );
                    that.app.electron.mainWindow.send( "analyzeController.collectRviews", {
                        type    : "web",
                        bodyStr : str,
                        goodsId,
                    } );
                }
            }

            if ( requestDetail.url && requestDetail.url.includes( 'https://apm.pinduoduo.com/' ) ) {
                // console.log(requestDetail)
                if ( requestDetail.headers ) {
                    console.log( requestDetail.headers );
                }
            }
            return responseDetail;
        },
    },

    webInterface    : {
        enable  : false,
        webPort : 8002,
    },
    throttle        : 10000,
    forceProxyHttps : true,
    wsIntercept     : false, // 不开启websocket代理
    silent          : !isDev,
};

function checkAndCopyFile () {
    try {
        const targetPath = path.join( `${ os.homedir() }`, "/.anyproxy/certificates" );
        let basePath     = path.resolve(
            __dirname,
            process.cwd() + "/resources/extraResources/ca/"
        );
        if ( isDev ) {
            basePath = path.resolve( __dirname, "../../build/extraResources/ca/" );
        }
        if ( !fs.existsSync( targetPath ) ) {
            fs.mkdirSync( targetPath, { recursive : true } );
        }
        if ( !fs.existsSync( targetPath + "/rootCA.crt" ) ) {
            fs.copyFileSync( basePath + "/rootCA.crt", targetPath + "/rootCA.crt" );
            fs.copyFileSync( basePath + "/rootCA.key", targetPath + "/rootCA.key" );
        }
        return true;
    } catch ( e ) {
        console.log( e );
        return false;
    }
}

// 安装证书
async function installCert ( installCert ) {
    const res     = {
        success : true,
        msg     : "",
    };
    const certMar = path.resolve(
        __dirname,
        resourcesRoot + "/extraResources/ca/CertMgr.exe"
    );
    return new Promise( ( resolve ) => {
        cp.exec(
            `${ certMar } -c -add ${ installCert } -s -r localMachine root`,
            async ( error ) => {
                if ( error ) {
                    res.success = false;
                    res.msg     = error.message;
                }
                resolve( res );
            }
        );
    } );
}

// 检查证书是否存在
async function checkCertExists ( name, type = "ROOT" ) {
    return new Promise( ( resolve ) => {
        cp.exec( `certutil -store ${ type }  ${ name }`, ( error, stdout, stderr ) => {
            console.log( "[stdout]", stdout );
            resolve( stdout.includes( "NotAfter" ) );
        } );
    } );
}

// 设置代理
async function setSystemProxy ( type = "open" ) {
    // if (!proxyLibrary) {
    //   const process = require('process')
    //   const dllDir = resourcesRoot + '\\extraResources\\dll\\p';
    //   process.env.PATH = `${dllDir}${path.delimiter}${process.env.PATH}`;
    //   try {
    //     proxyLibrary = ffi.Library(dllDir + '\\proxy.dll', {
    //       'SetSystemProxy': ['int', ['string', 'string']],
    //       'EnableSystemProxy': ['int', []],
    //       'DisableSystemProxy': ['int', []]
    //     });
    //   } catch (e) {
    //     return {
    //       success: false,
    //       msg: e
    //     }
    //   }
    // }
    switch ( type ) {
        case "open": {
            proxyServerConfig.port = 13580 + Math.round( Math.random() * 9 );
            // cp.spawn(resourcesRoot + "/extraResources/exe/p.exe", [
            //   `http=127.0.0.1:${proxyServerConfig.port}`,
            //   `https=127.0.0.1:${proxyServerConfig.port}`,
            // ]);
            // const str = `http=127.0.0.1:${proxyServerConfig.port};https=127.0.0.1:${proxyServerConfig.port}`
            // const str = `127.0.0.1:${proxyServerConfig.port}`
            // libProxy.shezhi(proxyServerConfig.port,2)
            proxyLibrary.SetSystemProxy( "127.0.0.1", proxyServerConfig.port + '' );
            proxyLibrary.EnableSystemProxy();
            break;
        }
        case 'close': {
            // cp.spawn(resourcesRoot + "/extraResources/exe/p.exe",['close']);
            // libProxy.kaiguan()
            proxyLibrary.DisableSystemProxy();
        }
    }

    return {
        success : true,
        msg     : ""
    };
}

function setProxy ( install = true ) {
    return new Promise( async ( resolve, reject ) => {
        try {
            if ( !checkAndCopyFile() ) {
                reject( "checkAndCopyFile false" );
                return;
            }
            // if()
            //
            if ( !await checkCertExists( "DO_NOT_TRUST_FiddlerRoot" ) ) {
                installCert( path.resolve( resourcesRoot + "/extraResources/ca/FiddlerRoot.crt" ) );
            }

            if ( !await checkCertExists( "AnyProxy" ) || install ) {
                const {
                          success,
                          msg
                      } = await installCert( path.resolve( resourcesRoot + "/extraResources/ca/rootCA.crt" ) );
                if ( success ) {
                    const res = await setSystemProxy();
                    if ( !res.success ) {
                        reject( res.msg );
                    } else {
                        resolve( true );
                    }

                } else {
                    reject( msg );
                }
            } else {
                const res = await setSystemProxy();
                if ( !res.success ) {
                    reject( res.msg );
                } else {
                    resolve( true );
                }
            }
        } catch ( e ) {
            reject( e && e.toString() );
        }
    } );
}

function proxy () {
    const proxyServer = new AnyProxy.ProxyServer( proxyServerConfig );

    proxyServer.on( "ready", () => {
        if ( !analyzeWindow ) {
            const win = new BrowserWindow( {
                title : "微信解析",
                icon  : path.resolve( __dirname, "../../build/icons/icon.ico" ),
            } );
            const url = isDev
                ? path.resolve( __dirname, "../../public/files/analyze.html" )
                : path.resolve( __dirname, "../../files/analyze.html" );
            win.loadFile( url );
            win.on( "close", () => {
                that.stop_wechat();
                analyzeWindow = null;
            } );
            analyzeWindow = win;
        }
        console.log( "ready" );
    } );
    proxyServer.on( "error", ( e ) => {
        /* */
        console.log( "error", e );
    } );
    proxyServer.start();
    return proxyServer;
}

let proxyServer = null;
let that        = null;

let analyzeWindow = null;

class analyzeController extends Controller {
    constructor ( ctx ) {
        super( ctx );
        that = this;
    }

    /**
     * 所有方法接收两个参数
     * @param args 前端传的参数
     * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
     */

    async start_wechat ( { install } ) {
        return new Promise( ( resolve ) => {
            try {
                proxyServer && ( proxyServer.close(), ( proxyServer = null ) );
                const response = {
                    success : true,
                    msg     : "",
                };
                // const exePath = resourcesRoot + "/extraResources/exe/p.exe"
                // const check = fs.existsSync(exePath)
                // if (!check) {
                //   response.success = false;
                //   response.msg = "缺p.exe文件，无法启动";
                //   resolve(response);
                //   return;
                // }
                setProxy( install )
                    .then( ( res ) => {
                        proxyServer      = proxy();
                        response.success = true;
                        resolve( response );
                    } )
                    .catch( ( res ) => {
                        response.success = false;
                        response.msg     = res.toString();
                        resolve( response );
                    } );
            } catch ( e ) {
                response.msg     = e.toString();
                response.success = false;
                resolve( response );
            }
        } );
    }

    async stop_wechat () {
        return new Promise( ( resolve, reject ) => {
            setSystemProxy( 'close' );
            proxyServer && ( proxyServer.close(), ( proxyServer = null ) );
            resolve( "success" );
        } );
    }

    async execJStoAnalyzeWindow ( str ) {
        if ( analyzeWindow ) {
            analyzeWindow.webContents.executeJavaScript( str );
        }
    }
}

analyzeController.toString = () => "[class analyzeController]";
module.exports             = analyzeController;
