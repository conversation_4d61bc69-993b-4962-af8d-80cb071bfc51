"use strict";

const Service = require("ee-core").Service;
const Storage = require("ee-core").Storage;
const _ = require("lodash");
const path = require("path");
const fs = require('fs')
const tableNames = {
  READLIST: "store_list",
};

/**
 * 数据存储
 * @class
 */
class StoresStorageService extends Service {
  constructor(ctx) {
    super(ctx);

    // sqlite数据库
    this.sqliteFile = "sqlite-stores-v2.db";
    let sqliteOptions = {
      driver: "sqlite",
      default: {
        timeout: 6000,
        verbose: console.log, // 打印sql语法
      },
    };
    this.goodsReadSqliteDB = Storage.JsonDB.connection(
      this.sqliteFile,
      sqliteOptions
    );
  }

  /*
   * 检查并创建表 (sqlite)
   */
  async checkAndCreateTableSqlite(tableName = "") {
    try {
      if (_.isEmpty(tableName)) {
        throw new Error(`table name is required`);
      }
      // 检查表是否存在
      const userTable = this.goodsReadSqliteDB.db.prepare(
        `SELECT * FROM sqlite_master WHERE type = ? AND name = ?`
      );

      const result = userTable.get("table", tableName);
      if (result) {
        return;
      }

      // 创建表
      let create_table_str = "";
      switch (tableName) {
        case tableNames.READLIST: {
          create_table_str = `CREATE TABLE ${tableName}
              (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  mallId INT,
                  mallName CHAR(255),
                  status INT,
                  cookieJSON TEXT,
                  infoJSON TEXT,

                  update_time INT
              );`;
          break;
        }
      }
      create_table_str && this.goodsReadSqliteDB.db.exec(create_table_str);
    } catch (e) {
      console.log(e);
    }
  }

  /*
   * 增 Test data (sqlite)
   */
  async addStoreItem(data) {
    const { mallId } = data;
    const isExist = await this.checkStoreItem(mallId);
    const response = {
      code: 0,
      msg: "",
    };
    if (isExist) {
      response.code = 2;
      response.msg = "已存在的店铺";
      return response;
      // return Promise.reject("已存在的商品");
    }
    let table = tableNames.READLIST;
    
    const keys = Object.keys(data);
    const insert = this.goodsReadSqliteDB.db.prepare(
      `INSERT INTO ${table} (${keys.join(",")}) VALUES (${keys
        .map((item) => "@" + item)
        .join(",")})`
    );
    insert.run(data);

    return response;
  }

  async checkStoreItem(mallId) {
    await this.checkAndCreateTableSqlite(tableNames.READLIST);
    const check = this.goodsReadSqliteDB.db.prepare(
      `SELECT COUNT(*) AS total FROM ${tableNames.READLIST} WHERE mallId = ?`
    );
    const total = check.get(mallId).total;
    return total ? true : false;
  }

  /*
   * 删 Test data (sqlite)
   */
  async deleteStoreItem(mallId) {
    //console.log("delete name:", name);

    let table = tableNames.READLIST;
    const isExist = await this.checkStoreItem(mallId);
    if (!isExist) {
      return Promise.reject("不存在当前店铺");
    }
    const delStore = this.goodsReadSqliteDB.db.prepare(
      `DELETE FROM ${table} WHERE mallId = ?`
    );
    delStore.run(mallId);

    return true;
  }

  /**
   * 查一个
   */
  async findStoreItem(mallId){
    let table = tableNames.READLIST;
    // const isExist = await this.checkStoreItem(mallId);
    // if (!isExist) {
    //   return false
    // }
    const findStore = this.goodsReadSqliteDB.db.prepare(
      `SELECT * FROM ${table} WHERE mallId = ?`
    );
    const store = findStore.get(mallId);
    return store
  }

  /**
   * 获取一个可用的
   * @returns 
   */
  async getAvailableOne(){
    let table = tableNames.READLIST;
    const findStore = this.goodsReadSqliteDB.db.prepare(
      `SELECT * FROM ${table} WHERE update_time > ${Date.now() - 6 * 3600 * 1000} AND status = 1 LIMIT 1`
    );
    const store = findStore.get();
    return store
  }

  /*
   * 改 Test data (sqlite)
   */
  async updateStore(data) {
    let strs = [];
    if(data.status !== 0){
      data.update_time = Date.now()
    }
    for (let key in data) {
      if (key === "id" || data[key] === undefined) {
        continue;
      } else {
        const value = typeof data[key] === 'string' ? `'${data[key]}'` : data[key]
        strs.push(`${key}=${value}`);
      }
    }
    let table = tableNames.READLIST;
    await this.checkAndCreateTableSqlite(table);
    const updateStr =  `UPDATE ${table} SET ${strs.join(",")} WHERE mallId  = ${data.mallId}`
    const updateUser = this.goodsReadSqliteDB.db.prepare(
      updateStr
    );
   updateUser.run(data);

    return true;
  }

  /*
   * 查 Test data (sqlite)
   */
  async getStoresList({ page, limit }) {
    //console.log("select :", {age});

    let table = tableNames.READLIST;
    await this.checkAndCreateTableSqlite(table);

    const countTotal = this.goodsReadSqliteDB.db.prepare(
      `SELECT COUNT(*) as total  FROM ${table}`
    );
    const selectUser = this.goodsReadSqliteDB.db.prepare(
      `SELECT *  FROM ${table}  LIMIT ${(page - 1) * limit},${limit}`
      // `SELECT *  FROM ${table} `
    );
    const stores = selectUser.all();
    const total = countTotal.get().total;
    return {
      total,
      list: stores,
    };
  }


}

StoresStorageService.toString = () => "[class StoresStorageService]";
module.exports = StoresStorageService;
