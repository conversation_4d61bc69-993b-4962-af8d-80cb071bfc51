import { App } from "vue";
import XEUtils from "xe-utils";
import { VXETable, Table, Column,List,Tooltip,Filter } from "vxe-table";
import zhCN from "vxe-table/es/locale/lang/zh-CN";
import Icon from "../components/Icon/Icon.vue";
import pagination from "../components/pagination/pagination.vue";
import scroll from "../components/scroll/scroll.vue";
import ScreenVideo from "../components/ScreenVideo/ScreenVideo.vue";

VXETable.setup({
  i18n: (key, args) => XEUtils.toFormatString(XEUtils.get(zhCN, key), args),
});

function bindComponents(app: App) {
  // 自定义组件
  app.component("Icon", Icon);
  app.component("ds-pagination", pagination);
  app.component("ds-scroll", scroll);
  app.component("ScreenVideo", ScreenVideo);

  // vxe-table组件
  app.use(Table).use(Column).use(List).use(Tooltip).use(Filter)
}

export default bindComponents;
