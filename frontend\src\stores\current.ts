import { ElMessage } from "element-plus";
import { defineStore } from "pinia";

import { changePddWindowUrl } from "../apis/pddWindow";

export const useCurrentGoods = defineStore("currentGoods", {
  state() {
    const state: {
      infos: null | anyObj;
    } = {
      infos: null,
    };
    return state;
  },
  getters: {
    goods(): undefined | anyObj {
      return this.infos?.store.initDataObj.goods;
    },
    mall(): undefined | anyObj {
      return this.infos?.store.initDataObj.mall;
    },
    // 上架时间
    shelfTime(): string {
      const reg = /\/(20\d{2}-\d{1,2}-\d{1,2})\//;
      const arr = this.infos?.store.initDataObj?.goods.topGallery as anyObj[];
      if (arr && typeof arr === "object") {
        let times: string[] = [];
        arr.forEach((item) => {
          const url: string = item.url;
          const res = url.match(reg);
          if (res) {
            times.push(res[1]);
          }
        });

        times.sort();
        if (times.length) {
          return times[0];
        }
      }
      return "-";
    },
    dsr(): anyObj | undefined {
      return this.infos?.store.initDataObj?.mall?.dsr;
    },
    isErrorPage(): boolean {
      if (!this.infos) {
        return false;
      }
      const status = this.infos.store.initDataObj.goods.status;
      const statusExplain = this.infos.store.initDataObj.goods.statusExplain;
      if (status == 5 || (statusExplain as string).includes("商品已告罄")) {
        return true;
      } else {
        return false;
      }
    },
  },
  actions: {},
});

export const useCurrentPddUrl = defineStore("currentPddUrl", {
  state: () => {
    return {
      url: "",
    };
  },
  getters: {
    isVerifiPage(): boolean {
      return (
        this.url.startsWith(
          "https://mobile.pinduoduo.com/psnl_verification.html?"
        ) ||
        this.url.startsWith(
          "https://mobile.yangkeduo.com/psnl_verification.html?"
        )
      );
    },
    isGoodsDetailsPage(): boolean {
      return /^(https|http):\/\/mobile\.(pinduoduo|yangkeduo)\.com\/goods\d*\.html\?.*goods_id=/.test(
        this.url
      );
    },
  },
  actions: {
    changeUrl(str: "back" | "next" | "home" | "reload" | string,clear = false) {
      if (!str) {
        return ElMessage.warning("请输入地址");
      } else {
        switch (str) {
          case "home":
          case "back":
          case "next":
          case "reload": {
            break;
          }
          default: {
            if (
              !str.startsWith("https://mobile.pinduoduo.com/") &&
              !str.startsWith("https://mobile.yangkeduo.com/")
            ) {
              return ElMessage.warning("不能跳转到拼多多以外的链接");
            }
          }
        }

        return changePddWindowUrl(str,clear);
      }
    },
  },
});
