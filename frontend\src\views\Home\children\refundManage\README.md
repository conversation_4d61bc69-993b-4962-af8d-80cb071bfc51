# 代拍退款管理功能

## 功能概述

代拍退款管理功能是一个完整的退款处理系统，位于店铺管理菜单后面，提供了退款申请的查看、处理、统计等功能。

## 功能特性

### 1. 退款列表管理
- 支持分页显示退款记录
- 提供多种筛选条件：时间范围、退款状态、关键词搜索
- 显示订单号、店铺名称、商品信息、退款金额、申请时间等详细信息
- 支持批量选择和操作

### 2. 退款处理
- **单个处理**：支持同意或拒绝单个退款申请
- **批量处理**：支持批量同意待处理的退款申请
- **状态管理**：自动更新退款状态（待处理→处理中→已完成/已拒绝）

### 3. 数据导出
- 支持将退款记录导出为CSV格式
- 包含完整的退款信息和处理状态

### 4. 退款详情
- 查看详细的退款信息
- 显示处理时间和备注信息

## 页面结构

```
refundManage/
├── refundManage.vue          # 主页面组件
└── README.md                 # 功能说明文档
```

## API接口结构

### 前端API文件
- `frontend/src/apis/refund.ts` - 退款相关的API接口定义

### 后端文件结构
- `electron/controller/refund.js` - 退款控制器
- `electron/service/refund.js` - 退款服务层

## 数据模型

### RefundRecord 退款记录
```typescript
interface RefundRecord {
  id: number;                    // 记录ID
  orderSn: string;              // 订单号
  shopName: string;             // 店铺名称
  goodsName: string;            // 商品名称
  goodsImg: string;             // 商品图片
  refundAmount: number;         // 退款金额
  applyTime: number;            // 申请时间
  processTime?: number;         // 处理时间
  status: 'pending' | 'processing' | 'completed' | 'rejected';  // 退款状态
  reason: string;               // 退款原因
  remark?: string;              // 备注信息
}
```

### 退款状态说明
- `pending` - 待处理：新提交的退款申请
- `processing` - 处理中：已同意，正在处理退款
- `completed` - 已完成：退款已成功处理
- `rejected` - 已拒绝：退款申请被拒绝

## 使用说明

### 1. 访问页面
在左侧导航菜单中，点击"代拍退款"即可进入退款管理页面。

### 2. 查看退款列表
- 页面默认显示所有退款记录
- 使用顶部的筛选条件可以过滤数据
- 支持按时间范围、退款状态、关键词进行搜索

### 3. 处理退款申请
- 对于状态为"待处理"的记录，可以点击"同意退款"或"拒绝退款"
- 拒绝退款时需要输入拒绝原因
- 支持批量选择多个待处理记录进行批量同意

### 4. 查看详情
点击"查看详情"按钮可以查看退款申请的完整信息。

### 5. 导出数据
点击"导出数据"按钮可以将当前筛选条件下的退款记录导出为CSV文件。

## 开发说明

### 当前状态
- ✅ 页面UI和交互功能已完成
- ✅ 模拟数据和基本业务逻辑已实现
- ✅ API接口结构已预留
- ⏳ 真实API集成待实现
- ⏳ 数据库表结构待创建

### 模拟数据
当前使用模拟数据进行展示和测试，模拟数据包含：
- 50条退款记录
- 随机的店铺名称、商品名称、退款金额
- 不同的退款状态和申请时间
- 真实的业务场景数据

### API集成指南
要集成真实API，需要：

1. **替换API调用**：
   ```typescript
   // 当前使用模拟API
   const response = await mockGetRefundList(params);
   
   // 替换为真实API
   const response = await getRefundList(params);
   ```

2. **实现后端接口**：
   - 完善 `electron/controller/refund.js` 中的业务逻辑
   - 实现 `electron/service/refund.js` 中的数据库操作
   - 创建对应的数据库表结构

3. **数据库表结构建议**：
   ```sql
   CREATE TABLE refund_records (
     id INTEGER PRIMARY KEY AUTOINCREMENT,
     order_sn VARCHAR(50) NOT NULL,
     shop_name VARCHAR(100) NOT NULL,
     goods_name VARCHAR(200) NOT NULL,
     goods_img VARCHAR(500),
     refund_amount DECIMAL(10,2) NOT NULL,
     apply_time BIGINT NOT NULL,
     process_time BIGINT,
     status VARCHAR(20) NOT NULL DEFAULT 'pending',
     reason VARCHAR(500) NOT NULL,
     remark VARCHAR(1000),
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

## 样式说明

页面样式与现有的店铺管理页面保持一致：
- 使用相同的表格样式和分页组件
- 遵循项目的设计规范和色彩搭配
- 响应式布局，适配不同屏幕尺寸

## 注意事项

1. **权限控制**：后续可能需要添加用户权限验证
2. **数据安全**：退款操作涉及资金，需要严格的安全控制
3. **日志记录**：建议记录所有退款操作的详细日志
4. **通知机制**：可以考虑添加退款状态变更的通知功能
5. **数据备份**：重要的退款数据需要定期备份

## 扩展功能建议

1. **退款统计图表**：添加可视化的退款统计图表
2. **自动退款**：对于符合条件的退款申请，支持自动处理
3. **退款模板**：预设常用的拒绝原因模板
4. **导出格式**：支持更多导出格式（Excel、PDF等）
5. **退款流程**：添加更复杂的退款审批流程
