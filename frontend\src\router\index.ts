import { ElMessage } from 'element-plus';
import {createRouter,createWebHashHistory} from 'vue-router'
import { useUserStore } from '../stores/user';
import routes from "./routes";

const router = createRouter({
    history:createWebHashHistory(),
    routes,
})

const notCheckSet = new Set(['/Login','login'])
router.beforeEach((to,from,next)=>{

    // if(notCheckSet.has(to.path) ){
    //     next()
    // }else{
    //     const userStore = useUserStore()
    //     if(!userStore.token){
    //         ElMessage.error('身份验证不通过')
    //         next({
    //             name:"Login"
    //         })
    //     }else{
    //         next()
    //     }
    // }
    next()
})

export default router