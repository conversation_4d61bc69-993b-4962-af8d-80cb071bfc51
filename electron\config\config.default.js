"use strict";

const dayjs = require("dayjs");
const path = require("path");
const appConfig = require("./config.app");
/**
 * 默认配置
 */
module.exports = (appInfo) => {
  const config = {};

  /**
   * 应用模式配置
   */
  config.developmentMode = {
    default: "vue",
    mode: {
      vue: {
        hostname: "localhost",
        port: 2341,
      },
      react: {
        hostname: "localhost",
        port: 3000,
      },
      html: {
        hostname: "localhost",
        indexPage: "index.html",
      },
    },
  };

  /**
   * 开发者工具
   */
  config.openDevTools = true;

  /**
   * 应用程序顶部菜单
   */
  config.openAppMenu = "dev-show";

  /**
   * 主窗口
   */
  config.windowsOption = {
    title: "k",
    width: 1350,
    height: 660,
    minWidth: 1350,
    minHeight: 660,
    x:250,
    y:60,
    transparent: true,
    webPreferences: {
      //webSecurity: false, // 跨域问题 -> 打开注释
      contextIsolation: false, // false -> 可在渲染进程中使用electron的api，true->需要bridge.js(contextBridge)
      nodeIntegration: true,
      // preload:path.join(__dirname,'../preload/bridge.js')
    },
    resizable: false,
    frame: false,
    show: false,
    icon: path.join(appInfo.home, "public", "images", "logo-32.png"),
  };

  /**
   * ee框架日志
   */
  config.logger = {
    appLogName: `ee-${dayjs().format("YYYY-MM-DD")}.log`,
    errorLogName: `ee-error-${dayjs().format("YYYY-MM-DD")}.log`,
  };

  /**
   * 远程模式-web地址
   */
  config.remoteUrl = {
    enable: false,
    url: "http://localhost:2336/",
  };

  /**
   * 内置socket服务
   */
  config.socketServer = {
    enable: false,
    port: 7070,
    path: "/socket.io/",
    connectTimeout: 45000,
    pingTimeout: 30000,
    pingInterval: 25000,
    maxHttpBufferSize: 1e8,
    transports: ["polling", "websocket"],
    cors: {
      origin: true,
    },
  };

  /**
   * 内置http服务
   */
  config.httpServer = {
    enable: false,
    https: {
      enable: false,
      key: "/public/ssl/localhost+1.key",
      cert: "/public/ssl/localhost+1.pem",
    },
    port: 7071,
    cors: {
      origin: "*",
    },
    body: {
      multipart: true,
      formidable: {
        keepExtensions: true,
      },
    },
    filterRequest: {
      uris: ["favicon.ico"],
      returnData: "",
    },
  };

  /**
   * 主进程
   */
  config.mainServer = {
    host: "127.0.0.1",
    port: 2341,
  };

  /**
   * 硬件加速
   */
  config.hardGpu = {
    enable: false,
  };

  /**
   * 插件功能
   */
  config.addons = {
    window: {
      enable: true,
    },
    tray: {
      enable: true,
      title: appConfig.tray.title,
      icon: appConfig.tray.icon,
    },
    security: {
      enable: true,
    },
    awaken: {
      enable: true,
      protocol: "ee",
      args: [],
    },
    autoUpdater: {
      enable: true,
      windows: true,
      macOS: false,
      linux: false,
      options: {
        provider: "generic",
        // url: 'http://duoduohuoyan.jycdy.com/static/software/'
        url: "",
      },
      force: false,
    },
    javaServer: {
      enable: false,
      port: 18080,
      jreVersion: "jre1.8.0_201",
      opt: '-server -Xms512M -Xmx512M -Xss512k -Dspring.profiles.active=prod -Dserver.port=${port} -Dlogging.file.path="${path}" ',
      name: "java-app.jar",
    },
    example: {
      enable: true,
    },
  };

  return {
    ...config,
  };
};
