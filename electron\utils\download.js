const axios = require("axios");
const fs = require("fs");
const path = require('path')

function legalFileName(str){
  return  str.replace(/[*|\":<>?[\\\]/]/g, "")
}

function checkPath(_path) {
  
  const directory = path.dirname(_path);
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }
}
// 下载图片
const downloadImage = async (url, dest, closeCallback,check = true) => {
  try {
    const response = await axios({
      url,
      method: "GET",
      responseType: "stream",
    });
    // console.log(dest)
    // fs.writeFile(
    //   "./log.txt",
    //   [url, dest, response.data].join("\n") + "\n",
    //   (error) => {
    //     // if (error) console.log(error);
    //   }
    // );
    check && checkPath(dest)
    const writer = fs.createWriteStream(dest)
    response.data.pipe(writer)

    writer.on("close", () => {
    //   console.log("Image downloaded successfully!");
      closeCallback && typeof closeCallback === "function" && closeCallback();
    });
    
  } catch (e) {
    console.log(e,'errror');
    return Promise.reject(dest)
  }
};

// 下载视频
const downloadVideo = async (url, dest, closeCallback,check = true) => {
  try {
    const response = await axios({
      url,
      method: "GET",
      responseType: "stream",
    });
    check && checkPath(dest)
    response.data.pipe(fs.createWriteStream(dest)).on("close", () => {
      //   console.log('Video downloaded successfully!');
      closeCallback && typeof closeCallback === "function" && closeCallback();
    });
  } catch (e) {
    console.log(e);
  }
};

// 哪个地方的下载
const downloadSource = {
  'common':1,
  'tools-changeImg':2,
  'tools-cutout':3,
  "plat-video-list":4
}

module.exports = {
  downloadImage,
  downloadVideo,
  legalFileName,
  downloadSource
};
