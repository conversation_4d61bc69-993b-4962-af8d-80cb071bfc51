import { ipc } from "./config";

const controller = "controller.refund.";

/**
 * 退款记录数据类型定义
 */
export interface RefundRecord {
  id: number;
  orderSn: string;
  shopName: string;
  goodsId: string;
  goodsName: string;
  goodsImg: string;
  goodsValue: number;
  refundAmount: number;
  createTime: number;
  applyTime: number;
  processTime?: number;
  status: number; // 0=待申请, 1=申请中, 2=执行中, 3=已完成, 4=执行失败
  reason: string;
  remark?: string;
  blacklistScore: number;
}

/**
 * 退款列表查询参数
 */
export interface RefundListParams {
  page: number;
  limit: number;
  status?: string;
  keyword?: string;
  startTime?: number;
  endTime?: number;
}

/**
 * 退款处理参数
 */
export interface RefundProcessParams {
  id: number;
  action: 'approve' | 'reject';
  remark?: string;
}

/**
 * 批量退款处理参数
 */
export interface BatchRefundParams {
  ids: number[];
  action: 'approve' | 'reject';
  remark?: string;
}

/**
 * 退款统计信息接口
 */
export interface RefundStatistics {
  totalCount: number;
  pendingCount: number;
  processingCount: number;
  completedCount: number;
  rejectedCount: number;
  totalAmount: number;
  completedAmount: number;
}

// 以下是真实API接口定义，当后端实现后可以启用
// 当前暂时注释掉，使用模拟数据

/**
 * 获取退款记录列表
 * @param params 查询参数
 * @returns Promise<responseData<{ list: RefundRecord[], total: number }>>
 */
// export function getRefundList(params: RefundListParams) {
//   return ipc.invoke(controller + "getRefundList", params);
// }

/**
 * 处理单个退款申请
 * @param params 处理参数
 * @returns Promise<responseData>
 */
// export function processRefund(params: RefundProcessParams) {
//   return ipc.invoke(controller + "processRefund", params);
// }

/**
 * 批量处理退款申请
 * @param params 批量处理参数
 * @returns Promise<responseData>
 */
// export function batchProcessRefund(params: BatchRefundParams) {
//   return ipc.invoke(controller + "batchProcessRefund", params);
// }

/**
 * 获取退款详情
 * @param id 退款记录ID
 * @returns Promise<responseData<RefundRecord>>
 */
// export function getRefundDetail(id: number) {
//   return ipc.invoke(controller + "getRefundDetail", id);
// }

/**
 * 导出退款记录
 * @param params 导出参数
 * @returns Promise<responseData<{ filePath: string }>>
 */
// export function exportRefundRecords(params: RefundListParams) {
//   return ipc.invoke(controller + "exportRefundRecords", params);
// }

/**
 * 获取退款统计信息
 * @param params 统计参数
 * @returns Promise<responseData<RefundStatistics>>
 */
// export function getRefundStatistics(params: {
//   startTime?: number;
//   endTime?: number;
// }) {
//   return ipc.invoke(controller + "getRefundStatistics", params);
// }

// 以下是模拟数据生成函数，用于开发阶段测试
// 在真实API集成时，这些函数应该被移除或注释掉

/**
 * 生成模拟退款数据
 * @param count 生成数量
 * @returns RefundRecord[]
 */
// export function generateMockRefundData(count: number = 50): RefundRecord[] {
//   const mockData: RefundRecord[] = [];
//   const shopNames = ['拼多多旗舰店', '优选好货店', '品质生活馆', '时尚潮流店', '数码专营店'];
//   const goodsNames = [
//     '苹果iPhone 15 Pro Max 256GB 深空黑色',
//     '华为Mate60 Pro 512GB 雅川青',
//     '小米14 Ultra 16GB+1TB 钛金属',
//     '三星Galaxy S24 Ultra 512GB 钛金色',
//     'OPPO Find X7 Ultra 16GB+512GB 海阔天空'
//   ];
//   const reasons = ['商品质量问题', '不喜欢/不想要', '商品描述不符', '发错货', '物流问题', '尺寸不合适'];
//   const statuses: Array<'pending' | 'processing' | 'completed' | 'rejected'> = ['pending', 'processing', 'completed', 'rejected'];

//   for (let i = 1; i <= count; i++) {
//     const status = statuses[Math.floor(Math.random() * statuses.length)];
//     const createTime = Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000); // 最近30天内
//     const applyTime = createTime + Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000); // 创建后7天内申请
//     const goodsValue = Math.floor(Math.random() * 5000) + 100; // 100-5100元

//     mockData.push({
//       id: i,
//       orderSn: `PDD${Date.now().toString().slice(-8)}${i.toString().padStart(3, '0')}`,
//       shopName: shopNames[Math.floor(Math.random() * shopNames.length)],
//       goodsId: `78330142${i.toString().padStart(3, '0')}`,
//       goodsName: goodsNames[Math.floor(Math.random() * goodsNames.length)],
//       goodsImg: `https://picsum.photos/200/200?random=${i}`,
//       goodsValue,
//       refundAmount: goodsValue * (0.8 + Math.random() * 0.2), // 商品价值的80%-100%
//       createTime,
//       applyTime,
//       processTime: status !== 'pending' ? applyTime + Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000) : undefined,
//       status,
//       reason: reasons[Math.floor(Math.random() * reasons.length)],
//       remark: Math.random() > 0.7 ? '客户要求加急处理' : undefined,
//       blacklistScore: Math.random() * 5 // 0-5分
//     });
//   }

//   return mockData.sort((a, b) => b.applyTime - a.applyTime); // 按申请时间倒序
// }

/**
 * 模拟API调用延迟
 * @param ms 延迟毫秒数
 */
// export function mockApiDelay(ms: number = 500): Promise<void> {
//   return new Promise(resolve => setTimeout(resolve, ms));
// }

/**
 * 模拟退款列表API
 * 在真实API集成时，应该替换为真实的API调用
 */
// export async function mockGetRefundList(params: RefundListParams): Promise<{
//   success: boolean;
//   data: { list: RefundRecord[], total: number };
//   message?: string;
// }> {
//   await mockApiDelay();
  
//   try {
//     let mockData = generateMockRefundData();
    
//     // 应用过滤条件
//     if (params.status) {
//       mockData = mockData.filter(item => item.status === params.status);
//     }
    
//     if (params.keyword) {
//       const keyword = params.keyword.toLowerCase();
//       mockData = mockData.filter(item => 
//         item.orderSn.toLowerCase().includes(keyword) ||
//         item.goodsName.toLowerCase().includes(keyword) ||
//         item.shopName.toLowerCase().includes(keyword)
//       );
//     }
    
//     if (params.startTime && params.endTime) {
//       mockData = mockData.filter(item => 
//         item.applyTime >= params.startTime! && item.applyTime <= params.endTime!
//       );
//     }
    
//     // 分页处理
//     const startIndex = (params.page - 1) * params.limit;
//     const endIndex = startIndex + params.limit;
    
//     return {
//       success: true,
//       data: {
//         list: mockData.slice(startIndex, endIndex),
//         total: mockData.length
//       }
//     };
//   } catch (error) {
//     return {
//       success: false,
//       data: { list: [], total: 0 },
//       message: '获取退款列表失败'
//     };
//   }
// }

/**
 * 模拟退款统计API
 */
// export async function mockGetRefundStatistics(): Promise<{
//   success: boolean;
//   data: RefundStatistics;
//   message?: string;
// }> {
//   await mockApiDelay();
  
//   const mockData = generateMockRefundData();
  
//   const statistics: RefundStatistics = {
//     totalCount: mockData.length,
//     pendingCount: mockData.filter(item => item.status === 'pending').length,
//     processingCount: mockData.filter(item => item.status === 'processing').length,
//     completedCount: mockData.filter(item => item.status === 'completed').length,
//     rejectedCount: mockData.filter(item => item.status === 'rejected').length,
//     totalAmount: mockData.reduce((sum, item) => sum + item.refundAmount, 0),
//     completedAmount: mockData
//       .filter(item => item.status === 'completed')
//       .reduce((sum, item) => sum + item.refundAmount, 0)
//   };
  
//   return {
//     success: true,
//     data: statistics
//   };
// }
