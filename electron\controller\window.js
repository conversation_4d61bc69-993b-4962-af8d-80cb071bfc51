"use strict";

const _ = require("lodash");
const path = require("path");
const fs = require("fs");
const { Controller } = require("ee-core");
const childProcess = require("child_process");
const electronApp = require("electron").app;
const { shell, dialog, BrowserWindow, Menu } = require("electron");
const xlsx = require("node-xlsx");
const { resolveTXT, deleteFolderHandle } = require("../utils/resolveFile");
const os = require("os");
// const jimp = require("jimp");
// const sharp = require('sharp')
const uuid = require("uuid");
const ffmpeg = require("fluent-ffmpeg");
const { isDev, userAgent } = require("../config/config.app");
const ffmpegPath = require("@ffmpeg-installer/ffmpeg").path;
const ffprobePath = require("@ffprobe-installer/ffprobe").path;
const axios = require("axios");
function checkPath(_path) {
  const directory = path.dirname(_path);
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }
}
electronApp.addListener('web-contents-created', (_, webContents) => {
  webContents.setUserAgent(userAgent)
  webContents.userAgent = userAgent
  // console.log(webContents.getUserAgent())
  webContents.on('will-prevent-unload',(e)=>{
    e.preventDefault()
  })
  webContents.session.webRequest.onBeforeSendHeaders(
    { urls: [] },
    (details, cb) => {
      details.requestHeaders["User-Agent"] = userAgent;
      cb({
        cancel: false,
        requestHeaders: details.requestHeaders,
      });
    }
  )
})

const { fork } = require("child_process");

let compressFork = fork(path.resolve(__dirname, "../utils/forks/compress.js"));
const compressForkMap = new Map()
compressFork.on('message', (data) => {
  const resolveFn = compressForkMap.get(data.id)
  if (resolveFn) {
    resolveFn(data.result.base64)
    compressForkMap.delete(data.id)
  }
})

if (isDev) {
  ffmpeg.setFfmpegPath(ffmpegPath);
  ffmpeg.setFfprobePath(ffprobePath);
} else {
  ffmpeg.setFfmpegPath(ffmpegPath.replace("app.asar", "app.asar.unpacked"));
  ffmpeg.setFfprobePath(ffprobePath.replace("app.asar", "app.asar.unpacked"));
}
let app = null;
/**
 * 示例控制器
 * @class
 */

// 菜单模板

// 构建菜单项
const menu = (function () {
  const arr = [
    {
      label: "全选",
      role: "selectAll",
    },
    {
      label: "剪贴",
      role: "cut",
    },
    {
      label: "复制",
      role: "copy",
    },
    {
      label: "粘贴",
      role: "paste",
    },
  ];
  if (isDev) {
    arr.push({
      label: "打开开发工具",
      click: () => {
        app.electron.mainWindow.openDevTools();
      },
    });
  }
  return Menu.buildFromTemplate(arr);
})();

const currentWindownInfo = {};

function checkWindowSize(option) {
  // const {width,height,scale,x,y} = currentPddWindownInfo
  let flag = false;
  Object.keys(option).forEach((key) => {
    if (currentWindownInfo[key] != option[key]) {
      flag = true;
    }
    currentWindownInfo[key] = option[key];
  });
  return flag;
}

class WindowController extends Controller {
  constructor(ctx) {
    super(ctx);
    app = this.app;
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  async menu(arg) {
    menu.popup({
      x: arg.x,
      y: arg.y,
    });
  }

  /**
   * test
   */
  async test(args) {
    const result = await this.service.example.test(args);

    // let tmpDir = Utils.getLogDir();
    // console.log("tmpDir:", tmpDir);

    // console.log('this.app.request:', this.app.request.query);

    // const exampleAddon = this.app.addon.example;
    // const str = exampleAddon.hello();
    // console.log('str:', str);

    return result;
  }

  async getMac() {
    // return Object.values(os.networkInterfaces())[0][0].mac
    return Object.values(os.networkInterfaces())[0][0].mac;
  }

  // 打开外部浏览器
  async openExternal(url) {
    if (!url) {
      return Promise.reject("没有url");
    } else {
      // console.log(url)
      shell.openExternal(url);
    }
  }
  async openFileWindow(url) {
    if (!url) {
      return Promise.reject("没有链接");
    } else {
      shell.openPath(url);
    }
  }
  async resize(options) {
    const { x, y, width, height } = options;
    let scale = options.scale
    if(scale > 2){
      scale = scale /100
    }
    console.log(options);
    // if (!checkWindowSize(options)) {
    //   return "same";
    // }
    this.app.electron.mainWindow.hide();
    this.app.electron.mainWindow.setBounds({
      width: Math.floor(width * scale),
      height: Math.floor(height * scale),
      // height: scale >= 1 ? Math.floor(height * scale) : height,
    });
    // console.log(width * scale,height * scale)
    setTimeout(() => {
      this.app.electron.mainWindow.show();
    }, 500);
    return true;
  }

  async minimize() {
    this.app.electron.mainWindow.minimize();
    return true;
  }

  async close() {
    this.app.appQuit();
  }

  /**
   * 检查是否有新版本
   */
  checkForUpdater(data) {
    const autoUpdaterAddon = this.app.addon.autoUpdater;
    autoUpdaterAddon.checkUpdate(data);

    return;
  }

  /**
   * 下载新版本
   */
  downloadApp() {
    const autoUpdaterAddon = this.app.addon.autoUpdater;
    autoUpdaterAddon.download();
    return;
  }

  /**
   * 消息提示与确认对话框
   */
  async messageShowConfirm(options) {
    // {
    //   type: "info",
    //   title: "自定义标题-message",
    //   message: "自定义消息内容",
    //   detail: "其它的额外信息",
    //   cancelId: 1, // 用于取消对话框的按钮的索引
    //   defaultId: 0, // 设置默认选中的按钮
    //   buttons: ["确认", "取消"], // 按钮及索引
    // }
    const res = await dialog.showMessageBox(options);
    // let data = res === 0 ? "点击确认按钮" : "点击取消按钮";

    return res;
  }

  /**
   * 选择目录
   */
  async selectFolder() {
    const result = await dialog.showOpenDialog({
      properties: ["openDirectory", "createDirectory"],
    });
    if (result.canceled) {
      return null;
    }

    return result.filePaths[0];
  }

  async showOpenDialog(options) {
    const result = await dialog.showOpenDialog(options);
    return result;
  }

  /**
   * 获取目录下的文件
   */
  async getFolderFiles(path) {
    return new Promise((reslove, reject) => {
      fs.readdir(path, (error, data) => {
        if (error) {
          reslove([]);
        } else {
          reslove(data);
        }
      });
    });
  }

  /**
   * 选择文件
   * @param {*} options
   * @returns
   */
  async selectFile(options) {
    const response = {
      code: 0,
      msg: "",
      data: [],
    };
    // const options = {
    //   title: 'Select Images',
    //   filters: [
    //     { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif'] }
    //   ],
    //   properties: ['openFile', 'multiSelections']
    // };

    const result = await dialog.showOpenDialog(options);
    if (result.canceled) {
      (response.code = 1), (response.msg = "取消选择");
    }
    response.data = result.filePaths;
    return response;
  }

  /**
   * 图片链接转base64
   */
  async imgToBase64({ url, addType, compress }) {
    return new Promise((resolve, reject) => {
      const { size } = fs.statSync(url);
      // 低于800k不压缩
      if (!compress || size < 800 * 1024) {
        fs.readFile(url, "base64", (error, data) => {
          if (error) {
            resolve("");
          } else {
            let _data = data;
            if (addType) {
              _data = `data:image/${path
                .extname(url)
                .replace(".", "")};base64,${data}`;
            }
            resolve(_data);
          }
        });
      } else {
        console.log("start compress");
        try {
          const id = uuid.v4()
          compressFork.send({
            url,
            compress,
            id
          })
          compressForkMap.set(id, resolve)

        } catch (e) {
          resolve("");
        }
      }
    });
  }

  async saveByBase64({ data, dest, filename }) {
    // console.log('saveByBase64')
    return new Promise((resolve, reject) => {
      const response = {
        code: 1,
        data: "",
        msg: "",
      };
      // console.log(dest,data)
      const buffer = Buffer.from(
        data.replace("data:image/png;base64,", ""),
        "base64"
      );
      fs.writeFile(dest, buffer, (error, data) => {
        if (error) {
          response.msg = err.message;
          reject(response);
        } else {
          response.code = 0;
          response.data = dest;
          resolve(response);
        }
      });
    });
  }

  async writeFile({ dest, data }) {
    return new Promise((resolve, reject) => {
      checkPath(dest)
      fs.writeFile(dest, data, (error) => {
        if (error) {
          return reject(error.message);
        } else {

          resolve(true);
        }
      });
    });
  }

  async download({ url, dest, delay }) {
    let tid = void 0
    return new Promise(async (resolve) => {
      const res = {
        code: 0,
        data: '',
        msg: ""
      }
      try {
        const response = await axios({
          url,
          method: "GET",
          responseType: "stream",
        });
        tid = setTimeout(() => {

          res.code = 1
          res.msg = '超时'
          resolve(res)
          try {
            writer.close()
            fs.unlink(dest)
          } catch (e) {

          }
        }, delay || 30000);
        checkPath(dest)
        const writer = fs.createWriteStream(dest);
        response.data.pipe(writer);

        writer.on('error', (err) => {
          clearTimeout(tid)
          res.code = 1
          res.msg = err.message
          resolve(res)
        })
        writer.on('finish', (err) => {

          clearTimeout(tid)
          res.data = true
          resolve(res)
        })

      } catch (e) {
        tid && clearTimeout(tid)
        res.code = 1
        res.msg = e.toString()
        resolve(res)
      }
    })
  }

  async pathFn({ action, params }) {
    switch (action) {
      case 'extname': {
        return path.extname(params)
      }
    }
    return ''
  }

  async writeExcel({ path, data, options }) {
    return new Promise((resolve, reject) => {
      const buffer = xlsx.build(data, options);
      fs.writeFile(path, Buffer.from(buffer), (error, data) => {
        if (error) {
          reject(error.message);
        } else {
          resolve(path);
        }
      });
    });
  }

  async resolveExcel({ path }) {
    return xlsx.parse(path);
  }

  async readTXT({ url }) {
    return new Promise((resolve, reject) => {
      resolveTXT(url)
        .then((res) => {
          resolve(res);
        })
        .catch((res) => {
          resolve("");
        });
    });
  }

  async createNewWindow(options) {
    return new Promise((resolve) => {
      const url = options.url;
      options.icon = path.resolve(__dirname, "../../build/icons/icon.ico");
      options.webPreferences = options.webPreferences || {};
      options.webPreferences.partition =
        options.webPreferences.partition || Date.now().toString();
      const win = new BrowserWindow(options);

      win.loadURL(url);
      win.on("close", () => {
        resolve("close");
      });
    });
  }

  async systemProcessList() {
    // console.log('systemProcessList')
    const exec = childProcess.exec;
    return new Promise((resolve, reject) => {
      let cmd = process.platform === "win32" ? "tasklist" : "ps aux";
      exec(cmd, function (err, stdout, stderr) {
        if (err) {
          resolve([]);
          return console.error(err);
        }
        // console.log(stdout,stderr)
        const list = stdout.split("\n").map((line) => {
          let processMessage = line.trim().split(/\s+/);
          return processMessage;
          // let processName = processMessage[0]; //processMessage[0]进程名称 ， processMessage[1]进程id
          // if (processName.toUpperCase().includes('FIDDLER')) {
          //   process.kill(processMessage[1])
          // }
        });
        resolve(list);
      });
    });
  }

  async exists({ path }) {
    return fs.existsSync(path);
  }

  async appInfo() {
    const data = {
      // appPath:electronApp.getAppPath(),
      appPath: process.cwd(),
      appVersion: electronApp.getVersion(),
      userAgent
    };

    return data;
  }

  async deleteFolder({ path }) {
    if (!path || !fs.existsSync(path)) {
      return false;
    }
    deleteFolderHandle(path);
    return true;
  }

  async deleteFile({ url }) {
    return new Promise((resolve) => {
      if (url && fs.existsSync(url)) {
        fs.unlink(url, (error) => {
          if (error) {
            resolve(false);
          } else {
            resolve(true);
          }
        });
      } else {
        resolve(false);
      }
    });
  }

  async isDirectory(url) {
    const stat = fs.statSync(url);
    return stat.isDirectory();
  }

  async makeDir(data) {
    const url = data.path;
    const response = {
      code: 0,
      msg: "",
      data: {},
    };
    return new Promise((resolve) => {
      const parentFolder = path.dirname(url);
      if (!fs.existsSync(parentFolder)) {
        fs.mkdirSync(parentFolder, () => { });
      }
      fs.mkdir(url, (error) => {
        if (error) {
          response.code = 1;
          response.msg = error.message;
        }
        resolve(response);
      });
    });
  }

  async getVideoInfo({ url, timestamps }) {
    const res = {
      code: 0,
      data: {
        info: {},
        imgs: [],
      },
      msg: "",
    };

    const videoInfoRes = await new Promise((resolve) => {
      try {
        ffmpeg.ffprobe(url, function (err, metadata) {
          if (err) {
            console.error("获取视频信息失败：", err);
            resolve({
              success: false,
              msg: err.toString(),
            });
          } else {
            // const { format, streams } = metadata;
            // const { duration, size, width, height } = format;
            resolve({
              success: true,
              data: metadata,
            });
          }
        });
      } catch (e) {
        resolve({
          success: false,
          msg: e.toString(),
        });
      }
    });
    if (videoInfoRes.success) {
      res.data.info = videoInfoRes.data;
    } else {
      res.code = 1;
      res.msg = videoInfoRes.msg;
      return res;
    }
    if (timestamps && timestamps.length) {
      const screenshotsRes = await new Promise((resolve) => {
        try {
          const key = uuid.v4();
          ffmpeg(url)
            .screenshots({
              // count: 1,
              folder: process.cwd() + "/temp",
              filename: `${key}%s.jpeg`,
              timestamps,
            })
            .on("end", function () {
              resolve({
                success: true,
                data: timestamps.map((item, index) => {
                  return process.cwd() + "/temp/" + `${key}${index}.jpeg`;
                }),
              });
            })
            .on("error", function (err) {
              resolve({
                success: false,
                msg: err.toString(),
              });
            });
        } catch (e) {
          resolve({
            success: false,
            msg: e.toString(),
          });
        }
      });
      if (screenshotsRes.success) {
        res.data.imgs = screenshotsRes.data;
      } else {
        res.code = 1;
        res.msg = screenshotsRes.msg;
        return res;
      }
    }
    return res;
  }
  async videoResize({ url, size }) {
    let _size = size || "720x1280";
    let tempPath = process.cwd() + "/temp/";
    if (!fs.existsSync(tempPath)) {
      fs.mkdirSync(tempPath);
    }
    return new Promise((resolve) => {
      const res = {
        success: true,
        msg: "",
        data: "",
      };
      let returnPath = tempPath + uuid.v4() + path.extname(url);
      try {
        ffmpeg(url)
          .size(_size)
          .output(returnPath)
          .on("end", () => {
            // console.log("视频尺寸修改完成");
            res.success = true;
            res.data = returnPath;
            resolve(res);
          })
          .on("error", (err) => {
            console.log(err);
            res.success = false;
            res.msg = err.toString();
            resolve(res);
          })
          .run();
      } catch (e) {
        console.log("e", e);
        res.success = false;
        res.msg = e.toString();
        resolve(res);
      }
    });
  }
}

WindowController.toString = () => "[class WindowController]";
module.exports = WindowController;
