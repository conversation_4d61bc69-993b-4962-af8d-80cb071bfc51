import { Ref, reactive, toRef, watch } from "vue";
import {
  VxeTableEventProps,
  VxeTablePropTypes,
  VxeTableDataRow,
  VxeTableProps,
  VxeTableInstance,
} from "vxe-table";
// import { useAppStore } from "../stores/app";
import { ElMessage, ElMessageBox } from "element-plus";

type TableActions =
  | "check-all"
  | "clear"
  | "reverse"
  | "area-select"
  | "sync-selection";

export function useTable<Row = VxeTableDataRow>(
  /**表格设计稿高度 */
  height: number,
  /**vxe-table表格各项参数 props 事件监听在options.tableEvents */
  tableProps?: VxeTableProps<Row>,
  /** 其他参数*/
  _options?: {
    pagination?: Partial<Omit<Pagination, "total">>;
    tableEvents?: VxeTableEventProps<Row>;
    tableRef?: Ref<VxeTableInstance | undefined>;
    clearWhenDataChange?: boolean;
  }
) {
  const options = {
    clearWhenDataChange: true,
    ..._options,
  };
  const props = reactive(tableProps || {});
  props.height = height;
  props.loading = false;
  props.round = props.round ?? true;
  props.columnConfig = { resizable: true, ...props.columnConfig };
  // props.rowConfig = {
  //   height:30,
  //   ...props.rowConfig
  // }
  // props.headerCellStyle = {
  //   height: '30px',
  //   ...props.headerCellStyle
  // }
  props.border = props.border?? true;
  props.stripe = props.stripe ?? true;
  props.showHeaderOverflow = props.showHeaderOverflow ?? "title";
  props.showOverflow = props.showOverflow ?? 'title';
  props.rowConfig = {
    height: void 0,
    ...props.rowConfig,
  };
  props.customConfig={
    storage:{
      resizable:true
    },
    ...props.customConfig
  }
  props.align = props.align || "center";
  props.checkboxConfig = {
    checkField: "checkField",
    ...props.checkboxConfig,
  };
  props.scrollY = {
    enabled: true,
    scrollToTopOnChange: true,
    gt: 0,
    ...props.scrollY,
  };
  // const appStore = useAppStore();
  if (options.clearWhenDataChange) {
    watch([() => props.loading, () => props.data], (params) => {
      tableState.selection =
        options.tableRef?.value?.getCheckboxRecords() || [];
    });
  }

  const tableState: {
    selectRow?: Row;
    selection: Row[];
  } = reactive({
    selection: [],
  });

  const tableEvents: VxeTableEventProps<Row> = options.tableEvents || {};
  /**表格选项操作 */
  const tableAction = async (action: TableActions) => {
    const tableRef = options.tableRef;
    if (!tableRef || !tableRef.value) {
      return Promise.reject("没有表格实例");
    }
    switch (action) {
      case "check-all": {
        tableRef.value?.setAllCheckboxRow(true);
        break;
      }
      case "clear": {
        tableRef.value?.clearCheckboxRow();
        break;
      }
      case "reverse": {
        const { fullData } = tableRef.value?.getTableData();
        const checkList = fullData || [];
        checkList.forEach((item) => {
          tableRef.value?.toggleCheckboxRow(item);
        });
        break;
      }
      case "area-select": {
        await ElMessageBox.prompt(
          "请输入需要选中的订单(用-隔开),留空表示起始值或末尾值\n(例：20-30 ; -30 ; 20- ; -)",
          "区域选择",
          {
            // inputType: 'textarea',
            showCancelButton: true,
            // customClass: 'limit-input-textarea'
            inputPlaceholder: "请输入范围",
          }
        ).then((res) => {
          const { fullData } = tableRef.value?.getTableData()!;
          const { value } = res;
          let [min, max] = value.split("-").map((item) => Number(item.trim()));
          min = min - 1 < 0 ? 0 : min - 1;
          max = max || fullData.length;
          if (min > max) {
            return ElMessage.warning({
              message: "最小值大于最大值",
              grouping: true,
            });
          }
          tableRef.value?.clearCheckboxRow();
          const list =
            tableRef.value?.getTableData().visibleData.slice(min, max) || [];
          list.forEach((item) => {
            tableRef.value?.setCheckboxRow(item, true);
          });
        });
      }
      case "sync-selection": {
        break;
      }
    }
    tableRef.value.dispatchEvent("checkbox-change", null, null);
  };
  const originOnCheckboxAll = tableEvents.onCheckboxAll;
  tableEvents.onCheckboxAll = (params) => {
    tableState.selection = options.tableRef?.value?.getCheckboxRecords() || [];
    originOnCheckboxAll && originOnCheckboxAll(params);
  };
  const originOnCheckboxChange = tableEvents.onCheckboxChange;
  tableEvents.onCheckboxChange = (params) => {
    tableState.selection = options.tableRef?.value?.getCheckboxRecords() || [];
    originOnCheckboxChange && originOnCheckboxChange(params);
  };
  /** end */

  const originRowClassName = props.rowClassName;
  const rowClassName: VxeTablePropTypes.RowClassName<Row> = (scope) => {
    let className: string[] = [];
    // @ts-ignore
    if (scope.row === tableState.selectRow) {
      className.push("row-current");
    }
    if (originRowClassName) {
      let outClassName: string[] = [];
      if (typeof originRowClassName === "function") {
        const result = originRowClassName(scope) || "";

        if (typeof result === "string") {
          outClassName.push(result);
        } else {
          for (let key in result) {
            if (result[key]) {
              outClassName.push(key);
            }
          }
        }
      } else {
        className.push(originRowClassName);
      }
      className = className.concat(outClassName);
    }
    return className.join(" ");
  };

  const originMenuConfigVisibleMethod = props.menuConfig?.visibleMethod;
  if (props.menuConfig) {
    Reflect.deleteProperty(props.menuConfig!, "visibleMethod");
  }
  const tableMenuConfig: VxeTablePropTypes.MenuConfig<Row> = {
    className: `table-menu ${props.menuConfig?.className || ""}`,
    visibleMethod: (scope) => {
      tableState.selectRow = scope.row;
      const fn = originMenuConfigVisibleMethod;
      return fn ? fn(scope) : true;
    },
    ...(props.menuConfig || {}),
  };

  // props.rowClassName = void 0;
  props.rowClassName = rowClassName;
  props.menuConfig = tableMenuConfig;

  const originCellClick = options.tableEvents?.onCellClick;
  tableEvents.onCellClick = (scope) => {
    tableState.selectRow = scope.row;
    originCellClick && originCellClick(scope);
  };

  const pagination: Pagination = reactive({
    total: 0,
    pageSizes: options.pagination?.pageSizes || [100, 200, 500, 1000],
    limit: options.pagination?.limit || 100,
    page: options.pagination?.page || 1,
  });

  return {
    tableAction,
    pagination,
    tableState,
    tableProps: props,
    loading: toRef(props, "loading"),
    tableList: toRef(props, "data") as Ref<Row[]>,
    tableEvents,
  };
}
