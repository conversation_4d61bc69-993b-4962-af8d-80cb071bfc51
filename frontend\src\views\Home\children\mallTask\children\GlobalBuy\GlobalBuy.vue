<template>
    <div class="global-buy">
        <div class="config">
            <div class="item">
                <div class="label">店铺：</div>
                <el-select v-model="searchState.mallId" placeholder="请选择店铺">
                    <el-option v-for="item in mallStore.tableList" :key="item.mallId" :label="item.mallName"
                        :value="item.mallId"></el-option>
                </el-select>
            </div>
            <div class="item">
                <div class="label">小号位置：</div>
                <el-input-number :controls="false" v-model="start_site" :precision="0" :min="1"></el-input-number>
            </div>
            <div class="item" title="开启后会自动添加选择sku的库存">
                <div class="label">自动添加库存:</div>
                <el-switch v-model="config.auto_add_stock"></el-switch>
            </div>
            <div class="item">
                <div class="label">数量：</div>
                <el-select v-model="config.num_type" style="width: 100px;">
                    <el-option label="固定数量" :value="1"></el-option>
                    <el-option label="范围随机" :value="2"></el-option>
                </el-select>
                <el-input-number v-show="config.num_type == 1" :controls="false" v-model="config.goods_num"
                    :precision="0" :min="1"></el-input-number>
                <el-input-number v-show="config.num_type == 2" :controls="false" v-model="config.goods_num_range.v1"
                    :precision="0" :min="1"></el-input-number>
                <el-input-number v-show="config.num_type == 2" :controls="false" v-model="config.goods_num_range.v2"
                    :precision="0" :min="1"></el-input-number>
            </div>
            <div class="item">
                <div class="label">每单最大销量：</div>
                <el-select v-model="config.order_per_max_count" style="width: 100px;">
                    <!-- <el-option :value="200001"></el-option> -->
                    <el-option label="9505" :value="9505"></el-option>
                    <el-option label="3845" :value="3845"></el-option>
                    <el-option label="1811(1.1)" :value="1811"></el-option>
                    <el-option label="359(5.5)" :value="359"></el-option>
                </el-select>
            </div>
        </div>
        <div class="table-header between-flex">
            <div class="center-flex">
                <el-button @click="tableAction('check-all')">全选</el-button>
                <el-button @click="tableAction('reverse')">反选</el-button>
                <el-button @click="tableAction('area-select')">区域选择</el-button>
                <el-button @click="tableAction('clear')">取消选择</el-button>
                <el-button
                    @click="searchState.is_show_textarea = true; searchState.goods_id_textarea = searchState.goods_id">多ID筛选</el-button>
                <el-input v-model="searchState.goods_id" placeholder="商品ID搜索,逗号隔开"
                    style="margin-left: 12px;margin-right: 12px;width: 110px;"></el-input>

            </div>
            <div class="center-flex">
                <el-button type="primary" @click="getList()" :loading="tableProps.loading">获取商品</el-button>
                <el-button type="success" @click="exec()" :loading="loading.exec">开始任务</el-button>
                <el-button type="warning" @click="sendStop()" :disabled="loading.stop">停止任务</el-button>
            </div>
        </div>
        <vxe-table v-bind="{ ...tableProps, ...tableEvents }" ref="tableRef">
            <VxeColumn type='checkbox' :width="50"></VxeColumn>
            <VxeColumn type='seq' title="序号" :width="50"></VxeColumn>
            <VxeColumn title="商品ID" field="id" :width="130"></VxeColumn>
            <VxeColumn title="商品名称" field="goods_name" :width="150"></VxeColumn>
            <VxeColumn title="SKU" :width="150">
                <template #default='scope'>
                    <el-select v-model="scope.row._skuSelect" @click="scope.row._sku_select_list = scope.row.sku_list">
                        <el-option v-for="item in scope.row._sku_select_list" :label="item.spec || scope.row.goods_name"
                            :value="item.skuId"></el-option>
                    </el-select>
                </template>
            </VxeColumn>
            <VxeColumn title="SKU拼团价格" :width="150">

                <template #default='scope'>
                    {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.groupPrice /
                        100 }}
                </template>
            </VxeColumn>

            <VxeColumn title="商品销量" field="sold_quantity" :width="80" sortable></VxeColumn>
            <VxeColumn title="提示" field="notice"></VxeColumn>
        </vxe-table>
        <paginationVue v-bind="pagination" v-model:page-size="pagination.limit" v-model:current-page="pagination.page"
            :selection="tableState.selection" @current-change="getList()" />
        <Log v-model:list="logList" style="margin-top: 12px">
        </Log>
        <!-- 批量输入商品id 逗号换行隔开 -->
        <el-dialog v-model="searchState.is_show_textarea" title="批量输入商品ID">
            <el-input type="textarea" v-model="searchState.goods_id_textarea" placeholder="用逗号,换行隔开" :rows="10"
                resize="none"></el-input>

            <template #footer>
                <el-button @click="searchState.is_show_textarea = false">取消</el-button>
                <el-button type="primary"
                    @click="searchState.is_show_textarea = false; searchState.goods_id = resolveInputStr(searchState.goods_id_textarea).join(',')">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang='ts'>
import { reactive, ref } from 'vue';
import { useMallStore } from '/@/stores/store';
import { usePersistenceRef } from '/@/hooks/ref';
import { batchRequest, delayPromise, pageStepRequest, pddResErrorMsg, random, resolveInputStr, retry } from '/@/utils/common';
import { useTable } from '/@/hooks/useTable';
import { ElMessage } from 'element-plus';
import { batchEditGoods, getGroupId, storeGoodsList } from '/@/apis/pddStore';
import { VxeTableInstance } from 'vxe-table';
import paginationVue from '/@/components/pagination/pagination.vue';
import type { RequestData } from '.';
import { useSubAccount } from '/@/stores/pageData';
import { addAddress, changeSkuPrice, closeMerchantCoupone, createMerchantGoods, getCouponList, getMerchantCouponV2, quickOrder } from '/@/apis/changeSales';
import { changeSkuPirceList } from '/@/utils/porpery';
import { cloneDeep, } from 'lodash';
import dayjs from 'dayjs'
import { rejectGoodsListApi, storeGoodsListApi } from '/@/apis/store';
import { getRandomItem } from '/@/apis/address';
import { useSetting } from '/@/stores/setting';
const mallStore = useMallStore()
const searchState = reactive({
    goods_id: '',
    goods_id_textarea: '',
    /**批量输入是否显示 */
    is_show_textarea: false,
    mallId: '' as '' | number,
})
const config = reactive({
    num_type: 1,
    goods_num: 1000,
    goods_num_range: {
        v1: 100,
        v2: 1000,
    },
    order_per_max_count: 1811,
    auto_add_stock: true,
})
const start_site = usePersistenceRef(1, 'start_site')
const logList = ref<LogItem[]>([])
const rowStatusMap = ref<Map<number, { msg: string, type?: string }>>(new Map())
const addToLog = (data: Partial<LogItem>, row?: anyObj) => {
    logList.value.push({
        date: Date.now(),
        msg: '',
        ...data,
    })

    if (row) {
        const msg = data.msg || ''
        if (rowStatusMap.value.has(row.id)) {
            rowStatusMap.value.get(row.id)!.msg = msg
            rowStatusMap.value.get(row.id)!.type = data.type
        } else {
            rowStatusMap.value.set(row.id, { msg, type: data.type })
        }
    }
}

const tableRef = ref<VxeTableInstance>()
const tableInstance = useTable(400, { id: 'mallTask-global-table' }, { tableRef, pagination: { limit: 100, pageSizes: [100, 1000, 2000], }, clearWhenDataChange: false })
const { tableProps, tableEvents, pagination, tableState, tableAction } = tableInstance
async function getList() {
    const mall = mallStore.tableList.find(item => item.mallId == searchState.mallId)
    if (!mall) {
        ElMessage.warning('请先选择店铺')
        return
    }
    if (!mallStore.checkAvailable(mall)) {
        ElMessage.warning('店铺不可用')
        return
    }
    const { goods_id } = searchState
    const { limit, page } = pagination
    const times = Math.round(limit / 100)
    tableProps.loading = true
    if (goods_id) {
        pagination.page = 1
    }
    const allList = await pageStepRequest({
        delay: 1000,
        pageStart: (page - 1) * times + 1,
        pageEnd: (page) * times,
        tableInstance,
        isStop: () => !tableProps.loading,
        request: async (page) => {
            const { data: res } = await storeGoodsList({ goods_id_list: goods_id.split(',').filter(item => item), page }, mall!)
            if (!res.success) {
                return Promise.reject()
            }
            const goodsList: anyObj[] = res.result.goods_list
            goodsList.forEach(item => {
                // isOnsale
                item.sku_list = (item.sku_list as anyObj[]).filter(item => item.isOnsale)
                if (!item.sku_list.length) {
                    return
                }
                const seleteItem = (item.sku_list as anyObj[]).sort((a, b) => a.groupPrice - b.groupPrice)[0]
                item._skuSelect = seleteItem.skuId
                item._sku_select_list = [seleteItem]
                // state.tableList.push(item)
                // list.push(item)
            });
            const _total = res.result.total
            pagination.total = _total
            return { list: goodsList, total: _total }
        }
    })
    tableProps.data = allList
    tableProps.loading = false
}

const loading = reactive({
    exec: false,
    stop:false
})
function sendStop(){
    loading.stop = true
    addToLog({ msg: '已发送停止指令', type: 'danger' })
}
async function checkStop(){
    if(loading.stop){
        addToLog({ msg: '检测到停止指令', type: 'warning' })
        return Promise.reject()
    }
}
async function exec() {
    const { mallId } = searchState
    const mall = mallStore.tableList.find(item => item.mallId == mallId)
    if (!mall) {
        ElMessage.warning({
            message: '请选择店铺',
            grouping: true
        })
        return
    }
    if (!mallStore.checkAvailable(mall)) {
        return ElMessage.warning({
            message: '当前店铺已过期，请重新登录',
            grouping: true
        })
    }
    const allList = [...tableState.selection]
    if (!allList.length) {
        ElMessage.warning({
            message: '请选择商品',
            grouping: true
        })
        return
    }
    loading.exec = true
    loading.stop = false
    await batchRequest(allList, {
        batch: 1,
        isStop:()=>!loading.exec && loading.stop,
        async request([row]) {
            const num = resolveTargetNum()
            const requestData: RequestData = {
                row,
                mall,
                num,
                ...resolveSingleBuyCount(num),
                price_info: {
                    goods_id: row.id,
                    market_price: row.market_price,
                    market_price_in_yuan: (Number(row.market_price) / 100).toFixed(2),
                    sku_prices: (row.sku_list as anyObj[]).filter(item => item.isOnsale).map(sku => {
                        return {
                            sku_id: sku.skuId,
                            single_price: sku.normalPrice,
                            single_price_in_yuan: (Number(sku.normalPrice) / 100).toFixed(2),
                            multi_price: sku.groupPrice,
                            multi_price_in_yuan: (Number(sku.groupPrice) / 100).toFixed(2)
                        }
                    })
                },
                accounts: []
            }
            resolveAccount(requestData)
            if (!requestData.accounts.length) {
                addToLog({ msg: '没有分配到小号', type: 'warning' }, row)
                return
            }
            await checkStop()
            if (config.auto_add_stock) {
                addToLog({ msg: '自动添加库存', type: 'success' }, row)
                const res = await batchEditGoods(mall, {
                    edit_list: [{
                        goods_id: row.id,
                        sku_id: row._skuSelect,
                        quantity_delta: requestData.num
                    }]
                })
                if(res.success){
                    addToLog({ msg: '添加库存成功', type: 'success' }, row)
                    addToLog({ msg: '等待添加库存同步数据', type: 'info' }, row)
                    await delayPromise(10 *　1000)
                }else{
                    addToLog({ msg: '添加库存失败' + pddResErrorMsg(res) + `(添加失败不暂停，继续执行)`, type: 'danger' }, row)
                }
            }
            await checkStop()
            await getGorupId(requestData)
            await checkStop()
            {
                const res = await skuPriceChange(requestData, 'toHighPrice')
                if (!res.success) {
                    return
                }

            }
            try {
                await createCoupon(requestData)
            } catch {
                skuPriceChange(requestData, 'recover')
                return
            }

            {
                const res = await skuPriceChange(requestData, 'toLowPrice')
                if (!res.success) {
                    skuPriceChange(requestData, 'recover')
                    closeCoupon(requestData)
                    return
                }
            }
            await createOrder(requestData)
            closeCoupon(requestData)
            skuPriceChange(requestData, 'recover')
        }
    })
    loading.exec = false
    loading.stop = false
    addToLog({ msg: '执行完毕', type: 'primary' })
}
function resolveTargetNum() {
    const { goods_num, goods_num_range: { v1, v2 }, num_type } = config
    if (num_type === 2) {
        return random(v1, v2)
    }
    return goods_num
}
function resolveSingleBuyCount(target: number,) {
    let obj = {
        num_per_order: 0,
        price_per_order: 0,
        max_num_per_order: 9505
    }
    const order_per_max_count = Number(config.order_per_max_count)

    if (order_per_max_count == 359) {
        obj.max_num_per_order = 359
        obj.price_per_order = 5.5
    } else if (order_per_max_count === 9505) {
        obj.price_per_order = 0.21
        obj.max_num_per_order = 9505
    } else if (order_per_max_count === 3845) {
        obj.price_per_order = 0.51
        obj.max_num_per_order = 3845
    } else if (order_per_max_count === 200001) {
        obj.price_per_order = 0.01
        obj.max_num_per_order = 200001
    } else if (order_per_max_count === 1811) {
        obj.price_per_order = 1.1
        obj.max_num_per_order = 1811
    }
    if (target <= 100) {
        obj.num_per_order = 101
    }
    else if (target >= obj.max_num_per_order) {
        obj.num_per_order = obj.max_num_per_order
    } else {
        obj.num_per_order = target
    }
    return obj
}
const subAccount = useSubAccount()
function resolveAccount(requestData: RequestData) {
    const target = requestData.num
    let singleMax = requestData.max_num_per_order
    const accountCount = Math.ceil(target / singleMax)
    let accounts: RequestData['accounts'] = []
    addToLog({
        msg: '需要可用小号次数：' + accountCount
    })
    for (let i = 0; i < accountCount; i++) {
        const res = subAccount.getAccount(start_site.value - 1)
        if (res.status) {
            accounts.push(res.data)
            const ind = (res.ind + 2) % subAccount.list.length
            start_site.value = ind === 0 ? subAccount.list.length : ind
        } else {
            addToLog({
                type: 'danger',
                msg: '获取小号出错' + res.msg
            }, requestData.row)
            break
        }
    }

    if (accounts.length < accountCount) {
        addToLog({ type: 'danger', msg: '没有获取到足够小号' })
        accounts.forEach(item => {
            subAccount.recover(String(item))
        })
        accounts = []
    }

    requestData.accounts = accounts
}
async function skuPriceChange(reqData: RequestData, type: "toHighPrice" | "toLowPrice" | "recover") {
    const res = {
        success: false,
        msg: "初始化",
        needCheckPrice: type !== 'recover',
    }
    let p: Promise<any> | undefined = void 0
    switch (type) {
        case 'toHighPrice': {
            const minPrice = Math.min(...reqData.price_info.sku_prices.map(item => item.multi_price))
            const result = minPrice > 4000 * 100
            if (result) {
                addToLog({ type: 'danger', msg: '当前商品sku全部高于4000，不需要提高价格' }, reqData.row)
                res.needCheckPrice = false
                break
            }
            let data = {
                goods_id: reqData.price_info.goods_id,
                "market_price_in_yuan": "5002.00",
                "market_price": 500200,
                sku_prices: reqData.price_info.sku_prices.map(item => {
                    const obj = {
                        sku_id: item.sku_id,
                        "multi_price_in_yuan": '4000',
                        "single_price_in_yuan": "5001.00",
                        "multi_price": 400000,
                        "single_price": 500100
                    }
                    return obj
                })
            }
            if (minPrice < 200 * 100) {
                const data2 = cloneDeep(data)
                data2.market_price = 211 * 100
                data2.market_price_in_yuan = '211.00'
                data2.sku_prices.forEach(item => {
                    item.multi_price = 200 * 100
                    item.multi_price_in_yuan = '200.00'
                    item.single_price = 210 * 100
                    item.single_price_in_yuan = '210.00'
                })
                await changeSkuPrice(reqData.mall, data2)
                await delayPromise(15000)
            }
            p = await changeSkuPrice(reqData.mall, data)
            break
        }
        case 'toLowPrice': {
            const priceItem = changeSkuPirceList.find(item => item.multi_price === Math.round(reqData.price_per_order * 100))
            if (!priceItem) {
                res.success = false
                res.msg = `--修改价格失败，没有找到修改价格参数`
            } else {
                const { market_price_in_yuan, market_price, multi_price, multi_price_in_yuan, single_price, single_price_in_yuan } = priceItem
                const data = {
                    goods_id: reqData.price_info.goods_id,
                    market_price_in_yuan,
                    market_price,
                    sku_prices: reqData.price_info.sku_prices.map(item => {
                        const obj = {
                            sku_id: item.sku_id,
                            multi_price_in_yuan,
                            single_price_in_yuan,
                            multi_price,
                            single_price
                        }
                        return obj
                    })
                }
                p = await changeSkuPrice(reqData.mall, data)
            }

            break
        }
        case 'recover': {
            p = await changeSkuPrice(reqData.mall, cloneDeep(reqData.price_info))
            break
        }
    }
    if (p) {
        const reqRes = await p
        if (reqRes.success) {
            res.success = true
        } else {
            res.success = false
            res.msg = '提交修改失败' + pddResErrorMsg(reqRes)
        }
    }
    addToLog({
        msg: res.success ? '提交修改价格成功' : res.msg,
        type: res.success ? 'success' : 'danger'
    }, reqData.row)
    if (res.success && res.needCheckPrice) {
        const checkRes = await checkPrice(reqData, type)
        if (checkRes) {
            addToLog({ msg: '价格检测通过', type: 'success' }, reqData.row)
        } else {
            addToLog({ msg: '价格检测未通过', type: 'danger' }, reqData.row)
            res.success = false
        }
    }
    return res
}

async function checkPrice(...args: Parameters<typeof skuPriceChange>) {
    const [reqData, type] = args
    const { row, mall } = reqData
    const goods_id = reqData.price_info.goods_id
    addToLog({ msg: '检查商品sku价格', type: 'info' }, row)
    await delayPromise(2000)
    return retry<boolean>(async (index) => {
        addToLog({ msg: `检查商品价格第${index}次`, type: 'info' })

        const res = await storeGoodsListApi(mall, { goods_id_list: [String(goods_id)], pre_sale_type: 4 })

        if (!res.success) {
            addToLog({ msg: `获取商品信息失败${res.error_msg || ''}`, type: 'danger' })
            return Promise.reject(false)
        }
        if (res && res.result && res.result.total) {
            const tempGoodsInfo = res.result.goods_list[0]
            if (!tempGoodsInfo.sku_list) {
                return Promise.reject(false)
            }
            const groupPrice: number = tempGoodsInfo.sku_list?.[0]?.groupPrice
            switch (type) {
                case 'toHighPrice': {
                    if (groupPrice >= 4000 * 100) {
                        return true
                    }
                    break
                }
                case 'recover': {
                    if (groupPrice >= Math.min(...reqData.price_info.sku_prices.map(item => item.multi_price))) {
                        return true
                    }
                    break
                }
                case 'toLowPrice': {
                    if (groupPrice <= 550) {
                        return true
                    }
                    break
                }
            }

        }
        if (index % 3 === 0) {
            //检测是否被驳回
            const rejectGoodsRes = await rejectGoodsListApi(mall, [String(goods_id)])
            if (rejectGoodsRes && rejectGoodsRes.success && rejectGoodsRes.result?.total) {
                const rejectGoodsList = rejectGoodsRes.result.list as anyObj[]
                addToLog({ msg: '检测到临时商品被驳回：' + rejectGoodsList[0]?.reject_comment, type: 'danger' }, row)
                return false
            }
        }
        return Promise.reject(false)
    }, 15, 3000)
        .then((res) => {
            if (res) {
                addToLog({ msg: '商品价格已更改', type: 'success' }, row)
                return true
            }
            return Promise.reject(false)
        })
        .catch(() => false)
}
async function createCoupon(requestData: RequestData) {
    const { mall, row, num_per_order, price_per_order } = requestData
    const batch_desc = '商品立减券' + Date.now()
    let _discount = num_per_order * price_per_order
    if (Number.isInteger(_discount)) {
        requestData.num_per_order++
    }
    _discount = requestData.num_per_order * price_per_order
    let discount = Math.floor(_discount) * 100
    if (discount > 200000) {
        discount = 200000
    }
    const goods_id = requestData.price_info.goods_id
    const reqData = [
        {
            goods_id,
            batch_desc,
            batch_start_time: dayjs().startOf('day').valueOf(),
            batch_end_time: dayjs().endOf('day').valueOf(),
            discount,
            init_quantity: 100000,
            user_limit: 10
        }
    ]
    const res = await createMerchantGoods(mall, reqData)
    //   console.log('createCoupon', res, reqData)

    if (!res.success) {
        const msg = res.error_msg || res.errorMsg || ''
        addToLog({ msg: `创建优惠券失败:${msg}`, type: 'danger' }, row)
        return Promise.reject()
    } else if (res.result.has_error_msg) {
        let failedFlag = false
        res.result.error_msg_list.forEach((item: anyObj) => {
            addToLog({ msg: `创建优惠券失败:${item.error_msg}`, type: 'danger' }, row)
            failedFlag = true
        })
        if (failedFlag) {
            return Promise.reject()
        }
    } else {
        addToLog({ msg: '提交创建优惠券成功', type: 'success' }, row)
    }
    addToLog({ msg: '准备获取优惠券信息', type: 'info' })
    await delayPromise(5 * 1000)
    await retry(async (index) => {
        addToLog({ msg: `获取优惠券列表第${index}次--` })
        const listRes = await getCouponList(mall, { goods_list: [String(goods_id || '')], batch_status: 1 })
        const list = listRes.result.data_list as anyObj[]
        const item = list.find(item => item.batch_desc === batch_desc)
        if (item) {
            requestData.coupon_info = {
                coupon_code: item.batch_sn,
                coupon_batch_id: item.batch_id
            }
            addToLog({ msg: '成功获取到优惠券码' + requestData.coupon_info?.coupon_code, type: 'success' }, row)
            return true
        }
        return Promise.reject(false)
    }, 30, 10 * 1000)
        .catch(() => {
            addToLog({ msg: '没有获取到优惠券码', type: 'danger' }, row)
            return Promise.reject()
        })
}
async function closeCoupon(requestData: RequestData) {
    const { mall, coupon_info: { coupon_batch_id } = {} } = requestData
    if (mall && coupon_batch_id) {
        return closeMerchantCoupone(mall, { batch_id: coupon_batch_id })
            .then(res => {
                if (res.success) {
                    addToLog({
                        msg: '成功关闭优惠券领取',
                        type: 'success'
                    })
                } else {
                    addToLog({
                        msg: `关闭优惠券领取失败${res.error_msg || res.errorMsg || '-'}`,
                        type: 'danger'
                    })
                }
            })
    }
}
async function getGorupId(requestData: RequestData) {
    const { mall, row, price_info: { sku_prices } } = requestData
    return getGroupId({ goods_id: row.id, sku_id: sku_prices.map(item => item.sku_id) }, void 0, mall)
        .then(res => {
            addToLog({
                msg: `成功获取拼团id:${res.data}`,
                type: 'success'
            }, row)
            requestData.group_id = res.data
        })
        .catch((res) => {
            addToLog({
                msg: `获取拼团id失败${res.msg}`,
                type: 'danger'
            }, row)
            return Promise.reject()
        })
}
async function createOrder(requestData: RequestData) {
    const { mall, row, group_id, num_per_order, accounts = [], coupon_info } = requestData
    if (!accounts.length) {
        closeCoupon(requestData)
        return Promise.reject({ msg: '没有小号' })
    }
    if (!group_id) {
        closeCoupon(requestData)
        return Promise.reject({ msg: '没有拼团id' })
    }
    const accountGroup = new Map<string, { account: string | number, count: number }>()
    accounts.forEach(item => {
        const a = String(item)
        if (!accountGroup.has(a)) {
            accountGroup.set(a, { account: item, count: 1 })
        } else {
            accountGroup.get(a)!.count++
        }
    })
    await checkStop()
    await skuPriceChange(requestData, 'toLowPrice')
    const skuList = row.sku_list as anyObj[]
    const sku = skuList.find(item => item.sku_id == row._skuSelect) || skuList[0]
    await batchRequest([...accountGroup.values()], {
        batch: 25,
        request: async (batchList) => {
            await Promise.allSettled(batchList.map(async item => {
                const list: string[] = new Array(item.count).fill(item.account)
                await batchRequest(list, {
                    batch: 1,
                    request: async (list) => {
                        const item = list[0]
                        const addressData = await resolveAddress(item, requestData)
                        const createOrderRequestData: any = {
                            account: item,
                            sku: sku.skuId,
                            sku_spec: sku.spec,
                            shop_id: mall.mallId,
                            shop_name: mall.mallName,
                            goods_id: row.id,
                            num: num_per_order,
                            // num: 50001,
                            mode: 'open_group',
                            // activity_id: coupon_code,
                            goods_name: row.goods_name,
                            group_id,
                            type: 'globalBuy',
                            use_coupon: true,
                            ...addressData,
                        }
                        await retry(async () => {
                            return getMerchantCouponV2({ coupon_code: coupon_info!.coupon_code, account: item, }, { showErrorMsg: false })
                        }, 5, 2000)
                            .then(res => {
                                const promotion_identity_vo = res.data && res.data.promotion_identity_vo

                                if (!promotion_identity_vo) {
                                    return Promise.reject({ msg: 'promotion_identity_vo不存在' })
                                }
                                const { promotion_id, promotion_type, extension } = promotion_identity_vo
                                createOrderRequestData.single_promotion_list = [
                                    {
                                        // ...promotion_identity_vo,
                                        promotion_id,
                                        promotion_type,
                                        sku_ids: [row._skuSelect],
                                        mall_id: mall.mallId
                                    }
                                ]

                                addToLog({ msg: `${item}领取优惠券成功`, type: 'success' }, row)
                            })
                            .catch(res => {
                                console.log(res, 'getMerchantCouponV2 -error')
                                addToLog({ msg: `${item}领取优惠券失败：${res.msg}`, type: 'danger' }, row)
                                // closeMerchantCoupon(requestData)
                                return Promise.reject()
                            });
                        await retry(async () => {
                            return quickOrder(createOrderRequestData, { showErrorMsg: false })
                                .then(res => {
                                    addToLog({
                                        msg: `${createOrderRequestData.goods_name}:下单成功。订单号:${res.data.order_sn}`,
                                        type: 'success'
                                    })
                                    // autoApply(res.data)
                                })
                        }, 3)
                            .catch(res => {
                                addToLog({
                                    msg: `${createOrderRequestData.goods_name}:下单失败:${res.msg}`,
                                    type: 'danger'
                                })
                            })
                    }
                })
            }))
        }
    })
}
async function resolveAddress(account: string, requestData: RequestData): Promise<anyObj> {
    const settingStore = useSetting()
    const {
        nameCode_active,
        nameCode_position,
        nameCode_str,
        addr_active,
        addr_position,
        addr_str,
        filterStr,
        type,
        appoint_address,
    } = settingStore.address;
    let addressData: anyObj = {
        appoint_address: appoint_address || void 0,
    }

    if (type == 'diy') {
        const result = await getRandomItem();
        Reflect.deleteProperty(result, "id");
        addressData = result;
    } else {
        addressData = {
            ...addressData,
            filter_address: filterStr.replace("，", ","),
            address_cipher: addr_active ? addr_str : void 0,
            address_site: addr_active ? addr_position : void 0,
            name_cipher: nameCode_active ? nameCode_str : void 0,
            name_site: nameCode_active ? nameCode_position : void 0,
        }
    }
    return addAddress({ account: account, ...addressData }, { showErrorMsg: false })
        .then(res => {
            return res.data
        })
        .catch(res => {
            // console.log('添加地址返回值-error：', res)

            useSubAccount().recover(account)
            addToLog({
                msg: '添加地址出错:' + res.msg,
                type: 'danger'
            }, requestData.row)
            return Promise.reject()
        })
}

</script>

<style lang="scss" scoped>
.config {
    border: var(--el-border);
    padding: 12px;
    border-radius: 5px;
    background-color: #fff;
    display: flex;
    flex-wrap: wrap;

    .item {
        display: flex;
        align-items: center;
        padding: 6px 12px;

        .label {
            margin-right: 6px;
            font-size: 14px;
        }
    }
}

.table-header {
    border-top: var(--el-border);
    border-bottom: var(--el-border);
    padding: 12px 0;
}
</style>