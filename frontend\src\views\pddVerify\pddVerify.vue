<template>
    <div>
        <div class="container" v-if="appStore.pddVerify.type === 22"
            v-loading="loading.submit || appStore.pddVerify.initing">
            <p class="tips">滑动验证</p>
            <div class="img-container">
                <img :src="appStore.pddVerify.imgs[0]" alt="" class="bg">
                <img ref="sliderImg" :src="appStore.pddVerify.imgs[1]" alt="" class="slider"
                    :style="{ left: appStore.pddVerify.slider + 'px' }">
            </div>
            <el-slider v-model="appStore.pddVerify.slider" :max="273"></el-slider>
            <p><el-button type="primary" @click="submit()">提交</el-button></p>
        </div>
        <div class="container type-22" v-else-if="appStore.pddVerify.type === 11"
            v-loading="loading.submit || appStore.pddVerify.initing">
            <p class="tips">点击验证</p>
            <p>{{ appStore.pddVerify.tips }} ; (需要点击{{ appStore.pddVerify.pointCount }}个点)</p>
            <div class="img-container" @click="containerClick">
                <img :src="appStore.pddVerify.imgs[0]" alt="" class="bg">
                <div class="click-point" v-for="item, index in appStore.pddVerify.clickPoint"
                    :style="{ left: item.x + 'px', top: item.y + 'px' }">{{ index + 1 }}</div>
            </div>
            <p style="margin-top: 10px;">
                <el-button type="primary" @click="appStore.pddVerify.clickPoint = []">重选</el-button>
                <el-button type="primary" @click="appStore.pddVerify.clickPoint.pop()">删除最后一个</el-button>
            </p>
        </div>
    </div>
</template>
<script lang='ts' setup>
import { ipc } from '/@/apis/config';
import { useAppStore } from '/@/stores/app';
import { onMounted, reactive, ref } from 'vue';
import { pdd_user_verify } from '/@/apis/store';
import { ElMessage } from 'element-plus';

const appStore = useAppStore()
const loading = reactive({
    submit: false
})
const sliderImg = ref<HTMLImageElement>()

const containerClick = (e: MouseEvent) => {
    const { offsetX, offsetY } = e
    appStore.pddVerify.clickPoint.push({ x: Math.round(offsetX ), y: Math.round(offsetY) })
    const { pointCount, clickPoint } = appStore.pddVerify
    if (pointCount && pointCount === clickPoint.length) {
        submit()
    }
}
const submit = () => {
    loading.submit = true
    let verify_code = ''
    const { clickPoint, slider, type, captcha_collect, verify_auth_token, cb } = appStore.pddVerify
    if (type === 22) {
        const width = (sliderImg.value?.width ? sliderImg.value?.width / 2 : 0) || 24.35
        verify_code = (slider + width).toFixed(2)
    } else if (type === 11) {
        verify_code = JSON.stringify(clickPoint)
    }
    pdd_user_verify({
        verify_auth_token,
        captcha_collect,
        verify_code
    })
        .then(res => {
            console.log('suucess', res)
            const { result, leftover } = res
            if (result) {
                appStore.pddVerify.show = false
                ElMessage.success('验证通过')
                return cb && cb(true)
            }
            if (!leftover) {
                appStore.pddVerify.show = false
                ElMessage.error('验证失败')
                return cb && cb(false)
            }
            appStore.openPddVerify({
                verify_auth_token,
                cb
            })
            ElMessage.warning('验证失败,请重试,还剩' + leftover + '次')
        })
        .catch((res) => {
            cb && cb(false)
        })
        .finally(() => {
            loading.submit = false
        })
}

ipc.invoke('controller.user.get_verify_auth_token')
    .then(res => {
        appStore.openPddVerify({
            verify_auth_token: res, cb: (result) => {
                ipc.invoke('controller.user.verify_auth_token_result', { result })
            }
        })
    })
onMounted(() => {
    document.body.classList.add("pddVerify");
});
</script>
<style lang='scss' rel="stylesheet/scsss" scoped>
.container {
    min-height: 600px;
    display: flex;
    padding-top: 100px;
    box-sizing: border-box;
    align-items: center;
    flex-direction: column;

    .tips {
        font-size: 18px;
        font-weight: bolder;
        margin-bottom: 5px;
    }

    .el-slider {
        width: 272px;
    }

    .img-container {
        width: 272px;
        height: 130px;
        border: var(--el-border);
        overflow: hidden;
        position: relative;

        img {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;

            &.bg {
                width: 100%;
            }
        }
    }

    &.type-22 {
        .img-container {
            // max-width: ;
            height: 136px;

            .click-point {
                width: 20px;
                height: 20px;
                border-radius: 10px;
                transform: translate(-10px, -10px);
                position: absolute;
                background-color: lightcoral;
                text-align: center;
                color: #fff;
                font-size: 14px;
                font-weight: bolder;
                line-height: 20px;
                cursor: pointer;
            }
        }
    }
}
</style>

