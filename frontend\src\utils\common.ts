import { ElMessage, MessageParamsWithType } from "element-plus";
import { useAppStore } from "../stores/app";
import { useUserStore } from "../stores/user";
import CryptoJS from "crypto-js";
import { useTable } from "../hooks/useTable";
export function format_date(value: number) {
  return value >= 10 ? value : "0" + value;
}
export function dateFormat(
  time: number,
  option: Partial<{
    year: boolean;
    month: boolean;
    day: boolean;
    hours: boolean;
    min: boolean;
    seconds: boolean;
  }> = {}
): string {
  if (!!!Number(time)) {
    return "-";
  }

  option = Object.assign(
    {
      year: true,
      month: true,
      day: true,
      hours: true,
      min: true,
      seconds: true,
    },
    option
  );
  if (time.toString().length <= 10) {
    time *= 1000;
  }
  let d = new Date(time);
  let year = d.getFullYear();
  let month = d.getMonth() + 1;
  let day = d.getDate();
  let hours = d.getHours();
  let min = d.getMinutes();
  let seconds = d.getSeconds();
  const frist: Array<string | number> = [];
  const second: Array<string | number> = [];
  option.year && frist.push(year);
  option.month && frist.push(format_date(month));
  option.day && frist.push(format_date(day));

  option.hours && second.push(format_date(hours));
  option.min && second.push(format_date(min));
  option.seconds && second.push(format_date(seconds));
  return [frist.join("-"), second.join(":")].join(" ");
}

/**
 * 复制
 * @param str
 */
export function copyStr(
  str: string,
  config: MessageParamsWithType = { message: "复制成功", grouping: true }
) {
  str = str || "";
  try {
    const clibord = navigator.clipboard;
    if (clibord) {
      clibord.writeText(str).then(() => {
        ElMessage.success(config);
      });
    } else {
      const input = document.createElement("input");
      input.value = str;
      document.body.appendChild(input);
      input.select();
      document.execCommand("Copy");
      ElMessage.success(config);
      document.removeChild(input);
      // console.log("inputNode");
    }
  } catch (e) {
    console.log("[copyStr-error]", e);
  }
}

export function paste() {
  const clibord = navigator.clipboard;
  return clibord.readText().then((res) => {
    if (res) {
      return res;
    } else {
      return Promise.reject();
    }
  });
}

export function pddGoodsDetailUrl(goods_id: string | number) {
  return `https://mobile.yangkeduo.com/goods.html?goods_id=${goods_id}`;
}


export function clientUrl(
  route: string,
  parmas: anyObj = {},
  withToken = true
) {
  const userInfo = useUserStore();
  const appStore = useAppStore();
  // let host: string = 'http://localhost:8888/' || appStore.systemValue?.web_url || "";
  let host: string = appStore.systemValue?.web_url || "";
  host = host.endsWith("/") ? host : host + "/";
  let url = `${host}#${route}?`;
  if (withToken) {
    url += `token=${userInfo.token}&`;
  }
  Object.keys(parmas).forEach((key) => {
    url += `${key}=${parmas[key]}&`;
  });
  // console.log(url)
  return url;
}

export function urlWidthToken(url: string) {
  const userInfo = useUserStore();
  if (!url.endsWith("?")) {
    url += "?";
  }
  return `${url}token=${userInfo.token}`;
  // return 'http://localhost:8888/#/Home/myextension?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HxRuyZGEnHGppr8wX_icD9H_kPMq-Lski-vEBBAMktA'
  // return 'http://localhost:8888/#/Home/myextension?token=' + userInfo.token
}

export function checkFileType(url: string) {
  const videoReg = /\.(mp4|avi|wmv|mpg|mpeg|mov|rm|ram|swf|flv)$/i;
  const audioReg =
    /\.(opus|flac|webm|weba|wav|ogg|m4a|mp3|oga|mid|amr|aiff|wma|au|aac)$/i;
  const imgReg =
    /\.(xbm|tif|pjp|svgz|jpg|jpeg|ico|tiff|gif|svg|jfif|webp|png|bmp|pjpeg|avif)$/i;
  const txtReg = /\.(txt|doc)$/;
  if (videoReg.test(url)) {
    return "video";
  } else if (audioReg.test(url)) {
    return "audio";
  } else if (imgReg.test(url)) {
    return "image";
  } else if (txtReg.test(url)) {
    return "txt";
  } else {
    return "unknow";
  }
}
export function returnFileFolder(path: string) {
  const result = path.match(/^(.*[\\|/]).*\..*$/);
  return result ? result[1] : path;
}

export function random(_min: number, _max: number) {
  const min = Math.min(_min,_max)
  const max = Math.max(_min,_max)
  if(min === max){
    return min;
  }
  let result = Math.floor(Math.random() * (max - min) + min);
  return result;
}

export function randomA2Z() {
  /**a-z所有字母数组 */
  const a2z = [
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
  ];

  /**随机返回一个字符 */
  return a2z[random(0, 25)];
}

export function getLessTime(time: number) {
  let _lessTime = time;
  const d = Math.floor(time / (1000 * 3600 * 24));
  _lessTime = time % (1000 * 3600 * 24);
  const h = Math.floor(_lessTime / (1000 * 3600));
  return (d ? `${d}天` : "") + `${h}小时`;
}

export function showSystemImg(url: string) {
  const path = "img:///" + url;
  return path;
}
export function safeFileUrl(url: string) {
  if (url.startsWith("http")) {
    return url
  }
  return "img:///" + url;
}

/**延时 */
export function delayPromise(delay = 1000) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(delay);
    }, delay);
  });
}


export function decrypt(str: string) {
  const key = CryptoJS.enc.Utf8.parse("1672369827627767");
  // const iv = CryptoJS.enc.Utf8.parse("3zyJFPEzh5rUeUNi");
  const iv = CryptoJS.enc.Utf8.parse("2688677969716887");
  const d = CryptoJS.AES.decrypt(str, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return JSON.parse(d.toString(CryptoJS.enc.Utf8));
}

export function retry<T = any>(
  fn: (index: number) => Promise<T>,
  max = 1,
  delay = 0
): Promise<T> {
  return new Promise((resolve, reject) => {
    let current = 0;
    const fn2 = () => {
      current++;
      fn(current)
        .then((res) => {
          resolve(res);
        })
        .catch(async (res) => {
          if (current < max) {
            if (delay) {
              await delayPromise(delay);
            }
            fn2();
          } else {
            reject(res);
          }
        });
    };
    fn2();
  });
}
// export function batchRequest<T = any, R = any>(
//   _list: Array<T>,
//   options: {
//     /**一批多少个 */
//     batch?: number;
//     /**请求方法 */
//     request: (
//       list: Array<T>,
//       index: number,
//       max: { listMax: number; reqListMax: number }
//     ) => Promise<R>;
//     isStop?: () => boolean;
//   }
// ) {
//   const { batch = 10, request, isStop } = options;
//   const list = [..._list];
//   return new Promise((resolve) => {
//     const reqList: T[][] = [];
//     let count = 0;
//     while (list.length) {
//       reqList.push(list.splice(0, batch));
//     }
//     const reqListMax = reqList.length;
//     const fn = () => {
//       const item = reqList.shift();
//       if (!item) {
//         resolve(true);
//         return;
//       }
//       count++;
//       request(item, count, { listMax: _list.length, reqListMax }).finally(
//         () => {
//           if (isStop && isStop()) {
//             resolve(true);
//           } else {
//             fn();
//           }
//         }
//       );
//     };
//     fn();
//   });
// }
export function batchRequest<T = any, R = any>(
  _list: Array<T>,

  options: {
    /**一批多少个 */
    batch?: number;
    /**请求方法 */
    request: (
      list: Array<T>,
      index: number,
      max: { listMax: number; reqListMax: number }
    ) => Promise<R>;
    isStop?: () => boolean;
    delay?: number
    onRequestFinished?: (data: { current: number, max: number, }) => any,
    onStop?: (remaingList: T[]) => any,
    pool?: number
  }
) {
  const { batch = 10, request, isStop, delay, onStop, pool } = options;
  const list = [..._list];
  return new Promise((resolve) => {
    const reqList: T[][] = [];
    let count = 0;
    while (list.length) {
      reqList.push(list.splice(0, batch));
    }
    const reqListMax = reqList.length;
    if (!pool || pool <= 0) {
      const fn = () => {
        const item = reqList.shift();
        if (!item) {
          resolve(true);
          return;
        }
        count++;

        request(item, count, { listMax: _list.length, reqListMax }).finally(
          async () => {
            if (options.onRequestFinished) {
              options.onRequestFinished({ current: count, max: reqListMax })
            }
            if (!reqList.length) {
              resolve(true);
              return
            }
            if (isStop && isStop()) {
              onStop && onStop(reqList.flat(1))
              resolve(true);
              return
            }
            if (delay) {
              await delayPromise(delay)
              if (isStop && isStop()) {
                onStop && onStop(reqList.flat(1))
                resolve(true);
                return
              }
            }
            fn();

          }
        );
      };
      fn();
    } else {
      let requestingCount = 0
      let count = 1
      const finish = () => {
        if (requestingCount <= 0) {
          resolve(true)
        }
      }
      const fn = async () => {
        const item = reqList.shift();
        if (item) {
          requestingCount++
          request(item, count++, { listMax: _list.length, reqListMax })
            .finally(async () => {
              requestingCount--
              if (options.onRequestFinished) {
                options.onRequestFinished({ current: count, max: reqListMax })
              }
              if (!reqList.length) {
                finish()
                return
              }
              if (isStop && isStop()) {
                onStop && onStop(reqList.flat(1))
                finish()
                return
              }
              if (delay) {
                await delayPromise(delay)
                if (isStop && isStop()) {
                  onStop && onStop(reqList.flat(1))
                  finish()
                  return
                }
              }
              fn();
            });
        }
      }
      for (let index = 0; index < pool; index++) {
        fn()
      }

    }
  })

}


export function uuid() {
  var temp_url = URL.createObjectURL(new Blob());
  var uuid = temp_url.toString(); // blob:https://xxx.com/b250d159-e1b6-4a87-9002-885d90033be3
  URL.revokeObjectURL(temp_url);
  return uuid.substring(uuid.lastIndexOf("/") + 1);
}
export function pddResErrorMsg(res: any, type?: 'yx') {
  let msg = ''
  if (res && !Array.isArray(res) && typeof res === 'object') {
    msg = res.error_msg || res.errorMsg || ''
  }
  return msg
}

export function randomIp() {
  const ip_3 = random(0, 256);
  const ip_4 = random(0, 256);
  const ipall = [
    [[58, 14], [58, 25]],
    [[58, 30], [58, 63]],
    [[58, 66], [58, 67]],
    [[60, 200], [60, 204]],
    [[60, 160], [60, 191]],
    [[60, 208], [60, 223]],
    [[117, 48], [117, 51]],
    [[117, 57], [117, 57]],
    [[121, 8], [121, 29]],
    [[121, 192], [121, 199]],
    [[123, 144], [123, 149]],
    [[124, 112], [124, 119]],
    [[125, 64], [125, 98]],
    [[222, 128], [222, 143]],
    [[222, 160], [222, 163]],
    [[220, 248], [220, 252]],
    [[211, 163], [211, 163]],
    [[210, 21], [210, 22]],
    [[125, 32], [125, 47]]
  ];
  let ip_2: number = 168
  const ip_p = random(0, ipall.length);
  const ip_1 = ipall[ip_p][0][0];
  if (ipall[ip_p][0][1] == ipall[ip_p][1][1]) {
    ip_2 = ipall[ip_p][0][1];
  } else {
    ip_2 = random(Math.round((ipall[ip_p][0][1])), Math.round(ipall[ip_p][1][1]));
  }
  return `${ip_1}.${ip_2}.${ip_3}.${ip_4}`
}


// export async function pageStepRequest<T = any>(options: {
//   delay?: number
//   pageStart?: number
//   isStop?: () => boolean
//   request: (mall: Store, page: number) => Promise<{ list: T[]; total: number; }>,
//   mallList: Array<Store>
//   // tableInstance?: ReturnType<typeof useTable<any>>
//   onUpdateData?: (total: number, current: number) => any
// }) {


//   const mallList = options.mallList
//   if (!mallList.length) {
//     return []
//   }


//   const totals: number[] = new Array(mallList.length).fill(0)
//   let current = 0
//   const allList: T[] = []

//   await Promise.allSettled(mallList.map(async (mall, index) => {
//     const itemList: T[] = []
//     let page = options.pageStart || 1
//     await new Promise(async (resolve) => {
//       const fn = async () => {
//         options.request(mall, page++)
//           .then(async ({ list, total }) => {
//             totals[index] = total
//             current += list.length
//             itemList.push(...list)
//             const max = totals.reduce((accumulator, item) => accumulator + item, 0)

//             options.onUpdateData && options.onUpdateData(max, current)



//             if (itemList.length >= total) {
//               resolve(true)
//               return
//             }
//             if (options.isStop && options.isStop()) {
//               resolve(true)
//               return
//             }
//             if (options.delay) {

//               // await delayPromise(options.delay, options.isStop)
//               await delayPromise(options.delay)
//               if (options.isStop && options.isStop()) {
//                 resolve(true)
//                 return
//               }
//             }

//             fn()
//           })
//           .catch(() => {
//             resolve(true)
//           })
//       };
//       fn()
//     })
//     allList.push(...itemList)
//   }))

//   return allList
// }

export function randomInt(_min: number, _max: number) {
  if (_min === _max) {
    return _min;
  }
  const max = Math.max(_min, _max);
  const min = Math.min(_min, _max);
  let result = Math.floor(Math.random() * (max - min) + min);
  return result;
}
export function resolveInputStr(str: string, split = /[,，\n]/) {
  const splitArr = str.split(split);
  const targetArr: string[] = [];
  splitArr.forEach((item) => {
    if (item.trim()) {
      targetArr.push(item.trim());
    }
  });
  return targetArr;
}

export async function pageStepRequest<T = any>(options: {
  delay?: number
  pageStart?: number,
  pageEnd?: number,
  isStop?: () => boolean
  request: (page: number) => Promise<{ list: T[]; total: number; }>,
  tableInstance?: ReturnType<typeof useTable<any>>
}) {

  function loadingUpdate(text: string) {
    if (options.tableInstance) {
      options.tableInstance.tableProps.loadingConfig = {
        text
      }
    }
  }
  let current = 0
  const allList: T[] = []
  loadingUpdate(`开始加载数据`)
  const itemList: T[] = []
  let page = options.pageStart || 1
  await new Promise(async (resolve) => {
    const fn = async () => {
      options.request(page++)
        .then(async ({ list, total }) => {
          current += list.length
          itemList.push(...list)
          loadingUpdate(`已加载：${current}/${total}`)
          if (itemList.length >= total) {
            resolve(true)
            return
          }
          if (options.isStop && options.isStop()) {
            resolve(true)
            return
          }
          if (options.pageEnd && page > options.pageEnd) {
            resolve(true)
            return
          }
          if (options.delay) {
            await delayPromise(options.delay)
            if (options.isStop && options.isStop()) {
              resolve(true)
              return
            }
          }
          fn()
        })
        .catch(() => {
          resolve(true)
        })
    };
    fn()
  })
  allList.push(...itemList)
  return allList
}
