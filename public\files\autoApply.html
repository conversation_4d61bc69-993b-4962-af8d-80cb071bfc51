<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动支付</title>
    <style>
        html{
            max-width: 100vw;
            max-height :100vh;
            overflow: hidden;
        }
        header{
            width: 100%;
            height: 60px;
            display: flex;
            align-items: center;
            font-size: 16px;
            justify-content: center;
        }
        .view-container{
            display: grid;
            width: calc(250px * 5 );
            height: 660px;
            grid-template-columns: repeat(5,1fr);
            gap: 5px;
        }
        .view-item{
            border: 1px solid #ccc;
            font-size: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bolder;
            color: #ccc;
            
        }
    </style>
</head>

<body>
    <header>
        当前有10个支付窗口，6个正在执行
    </header>
    <div class="view-container">
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
        <div class="view-item"> <span>未开启</span> </div>
    </div>
</body>
<script >
    const header = document.querySelector('header')
    const sendInfo = window.electron.sendInfo
    const applyMsgFn = window.electron.msgFn
    console.log(applyMsgFn)
    const fn = (data) => {
        const {pending,loading,scaleFactor} = data
        document.documentElement.style = `zoom:${1/scaleFactor}`
        header.innerText = `当前有${pending}个订单在排队，${loading}个订单正在执行`
       
    }
    applyMsgFn(fn)
</script>
</html>