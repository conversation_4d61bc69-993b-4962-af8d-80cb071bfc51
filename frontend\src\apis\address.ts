import { ipc } from "./config";



export function getList(data: { page: number; limit: number }) {
  return ipc.invoke("controller.address.getList", data);
}

export function addItem(data: AddressItem) {
  return ipc.invoke("controller.address.addItem", {
    ...data,
  });
}
export function multipleAdd(list: AddressItem[]) {
  if (!list.length) {
    return Promise.reject("没有数据");
  }
  const item = list[0]
  const keys = Object.keys(item).join(',')
  const values = list.map(item =>{
    return `(${Object.values(item).map(item => `'${item}'`).join(',')})`
}).join(',')
// console.log(keys,values)
  return ipc.invoke("controller.address.multipleAdd", {
    keys,
    values,
  });
}

export function delItem(data: { id: number }) {
  return ipc.invoke("controller.address.delItem", data);
}

export function delSelected(data: { ids: string }) {
  return ipc.invoke("controller.address.delSelected", data);
}
export function delAddrAll() {
  return ipc.invoke("controller.address.delAddrAll");
}
export function getRandomItem() {
  return ipc.invoke("controller.address.getRandomItem");
}
