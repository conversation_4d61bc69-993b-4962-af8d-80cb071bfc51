<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>解析</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
      }
      body {
        padding: 5px;
        box-sizing: border-box;
      }
      .notice {
        box-sizing: border-box;
        padding: 5px;
        border: 1px solid #aaa;
        border-radius: 5px;
      }
      .notice p {
        line-height: 25px;
        font-size: 13px;
      }
      .resolve-log {
        padding: 5px 10px;
        box-sizing: border-box;
      }
      .resolve-log header {
        text-align: center;
        font-weight: bolder;
      }
      .resolve-log .wrapper {
        overflow-y: auto;
        max-height: 300px;
      }
      .resolve-log p {
        font-size: 13px;
        line-height: 20px;
      }
    </style>
  </head>

  <body>
    <div class="notice">
      <p>1.打开微信,在窗口中发送pdd商品详情链接并点击打开，可以解析商品信息</p>
      <p>2.进入商品详情后,查看商品的评论,可以解析商品的评论(在采集页面查看)</p>
      <p>3.关闭此窗口，结束解析</p>
    </div>

    <div class="resolve-log">
      <header>解析日志</header>
      <div class="wrapper"></div>
    </div>
  </body>

  <script>
     function format_date(value) {
      return value >= 10 ? value : "0" + value;
    }
     function dateFormat(time,option={}) {
      if (!!!Number(time)) {
        return "-";
      }

      option = Object.assign(
        {
          year: true,
          month: true,
          day: true,
          hours: true,
          min: true,
          seconds: true,
        },
        option
      );
      if (time.toString().length <= 10) {
        time *= 1000;
      }
      let d = new Date(time);
      let year = d.getFullYear();
      let month = d.getMonth() + 1;
      let day = d.getDate();
      let hours = d.getHours();
      let min = d.getMinutes();
      let seconds = d.getSeconds();
      const frist= [];
      const second= [];
      option.year && frist.push(year);
      option.month && frist.push(format_date(month));
      option.day && frist.push(format_date(day));

      option.hours && second.push(format_date(hours));
      option.min && second.push(format_date(min));
      option.seconds && second.push(format_date(seconds));
      return [frist.join("-"), second.join(":")].join(" ");
    }
  </script>
  <script>
    const wrapper = document.querySelector(".resolve-log .wrapper");
    window.addLog = (str) => {
      wrapper.innerHTML += `<p>${dateFormat(Date.now())}-${str}</p>`;
      wrapper.scrollTop = wrapper.scrollHeight;
    };
  </script>
</html>
