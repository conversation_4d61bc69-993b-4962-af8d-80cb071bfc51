type ProgressItem = {
    goodsId:number,
    /**拼团ID */
    groupId?:number,
    row:anyObj,
    /**下多少单 */
    target:number
    /**当前多少单 */
    current:number
    /**正在请求的次数 */
    requestCount:number
    /**任务id  */
    taskId:string,
    status:'pending' | 'loading' | 'finish' | 'failed'
    /**失败次总数 */
    failCount:number
    /**连续失败次数 */
    fail_consecutive:number
}
export {
    ProgressItem
}
