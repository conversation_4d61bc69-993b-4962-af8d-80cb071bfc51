type ChangePriceReqData = {
    goods_id: number
    market_price: number
    market_price_in_yuan: string
    sku_prices: Array<{
        sku_id: number
        multi_price: number
        multi_price_in_yuan: string
        single_price: number
        single_price_in_yuan: string
    }>
}
type RequestData = {
    mall: Store
    row: anyObj
    price_info: ChangePriceReqData
    /** 拼团id*/
    group_id?:number
    /**目标销量 */
    num: number
    /**下单单价 */
    price_per_order: number
    /**当前一单最大多少数量 */
    max_num_per_order: number
    /**每单购买数量 */
    num_per_order: number
    /**小号 */
    accounts: Array<string>
    /**优惠券信息 */
    coupon_info?: {
        /**优惠券码 */
        coupon_code: string,
        /**用来取消优惠券 */
        coupon_batch_id: string
    }

}
export { RequestData }