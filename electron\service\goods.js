"use strict";

const Service = require("ee-core").Service;
const Storage = require("ee-core").Storage;
const _ = require("lodash");
const path = require("path");
const fs = require("fs");
const tableNames = {
  READLIST_old: "read_list",
  READLIST: "read_list_new",
};

/**
 * 数据存储
 * @class
 */
class GoodsStorageService extends Service {
  constructor(ctx) {
    super(ctx);

    // sqlite数据库
    this.sqliteFile = "sqlite-goodsRead.db";
    let sqliteOptions = {
      driver: "sqlite",
      default: {
        timeout: 6000,
        verbose: console.log, // 打印sql语法
      },
    };
    this.goodsReadSqliteDB = Storage.JsonDB.connection(
      this.sqliteFile,
      sqliteOptions
    );
  }

  /*
   * 检查并创建表 (sqlite)
   */
  async checkAndCreateTableSqlite(tableName = "") {
    try {
      if (_.isEmpty(tableName)) {
        throw new Error(`table name is required`);
      }
      // 检查表是否存在
      const userTable = this.goodsReadSqliteDB.db.prepare(
        `SELECT * FROM sqlite_master WHERE type = ? AND name = ?`
      );

      const result = userTable.get("table", tableName);
      if (result) {
        return;
      }

      // 创建表
      let create_table_str = "";

      // goods_name: string,
      // goods_id: string | number,
      // goods_img: string

      // activity_id: number
      // mallId: string | number
      // mallName: string,
      // groupPrice: number,
      // normalPrice: number,

      // skus: Array<{
      //   skuId: number
      //   spec: string
      //   skuImg: string
      //   groupPrice: number
      //   normalPrice: number
      // }>,
      // group_order_ids: number[],
      // group_id: {
      //   alone?: number
      //   multiple: number[]
      // },
      // coupon_code: number | string
      switch (tableName) {
        case tableNames.READLIST: {
          create_table_str = `CREATE TABLE ${tableName}
              (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  goods_name CHAR(255) NOT NULL,
                  goods_id INT,
                  goods_img CHAR(255),

                  activity_id INT,
                  mallId INT,
                  mallName CHAR(255),
                  groupPrice INT,
                  normalPrice INT,
                  skus TEXT,
                  group_order_ids CHAR(255),
                  group_id CHAR(255),
                  coupon_code CHAR(255),
                  add_time CHAR(20)
              );`;
          break;
        }
      }
      create_table_str && this.goodsReadSqliteDB.db.exec(create_table_str);
    } catch (e) {
      console.log(e);
    }
  }

  /*
   * 增 Test data (sqlite)
   */
  async addGoodsItem(data) {
    const { goods_id } = data;
    const isExist = await this.checkGoodsItem(goods_id);
    const response = {
      code: 0,
      msg: "",
    };
    if (isExist) {
      response.code = 2;
      response.msg = "已存在的商品";
      return response;
      // return Promise.reject("已存在的商品");
    }
    let table = tableNames.READLIST;
    const keys = Object.keys(data);
    const insert = this.goodsReadSqliteDB.db.prepare(
      `INSERT INTO ${table} (${keys.join(",")}) VALUES (${keys
        .map((item) => "@" + item)
        .join(",")})`
    );
    insert.run(data);

    return response;
  }

  async checkGoodsItem(goods_id) {
    await this.checkAndCreateTableSqlite(tableNames.READLIST);
    const check = this.goodsReadSqliteDB.db.prepare(
      `SELECT COUNT(*) AS total FROM ${tableNames.READLIST} WHERE goods_id = ?`
    );
    const total = check.get(goods_id).total;
    return total ? true : false;
  }

  /*
   * 删 Test data (sqlite)
   */
  async deleteGoodsItem(goods_id) {
    //console.log("delete name:", name);

    let table = tableNames.READLIST;
    // const isExist = await this.checkGoodsItem(goods_id);
    // if (!isExist) {
    //   return Promise.reject("不存在当前商品");
    // }
    const delUser = this.goodsReadSqliteDB.db.prepare(
      // `DELETE FROM ${table} WHERE goods_id = ?`
      `DELETE FROM ${table} WHERE goods_id in (${goods_id})`
    );
    delUser.run();

    return true;
  }
  async deleteAll(){
    let table = tableNames.READLIST;
    await this.checkAndCreateTableSqlite(table);
    const deleteAll = this.goodsReadSqliteDB.db.prepare(
      `DELETE FROM ${table} WHERE 1`
    );
    deleteAll.run();

    return true;
  }

  /*
   * 改 Test data (sqlite)
   */
  async updateGoods(data) {
    let strs = [];
    for (let key in data) {
      if (key === "id" || !data[key]) {
        continue;
      } else {
        const value =
          typeof data[key] === "string" ? `'${data[key]}'` : data[key];
        strs.push(`${key}=${value}`);
      }
    }
    let table = tableNames.READLIST;
    await this.checkAndCreateTableSqlite(table);
    const updateStr = `UPDATE ${table} SET ${strs.join(",")} WHERE goods_id = ${
      data.goods_id
    }`;
    const updateUser = this.goodsReadSqliteDB.db.prepare(updateStr);
    updateUser.run(data);

    return true;
  }

  /*
   * 查 Test data (sqlite)
   */
  async getGoodsList({ page, limit, goods_id }) {
    //console.log("select :", {age});

    let table = tableNames.READLIST;
    await this.checkAndCreateTableSqlite(table);

    let countStr = `SELECT COUNT(*) as total  FROM ${table}`;
    if (goods_id){
      countStr += ` WHERE goods_id = ${goods_id} `
    }
    const countTotal = this.goodsReadSqliteDB.db.prepare(countStr);
    let selectStr = `SELECT *  FROM ${table}  LIMIT ${
      (page - 1) * limit
    },${limit}`;
    if (goods_id) {
      selectStr = `SELECT *  FROM ${table} WHERE goods_id = ${goods_id}  LIMIT ${
        (page - 1) * limit
      },${limit}`;
    }
    console.log(selectStr);
    const selectUser = this.goodsReadSqliteDB.db.prepare(selectStr);
    const users = selectUser.all();
    const total = countTotal.get().total;
    //console.log("select users:", users);
    return {
      total,
      list: users,
    };
  }

  /*
   * get data dir (sqlite)
   */
  async getDataDir() {
    const dir = this.demoSqliteDB.getStorageDir();

    return dir;
  }

  /*
   * set custom data dir (sqlite)
   */
  async setCustomDataDir(dir) {
    if (_.isEmpty(dir)) {
      return;
    }

    // the absolute path of the db file
    const dbFile = path.join(dir, this.sqliteFile);
    const sqliteOptions = {
      driver: "sqlite",
      default: {
        timeout: 6000,
        verbose: console.log,
      },
    };
    this.demoSqliteDB = Storage.JsonDB.connection(dbFile, sqliteOptions);

    return;
  }
}

GoodsStorageService.toString = () => "[class GoodsStorageService]";
module.exports = GoodsStorageService;
