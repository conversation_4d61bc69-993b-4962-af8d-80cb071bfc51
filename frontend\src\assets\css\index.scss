// *::-webkit-scrollbar {
//     display: none;
// }
.no-visible {
    visibility: hidden;
}

.el-radio-group:not(.default) {
    .el-radio-button {
        --el-radio-button-checked-bg-color: var(--el-color-primary-light-9) !important;
        --el-radio-button-checked-text-color: var(--el-color-primary) !important;
    }
}






/*边角，即两个滚动条的交汇处*/
.vxe-table ::-webkit-scrollbar-corner {
    background-color: #FFFFFF;
}


// .el-overlay,
// .el-overlay-message-box {
//     background: transparent !important;
// }

.el-image-viewer__wrapper {
    width: 1060px;
    // left: 463px !important;

    .el-image-viewer__canvas {
        overflow: hidden;
    }
}



.el-message-box {
    padding-bottom: 0;
    position: absolute;
    top: 400px;

    .el-message-box__btns {
        // background-color: var(--el-color-primary-light-9 ) !important;
        height: 48px;
        box-sizing: border-box;
    }
}
.el-overlay-message-box,
.el-overlay,
.el-overlay-dialog {
    max-width: 1117px;
    // .el-dialog {
    //     margin-left: 464px;
    //     overflow: hidden;

    //     &.dialog-480 {
    //         margin-left: 700px;
    //     }
    // }
}

.el-dialog {

    .el-dialog__header {
        border-bottom: var(--el-border);
        margin-right: 0;
        height: 40px;
        box-sizing: border-box;
        padding: 10px;
        // background-color: var(--el-fill-color) !important;
    }

    .el-dialog__footer {
        // background-color: var(--el-fill-color) !important;
        height: 48px;
        box-sizing: border-box;
    }
}

.drag-able {
    -webkit-app-region: drag;
    cursor: move;
}

.drag-enable {
    -webkit-app-region: no-drag;
    cursor: default;
}

.drop-able {
    -webkit-app-regino: droppable;
}

.no-select {
    user-select: none;
}

.able-select {
    user-select: text;
}

body {
    user-select: none;

    .el-form,
    .el-table,
    .vxe-table {
        user-select: text;
    }

    .pointer {
        cursor: pointer;
    }
}

// html {

//     &:not(.el-table),
//     &:not(.el-form) {
//         user-select: none;
//     }
// }

h3 {
    font-size: 20px;
}



@keyframes spin {
    from {
        transform: rotateZ(0deg);
    }

    to {
        transform: rotateZ(360deg);
    }
}

.spin-animation {
    animation: spin 5s linear infinite;
}


$color-arr : ("primary", "danger", "success", "warning");

@each $var in $color-arr {
    .#{$var} {
        color: var(--el-color-#{$var});
    }
}


@for $i from 1 through 200 {
    .m-#{$i} {
        margin: ($i) + px;
    }

    .m-t-#{$i} {
        margin-top: ($i) + px;
    }

    .m-r-#{$i} {
        margin-right: ($i) + px;
    }

    .m-b-#{$i} {
        margin-bottom: ($i) + px;
    }

    .m-l-#{$i} {
        margin-left: ($i) + px;
    }
}

@for $i from 12 through 40 {
    .f-s-#{$i} {
        font-size: ($i) + px;
    }
}

.between-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
};



