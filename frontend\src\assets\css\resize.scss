// .index-autostart-tooltip{
//     inset: auto auto 0px 0px !important;
//     transform: translate(450px, -88px) !important;
// }
// .index-taskmax-tooltip{
//     inset: auto auto 0px 0px !important;
//     transform: translate(490px, -88px) !important;
// }

// .ordermanage-ordertype-select-popper{
//     inset: 173px auto auto 909px !important;
// }
// .ordermanage-searchkey-select-popper{
//     inset: 173px auto auto 1041px !important;
// }
// .ordermanage-timerange-select-popper{
//     inset: 173px auto auto 281px !important;
// }

// .ordersetting-viewsetting-paydelay-tooltip{
//     transform: translate(932px, 371px) !important;
//     inset: 0px auto auto 0px !important;
// }