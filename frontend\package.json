{"name": "kh", "private": true, "version": "1.0.0", "scripts": {"serve": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "axios": "^1.3.5", "echarts": "^5.4.0", "element-plus": "2.2.25", "nanoid": "^4.0.2", "pinia": "^2.0.32", "qrcode": "^1.5.3", "vue": "^3.2.41", "vue-router": "^4.1.6", "vxe-table": "^4.4.2", "xe-utils": "^3.5.11"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/node": "^18.11.9", "@vitejs/plugin-vue": "^3.2.0", "consola": "^3.2.3", "crypto-js": "^4.1.1", "electron": "^23.1.2", "sass": "^1.56.1", "scss": "^0.2.4", "typescript": "4.9.3", "unplugin-auto-import": "^0.12.0", "unplugin-vue-components": "^0.22.11", "vite": "^6.1.1", "vite-plugin-style-import": "^2.0.0", "vue-tsc": "^1.0.9"}}