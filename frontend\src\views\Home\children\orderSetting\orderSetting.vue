<template>
  <div class="order-setting">
    <div class="box pay-setting">
      <h4 class="title">支付设置</h4>
      <div class="container">

        <el-form label-width="100px">
          <el-form-item label="何时付款：">
            <el-radio-group v-model="settingStore.pay.chance">
              <el-radio border label="immediate">下单立即付款</el-radio>
              <el-radio border label="delay">先下单再付款</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="付款方式：">
            <el-radio-group v-model="settingStore.pay.type">
              <!-- <el-radio border label="code">支付宝扫一扫</el-radio> -->
              <div class="el-radio no-bg">
                <el-radio border label="auto">支付宝自动付款</el-radio>
                <span class="tips">（首次自动需要填写验证码）</span>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="同时支付：">
            <el-select style="width: 100px;" v-model="settingStore.pay.winCount">
              <el-option v-for="item in 10" :label="item" :value="item"></el-option>
            </el-select>
            <span class="m-l-10">单</span>
            <span class="tips">（最多支持10单同时付款）</span>
          </el-form-item>
          <el-form-item label="使用新版支付:">
            <el-switch v-model="settingStore.pay.isNew" ></el-switch>
            <span class="tips">（请不要在执行支付时修改）</span>
          </el-form-item>
          <el-form-item label="支付密码：">

            <p>
              <el-input placeholder="请输入支付宝数字密码" type="password" :show-password="true" style="width: 200px;"
                class="m-r-10" v-model="settingStore.pay.zfb_pass"></el-input>
              <el-button :underline="false" @click="sessionClear" type="danger">更换支付宝账号</el-button>
            </p>
            <p class="tips">服务器不保存账号密码，仅本地自动填写</p>
            <!-- <p class="tips">目前仅能使用6位数短密码</p> -->
          </el-form-item>
        </el-form>

      </div>
    </div>

    <div class="box view-setting">
      <h4 class="title">浏览设置</h4>
      <div class="container">
        <p>
          <el-checkbox v-model="settingStore.visit.compare.active">货比几家：</el-checkbox>
          <el-input-number :precision="0" :disabled="!settingStore.visit.compare.active" :controls="false"
            v-model="settingStore.visit.compare.min" :min="0"
            @change="minNumberChange($event, 'compare')"></el-input-number>
          <span>至</span>
          <el-input-number :precision="0" :disabled="!settingStore.visit.compare.active" :controls="false"
            v-model="settingStore.visit.compare.max" :min="settingStore.visit.compare.min"></el-input-number>
          <span>家</span>
        </p>
        <p>
          <el-checkbox v-model="settingStore.visit.visitDetails.active">浏览详情：</el-checkbox>
          <el-input-number :precision="0" :disabled="!settingStore.visit.visitDetails.active" :controls="false"
            v-model="settingStore.visit.visitDetails.min" :min="0"
            @change="minNumberChange($event, 'visitDetails')"></el-input-number>
          <span>至</span>
          <el-input-number :precision="0" :disabled="!settingStore.visit.visitDetails.active" :controls="false"
            v-model="settingStore.visit.visitDetails.max" :min="settingStore.visit.visitDetails.min"></el-input-number>
          <span>秒</span>
        </p>
        <p>
          <el-checkbox v-model="settingStore.visit.visitCount.active">访客数量：</el-checkbox>
          <el-input-number :precision="0" :disabled="!settingStore.visit.visitCount.active" :controls="false"
            v-model="settingStore.visit.visitCount.min" :min="0" :max="30"
            @change="minNumberChange($event, 'visitCount')"></el-input-number>
          <span>至</span>
          <el-input-number :precision="0" :disabled="!settingStore.visit.visitCount.active" :controls="false"
            v-model="settingStore.visit.visitCount.max" :min="settingStore.visit.visitCount.min"
            :max="30"></el-input-number>
          <span>个</span>
        </p>
        <p>
          <el-checkbox v-model="settingStore.visit.orderDelay.active">
             <el-tooltip
              content="单个任务的下单间隔，不同任务之间不间隔">下单间隔：</el-tooltip> 
            </el-checkbox>
          <el-input-number :precision="0" :disabled="!settingStore.visit.orderDelay.active" :controls="false"
            v-model="settingStore.visit.orderDelay.min" @change="minNumberChange($event, 'orderDelay')"
            :min="0"></el-input-number>
          <span>至</span>
          <el-input-number :precision="0" :disabled="!settingStore.visit.orderDelay.active" :controls="false"
            v-model="settingStore.visit.orderDelay.max" :min="settingStore.visit.orderDelay.min"></el-input-number>
          <span>秒</span>
        </p>
        <el-tooltip popper-class="ordersetting-viewsetting-paydelay-tooltip">
          <template #content>
            <p>由于执行自动支付流程需要时间(大概3-5秒)</p>
            <p>所以5秒以下的间隔，在视觉上没有间隔</p>
            <p>但真实执行上是有间隔的</p>
            <p></p>
          </template>
          <p>
            <el-checkbox v-model="settingStore.visit.payDelay.active">付款间隔：</el-checkbox>
            <el-input-number @change="minNumberChange($event, 'payDelay')" :precision="0"
              :disabled="!settingStore.visit.payDelay.active" :controls="false" v-model="settingStore.visit.payDelay.min"
              :min="0"></el-input-number>
            <span>至</span>
            <el-input-number :precision="0" :disabled="!settingStore.visit.payDelay.active" :controls="false"
              v-model="settingStore.visit.payDelay.max" :min="settingStore.visit.payDelay.min"></el-input-number>
            <span>秒</span>
          </p>
        </el-tooltip>
        <p>
          <el-checkbox v-model="settingStore.visit.confirm.active">收货间隔：</el-checkbox>
          <el-input-number :precision="1" :disabled="!settingStore.visit.confirm.active" :controls="false"
            v-model="settingStore.visit.confirm.min" :min="0" :max="30"
            @change="minNumberChange($event, 'confirm')"></el-input-number>
          <span>至</span>
          <el-input-number :precision="1" :disabled="!settingStore.visit.confirm.active" :controls="false"
            v-model="settingStore.visit.confirm.max" :min="settingStore.visit.confirm.min" :max="30"></el-input-number>
          <span>秒</span>
        </p>
      </div>
    </div>

    <div class="box address-setting">
      <h4 class="title">地址设置：</h4>
      <div class="container">
        <div class="item">
          <div class="label">地址类型：</div>
          <div class="content">
            <el-radio-group class="addr-type" v-model="settingStore.address.type">
              <el-radio border label="random">系统随机真实地址【无重复】</el-radio>
              <el-radio border label="diy">自定义地址</el-radio>
            </el-radio-group>

            <div class="diy-addr" v-show="settingStore.address.type === 'diy'">
              <el-button type="primary" @click="importFile">文件导入</el-button>
              <el-button type="success" :disabled="false" @click="importAddrState.dialog_import = true">手动输入</el-button>
              <el-button :disabled="false" @click="importAddrState.dialog_list = true">查看地址</el-button>
              <p class="m-t-10">
                <span class="tips ">已导入{{ importAddrState.listPagination.total }}条地址</span>
              </p>

            </div>
          </div>
        </div>
        <template v-if="settingStore.address.type === 'random'">
          <div class="item">
            <div class="label">过滤省份：</div>
            <div class="content">
              <el-input :disabled="true" class="addr-filter" v-model="settingStore.address.filterStr"
                placeholder="有指定省份优先使用指定省份"></el-input>

              <!-- <span class="tips m-l-8">逗号隔开</span>  -->
              <el-link :underline="false" class="m-l-8" type="primary" @click="changeFilterAddr">选择</el-link>

            </div>
          </div>
          <div class="item">
            <div class="label">指定省份：</div>
            <div class="content">
              <el-input :disabled="true" class="addr-filter" v-model="settingStore.address.appoint_address"
                placeholder="设置指定省份会让过滤省份失效"></el-input>
              <!-- <span class="tips m-l-8">逗号隔开</span>  -->
              <el-link :underline="false" class="m-l-8" type="primary" @click="changeAppointAddr">选择</el-link>
            </div>
          </div>
        </template>
        <div class="item name-pass">
          <div class="label">
            <el-checkbox label="姓名暗号：" size="default" v-model="settingStore.address.nameCode_active"></el-checkbox>
          </div>
          <div class="content">
            <el-radio-group v-model="settingStore.address.nameCode_position"
              :disabled="!settingStore.address.nameCode_active">
              <el-radio-button label="before">加前</el-radio-button>
              <el-radio-button label="after">加后</el-radio-button>
            </el-radio-group>
            <el-input v-model="settingStore.address.nameCode_str"
              :disabled="!settingStore.address.nameCode_active"></el-input>
          </div>
        </div>
        <div class="item addr-pass">
          <div class="label">
            <el-checkbox label="地址暗号：" size="default" v-model="settingStore.address.addr_active"></el-checkbox>
          </div>
          <div class="content">
            <el-radio-group>
              <el-radio-group v-model="settingStore.address.addr_position" :disabled="!settingStore.address.addr_active">
                <el-radio-button label="before">加前</el-radio-button>
                <el-radio-button label="after">加后</el-radio-button>
              </el-radio-group>
            </el-radio-group>
            <el-input v-model="settingStore.address.addr_str" :disabled="!settingStore.address.addr_active"></el-input>
          </div>
        </div>
      </div>
    </div>
    <div class="box chat-setting">
      <h4 class="title">假聊设置</h4>
      <div class="container">
        <div class="item">
          <div class="label">
            <el-checkbox v-model="settingStore.chat.active" label="开启假聊：" size="default"></el-checkbox>
          </div>
          <div class="content">
            <el-radio-group v-model="settingStore.chat.type">
              <el-radio-button label="random">随机发送</el-radio-button>
              <el-radio-button label="order">固定发送</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="item">
          <div class="label">发送数量：</div>
          <div class="content">
            <el-input-number :min="1" :precision="0" v-model="settingStore.chat.num"></el-input-number>
            <span class="tips">条</span>
          </div>
        </div>
        <div class="item">
          <div class="content" style="margin-left: 20px">
            <el-input v-model="settingStore.chat.content" type="textarea" :rows="10" resize="none"
              placeholder="换行隔开每一条消息"></el-input>
            <p class="tips">
              提示：也可以在订单管理，【与此商家聊天】进行手工聊天
            </p>
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="importAddrState.dialog_import" title="导入地址" :width="480" class="import-address-dialog dialog-480">
      <p class="tips able-select">
        地址格式：<span class="danger">姓名|手机号|省|市|区|详细地址 </span>
        用“|”隔开！一个一行
      </p>
      <el-input v-model="importAddrState.addrInput" type="textarea" placeholder="请输入" :rows="20" resize="none"></el-input>
      <p style="color: var(--el-text-color-secondary)">
        如果此处地址使用完了，会循环调用，请及时更新
      </p>
      <template #footer>
        <el-button @click="importAddrState.dialog_import = false">取消</el-button>
        <el-button type="primary" @click="importAddrSubmit" :loading="buttonLoading.importAddr"
          :disabled="buttonLoading.importAddr">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog v-model="importAddrState.dialog_list" top="110px" title="我的地址" :width="800" class="address-list-dialog">

      <el-space class="table-header" :size="16">
        <el-link @click="tableSelectAction('all')" type="primary">全选</el-link>
        <el-link @click="tableSelectAction('reverse')">反选</el-link>
        <el-link @click="tableSelectAction('clear')">取消选择</el-link>
        <el-button type="danger" :loading="buttonLoading.delSelected" plain
          :disabled="!importAddrState.selection.length || buttonLoading.delSelected"
          @click="delAddress(importAddrState.selection)">删除选中</el-button>
        <el-button type="danger" :disabled="!importAddrState.listPagination.total"
          @click="delAddress('all')">删除所有</el-button>
      </el-space>

      <el-table ref="addrTableEl" :data="importAddrState.addressList" :height="500"
        @selection-change="(data) => importAddrState.selection = data">
        <el-table-column label="" type="selection" width="50"></el-table-column>
        <el-table-column label="序号" type="index" width="80"></el-table-column>
        <el-table-column label="姓名" prop="name"></el-table-column>
        <el-table-column label="手机号" prop="phone" width="100"></el-table-column>
        <el-table-column label="省" prop="province"></el-table-column>
        <el-table-column label="市" prop="city"></el-table-column>
        <el-table-column label="区" prop="district"></el-table-column>
        <el-table-column label="地址" prop="address"></el-table-column>
        <el-table-column width="80" label="操作" prop="">
          <template #default='scope'>
            <p>
              <el-link type="danger" :underline="false" @click="delAddress([scope.row])">删除</el-link>
            </p>
          </template>
        </el-table-column>
      </el-table>
      <ds-pagination :selection="importAddrState.selection" v-model:current-page="importAddrState.listPagination.page"
        v-model:page-size="importAddrState.listPagination.limit" :total="importAddrState.listPagination.total"
        @size-change="getAddressList()" @current-change="getAddressList()"></ds-pagination>
      <template #footer>
        <el-button @click="importAddrState.dialog_list = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { h, reactive, ref, render, watch } from "vue";
import { useSetting } from "/@/stores/setting";
import { ElCheckbox, ElCheckboxGroup, ElMessage, ElMessageBox, TableInstance } from "element-plus";
import { addItem, delAddrAll, delItem, delSelected, getList, getRandomItem, multipleAdd } from "/@/apis/address";
import { readTXT, selectFile } from "/@/apis/mainWindow";
import { addressList, clearSession } from "/@/apis/page";

import checkGroup from '/@/components/tools/checkGroup.vue'
import { autoApplyController } from "/@/apis/autoApply";

const settingStore = useSetting()

watch(() => settingStore.pay, (value) => {
  if (value) {
    if(value.isNew === undefined){
      value.isNew = true
    }
    settingStore.setOrderSetting('pay', { ...value })
  }
}, { deep: true })
watch(() => settingStore.visit, (value) => {
  if (value) {
    const str = JSON.stringify(value)
    settingStore.setOrderSetting('visit', JSON.parse(str))
  }
}, { deep: true })
watch(() => settingStore.address, (value) => {
  if (value) {
    settingStore.setOrderSetting('address', { ...value })
  }
}, { deep: true })
watch(() => settingStore.chat, (value) => {
  if (value) {
    settingStore.setOrderSetting('chat', { ...value })
  }
}, { deep: true })

function minNumberChange(val: number | undefined, key: string) {
  //@ts-ignore
  if (val && val > settingStore.visit[key]!.max) {
    //@ts-ignore
    settingStore.visit[key]!.max = val
  }
}

const state = reactive({
  provinceList: [] as anyObj[]
})

addressList({ pid: 0 })
  .then(res => {
    console.log(res.data)
    state.provinceList = res.data
  })





const addrTableEl = ref<TableInstance>()
const importAddrState = reactive({
  dialog_import: false,
  dialog_list: false,
  addressList: [] as Array<AddressItem & { id: number }>,
  selection: [] as Array<AddressItem & { id: number }>,
  addrInput: "",
  listPagination: {
    page: 1,
    limit: 100,
    total: 0
  }
});

const buttonLoading = reactive({
  importAddr: false,
  delSelected: false,
  delAddrAll: false
})

function tableSelectAction(action: 'all' | 'clear' | 'reverse') {
  switch (action) {
    case 'all': {
      importAddrState.addressList.forEach(item => {
        addrTableEl.value?.toggleRowSelection(item, true)
      })
      break
    }
    case 'reverse': {
      const selectedSet = new Set(importAddrState.selection.map(item => item.id))
      importAddrState.addressList.forEach(item => {
        if (selectedSet.has(item.id)) {
          addrTableEl.value?.toggleRowSelection(item, false)
        } else {
          addrTableEl.value?.toggleRowSelection(item, true)
        }

      })
      break
    }
    case 'clear': {
      addrTableEl.value?.clearSelection()
    }
  }
}

function getAddressList() {
  const { limit, page } = importAddrState.listPagination
  getList({ limit, page })
    .then(res => {
      // console.log(res)
      importAddrState.listPagination.total = res.total
      importAddrState.addressList = res.list
    })
}
getAddressList()


async function delAddress(list: typeof importAddrState.addressList | 'all') {
  if (typeof list === 'string' || list.length > 1) {
    await ElMessageBox({
      title: "警告",
      type: "warning",
      message: "确认要删除吗？",
      showCancelButton: true
    })
  }
  let promise: Promise<any>
  if (typeof list === 'string') {
    buttonLoading.delAddrAll = true
    promise = delAddrAll()
  } else {
    buttonLoading.delSelected = true
    // promise = Promise.allSettled(list.map(item => delItem({ id: item.id })))
    promise = delSelected({ ids: list.map(item => item.id).join(',') })
  }
  promise.then(() => {
    getAddressList()
  })
    .finally(() => {
      buttonLoading.delSelected = false
      buttonLoading.delAddrAll = false
    })

}

async function importAddrSubmit() {
  const { addrInput } = importAddrState

  const addressStr = addrInput.split('\n').filter(item => item)
  const addressList: Array<AddressItem> = []
  const faildArr: string[] = []
  addressStr.forEach(item => {
    const [name, phone, province, city, district, address] = item.split('|')
    if (name && phone && province && city && district && address && /^1[3-9]\d{9}$/.test(phone)) {
      addressList.push({
        name, phone, province, city, district, address
      })
    } else {
      faildArr.push(item)
    }
  })

  if (addressList.length) {
    // importAddrState.addressList.push(...addressList)
    ElMessage.success({
      message: `本次已成功解析并导入${addressList.length}条数据`,
      type: 'success'
    })
    buttonLoading.importAddr = true

    multipleAdd(addressList)
      .then(() => {
        getAddressList()
      })
      .finally(() => {
        buttonLoading.importAddr = false
      })


  } else {
    ElMessage.warning({
      message: "写入的数据没有分析出有效地址",
      grouping: true
    })
  }
  importAddrState.addrInput = faildArr.join('\n')
}

async function importFile() {
  const res = await selectFile({
    properties: ['openFile'],
    filters: [{ extensions: ['txt'], name: 'txt' }]
  })
  if (res.code && !res.data.length) {
    return
  }

  await Promise.allSettled(res.data.map((url: string) => {
    return readTXT({ url })
      .then((data) => {
        importAddrState.addrInput += ('\n' + data)
        return data
      })
  }))
  importAddrState.dialog_import = true

}

async function sessionClear() {
  await ElMessageBox({
    message: "确定要重置吗?",
    type: 'warning',
    title: "提示",
    showCancelButton:true
  })
  autoApplyController('reset')
  clearSession()
    .then(() => {
      ElMessage.success('清除成功')
    })
}

async function changeFilterAddr() {
  const arr = settingStore.address.filterStr.split(/[,，]/).filter(item => item)
  let v: Array<string | number> = []
  await ElMessageBox({
    title: "选择地址",
    message: h(checkGroup, { onChange: (val) => v = val, list: state.provinceList, title: 'name', valueKey: 'name', defaultValue: arr }),
    showCancelButton: true
  })

  settingStore.address.filterStr = v.join(',')
}

async function changeAppointAddr() {
  const arr = (settingStore.address.appoint_address || '').split(/[,，]/).filter(item => item)
  let v: Array<string | number> = []
  await ElMessageBox({
    title: "选择地址",
    message: h(checkGroup, { onChange: (val) => v = val, list: state.provinceList, title: 'name', valueKey: 'name', defaultValue: arr }),
    showCancelButton: true
  })

  settingStore.address.appoint_address = v.join(',')
}
</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
.order-setting {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(2, 1fr);
}

.box {
  //   width: 439px;
  width: 100%;

  .tips {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }

  h4.title {
    width: 100%;
    height: 30px;
    box-sizing: border-box;
    padding-bottom: 8px;
    font-size: 14px;
    line-height: 22px;
  }

  div.container {
    width: 100%;
    height: 340px;
    background-color: #eee;
    border: 1px solid var(--el-border-color-darker);
    border-radius: 6px;
    padding: 24px;
    box-sizing: border-box;
  }

  .el-checkbox {
    :deep(.el-checkbox__label) {
      color: var(--el-text-color-primary);
    }
  }
}

.pay-setting {
  .el-form {
    :deep(.el-form-item__label) {
      color: var(--el-text-color-primary);
      font-size: 14px;
    }

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-radio-group {
      flex-direction: column;
      align-items: flex-start;

      .el-radio {
        &:not(.no-bg) {
          background-color: var(--el-fill-color-blank);
        }

        margin-right: 0;
        margin-top: 8px;

        &:first-child {
          margin-top: 0;
        }
      }
    }

    .el-input {
      width: 280px;
    }
  }
}

.view-setting {
  p {
    display: flex;
    align-items: center;
    margin-top: 16px;

    &:first-child {
      margin-top: 0px;
    }

    .el-input-number {
      width: 96px;
    }

    &>span {
      font-size: 12px;
      margin: 0 8px;
    }
  }
}

.address-setting {
  .container {
    .item {
      display: flex;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 92px;
        font-size: 14px;
        text-align: right;
        padding-right: 12px;
      }

      .content {
        flex-grow: 1;

        .el-radio-group.addr-type {
          flex-direction: column;
          align-items: flex-start;

          .el-radio {
            margin-right: 0;
            background-color: var(--el-fill-color-blank);
            margin-bottom: 8px;
          }
        }

        .diy-addr {
          padding: 16px;
          border: 1px dashed var(--el-border-color);
          border-radius: 4px;

          .el-button {
            margin-right: 8px;
          }
        }

        .el-input.addr-filter {
          width: 280px;
        }

        p {
          .el-link {
            font-size: 12px;

          }
        }
      }

      &.name-pass,
      &.addr-pass {
        .content {
          display: flex;
          align-items: center;
        }

        .el-input {
          width: 136px;
          margin-left: 8px;
        }
      }
    }
  }
}

.chat-setting {
  .container {
    .item {
      display: flex;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 92px;
        font-size: 14px;
        text-align: right;
        padding-right: 12px;
      }

      .content {
        flex-grow: 1;

        .el-input {
          width: 73px;
          margin-right: 8px;
        }

      }

      .el-textarea {
        width: 432px;
      }


      p.tips {
        margin-top: 8px;
      }
    }
  }
}
</style>

<style lang="scss">
.el-dialog.import-address-dialog {
  .tips {
    font-size: 12px;

  }

  .el-textarea {
    margin: 10px 0;
  }
}

.el-dialog.address-list-dialog {
  // margin-left: 600px;

  .table-header {
    height: 40px
  }

  .el-table {
    @include commonTableHeader(false);
  }
}
</style>
