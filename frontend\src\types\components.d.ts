// Icon: typeof Icon
import { ipc<PERSON><PERSON><PERSON> } from "electron";
import Icon from "/@/components/Icon/Icon.vue";
import pagination from "/@/components/pagination/pagination.vue";
import scroll from '/@/components/scroll/scroll.vue'
import ScreenVideo from '/@/components/ScreenVideo/ScreenVideo.vue'


declare module "@vue/runtime-core" {
  export interface GlobalComponents {
    Icon: typeof Icon;
    'ds-pagination': typeof pagination;
    'ds-scroll': typeof scroll;
    'ScreenVideo': typeof ScreenVideo;
  }
}


