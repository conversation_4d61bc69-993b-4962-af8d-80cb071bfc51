<template>
  <div class="app-info">
    <div class="box">
      <p class="title">应用信息</p>
      <el-form :inline="true" label-postion="top" label-width="100px">
        <el-form-item label="系统时间差值">
          <el-space>
            {{ appStore.time_diff }}毫秒
            <el-link :underline="false" @click="changeTimeDiff" type="primary">修正</el-link>
          </el-space>
        </el-form-item>
        <el-form-item label="应用路径">
          {{ appStore.appInfo.appPath }}
        </el-form-item>
        <br>
        <el-form-item label="当前请求地址">
          <el-space>
            <!-- <el-select v-model="appStore.currentUrl">
              <el-option v-for="item in appStore.urls" :label="item" :value="item"></el-option>
            </el-select> -->
            <span>{{ appStore.currentUrl }}</span>
            <el-link :underline="false" @click="changeUrl" type="primary">手动修改</el-link>
          </el-space>
        </el-form-item>
        <el-form-item label="当前频繁次数">
          {{ appStore.urlErrorCount }}
        </el-form-item>
        <el-form-item label="最新频繁时间">
          {{ dateFormat(appStore.urlErrorTime) }}
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script lang='ts' setup>
import { ElMessageBox } from 'element-plus';
import { useAppStore } from '/@/stores/app'
import { dateFormat } from '/@/utils/common';
const appStore = useAppStore()

async function changeTimeDiff() {
  const { value } = await ElMessageBox.prompt('请输入修正值（毫秒）')
  const num = Number(value)
  if (!Number.isNaN(num)) {
    appStore.time_diff = num
  }
}
async function changeUrl() {
  const { value } = await ElMessageBox.prompt('请输入请求地址',{
    title:'修改请求地址',
    inputPattern:/^http/,
    inputErrorMessage:'请输入合法的请求地址'
  })
  const url = value.trim()
  appStore.currentUrl = url
  const index = appStore.urls.findIndex(item => item === url)
  if(index >= 0 ){
    appStore.urlIndex= index
  }else{
    appStore.urls.push(url)
    appStore.urlIndex = appStore.urls.length - 1
  }
}
</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
.app-info {
  .box {
    padding: 12px;
    border: var(--el-border);
    border-radius: 5px;
    background-color: #fff;

    p.title {
      font-weight: bold;
      margin-bottom: 16px;
    }
  }
}
</style>
