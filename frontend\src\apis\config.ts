let ipcRenderer = window.require && window.require("electron")?.ipcRenderer;
const fs = window.require && window.require("fs");
const path = window.require && window.require("path");
const app = window.require && window.require("electron");
// let ipcRenderer = window.electron.ipcRenderer 
// 不开启后台修改页面的时候开这个避免报错
if (!ipcRenderer) {
  ipcRenderer = {
    // @ts-ignore
    on: (eventName: string, fn: (event: Event, value: any) => any) => {
      return Promise.reject();
    },
    // @ts-ignore
    off: (eventName: string, fn: (event: Event, value: any) => any) => {
      return Promise.reject();
    },
    invoke: (eventName: string, data: any) => {
      return Promise.reject();
    },
  };
}
const ipc = ipcRenderer;

export { ipc, fs, path,app };
