type RequestData = {
    /**原商品 */
    row: anyObj
    /**原商品详情 */
    cloneGoodsDetails?: anyObj
    /**新商品信息 */
    tempGoodsInfo?: anyObj
    /**新商品ID */
    goods_id?: number
    /**新商品提交ID */
    goods_commit_id?: number
    /**购买数量 */
    num: number
    /**每单购买数量 */
    num_per_order: number
    /**每单价格 */
    price_per_order:number
    /**当前一单最大多少数量 */
    max_num_per_order: number
    /**小号 */
    account?: string | number
    accounts?: Array<string|number>
    /**店铺 */
    mall: Store
    /**优惠券码 */
    coupon_code: string,
    /**用来取消优惠券 */
    coupon_batch_id?: string
    // /**优惠券价格（分） */
    // coupon_discount?:number
    /**下单送赠礼活动管理sn 用于取消赠品 */
    promotion_event_sn?: string
    /**是否是旗舰店 */
    isFlagshipStore?: boolean
  
    newGoodsCloneInfo?: anyObj
    giftInfo?: {
      mainGoodsId: number
      mainGoodsSkuId: number
      giftGoodsId: number
      giftGoodsSkuId: number
    },
    goodsInfo?: GoodsInfo,
    isCacheTempGoods?: boolean
    costTemplateInfo?:{
      id: number
      province:Array<{id:number,name:string}>
    }
  }


  type TempGoodsCacheItem = {
    goodsInfo:GoodsInfo,
    mallId:Store['mallId'],
    tempGoodsInfo:anyObj
    lastUsedTime:number,
    status:'using'|'pending'
  }
  export {RequestData,TempGoodsCacheItem}