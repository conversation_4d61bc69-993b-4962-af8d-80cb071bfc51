import { watch, ref, UnwrapRef } from "vue";
import { useSetting } from "../stores/setting";
/**
 * @description 持久化ref
 */
export function usePersistenceRef<T = any>(
  _value: T,
  savekey: string,
  options: {
    delay?: number;
    berforeInit?: (v: Partial<T>) => UnwrapRef<T>;
    save?: boolean;
  } = {}
) {
  const settingStore = useSetting()
  const { delay, berforeInit, save = true } = options;
  let tid: NodeJS.Timeout | undefined = void 0;
  let value = _value;
  function saveToLocal(v: any) {
    if (!save) return;
    if (delay) {
      tid && clearTimeout(tid);
      tid = setTimeout(() => {
        tid = void 0;

        settingStore.setAnySetting(savekey,v)
      }, delay);
    } else {
      settingStore.setAnySetting(savekey,v)
    }
  }
  settingStore.getAnySetting(savekey)
  .then((res) => {
    if (berforeInit) {
      refInstace.value = berforeInit(res);
      return;
    }

    if(typeof res !== 'undefined'){
      refInstace.value = res;
    }
  });

  const refInstace = ref<T>(value);
  if (save) {
    watch(
      refInstace,
      (v) => {
        saveToLocal(v);
      },
      { deep: true }
    );
  }
  return refInstace;
}
