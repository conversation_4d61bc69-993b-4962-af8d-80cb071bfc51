const { Controller } = require("ee-core");
const axios = require("axios");
const dayjs = require("dayjs");
const fs = require("fs");
const path = require("path");
const Storage = require("ee-core").Storage;
const { BrowserWindow, dialog, session, webContents } = require("electron");
const { resolveTXT } = require("../utils/resolveFile");
const uuid = require("uuid");
const { get_anti_content } = require("../utils/antiContent");
const FormData = require("form-data");
const { userAgent, isDev, } = require("../config/config.app");
function resoleCookieStr (cookieJSON) {
  const cookieArr = JSON.parse(cookieJSON)
  return cookieArr.map(item => `${item.name}=${item.value}`).join("; ")
}

let pddUserInfo = {};
function initPddUserInfo () {
  const userJson = Storage.JsonDB.connection("user");
  pddUserInfo = userJson.getItem("cookies") || {};
  console.log("pddUserInfo", pddUserInfo);
}

let app = null;

const WinStatus = {
  PENDDING: 0,
  LOADING: 1,
  DISABLED: 2,
};

const autoApplyState = {
  // 自动支付订单列表
  orderList: [],
  // 密码是否有效（只有支付成功一次后才可开多窗口）
  isVerifyPass: false,
  // 自动支付窗口
  winList: [],
  winPosition: [],
  errorPassSet: new Set(),

  // 出现了密码错误 (停止所有窗口的操作)
  isPassError: false,
};

function delayPromise (delay) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (autoApplyState.isPassError) {
        reject();
        return
      }
      resolve(true);
    }, delay);
  });
}
function initWinList (max) {
  const settingJson = Storage.JsonDB.connection("setting");
  if (!max) {
    const payConfig = settingJson.getItem("pay") || {};
    max = payConfig.winCount || 1;
  }
  // console.log('initWinList',max)
  for (let i = 0; i < 5; i++) {
    if (autoApplyState.winList[i]) {
      const status = autoApplyState.winList[i].status;
      autoApplyState.winList[i].status =
        i < max
          ? status === WinStatus.DISABLED
            ? WinStatus.PENDDING
            : status
          : WinStatus.DISABLED;
    } else {
      autoApplyState.winList.push({
        win: null,
        id: i,
        // pos: { x: 375 * i, y: 0 },
        pos: { x: 188 * i, y: 0 },
        status: i < max ? WinStatus.PENDDING : WinStatus.DISABLED,
        order: null,
      });
    }
  }
}
initWinList();

function clearErrorPassOrder () {
  const { orderList, errorPassSet } = autoApplyState;
  autoApplyState.orderList = orderList.filter((item) =>
    errorPassSet.has(item.pass)
  );
}
function createApplyWin (partition, winItem) {
  if (winItem.win) {
    try {
      winItem.win.close();
    } catch (e) { }
  }
  const win = new BrowserWindow({
    width: 375,
    height: 667,
    x: winItem.pos.x,
    y: winItem.pos.y,
    alwaysOnTop: false,
    minimizable: true,
  });

  winItem.win = win;

  return winItem;
}
function startAutoApply (order) {
  // console.log('startAutoApply - start')
  const { isVerifyPass, winList, orderList } = autoApplyState;
  if (order) {
    autoApplyState.orderList.push(order);
  }
  if (!orderList.length) {
    return;
  }
  if (!isVerifyPass && winList[0].win) {
    return;
  }
  // console.log('startAutoApply - end')
  winList.forEach((item) => {
    if (item.status === WinStatus.PENDDING) {
      const order = orderList.shift();
      order && autoApply(order, item);
    }
  });
}
function autoApply (order, winItem) {
  const { orderList, winList, isVerifyPass } = autoApplyState;
  const { id, url, order_sn, type, partition, pass } = order;

  const load = () => {
    if (winItem.status !== WinStatus.DISABLED) {
      winItem.status = WinStatus.LOADING;
    }
    winItem.order = order;
    switch (type) {
      case "code": {
        // QRCode.toDataURL(url, function (err, url)
        winItem.win.loadURL(url, { userAgent });
        // })
        break;
      }
      case "auto": {
        // autoApply(item);
        winItem.win.loadURL(url, { userAgent });
        break;
      }
    }
  };

  // console.log(account,pass)
  if (!winItem.win) {
    function notice (type, msg = "") {
      const order = winItem.order;
      order &&
        app.electron.mainWindow.send("autoapply-complete", {
          id: order.id,
          order_sn: order.order_sn,
          type,
          msg,
          left: autoApplyState.orderList.length,
        });
    }

    function resolveWinItem () {
      const win = winItem.win;
      if (winItem.status === WinStatus.DISABLED) {
        beforeClose();
        try {
          win.close();
          win.destroy();
        } catch (e) { }
        return;
      }
      winItem.status = WinStatus.PENDDING;
      if (!orderList.length) {
        beforeClose();
        try {
          win.close();
          win.destroy();
        } catch (e) { }
        return;
      }

      if (orderList.length) {
        startAutoApply();
      }
    }

    const beforeClose = () => {
      winItem.win = null;
      if (winItem.status !== WinStatus.DISABLED) {
        winItem.status = WinStatus.PENDDING;
      }
      notice("close");
      winItem.order = null;
    };
    createApplyWin(order.partition, winItem);
    const { win } = winItem;
    const webContents = win.webContents;

    webContents.on("did-finish-load", async () => {
      const { id, order_sn, pass, passArr } = winItem.order;
      const inputPassStr = `
      var input = document.getElementById('pwd_unencrypt') ;
      input.click();
      setTimeout(()=>{
        var passArr = [${passArr.map((i) => `"${i}"`).join(",")}]
        passArr.forEach((value,index) => {
          input.value = value
          input.dispatchEvent(new Event('input', { bubbles:true}))
        })
      },100)
      `;

      // console.log(`[${passArr.map(i => `"${i}"`).join(',')}]`)
      // return
      const url = webContents.getURL();

      async function shortPass () {
        win.showInactive();
        console.log("shortpass");
        if (
          url.includes("exterfaceAssign.htm") ||
          url.includes("h5Continue") ||
          url.includes("cashierSafeprotectSms.htm")
        ) {
          // 支付页或者输入手机号页
          // document.documentElement.innerHTML.includes(
          //   "输入手机号，使用支付宝"
          // )
          // `document.getElementById('logon_phone').value = ${account}`
          // `document.querySelector('button[type=submit]').click()`;

          // document.documentElement.innerHTML.includes("需付款");
          // const inputPass = `document.getElementById('pwd_unencrypt').value = ${pass}`;
          // const submitForm = `document.getElementById('cashier').submit()`;
          await delayPromise(1000);
          const str = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );

          if (str.includes("输入手机号，使用支付宝")) {
            autoApplyState.isVerifyPass = false;
            console.log("new login,need input phone");
            // await delayPromise(2000);
            // webContents.executeJavaScript(
            //   `document.getElementById('logon_phone').value = ${account}`
            // );
            // console.log("input phone");
            // await delayPromise(2000);
            // webContents.executeJavaScript(
            //   `document.querySelector('button[type=submit]').click()`
            // );
            // console.log("submit");
          } else if (str.includes("需付款")) {
            console.log("widthCookie need input password");
            if (str.includes("确认交易")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              webContents.executeJavaScript(inputPassStr);
              await delayPromise(passArr.length * 100);
              // webContents.executeJavaScript(
              //   `document.getElementById('cashier').submit()`
              // );
            } else {
              webContents.executeJavaScript(inputPassStr);
              console.log("input pass");
            }
          } else if (str.includes("订单已付款成功，请勿重复提交")) {
            console.log("do not reapply");
            notice("error", "订单已付款");
            resolveWinItem();
          } else if (str.includes("系统繁忙请稍后再试")) {
            notice("error", "系统繁忙请稍后再试");
            resolveWinItem();
          } else {
            console.log("unknown");
            notice("error", "遭遇未知错误");
            resolveWinItem();
          }
        } else if (url.includes("cashierPay.htm")) {
          console.log("cashierPay");
          // 结算页面

          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          console.log("pass error", innerHTML.includes("你还可以输入"));
          console.log("apply success", innerHTML.includes("成功"));
          if (innerHTML.includes("你还可以输入")) {
            // 密码错误
            autoApplyState.errorPassSet.add(pass);
            clearErrorPassOrder();
          } else if (innerHTML.includes("成功")) {
            // 支付成功
            autoApplyState.isVerifyPass = true;
            if (!autoApplyState.errorPassSet.has(pass)) {
              autoApplyState.isPassError = false;
            }

            notice("success");
            // console.log(orderList.length);
            resolveWinItem();
          }
        } else if (url.includes("phoneLogin.htm")) {
          // 短信验证码页面
          console.log("need-input-verify-code");
        } else if (url.includes("smsValidatePhoneLogin")) {
          // 验证之后的支付页
          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          console.log("verify code error", innerHTML.includes("校验码错误"));
          console.log("need pay", innerHTML.includes("需付款"));
          if (
            !innerHTML.includes("校验码错误") &&
            innerHTML.includes("需付款")
          ) {
            console.log("smsValidatePhoneLogin need input pass");
            await delayPromise(1000);

            if (innerHTML.includes("确认交易")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              webContents.executeJavaScript(inputPassStr);
              await delayPromise(passArr.length * 100);
              // webContents.executeJavaScript(
              //   `document.getElementById('cashier').submit()`
              // );
            } else {
              webContents.executeJavaScript(inputPassStr);
              console.log("input pass");
            }
          }
        } else if (url.includes("phoneLoginAccountSel")) {
          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(`
            document.documentElement.innerHTML
            `);
          if (
            innerHTML.includes("确认付款") ||
            innerHTML.includes("确认交易")
          ) {
            webContents.executeJavaScript(
              `document.querySelector('[type=submit]').click()`
            );
            await delayPromise(1000);
            webContents.executeJavaScript(
              // `document.getElementById('pwd_unencrypt').value = ${pass}`
              inputPassStr
            );
            await delayPromise(passArr.length * 100);
            // webContents.executeJavaScript(
            //   `document.getElementById('cashier').submit()`
            // );
          } else if (innerHTML.includes("输入支付密码")) {
            webContents.executeJavaScript(
              // `document.getElementById('pwd_unencrypt').value = ${pass}`
              inputPassStr
            );
            // await delayPromise(1000);
            // webContents.executeJavaScript(
            //   `document.getElementById('cashier').submit()`
            // );
          }
        } else if (url.includes("error")) {
          // notice('error')
          resolveWinItem();
        } else if (url.includes("cashierSwitchChannelSel.htm")) {
          await delayPromise(1000);
          const str = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          if (str.includes("需付款")) {
            console.log("widthCookie need input password");
            if (str.includes("确认交易")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              webContents.executeJavaScript(inputPassStr);
              // await delayPromise(passArr.length * 100);
              // webContents.executeJavaScript(
              //   `document.getElementById('cashier').submit()`
              // );
            } else {
              webContents.executeJavaScript(inputPassStr);
              console.log("input pass");
            }
          }
          // await delayPromise(1000);
          // const innerHTML = await webContents.executeJavaScript(
          //   `document.documentElement.innerHTML`
          // );
        } else {
          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          if (innerHTML.includes("你还可以输入")) {
            // 密码错误
          } else if (innerHTML.includes("成功")) {
            // 支付成功
            autoApplyState.isVerifyPass = true;
            notice("success");
            resolveWinItem();
          } else {
            notice("success");
            resolveWinItem();
          }
        }
      }
      async function longPass () {
        console.log("langpass");
        function inputLangePass () {
          webContents.sendInputEvent({
            type: "mouseDown",
            x: 150,
            y: 188,
          });
          webContents.sendInputEvent({
            type: "mouseUp",
            x: 150,
            y: 188,
          });
          pass.split("").forEach((item, index) => {
            setTimeout(() => {
              webContents.sendInputEvent({ type: "char", keyCode: item });
            }, index * 30);
          });
        }

        if (
          url.includes("exterfaceAssign.htm") ||
          url.includes("h5Continue") ||
          url.includes("cashierSafeprotectSms.htm")
        ) {
          await delayPromise(1000);
          const str = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );

          if (str.includes("输入手机号，使用支付宝")) {
            autoApplyState.isVerifyPass = false;
            console.log("new login,need input phone");
          } else if (str.includes("需付款")) {
            console.log("widthCookie need input password");
            if (str.includes("确认交易")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              inputLangePass();
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            }
            if (str.includes("确认付款")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              inputLangePass();
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            } else {
              inputLangePass();
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            }
          } else if (str.includes("订单已付款成功，请勿重复提交")) {
            console.log("do not reapply");
            notice("error", "订单已付款");
            resolveWinItem();
          } else if (str.includes("系统繁忙请稍后再试")) {
            notice("error", "系统繁忙请稍后再试");
            resolveWinItem();
          } else {
            console.log("unknown");
            notice("error", "遭遇未知错误");
            resolveWinItem();
          }
        } else if (url.includes("cashierPay.htm")) {
          console.log("cashierPay");
          // 结算页面

          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          console.log("pass error", innerHTML.includes("你还可以输入"));
          console.log("apply success", innerHTML.includes("成功"));
          if (innerHTML.includes("你还可以输入")) {
            // 密码错误
            autoApplyState.errorPassSet.add(pass);
            clearErrorPassOrder();
          } else if (innerHTML.includes("成功")) {
            // 支付成功
            autoApplyState.isVerifyPass = true;
            if (!autoApplyState.errorPassSet.has(pass)) {
              autoApplyState.isPassError = false;
            }

            notice("success");
            // console.log(orderList.length);
            resolveWinItem();
          }
        } else if (url.includes("phoneLogin.htm")) {
          // 短信验证码页面
          console.log("need-input-verify-code");
        } else if (url.includes("smsValidatePhoneLogin")) {
          // 验证之后的支付页
          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          console.log("verify code error", innerHTML.includes("校验码错误"));
          console.log("need pay", innerHTML.includes("需付款"));
          if (
            !innerHTML.includes("校验码错误") &&
            innerHTML.includes("需付款")
          ) {
            console.log("smsValidatePhoneLogin need input pass");
            await delayPromise(1000);

            if (innerHTML.includes("确认交易")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              inputLangePass();
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            } else {
              inputLangePass();
              console.log("input pass");
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            }
          }
        } else if (url.includes("phoneLoginAccountSel")) {
          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(`
            document.documentElement.innerHTML
            `);
          if (
            innerHTML.includes("确认付款") ||
            innerHTML.includes("确认交易")
          ) {
            webContents.executeJavaScript(
              `document.querySelector('[type=submit]').click()`
            );
            await delayPromise(1000);
            inputLangePass();
            await delayPromise(passArr.length * 50);
            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          } else if (innerHTML.includes("输入支付密码")) {
            inputLangePass();
            await delayPromise(passArr.length * 50);
            webContents.executeJavaScript(
              `document.getElementById('cashier').submit()`
            );
          }
        } else if (url.includes("error")) {
          // notice('error')
          resolveWinItem();
        } else if (url.includes("cashierSwitchChannelSel.htm")) {
          await delayPromise(1000);
          const str = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          if (str.includes("需付款")) {
            console.log("widthCookie need input password");
            if (str.includes("确认交易")) {
              webContents.executeJavaScript(
                `document.querySelector('button[type=submit]').click()`
              );
              await delayPromise(500);
              inputLangePass();
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
            } else {
              inputLangePass();
              await delayPromise(passArr.length * 50);
              webContents.executeJavaScript(
                `document.getElementById('cashier').submit()`
              );
              console.log("input pass");
            }
          }
        } else {
          await delayPromise(1000);
          const innerHTML = await webContents.executeJavaScript(
            `document.documentElement.innerHTML`
          );
          if (innerHTML.includes("你还可以输入")) {
            // 密码错误
          } else if (innerHTML.includes("成功")) {
            // 支付成功
            autoApplyState.isVerifyPass = true;
            notice("success");
            resolveWinItem();
          } else {
            notice("success");
            resolveWinItem();
          }
        }
      }
      if (passArr.length > 6) {
        longPass();
      } else {
        shortPass();
      }
    });
    win.on("closed", beforeClose);
  }

  load();
}

const requestState = {
  appId: 0
}

class PageController extends Controller {
  constructor(ctx) {
    super(ctx);
    app = this.app;
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */
  async store_anti_content () {
    const res = await get_anti_content()
    return res
  }
  async addPddStore ({ store_name }) {
    function insertStoreInfo (webContents) {
      if (!store_name) {
        return;
      }
      const html = `<div style="font-size:30px;position:fixed;left:20px;top:20px;background:#f9ded3;padding:10px;max-width:800px"><h2 >请登录指定店铺</h2><h2 >店铺名：${store_name || ""
        }</h2></div>`;
      // const str = `document.body.innerHTML += '${html}'`
      const str = `const node = document.createElement('div');node.innerHTML = '${html}';document.body.appendChild(node)`;

      webContents.executeJavaScript(str).catch((e) => {
        console.log("insertStoreInfo - error", e);
      });
    }
    return new Promise((resolve, reject) => {
      const width = 1200;
      const height = 660;
      const win = new BrowserWindow({
        width,
        height,
        // modal: true,
        // parent: this.app.electron.mainWindow,
        webPreferences: {
          partition: uuid.v4(),
        },
      });

      win.onbeforeunload = () => {
        console.log("before-close");
      };
      const webContents = win.webContents;
      webContents.on("did-finish-load", async () => {
        if (webContents.getURL().includes("mms.pinduoduo.com/login")) {
          insertStoreInfo(webContents);
          return;
        }

        await delayPromise(1000);
        let userInfoJson = "";
        try {
          userInfoJson = await webContents.executeJavaScript(
            "localStorage.getItem('new_userinfo')"
          );
        } catch (e) {
          console.log("get userInfoJson false");
        }
        let cookieArr = []
        webContents.session.cookies.get({}).then((res) => {
          // const ignoreSet = new Set(["PDDAccessToken", "pdd_user_id"]);
          const data = {};
          cookieArr = res
          res.forEach((item) => {
            // if (ignoreSet.has(item.name)) {
            //   return;
            // }
            // if (item.domain !== "mms.pinduoduo.com") {
            //   return;
            // }
            // console.log(item)
            data[item.name] = item.value;
          });
          if (!data.PASS_ID) {
            return;
          }
          const response = {
            cookieObj: data,
            userInfoJson,
            cookieArr,
          };
          resolve(response);
          win.closable && win.close();
        });
      });
      win.loadURL("https://mms.pinduoduo.com/login/", { userAgent });

      win.on("close", () => {
        resolve(false);
      });
    });
  }
  async setAppId (appId) {
    requestState.appId = appId
  }
  async request (config, options) {
    if (!config.headers) {
      config.headers = {}
    }
    config.headers.appid = requestState.appId
    return this.service.request
      .createAxios(config, options)
      .then((res) => {
        // console.log('success')
        return res;
      })
      .catch((res) => {
        // console.log('failed',res)
        return {
          code: 1,
          msg: "网络超时",
        };
      });
  }

  async anyRequest (config) {
    return this.service.request.createAnyAxios(config);
  }


  // 支付
  async resolveApply ({ item, win }) {
    switch (item.type) {
      case "code":
      case "auto": {
        // autoApply({ ...item, ...win });
        startAutoApply({ ...item, ...win });
        break;
      }
    }
    return true;
  }

  async collectComment ({ goodsID, count, collectMode, cookie = '' }) {
    initPddUserInfo();
    const response = {
      code: 0,
      data: {},
      msg: "",
      verifyCode: "",
    };
    if (!pddUserInfo.PDDAccessToken) {
      response.code = 2;
      response.msg = "请在右侧登录拼多多账号";
      return response;
    }

    let reviewList = [];

    await new Promise(async (resolve) => {
      let pageStart = 1;
      const size = 20;
      const fn = () => {
        axios({
          url: `https://mobile.yangkeduo.com/proxy/api/reviews/${goodsID}/list?label_id=0&page=${pageStart++}&size=${size}&enable_video=1&enable_group_review=1&pdduid=${pddUserInfo.pdd_user_id}`,

          // https://mobile.yangkeduo.com/proxy/api/reviews/************/list?label_id=800000000&page=1&size=20&enable_video=1&enable_group_review=1&pdduid=3159076412
          method: "post",
          // params: {
          //   pdduid: pddUserInfo.pdd_user_id,
          //   page: pageStart++,
          //   size,
          //   enable_group_review: 1,
          //   enable_video: 1,
          //   label_id: 0,
          // },

          headers: {
            accesstoken: pddUserInfo.PDDAccessToken,
            Referer: `https://mobile.yangkeduo.com/goods_comments.html?goods_id=${goodsID}`,
            userAgent,
            // Cookie: `PDDAccessToken=${pddUserInfo.PDDAccessToken}`,
            Cookie: cookie,
            Origin: "https://mobile.yangkeduo.com"
          },
          data: {
            name: "goodsCommentListAxios",
          },
        })
          .then((_response) => {
            // console.log(_response.data)
            if (
              _response.data.error_code === 54001 ||
              _response.data.verify_auth_token
            ) {
              response.msg = "该账号查看评论被风控，需要验证";
              response.code = 3;
              // console.log(_response.data)
              response.verifyCode = _response.data.verify_auth_token;
              resolve();
              return;
            }
            const data = _response.data.data;
            if (!data) {
              resolve(true);
              return;
            }
            if (data.length) {
              const filterList = data.filter((_item) => {
                const filter =
                  _item.comment.includes("该用户觉得商品较好") ||
                  _item.comment.includes("该用户觉得商品很好，给出了5星好评") ||
                  _item.comment.includes("该用户未填写文字评价") ||
                  _item.desc_score < 5;

                return !filter;
              });
              reviewList.push(...filterList);
            } else {
              resolve(true);
              return;
            }
            if (reviewList.length >= count) {
              resolve(true);
              return;
            } else {
              setTimeout(() => {
                fn();
              }, 500);
            }
          })
          .catch((res) => {
            if (!reviewList.length) {
              response.code = 1;
              response.msg = "请求出错";
              response.resData = res.response.data;
            }
            resolve(true);
          });
      };
      fn();
    });
    if (count) {
      reviewList = reviewList.slice(0, count);
    }

    if (!reviewList.length && !response.code) {
      response.code = 1;
      response.msg = "该商品过滤后没有评论";
    }
    response.data.reviewList = reviewList;
    return response;
  }

  // 获取文件夹下的txt评论和图片
  async getFolderComment (folder) {
    const response = {
      code: 1,
      msg: "",
      data: null,
    };
    const result = await new Promise((resolve, reject) => {
      fs.readdir(folder, (error, files) => {
        if (error) {
          resolve(false);
          return;
        }
        const data = { folder, imgs: [], txt: [], videos: [] };
        files.forEach((file) => {
          if (
            /\.(xbm|tif|pjp|svgz|jpg|jpeg|ico|tiff|gif|svg|jfif|webp|png|bmp|pjpeg|avif)$/i.test(
              file
            )
          ) {
            data.imgs.push(file);
          } else if (file.endsWith("txt")) {
            data.txt.push(`${folder}/${file}`);
          } else if (
            /\.(mp4|avi|wmv|mpg|mpeg|mov|rm|ram|swf|flv)$/i.test(file)
          ) {
            data.videos.push(file);
          }
        });

        resolve(data);
      });
    });

    if (!result) {
      response.msg = "解析失败";
      return response;
    } else {
      const { folder, imgs, txt, videos } = result;
      const responseData = {
        folder,
        imgs,
        videos,
        comment: "",
      };
      response.code = 0;
      response.data = responseData;
      const txtPath = txt.pop();
      if (txtPath) {
        responseData.comment = await new Promise((resolve, reject) => {
          resolveTXT(txtPath)
            .then((res) => {
              resolve(res);
            })
            .catch((res) => {
              resolve("");
            });
        });
      }
      return response;
    }
  }

  async getComment (path) {
    const response = {
      code: 0,
      data: "",
      msg: "",
    };

    let save_path = path;

    if (!save_path) {
      const result = await dialog.showOpenDialog({
        properties: ["openDirectory"],
      });
      // console.log(result)
      if (result.canceled) {
        response.code = 1;
        response.msg = "取消选择文件路径";
        return response;
      } else {
        save_path = result.filePaths[0];
      }
    }

    const commentFolders = await new Promise((resolve, reject) => {
      fs.readdir(save_path, (error, data) => {
        if (error) {
          reject(error);
        } else {
          const list = [];
          data.forEach((file) => {
            const filePath = `${save_path}/${file}`;
            const stat = fs.statSync(filePath);
            if (stat.isDirectory()) {
              list.push(filePath);
            }
          });
          resolve(list);
        }
      });
    });

    const data = await new Promise((resolve, reject) => {
      const data = [];
      let resolveCount = 0;
      commentFolders.forEach((folder) => {
        fs.readdir(folder, (error, files) => {
          const item = { folder, imgs: [], txt: [] };
          files.forEach((file) => {
            if (file.startsWith("img")) {
              item.imgs.push(file);
            } else if (file.startsWith("comment")) {
              item.txt.push(file);
            }
          });
          data.push(item);
          resolveCount++;
          if (resolveCount >= commentFolders.length) {
            resolve(data);
          }
        });
      });
    });
    const result = await Promise.all(
      data.map((item) => {
        return new Promise((resolve) => {
          const txt = item.txt[0];
          if (!txt) {
            item.comment = "";
            resolve(item);
            return;
          }
          const path = `${item.folder}/${txt}`;
          fs.readFile(path, { encoding: "utf-8" }, (error, data) => {
            item.comment = data || "";
            resolve(item);
          });
        });
      })
    );
    response.data = result;
    return response;
    // await new Promise((res,rej)=>{
    //   readDirRecursive(path,res,rej)
    // })
  }

  // 通过多多进宝的链接获取一些数据
  async getJinBaoData (url) {
    let win = new BrowserWindow({
      modal: true,
      parent: this.app.electron.mainWindow,
    });
    const response = {
      code: 0,
      data: "",
      msg: "",
    };

    return new Promise((resolve, reject) => {
      const webContents = win.webContents;
      webContents.on('update-target-url', () => {
        const url = webContents.getURL();
        console.log(url);
        if (url.includes("duo_coupon_landing.html")) {
          response.data = url;
          resolve(response);
          setTimeout(() => {
            win && win.closable && win.close();
            win = null;
          }, 1000);
        }
      });
      win.on("closed", () => {
        response.code = 1;
        response.msg = "关闭了窗口";
        resolve(response);
      });
      win.loadURL(url);
    });
  }

  // 清除支付缓存
  async clearSession () {
    autoApplyState.isVerifyPass = false;
    return session.defaultSession.clearStorageData();
  }

  // 改变支付窗口数量
  async changeApplyWinCount (count) {
    count && initWinList(count >= 5 ? 5 : count);
  }

  // 清除支付订单列表
  async clearApplyList () {
    autoApplyState.orderList.length = 0;
  }
  // 批发读取商品
  async getGoodsDetails ({ goods_id }) {
    const key = "pifaCookie";
    const userJson = Storage.JsonDB.connection("user");
    let pifaCookie = userJson.getItem(key);
    if (!pifaCookie || !Object.keys(pifaCookie).length) {
      pifaCookie = {};
      const res = await new Promise((resolve, reject) => {
        const win = new BrowserWindow();
        win.loadURL(
          "https://mms.pinduoduo.com/login/sso?platform=wholesale&redirectUrl=https%3A%2F%2Fpifa.pinduoduo.com%2Fclint%2Fapi%2Flogin%3Fredirect%3Dhttps%253A%252F%252Fpifa.pinduoduo.com%252F"
        );
        win.on("closed", () => {
          resolve(false);
        });
        win.webContents.on("did-finish-load", async () => {
          const url = win.webContents.getURL();
          console.log(url);
          if (url.includes("https://mms.pinduoduo.com/login/")) {
          } else {
            await delayPromise(2000);
            win.webContents.session.cookies
              .get({ domain: "pifa.pinduoduo.com" })
              .then((res) => {
                // console.log('sessionxxxxxxxxxxxxxx',res)
                res.forEach((item) => {
                  if (item.name === "msfe-pc-cookie-captcha-token") {
                    return;
                  }
                  pifaCookie[item.name] = item.value;
                });
                if (pifaCookie.VISITOR_PASS_ID || pifaCookie.SUB_PASS_ID) {
                  win.closable && win.close();
                  resolve(pifaCookie);
                  userJson.setItem(key, pifaCookie);
                }
                // console.log(pifaCookie)
              });
          }
        });
      });
      if (!res) {
        return {
          success: false,
        };
      }
    }

    try {
      // const { cookieJSON } = storeInfo;
      // const cookieObj = JSON.parse(pifaCookie);
      const anti_content = await get_anti_content();
      const response = await axios({
        method: "post",
        url: "https://pifa.pinduoduo.com/pifa/goods/queryGoodsDetail",
        data: {
          goodsId: goods_id,
        },
        headers: {
          authority: "pifa.pinduoduo.com",
          accept: "*/*",
          "accept-language": "zh-CN,zh;q=0.9",
          "content-type": "application/json",
          // cookie: "terminalFinger=gKr9bW6NUYOQGo0naKdRVAI520LQEr6S; api_uid=CgqbCGLLhqhuzhjU/ZgKAg==; _f77=675423f0-7544-4af2-90a5-00dae45a4b71; ru1k=675423f0-7544-4af2-90a5-00dae45a4b71; _a42=0e9cdb08-dc19-436b-aa79-51336c2726ee; ru2k=0e9cdb08-dc19-436b-aa79-51336c2726ee; dilx=SHRc5FM7ttpn_cIglIn0R; _bee=YLyAxX6fC80AB3BmqEKVa2iKnSkSfVlq; rckk=YLyAxX6fC80AB3BmqEKVa2iKnSkSfVlq; _nano_fp=XpEJX0gjXpCJXqT8Xo_6IHOfb_eggLgqAPMuYv1Y; webp=true; jrpl=bNvVu4Ji2GMF0xLmI0ATNpCZK2Si7cBd; njrpl=bNvVu4Ji2GMF0xLmI0ATNpCZK2Si7cBd; VISITOR_PASS_ID=5pKKQVl5sEUxUmEPNmtECna2BusvyvDKpG6D5Um3u_Nj7HrKaEgMH7jzWW4neqC-PmS4S81r1_aP0QlBVr3mvc6X0en4WRqdptIGy131dNc_f6b2b112f2; SUB_PASS_ID=eyJ0IjoiQ1UyYlZMdDVyTWd3R3lUVkV0VkV6eGZoSmltZzd5aG5hakhQbmJIMlRJc0FyRDVkQ01xV2xRNy9WMW0yWTJ1LyIsInYiOjEsInMiOjE0LCJtIjo3NjE1NzMyNTcsInUiOjQ4MzEwNDgxfQ",
          cookie: Object.keys(pifaCookie).map((key) => `${key}=${pifaCookie[key]}`).join("; "),
          origin: "https://pifa.pinduoduo.com",
          Host: "pifa.pinduoduo.com",
          "User-Agent":
            "android Mozilla/5.0 (Linux; Android 8.1.0; vivo NEX A Build/OPM1.171019.019; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/62.0.3202.84 Mobile Safari/537.36  phh_android_version/3.23.0 phh_android_build/228842 phh_android_channel/qihu360",
          Connection: "keep-alive",
          "cache-control": "max-age=0",
          "Anti-Content": anti_content,
        },
      });
      const data = response.data;
      console.log(data);
      if (data.result && data.result.verifyAuthToken) {
        const win = new BrowserWindow();
        win.loadURL("https://pifa.pinduoduo.com/");
        win.webContents.on("did-start-loading", () => {
          // const url = win.webContents.getURL()
          // if(url.includes)
          Object.keys(pifaCookie).forEach((key) => {
            const value = pifaCookie[key];
            const str = `window.cookieStore.set("${key}","${value}")`;
            win.webContents.executeJavaScript(str);
          });
        });
        win.webContents.on("did-finish-load", () => { });
      } else {
        if (!data.success) {
          userJson.setItem(key, {});
        }
        return data;
      }
    } catch (e) {
      userJson.setItem(key, {});
      // console.log(e)
    }
    return {
      success: false,
    };
  }

  // 自动设供货
  async autoSupply ({ mallId, goods_id }) {
    const response = {
      code: 0,
      data: "",
      msg: "",
    };
    function returnError (msg = "", code = 1) {
      response.code = code;
      response.msg = msg;
      return response;
    }

    if (!mallId || !goods_id) {
      return returnError("信息传入不完整");
    }
    const mallInfo = await this.service.stores.findStoreItem(mallId);
    if (!mallInfo) {
      return returnError("未找到店铺信息,请登录店铺");
    }
    const { cookieJSON } = mallInfo;
    let cookies = "";
    try {
      cookies = resoleCookieStr(cookieJSON);
    } catch (e) {
      console.log(e);
    }
    if (!cookies) {
      return returnError("解析店铺数据失败");
    }
    const addRes = await new Promise(async (resolve) => {
      const res = {
        code: 1,
        data: "",
        msg: "",
      };
      try {
        const antiContent = await get_anti_content();
        const response = await axios({
          url: "https://mms.pinduoduo.com/mille/mms/goods/addGoods",
          method: "post",
          headers: {
            Host: "mms.pinduoduo.com",
            Connection: "keep-alive",
            "anti-content": antiContent,
            Cookie: cookies,
            "content-type": "application/json",
            "User-Agent": userAgent,
          },
          data: {
            activityGoodsConfigs: [
              {
                goodsId: goods_id,
                goodsLadderDiscounts: [
                  { ladderStartValue: 2, ladderDiscount: 90 },
                ],
                supportDropShipping: 0,
              },
            ],
            syncAllGoodsDiscount: false,
            bizId: 1,
          },
        });
        const data = response.data;
        // if(!data.result){
        //   // console.log('data',data)
        // }
        const failGoodsReasons =
          (data.result && data.result.failGoodsReasons) || {};

        if (data && data.success) {
          res.code = 0;
        } else if (!data.result) {
          res.msg = data.errorMsg || data.error_msg;
        } else if (data.result && data.result.verifyAuthToken) {
          res.msg = "需要验证";
          res.data = data.result.verifyAuthToken;
          res.code = 2
        } else if (failGoodsReasons[goods_id]) {
          if (failGoodsReasons[goods_id].includes("商品已存在")) {
            res.code = 0;
          }
          res.msg = failGoodsReasons[goods_id];
        } else {
          console.log(data);
          res.msg = "添加折扣失败:" + failGoodsReasons[goods_id];
          res.res = data;
        }
        resolve(res);
      } catch (e) {
        // res.data = e;
        console.log("auto-support", e);
        res.msg = "添加折扣失败-服务器繁忙";
        resolve(res);
      }
    });
    if (addRes.code) {
      response.data = addRes.data
      return returnError(addRes.msg, addRes.code);
    }
    // 修改折扣
    const changeRes = await new Promise(async (resolve) => {
      const res = {
        code: 1,
        data: "",
        msg: "",
      };
      try {
        const antiContent = await get_anti_content();
        const response = await axios({
          url: "https://mms.pinduoduo.com/mille/mms/goods/batchUpdateGoodsDiscount",
          method: "post",
          headers: {
            Host: "mms.pinduoduo.com",
            Connection: "keep-alive",
            "anti-content": antiContent,
            Cookie: cookies,
            "content-type": "application/json",
            "User-Agent": userAgent,
          },
          data: {
            operateSource: 0,
            batchUpdate: [
              {
                goodsId: goods_id,
                goodsLadderDiscounts: [
                  { ladderStartValue: 2, ladderDiscount: 90 },
                ],
                supportDropShipping: 1,
              },
            ],
            syncAllGoodsDiscount: false,
            bizId: 1,
          },
        });
        const data = response.data;
        if (data && data.success) {
          res.code = 0;
          resolve(res);
        } else if (data.result && data.result.verifyAuthToken) {
          res.msg = "需要验证";
          res.data = data.result.verifyAuthToken;
          res.code = 2
        } else {
          res.msg = "修改折扣失败";
          resolve(res);
        }
      } catch (e) {
        res.msg = "修改折扣失败";
        resolve(res);
      }
    });
    if (changeRes.code) {
      response.data = changeRes.data
      return returnError(changeRes.msg, changeRes.code);
    }
    response.code = 0;
    return response;
  }


  async getCommonComment () {
    return new Promise((resolve) => {
      fs.readFile(
        isDev ? path.resolve(__dirname, '../../public/files/comment.txt') : path.resolve(__dirname, "../../files/comment.txt"),
        "utf-8",
        (error, data) => {
          if (error) {
            resolve("");
          } else {
            resolve(data);
          }
        }
      );
    });
  }

  async uploadImage_base64 ({ image, order_sn, tk }) {
    let anti_content = await get_anti_content();
    const res = {
      code: 1,
      msg: "",
      data: void 0,
    };
    let signature = "";
    try {
      const response = await axios({
        url: "https://mobile.pinduoduo.com/proxy/api/image/signature",
        method: "post",
        data: {
          bucket_tag: "review_image",

        },
        headers: {
          AccessToken: tk,
          // Host: "file.pinduoduo.com",
        },

      });
      signature = response.data.signature;
    } catch (e) {
      console.log(e.response.data.error_code, '???')
      res.msg = `-拼多多返回错误,错误码:${e.response.data.error_code}-${e.response.data.error_msg}`;
      return res;
    }

    try {
      const response = await axios({
        //url:"https://file.pinduoduo.com/v4/store_image?sdk_version=android-0.1.29-5.80.0&tag_name=review_image",
        url: "https://file.pinduoduo.com/v2/store_image",
        method: "post",
        data: {
          image,
          upload_sign: signature,
        },
        headers: {
          AccessToken: tk,
          // Host: "file.pinduoduo.com",
        },
      });
      // signature = res.data.signature;
      res.data = response.data;
      res.code = 0;
      return res;
    } catch (e) {
      // console.log(e.response.data)
      // fs.writeFile(path.resolve(__dirname,'../../temp/response.txt'),e.response.data.error_msg,()=>{})
      res.msg = `-拼多多返回错误,错误码:${e.response.data.error_code}-${e.response.data.error_msg}`;;
      return res;
    }
  }
  async uploadVideo ({ url, order_sn, tk }) {
    const res = {
      code: 1,
      msg: "",
      data: void 0,
    };
    let signature = "";
    try {
      const response = await axios({
        url: "https://api.pinduoduo.com/image/signature?pdduid=3133655130589+HTTP%2F1.1",
        method: "post",
        data: { bucket_tag: "review_video" },
        headers: {
          AccessToken: tk,
          // Host: "file.pinduoduo.com",
        },
      });
      signature = response.data.signature;
    } catch (e) {
      // console.log('tk',tk)
      res.msg = `-拼多多返回错误,错误码:${e.response.data.error_code}-`;;
      return res;
    }
    try {
      const formData = new FormData();
      formData.append("file", fs.createReadStream(url)),
        formData.append("sign", signature);
      const response = await axios({
        url: "http://file.yangkeduo.com/general_file",
        // url: "https://file.pinduoduo.com/api/galerie/v2/general_file?sdk_version=android-0.1.29-6.63.0&tag_name=review_video",
        method: "post",
        data: formData,
        headers: {
          AccessToken: tk,
          "User-Agent": userAgent,
          Referer: "http://file.yangkeduo.com/general_file",
        },
      });
      // signature = res.data.signature;
      res.data = response.data;
      res.code = 0;
      return res;
    } catch (e) {
      res.msg = `-拼多多返回错误,错误码:${e.response.data.error_code}-`;;
      return res;
    }
  }
}

PageController.toString = () => "[class PageController]";
module.exports = PageController;
