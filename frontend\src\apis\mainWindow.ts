// let ipc = window?.electron?.ipcRenderer

import { ElMessage } from "element-plus";
import { ipc } from "./config";
import { BrowserWindowConstructorOptions } from "electron";
import { useSetting } from "../stores/setting";
const controller = "controller.window."
const ipcRoute = {
  minimize: "controller.window.minimize",
  close: "controller.window.close",
  setSize: "controller.window.setSize",
  resize: "controller.window.resize",
  checkUpdate: "controller.window.checkForUpdater",
  downloadApp: "controller.window.downloadApp",
  openExternal: "controller.window.openExternal",
  openFileWindow: "controller.window.openFileWindow",
  messageShowConfirm: "controller.window.messageShowConfirm",
  showOpenDialog: "controller.window.showOpenDialog",
  selectFolder: "controller.window.selectFolder",
  selectFile: "controller.window.selectFile",
  getFolderFiles: "controller.window.getFolderFiles",
  imgToBase64: "controller.window.imgToBase64",
  saveByBase64: "controller.window.saveByBase64",
  writeFile: "controller.window.writeFile",
  writeExcel: "controller.window.writeExcel",
  readTXT: "controller.window.readTXT",
  createNewWindow: "controller.window.createNewWindow",
  menu: "controller.window.menu",
  resolveExcel: "controller.window.resolveExcel",
  systemProcessList: "controller.window.systemProcessList",
  getMac: "controller.window.getMac",
  isDirectory: "controller.window.isDirectory",
  deleteFolder: "controller.window.deleteFolder",
  deleteFile: "controller.window.deleteFile",
  makeDir: "controller.window.makeDir",
  exists: "controller.window.exists",
  appInfo: "controller.window.appInfo",
  getVideoInfo: "controller.window.getVideoInfo",
  videoResize: "controller.window.videoResize",
  pathFn: "controller.window.pathFn",
  download: "controller.window.download",
};

window.addEventListener("contextmenu", (ev) => {
  ev.preventDefault();
  // 获取鼠标位置
  const client = {
    x: ev.clientX,
    y: ev.clientY,
  };
  // 把鼠标位置发送到主进程
  ipc.invoke(ipcRoute.menu, client);
});

/**
 * 最小化窗口
 * @returns
 */
export function minimizeWindow() {
  return ipc.invoke(controller + "minimize");
}
/**
 * 关闭窗口
 * @returns
 */
export function closeWindow() {
  return ipc.invoke(ipcRoute.close);
}
export function setWindowSize(data: {
  x?: number;
  y?: number;
  type?: "normal" | "login";
}) {
  return ipc.invoke(ipcRoute.setSize, data);
}
export function resize(data?: {
  width: number;
  height: number;
  scale: number;
}) {
  const { scale, appHeight, appWidth } = useSetting().appWindow;
  const _data = data || { scale, height: appHeight, width: appWidth };
  return ipc.invoke(ipcRoute.resize, { ..._data });
}
export function checkUpdate(url?: string) {
  return ipc.invoke(ipcRoute.checkUpdate, url);
}
export function downloadApp() {
  return ipc.invoke(ipcRoute.downloadApp);
}

export function openExternal(url: string) {
  if (!url) {
    return ElMessage.warning("没有地址");
  } else if (!url.startsWith("http://") && !url.startsWith("https://")) {
    return ElMessage.error("错误的访问地址");
  } else {
    return ipc.invoke(ipcRoute.openExternal, url);
  }
}

export function openFileWindow(url: string) {
  if (!url) {
    ElMessage.warning("没有路径");
    return Promise.reject();
  }
  return ipc.invoke(ipcRoute.openFileWindow, url);
}

export function windowMessageBox(options: {
  type: "none" | "info" | "none" | "question" | "warning";
  title: string;
  message: string;
  detail: string;
  //   cancelId: 1; // 用于取消对话框的按钮的索引
  //   defaultId: 0; // 设置默认选中的按钮
  buttons: string[]; // 按钮及索引
}) {
  return ipc.invoke(ipcRoute.messageShowConfirm, options);
}

export function showOpenDialog(options: Electron.OpenDialogOptions): Promise<{
  canceled: boolean;
  filePaths: any[];
}> {
  return ipc.invoke(ipcRoute.showOpenDialog, options);
}

export function selectFolder(): Promise<string | undefined | null> {
  return ipc.invoke(ipcRoute.selectFolder);
}

export function selectFile(options: Electron.OpenDialogOptions) {
  return ipc.invoke(ipcRoute.selectFile, options);
}

export function getFolderFiles(url: string): Promise<string[]> {
  return ipc.invoke(ipcRoute.getFolderFiles, url);
}

export function imgToBase64(url: string, addType = true, compress?: number) {
  return ipc
    .invoke(ipcRoute.imgToBase64, { url, addType, compress })
    .then((res) => {
      if (res) {
        return Promise.resolve(res);
      } else {
        return Promise.reject(res);
      }
    });
}

export function saveByBase64(data: {
  dest: string;
  // filename:string
  data: string;
}) {
  return ipc.invoke(ipcRoute.saveByBase64, data);
}

export function writeFile(data: { dest: string; data: string }) {
  return ipc.invoke(ipcRoute.writeFile, data);
}

export function writeExcel(data: {
  path: string;
  data: Array<{ name: string; data: string[][] }>;
  options?: anyObj;
}) {
  return ipc.invoke(ipcRoute.writeExcel, data);
}

export function readTXT(data: { url: string }): Promise<string> {
  return ipc.invoke(ipcRoute.readTXT, data);
}

export function createNewWindow(
  options: BrowserWindowConstructorOptions & { url: string }
) {
  // console.log(data)
  return ipc.invoke(ipcRoute.createNewWindow, options);
}

export function resolveExcel(data: { path: string }): Promise<
  {
    name: string;
    data: Array<Array<string | number>>;
  }[]
> {
  return ipc.invoke(ipcRoute.resolveExcel, data);
}

export function systemProcessList(): Promise<string[][]> {
  return ipc.invoke(ipcRoute.systemProcessList);
}
export function getMac(): Promise<string> {
  return ipc.invoke(ipcRoute.getMac);
}

export function deleteFolder(path: string): Promise<boolean> {
  return ipc.invoke(ipcRoute.deleteFolder, { path }).then((res) => {
    if (!res) {
      return Promise.reject(res);
    }
    return res;
  });
}

export function deleteFile(url: string): Promise<boolean> {
  return ipc.invoke(ipcRoute.deleteFile, { url }).then((res) => {
    if (!res) {
      return Promise.reject(res);
    }
    return res;
  });
}

export function isDirectory(url: string): Promise<boolean> {
  return ipc.invoke(ipcRoute.isDirectory, url).then((res) => {
    if (!res) {
      return Promise.reject(res);
    }
    return res;
  });
}

export function makeDir(data: { path: string }): Promise<responseData> {
  return ipc.invoke(ipcRoute.makeDir, { ...data }).then((res: responseData) => {
    if (res.code) {
      let msg = res.msg;
      if (msg.includes("file already exists")) {
        msg = "文件夹已存在";
      }
      res.msg &&
        ElMessage.warning({
          message: msg,
          grouping: true,
        });
      return Promise.reject(res);
    }
    return res;
  });
}

export function exists(path: string): Promise<boolean> {
  return ipc.invoke(ipcRoute.exists, { path });
}
export function appInfo() {
  return ipc.invoke(ipcRoute.appInfo);
}
export function getVideoInfo(data: { url: string; timestamps?: number[] }) {
  if (!data.timestamps) {
    data.timestamps = [];
  }
  return ipc.invoke(ipcRoute.getVideoInfo, { ...data });
}

export function videoResize(data: {
  url: string;
  size?: string;
}): Promise<{ suucess: boolean; msg: string; data: string }> {
  return ipc.invoke(ipcRoute.videoResize, { ...data }).then((res) => {
    if (res.success) {
      return res;
    } else {
      return Promise.reject(res);
    }
  });
}

/**path path函数相关方法 */
export function pathFn(action: string, params: any) {
  return ipc.invoke(ipcRoute.pathFn, { action, params });
}
/**
 * url:下载链接 dest:存储路径 delay超时时间（默认30s）
 */
export function download(data: { url: string; dest: string; delay?: number }):Promise<responseData> {
  return ipc.invoke(ipcRoute.download, data)
  .then(res => {
    if(res.code){
      return Promise.reject(res)
    }
    return res
  })
}
