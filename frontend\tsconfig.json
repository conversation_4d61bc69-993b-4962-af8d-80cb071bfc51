{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "types": ["element-plus/global"], "baseUrl": "./", "paths": {"/@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types/", "src/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "./auto-imports.d.ts", "./components.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}