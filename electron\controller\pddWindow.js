"use strict";
const _ = require("lodash");
const path = require("path");
const fs = require("fs");
const { exec } = require("child_process");
const { Controller, Utils } = require("ee-core");
const electronApp = require("electron").app;
const { screen, BrowserWindow, BrowserView } = require("electron");
const { userAgent } = require("../config/config.app");
const dayjs = require("dayjs");
const axios = require("axios");

const Storage = require("ee-core").Storage;
const uuid = require("uuid");
/**
 * @type {BrowserView | null}
 */
let pddBrowserObj = null;

let app = null;
// 允许插入cookie  （控制登录后直插入一次 ）
let flag = true;
let userInfo = {
  PDDAccessToken: "",
  pdd_user_id: "",
  // PDDAccessToken: "6QXXPOMTEVMBGMJNIO6EHXM6ZAG2LM7ST3PGKKZNM6SM5ZRMKEQA1107829",
  // pdd_user_id :'6014271773816',
};

function initUserInfo () {
  const userJson = Storage.JsonDB.connection("user");
  const obj = userJson.getItem("cookies");
  if (obj) {
    userInfo = Object.assign(userInfo, obj);
  }
}
initUserInfo();
const _state = {
  url: "",
  currentGoodsDetails: "",
};

const state = new Proxy(_state, {
  get (target, prop, receiver) {
    return Reflect.get(target, prop);
  },
  set (target, prop, value) {
    Reflect.set(target, prop, value);
    prop === "url" && urlChange(value);
    prop === "currentGoodsDetails" && currentGoodsDetailsChange(value);
    return true;
  },
});

// 路径变化
function urlChange (value) {
  urlUpdate(value);
  getGoodsInfo();
}

// 商品详情变化
function currentGoodsDetailsChange (value) {
  // fs.writeFile(path.resolve(__dirname,'../../logs/text.json'),value,()=>{})
  currentGoodsDetailsUpdate(value);
  // http://ddtest.jindouyundd.com/api/goods/getCategory?cate_ids=
}

// 通知页面窗口的商品更新
async function currentGoodsDetailsUpdate (value) {
  app.electron.mainWindow.send(
    "controller.pddWindow.currentGoodsDetailsUpdate",
    value
  );
}

// 通知页面路径更新
function urlUpdate (value) {
  app.electron.mainWindow.send("controller.pddWindow.updateUrl", value);
}

// 从cookie中获取数据
function getInfoInCookies () {
  const webContents = pddBrowserObj.webContents;
  if (!webContents) {
    return;
  }
  // if (userInfo.PDDAccessToken && userInfo.api_uid && userInfo.pdd_user_id) {
  //   return;
  // }
  webContents.session.cookies
    .get({
      domain: "mobile.yangkeduo.com",
      name: "PDDAccessToken",
    })
    // webContents
    //   .executeJavaScript(`window.cookieStore.get('PDDAccessToken')`)
    .then((res) => {
      // console.log("window.cookieStore.get('PDDAccessToken')",res)
      if (!res || !res.length) {
        return;
      }

      const item = res.find(
        (item) =>
          item.name === "PDDAccessToken" &&
          item.domain === "mobile.yangkeduo.com"
      );
      let token = "";
      if (item) {
        token = item.value;
      }
      if (token && userInfo.PDDAccessToken !== token) {
        userInfo.PDDAccessToken = token;
        console.log(userInfo);
        const userJson = Storage.JsonDB.connection("user");
        userJson.setItem("cookies", userInfo);
      }

      // fs.writeFile(path.resolve(__dirname,'../../logs/cookies.json'),JSON.stringify(res),()=>{})
      // const names = new Set([
      //   "PDDAccessToken",
      //   "api_uid",
      //   "_nano_fp",
      //   "webp",
      //   "jrpl",
      //   "njrpl",
      //   "dilx",
      //   "pdd_user_id",
      //   "pdd_user_uin",
      //   "pdd_vds",
      // ]);
      // //   const arr = []
      // const obj = {};
      // res.forEach((item) => {
      //   if (names.has(item.name)) {
      //     //   const str = item.value;
      //     //   arr.push(`${item.name}=${str}`)
      //     //   names.delete(item.name)
      //     obj[item.name] = item.value;
      //   }
      // });
      // console.log()
      // if(obj.PDDAccessToken && obj.PDDAccessToken !== userInfo.PDDAccessToken){
      //   const userJson = Storage.JsonDB.connection('user');
      //   userJson.setItem('cookies',obj)
      //   userInfo = Object.assign(userInfo, obj);
      // }
    })
    .catch((e) => {
      console.log("window.cookieStore.get('PDDAccessToken') error\n", e);
    });

  webContents
    .executeJavaScript(`window.cookieStore.get('pdd_user_id')`)
    .then((res) => {
      if (!res) {
        return;
      }
      const pdd_user_id = res.value;
      if (pdd_user_id && userInfo.pdd_user_id !== pdd_user_id) {
        userInfo.pdd_user_id = pdd_user_id;
        const userJson = Storage.JsonDB.connection("user");
        userJson.setItem("cookies", userInfo);
      }
    })
    .catch((e) => {
      console.log("window.cookieStore.get('pdd_user_id') error\n", e);
    });

  // webContents.executeJavaScript
}

// 插入登录信息
async function insertCookie (type = "insert") {
  const webContents = pddBrowserObj.webContents;
  if (!webContents) {
    return;
  }
  //   webContents.openDevTools();
  //
  if (type == "insert" && !flag) {
    return;
  }
  if (type == "clear") {
    userInfo.PDDAccessToken = "";
    userInfo.pdd_user_id = "";
    const userJson = Storage.JsonDB.connection("user");
    userJson.setItem("cookies", userInfo);
    // await webContents.executeJavaScript(`window.cookieStore.delete({name:'PDDAccessToken'})`)
    webContents.session.clearStorageData()
    return;
  }
  flag = false;
  const obj = userInfo;
  Object.keys(obj).forEach((key) => {
    if (type == "insert" && !obj[key]) {
      return;
    }
    const value = obj[key];
    const str = `window.cookieStore.set("${key}","${value}")`;
    webContents
      .executeJavaScript(str)
      .then((res) => {
        console.log("success", str);
      })
      .catch((e) => {
        console.log("error", str, e);
      });
  });
}

/**注入初始化样式 */
function insertCSS () {
  const webContents = pddBrowserObj.webContents;
  if (!webContents) {
    return;
  }

  webContents.insertCSS(`body::-webkit-scrollbar {
            width: 0;
            height: 0;
            background-color: transparent;
          };`);
  webContents.insertCSS(`body .pdd-go-to-app{
            display:none !important;
          }`);
  webContents.insertCSS(`html{
    z-index:0
  }`);
}

// 获取详情页的数据
function getGoodsInfo () {
  const webContents = pddBrowserObj.webContents;
  console.log("getGoods-info before");
  const reg =
    /^(https|http):\/\/mobile\.(pinduoduo|yangkeduo)\.com\/goods\d*\.html\?.*goods_id=/;
  if (!reg.test(state.url)) {
    state.currentGoodsDetails = "";
    return;
  }
  console.log("getGoods-info after");
  webContents
    .executeJavaScript(
      `
     JSON.stringify(window.rawData);
    
  `
    )
    .then((res) => {
      // console.log(res)
      // const file = path.resolve(__dirname,'../../logs/text.html')
      // console.log(typeof res)
      // fs.writeFile(path.resolve(__dirname,'../../logs/text.json'),res,(error,res)=>{
      //     console.log(error)
      // })
      if (res) {
        state.currentGoodsDetails = res;
        const obj = JSON.parse(res);
        if (!obj) {
          return;
        }
        const goods = obj.store.initDataObj?.goods;
        const skuIds = Object.keys(goods)
          .filter((item) => new RegExp(/^catID\d+$/, "i").test(item))
          .map((item) => goods[item]);
        // console.log(skuIds)
        axios
          .get(
            "http://ddtest.jindouyundd.com/api/goods/getCategory?cate_ids=" +
            skuIds.join(",")
          )
          .then((response) => {
            obj.GoodsCates = response.data.data;
            obj.GoodsCatesStr = skuIds
              .map((cateId) => {
                const detail = obj.GoodsCates.find((c) => c.cat_id == cateId);
                return detail?.cat_name || "";
              })
              .filter((item) => item)
              .join(">");
            state.currentGoodsDetails = JSON.stringify(obj);
          })
          .catch((e) => {
            console.log("get goods cate error");
          });
      } else {
        state.currentGoodsDetails = "";
      }
    })
    .catch((e) => {
      console.log("getGoodsInfo error", e);
    });
}

const currentPddWindownInfo = {
  with: 0,
  height: 0,
  scale: 1,
  x: 0,
  y: 0,
};

function checkPddWindowSize (option) {
  // const {width,height,scale,x,y} = currentPddWindownInfo
  let flag = false;
  Object.keys(option).forEach((key) => {
    if (currentPddWindownInfo[key] != option[key]) {
      flag = true;
    }
    currentPddWindownInfo[key] = option[key];
  });
  return flag;
}

/**
 * PDD窗口控制器
 * @class
 */
class PddWindowController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */
  async resize (options) {
    const { x, y, width, height } = options;
    let scale = options.scale
    if (scale > 2) {
      scale = scale / 100
    }
    if (!pddBrowserObj) {
      // this.open(options);
      return "no-window";
    }
    if (!width || !height) {
      this.app.electron.mainWindow.removeBrowserView(pddBrowserObj);
      pddBrowserObj = null;
      return;
    }
    if (!checkPddWindowSize(options)) {
      return;
    }
    pddBrowserObj.setBounds({
      width: Math.floor(width * scale),
      height: Math.floor(height * scale),
      x: Math.floor(1125 * scale),
      y: Math.floor(80 * scale),
    });
  }

  async open (options) {
    const { x, y, width, height, scale } = options;
    console.log(options);
    if (!width || !height) {
      return;
    }
    if (pddBrowserObj) {
      this.resize(options);
      return;
    }
    app = this.app;
    pddBrowserObj = new BrowserView();
    // pddBrowserObj.webContents.openDevTools()
    this.app.electron.mainWindow.setBrowserView(pddBrowserObj);
    // const currentWindowSize = this.app.electron.mainWindow.getBounds();
    this.resize(options);
    state.url = "https://mobile.yangkeduo.com/";
    pddBrowserObj.webContents.loadURL(state.url);
    pddBrowserObj.webContents.on("did-start-loading", () => { });
    pddBrowserObj.webContents.on("did-finish-load", () => {
      console.log("finish");
      insertCookie();
      insertCSS();
      state.url = pddBrowserObj.webContents.getURL();

      getInfoInCookies();

      // clearCookies()
    });

    // try {
    //   pddBrowserObj.webContents.debugger.attach('1.1')
    // } catch (err) {
    //   console.log('调试器连接失败: ', err)
    // }
    // pddBrowserObj.webContents.debugger.on('detach', (event, reason) => {
    //   console.log('Debugger detached due to: ', reason);
    // });

    // pddBrowserObj.webContents.debugger.on('message', (event, method, params) => {
    //   if (method === 'Network.responseReceived') {
    //     const url = params.response.url
    //     if(url.includes('proxy/api/reviews')){
    //       pddBrowserObj.webContents.debugger.sendCommand('Network.getResponseBody', { requestId: params.requestId }).then(function (response) {
    //         console.log(response);
    //       });
    //     }

    //   }
    // })

    // pddBrowserObj.webContents.debugger.sendCommand('Network.enable');
  }

  async changeUrl ({ url, clear }) {
    const webContents = pddBrowserObj.webContents;
    url = url || "home";
    // console.log(clear);
    if (clear) {
      insertCookie("clear");
      // webContents.executeJavaScript(`
      //   window.cookieStore.delete('PDDAccessToken');
      //   window.cookieStore.delete("pdd_user_id")
      // `);
    }
    switch (url) {
      case "home": {
        url = "https://mobile.yangkeduo.com/";
        break;
      }
      case "back": {
        webContents.canGoBack() && webContents.goBack();
        return "back";
      }
      case "next": {
        webContents.canGoForward() && webContents.goForward();
        return "next";
      }
      case "reload": {
        webContents.reload();
        return "reload";
      }
      case "reloadIgnoringCache": {
        webContents.reloadIgnoringCache();
        return "reloadIgnoringCache";
      }
      default: {
        if (
          !url.startsWith("https://mobile.pinduoduo.com/") &&
          !url.startsWith("https://mobile.yangkeduo.com/")
        ) {
          return Promise.reject("不能跳转到拼多多以外的链接");
        }
      }
    }
    webContents.loadURL(url);
    return url;
  }

  async goodsInfo () {
    return state.currentGoodsDetails;
  }

  async loginPddStorePage ({ cookieJSON, infoJSON }) {
    return new Promise((resolve, reject) => {
      const cookieArr = JSON.parse(cookieJSON);
      const window = new BrowserWindow({
        width: 1340,
        height: 800,
        webPreferences: {
          partition: uuid.v4(),
        },
      });
      const webContents = window.webContents;
      let loginPageCount = 0;
      cookieArr.forEach((item) => {
        window.webContents.session.cookies.set({
          url: "https://mms.pinduoduo.com/",
          ...item
        });
      });
      webContents.on("did-finish-load", async () => {
        const url = webContents.getURL();
        if (url.includes("https://mms.pinduoduo.com/login/", { userAgent })) {
          //   await webContents.executeJavaScript(`
          //   localStorage.setItem('pddUser',${infoJSON})
          // `);
          // cookieStore.set('PASS_ID','${cookieObj.PASS_ID}')
          await webContents.executeJavaScript(`
          var cookieObj = ${cookieJSON};
          Object.keys(cookieObj).forEach(key => {
            cookieStore.set(key,cookieObj[key])
          })
        `);
          loginPageCount++;
          if (loginPageCount >= 5) {
            resolve(false);
            window.closable && window.close();
          } else {
            window.loadURL("https://mms.pinduoduo.com/home/", { userAgent });
          }
        } else if (url.includes("https://mms.pinduoduo.com/home/")) {
          resolve(true);
        }

        // window.loadURL('https://mms.pinduoduo.com/home/')
      });
      // window.loadURL("https://mms.pinduoduo.com/login/", { userAgent });
      window.loadURL("https://mms.pinduoduo.com/express/batch", { userAgent });
      // window.webContents.setWindowOpenHandler((details) => {
      //   window.loadURL(details.url, { userAgent });
      // });
    });
  }

  async getGoodsDetails ({ goodsId, tk }) {
    tk = "RF4355WY6FBU4FZI2EHDD5GMGHQ25AYYXXKVZZHKYIMCJX4OJPBQ1223ca7";
    return new Promise(async (resolve, reject) => {
      console.log("getGoodsDetails", goodsId, tk);
      const response = await axios({
        url: "https://mobile.pinduoduo.com/goods.html?goods_id=" + goodsId,
        headers: {
          // Cookie: `_bee=76hgdSUIzOcgTCY6B1NlOy8Z70fPDKlQ; _f77=129d102a-588f-4dbc-8e06-73dd9df28c5f; _a42=2a1be2f4-da43-4fc0-adc4-dc874bac27db; rckk=76hgdSUIzOcgTCY6B1NlOy8Z70fPDKlQ; ru1k=129d102a-588f-4dbc-8e06-73dd9df28c5f; ru2k=2a1be2f4-da43-4fc0-adc4-dc874bac27db; api_uid=CihTzmRsaZYaHwBtjkXoAg==; dilx=vUPVHnJ_e68TCUErlYNY1; _nano_fp=XpEJn5CaX5EJnqdYno_7ErekCTrX5ln7rzzqTa6X; webp=1; jrpl=GlgcRRYSeaZrj6olQI26fHXPdByiyjMS; njrpl=GlgcRRYSeaZrj6olQI26fHXPdByiyjMS; PDDAccessToken=${tk};  pdd_vds=gaLLNOIoabNNmttENNIGEQPmyGIENINmbLGaGEtymPItmPmQaEIQaQntNQiy`,
          Cookie: ` PDDAccessToken=${tk}`,
          method: "get",
        },
      });
      const filename = uuid.v4();
      const fileUrl = path.resolve(__dirname, `../files/${filename}.html`);
      fs.writeFile(fileUrl, response.data, (error) => {
        if (error) {
          resolve("");
          win.closable && win.close();
          return;
        }
        const win = new BrowserWindow({
          show: false,
          // width:10,
          // height:10,
          // x:1,
          // y:1
        });
        win.loadFile(fileUrl);
        win.webContents.addListener("did-finish-load", (error) => {
          win.webContents
            .executeJavaScript(
              `
            JSON.stringify(window.rawData)
          `
            )
            .then((res) => {
              console.log("success");
              resolve(res);
            })
            .catch((res) => {
              console.log("failed");
              resolve("");
            })
            .finally(() => {
              win.closable && win.close();
              fs.unlink(fileUrl, () => { });
            });
        });
      });
    });
  }
  async logout () {
    insertCookie("clear");
  }
  async getCookies () {
    if (!pddBrowserObj) {
      return []
    }
    return pddBrowserObj.webContents.session.cookies.get({})
  }
}

PddWindowController.toString = () => "[class PddWindowController]";
module.exports = PddWindowController;
