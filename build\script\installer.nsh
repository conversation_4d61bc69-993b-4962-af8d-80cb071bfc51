!define DIR_NAME "k"



Function .onVerifyInstDir
  StrCpy $INSTDIR "$INSTDIR\${DIR_NAME}"
FunctionEnd

!macro customFinishPage
AutoCloseWindow true
Function StartApp
    ${if} ${isUpdated}
      StrCpy $1 "--updated"
    ${else}
      StrCpy $1 ""
    ${endif}
    ${StdUtils.ExecShellAsUser} $0 "$launchLink" "open" "$1"
FunctionEnd

Function AddToPathEnv

   ReadRegStr $0 HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation
    ${if} $0 == ""
        ReadRegStr $0 HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation
    ${endif}
    ${if} $0 != ""
      
          ReadRegStr $1 HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "Path"
          ;Manager\Environment" "Path"
          StrCpy $2 "$0\resources\extraResources\dll\p"
          ${StrContains} $4 $2 $1 
          ${if} $4 != ""
              ;MessageBox MB_OK "路径 '$2' 已在 PATH 环境变量中。'$1'"
          ${else}
              ;MessageBox MB_OK "路径 '$2' 不在 PATH 环境变量中。'$1'"
              WriteRegExpandStr HKLM "SYSTEM\CurrentControlSet\Control\Session Manager\Environment" "Path" "$1;$2"
              SendMessage ${HWND_BROADCAST} ${WM_WININICHANGE} 0 "STR:Environment" /TIMEOUT=3000
          ${endif}

    ${endif}
FunctionEnd

Function .onInstSuccess
    Call AddToPathEnv
    Call StartApp
FunctionEnd

!macroend