import { LoadingOptions } from "element-plus";

import request, { Options, anyRequest, storeAntiContent, updateOrderInfo } from "./page";
import {  retry } from "../utils/common";
import { cloneDeep } from "lodash";
import { AxiosRequestConfig } from "axios";
import { useSetting } from "../stores/setting";

export async function pddStoreRequestHeader(store: Store, headers: anyObj = {}) {
  const key = await storeAntiContent()
  const cookieArr = JSON.parse(store.cookieJSON) as Electron.Cookie[]
  return {
    Cookie:cookieArr.map(item => `${item.name}=${item.value}`).join("; "),
    Origin: "https://mms.pinduoduo.com",
    "anti-content": key,
    "Etag":"ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k",
    ...headers,
  };
}

/**
 * 店铺可参加活动的商品列表
 */
export async function storeGoodsList(
  store: Store,
  page: number,
  goods_id?: string,
  goods_name?: string
): Promise<anyObj> {

  const requestData:any = {
    // pre_sale_type: 4,
    is_onsale: 1,
    page,
    size: 100,
    order_by: "sold_quantity:asc,id:desc",
    sold_out: 0,
    // keywords,
  };
  if (goods_id) {
    requestData.goods_id_list = goods_id.split(/[,，\n]/).filter((item) => item);
  }
  if(goods_name){
    requestData.goods_name = goods_name
  }
  const config = {
    url: "https://mms.pinduoduo.com/vodka/v2/mms/query/display/mall/goodsList",
    method: 'post',
    headers: await pddStoreRequestHeader(store, {
      "Referer": 'https://mms.pinduoduo.com/goods/goods_list',
    }),
    data:requestData,
  };
  return anyRequest(config);

  // return ipc.invoke(controller + "storeGoodsList", data);
}

/**批量下架 */
export async function batchOffSale(store: Store, goodsIds: number[]) {
  const headers = await pddStoreRequestHeader(store)
  const crawlerInfo = await storeAntiContent()
  const config = {
    url: 'https://mms.pinduoduo.com/vodka/v2/mms/batchOffSale',
    method: 'post',
    headers,
    data: {
      crawlerInfo,
      goodsIds
    }
  }
  return anyRequest(config)
}

/**
 * 检测店铺是否可参加活动
 */
export async function checkActivityEnable(
  store: Store,
  activity_id: string | number
) {
  const config = {
    url: 'https://mms.pinduoduo.com' + "/leekmms/activity/query/detail",
    method: "post",
    data: { activity_id, query_source: 1 },
    headers: await pddStoreRequestHeader(store)
  }
  return anyRequest(config);

}

// export async function  activitySubmit(
//   store: Store,
//   activity_id: string | number,
//   goods_volist: {
//     goods_id: number;
//     create_activity_price_vo: {
//       create_time_vo: anyObj;
//       create_quantity_vo: { goods_activity_quantity?: string };
//       create_sku_vos: Array<{
//         sku_id: number;
//         sku_activity_price: number;
//         sku_front_ctx_vo?: {
//           require_price: number;
//           filling_sku_price: number;
//         };
//       }>;
//     };
//   }[]
// ) {
//   const config = {
//     url: "https://mms.pinduoduo.com" + "/lakemms/enrollV2",
//     method: "post",
//     data: {
//       enroll_source_ctx: {
//         enroll_type: 3,
//         source_type: "PROMO-OwnHome",
//       },
//       /*商品信息 可多个*/
//       goods_volist,
//       /**使用活动运费模板 */
//       create_protocol_vo: {
//         need_create_cost_template: true,
//       },
//       /*活动ID  默认为19521*/
//       activity_id,
//     },
//     headers: await pddStoreRequestHeader(store),
//   }
//   return anyRequest(config);
//   // return ipc.invoke(controller + "activitySubmit", {
//   //   ...store,
//   //   activity_id,
//   //   goods_volist,
//   // });
// }
export async function activitySubmit(
  store: Store,
  activity_id: string | number,
  goods_volist: {
    goods_id: number;
    create_activity_price_vo: {
      create_time_vo: anyObj;
      create_quantity_vo: { goods_activity_quantity?: string };
      create_sku_vos: Array<{
        sku_id: number;
        sku_activity_price: number;
        sku_front_ctx_vo?: {
          require_price: number;
          filling_sku_price: number;
        };
      }>;
    };
  }[]
) {
  const config = {
    url: "https://mms.pinduoduo.com/lakemms/enrollV2",
    method: "post",
    headers: await pddStoreRequestHeader(store, {
      Referer: `https://mms.pinduoduo.com/act/goods_price/confirm?activityId=${activity_id}&activityType=27&from=chooseGoods&SourceType=MMSActicon&SceneType=ActivityCard`
    }),
    data: {
      activity_id,
      behavior_collection_v1_cas_boost_factor: window.btoa(`qamwswuh-${store.mallId}-${Date.now()}`),
      create_outer_activity_price_ratio_vo: {
        max_price_ratio: 110,
        min_price_ratio: 110
      },
      create_protocol_vo: {
        confirmed_protocol_volist: [],
        need_create_cost_template: true
      },
      custom_ability_vo: {},

      enroll_source_ctx: {
        enroll_type: 3,
        // scene_type: "ActivityCard",
        // source_type: "MMSActicon",
        "source_type": "PROMO-ActivityEnrollListName"
      },
      goods_volist,
    },
  };
  console.log(config, 'activitySubmit')
  return anyRequest(config);
}
/**活动中的列表 */
export async function activityGoodsList(
  store: Store,
  goods_id_list: number[],
  activity_id?: string
) {

  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: "https://mms.pinduoduo.com" + "/lakemms/activityGoods/list",
    method: "post",
    headers,
    data: {
      status: [501],
      /*每页数量*/
      page_size: 40,
      /*页数*/
      page_number: 1,
      order_by: "created_at",
      sort_by: "desc",
      /*商品ID*/
      goods_id_list,
      // activity_id:activity_id ? String(activity_id) : void 0
    },
  };
  return anyRequest(config);
}

/**取消活动 */
export async function activityCancel(
  store: Store,
  activity_goods_id: number,
  goods_id?: number
) {
  const config = {
    url: "https://mms.pinduoduo.com" + "/lakemms/activityGoods/applyOffline",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: {
      activity_goods_id,
      goods_id
    },
  }
  return anyRequest(config);

}

/**活动详情 */
export async function activityDetails(
  store: Store,
  activity_goods_id: number,
  goods_id: number
) {
  const config = {
    url: "https://mms.pinduoduo.com" + "/lakemms/activityGoods/detail",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: {
      /*活动商品ID 在获取活动中商品列表取*/
      activity_goods_id,

      /*商品ID*/
      goods_id,

      query_source: 1,
    },
  };
  return anyRequest(config);
}

/**
 * 快速下单
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function quickOrder(
  data: {
    account: string | number;
    type?: string;
    // mode?: "alone";
    // mode?: "alone" | 'open_group';
    /**"alone" | 'open_group' */
    mode?: string;
    sku: number | string;
    sku_spec: string;
    goods_id: number | string;
    num: number;
    activity_id?: number;
    anti_content?: string;
    shop_name: string;
    shop_id: number | string;
    goods_name: string;
    group_id: string | number;
    use_coupon?: boolean | number;
    [key: string]: any
  },
  options?: Options,
  loading?: LoadingOptions
) {
  data.type = data.type || "paidan";
  data.mode = data.mode || "alone";
  data.anti_content = window.get_anti_content();
  data.use_coupon = data.use_coupon ? 1 : void 0;
  return request(
    {
      url: "/api/task/quickOrder",
      data,
    },
    options,
    loading
  ).then(async res => {
    if (res.data?.order_amount <= 0) {
      await Promise.allSettled([
        updateOrderInfo({ order_sn: res.data.order_sn })
          .then(res2 => {
            if (res2.data.order_amount) {
              res.data.order_amount = res2.data.order_amount
              res.data.shop_id = res2.data.shop_id
            }
          })
      ])
    }
    return res
  })
}
/**
 * 快速下单
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function quickOrder_mallTask(
  data: any,
  options?: Options,
  loading?: LoadingOptions
) {
  // data.type = data.type || "paidan";
  // data.mode = data.mode || "alone";
  // data.anti_content = window.get_anti_content();
  // data.use_coupon = data.use_coupon ? 1 : void 0
  return request(
    {
      url: "/api/task/order",
      data,
    },
    options,
    loading
  ).then(async res => {
    if (res.data?.order_amount <= 0) {
      await Promise.allSettled([
        updateOrderInfo({ order_sn: res.data.order_sn })
          .then(res2 => {
            if (res2.data.order_amount) {
              res.data.order_amount = res2.data.order_amount
              res.data.shop_id = res2.data.shop_id
            }
          })
      ])
    }
    return res
  })
}

/**给小号添加地址 */
export function addAddress(
  data: {
    account: string | number;
    name?: string;
    phone?: string;
    province?: string;
    city?: string;
    district?: string;
    address?: string;
    anti_content?: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  const setting = useSetting()
  return request(
    {
      url: "/api/task/addAddress",
      data,
    },
    {
      ...options,
      beforeRequest(config) {
        config.data.anti_content = window.get_anti_content()
        // config.data.filter = setting.address.filterStr
        config.data.filter_address = setting.address.filterStr
        if (!config.data.appoint_address) {
          config.data.appoint_address = setting.address.appoint_address
        }
        console.log(config)
      },
    },
    loading
  );
}

export async function changeSkuQuantity(
  data: Array<{ goodsId: number; quantity: number; skuId: number }>,
  store: Store
) {
  const config = {
    url: "https://mms.pinduoduo.com/vodka/v2/mms/edit/quantity/increase",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: {
      data,
      sourceId: 1,
    },
  }
  return anyRequest(config);
}

export async function getGoodsSkuDetails(store: Store, goodsId: number) {
  const config = {
    url: "https://mms.pinduoduo.com/vodka/v2/mms/query/sku/info",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: { goodsId },
  }
  return anyRequest(config);
}

/**创建私密优惠券 */
export async function createPrivateCoupon(
  store: Store,
  data: { goods_id: number; price: number; batch_desc: string }
) {
  const {goods_id,price,batch_desc } = data
  const time = Date.now();
  function resolveTime(time = Date.now()) {
    const date = new Date(time);
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0);
    date.setMilliseconds(0);
    return date.valueOf();
  }
  const config = {
    url: "https://mms.pinduoduo.com/madrid/create_no_public_goods_batches",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: {
      source_type: 349,
      create_request_list: [
        {
          /*优惠卷名称*/
          batch_desc,
          /*结束时间*/
          batch_end_time: resolveTime(time + 3600 * 1000 * 48) - 1,
          /*开始时间*/
          batch_start_time: resolveTime(time),
          /*优惠卷价格  乘以100*/
          discount: Math.floor(price * 100),
          /*发行量*/
          init_quantity: "1",
          /*发行量*/
          user_limit: 1,
          /*商品ID*/
          goods_id,
          period_type: 1,
        },
      ],
      // phone_code:111114
    },
  }
  return anyRequest(config);
}

/**优惠券列表 */
export async function couponList(store: Store, data: { goods_id: number }) {
  const config = {
    url: "https://mms.pinduoduo.com/madrid/query_mms_mall_batch_list",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: {
      page_num: 1,
      page_size: 50,
      /*筛选 优惠卷类型 2代表商品卷*/
      batch_dimension: "2",
      /*筛选 优惠卷类型 349代表 商品卷/私密卷*/
      source_type: 349,
      /*筛选状态 全部batch_status不需要传 0为生效中 3已结束 4待激活*/
      batch_status: 0,
      goods_list: [data.goods_id] /*筛选商品ID*/,
    },
  }
  return anyRequest(config);
}
/**领取商品私密券 */
export function claimCoupon(data: {
  batch_sn: string;
  account: string | number;
}) {
  return request({
    url: "/api/task/getGoodsCoupon",
    data,
  });
}

/**  市场营销 限时限量购 活动列表 */
export async function marketingList(
  store: Store,
  data: {
    goods_id: number | Array<number>
    /**12限时 3限量 */
    activity_types?: Array<3 | 12>
  }
) {
  const headers = await pddStoreRequestHeader(store)
  const { goods_id } = data;
  const goods_id_list = Array.isArray(goods_id) ? goods_id : [goods_id];
  const config = {
    url: "https://mms.pinduoduo.com/libra-backend/mms/activity/marketing/query",
    method: "post",
    headers,
    data: {
      status_list: [2],
      activity_types: data.activity_types || [3, 12],
      page_no: 1,
      page_size: 10,
      order: 1,
      goods_id_list,
    },
  };
  return anyRequest(config);
}
/**  关闭市场营销活动 */
export async function marketingStop(
  store: Store,
  data: { goods_id: number; activity_id: number }
) {
  const headers = await pddStoreRequestHeader(store)
  const { goods_id, activity_id } = data;
  const config = {
    url: "https://mms.pinduoduo.com/libra-backend/mms/activity/marketing/stop",
    method: "post",
    headers,
    data: { activity_id, goods_id },
  };
  return anyRequest(config);
}

/**限时限量购可参加商品列表 */
export async function limitCouponGoodsList(mall: Store, _data: {
  goods_id_list: number[],
  /**可选？ */
  is_valid?: boolean
  /**仅新品 */
  only_new_goods?: false
  page_no?: number
  page_size?: number
  /**12限时 3限量 */
  query_activity_type: 3 | 12
}) {
  const data: typeof _data = {
    is_valid: true,
    only_new_goods: false,
    page_no: 1,
    page_size: 20,
    ..._data
  }
  const config: AxiosRequestConfig = {
    url: 'https://mms.pinduoduo.com/libra-backend/mms/activity/marketing/goods/search',
    method: 'post',
    headers: await pddStoreRequestHeader(mall),
    data
  }
  return anyRequest(config)
}
/**限时限量购可参加商品sku列表 */
export async function limitCouponGoodsSkuList(mall: Store, data: {
  search_marketing_tool_sku_request_list: Array<{ goods_id: number }>
}) {
  const config = {
    url: 'https://mms.pinduoduo.com/libra-backend/mms/activity/marketing/batch/sku/search',
    method: 'post',
    headers: await pddStoreRequestHeader(mall, { authority: "mms.pinduoduo.com" }),
    data
  }
  return anyRequest(config)
}

/**创建限时限量购 */
export async function createLimitCouponApi(mall: Store, data: {
  call_source?: number,
  batch_stage_activities: Array<{
    stage_activities: Array<{
      goods_unified_activity: boolean;
      activity_name: string;
      /**3限量 12限时 */
      activity_type: number;
      check_low_price: boolean;
      goods_id: number;
      tool_full_channel: string;
      auto_create_after_finished: boolean;
      /**限时 开始时间  秒 当天开始时间*/
      start_time?: number
      /**限时 结束时间  秒 当天结束时间*/
      end_time?: number
      price_list: Array<{
        user_activity_limit: number;
        sku_price_dtos: Array<{
          sku_id: number;
          activity_price: number;
        }>;
        quantity: number;
      }>
      pattern: number;
    }>
  }>
}) {
  if (data.call_source === void 0) {
    data.call_source = 1
  }
  const config = {
    url: "https://mms.pinduoduo.com//libra-backend/mms/activity/marketing/stage/batchCreate",
    method: 'post',
    headers: await pddStoreRequestHeader(mall),
    data
  }
  console.log(config)
  return anyRequest(config)
}


/**（创建商品）获取商品ID */
export async function createGoodsId(store: Store) {
  const headers = await pddStoreRequestHeader(store, { authority: "mms.pinduoduo.com" })
  const config = {
    url: "https://mms.pinduoduo.com/glide/v2/mms/edit/commit/create_new",
    method: "post",
    headers,
    data: {},
  };
  return anyRequest(config);
}
/**创建商品信息 */
export async function createGoodsInfo(
  store: Store,
  data: any
) {
  const headers = await pddStoreRequestHeader(store, {
    "Referer": `https://mms.pinduoduo.com/goods/goods_add/index?id=${data.goods_commit_id}&goods_id=${data.goods_id}&type=edit`,
    "Sec-Ch-Ua": `"Chromium";v="122", "Not(A:Brand";v="24", "Microsoft Edge";v="122"`,
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Fetch-Platform": `"Windows"`,
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": `cors`,
    "Sec-Fetch-Site": `same-origin`,
  })
  const config = {
    url: "https://mms.pinduoduo.com/glide/mms/goodsCommit/action/edit",
    method: "post",
    headers,
    data,
  };
  return anyRequest(config);
}
export async function goodsSubmit(
  store: Store,
  data: { goods_id: number; goods_commit_id: string },
  antiContent: string[]
) {
  const headers = await pddStoreRequestHeader(store, {
    "anti-content": antiContent[0],
    "Referer": `https://mms.pinduoduo.com/goods/goods_add/index?id=${data.goods_commit_id}&goods_id=${data.goods_id}&type=edit`,
    "Sec-Ch-Ua": `"Chromium";v="122", "Not(A:Brand";v="24", "Microsoft Edge";v="122"`,
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Fetch-Platform": `"Windows"`,
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": `cors`,
    "Sec-Fetch-Site": `same-origin`,
  });

  const { goods_commit_id, goods_id } = data;
  const config = {
    url: "https://mms.pinduoduo.com/glide/v2/mms/edit/commit/submit",
    method: "post",
    headers,
    data: {
      goods_commit_id: String(goods_commit_id),
      goods_id,
      is_onsale: true,
      third_type: 0,
      if_allow_submit_mall_checking: true,
      crawlerInfo: antiContent[1],
    },
  };
  return anyRequest(config);
}

export async function antiContentFromWeb() {
  try {
    const a1 = await storeAntiContent()
    const a2 = await storeAntiContent()
    return {
      code: 0,
      data: {
        anti_content1: a1,
        anti_content2: a2,
      },
      msg: ''
    }
  } catch (e) {
    return {
      code: 0,
      data: {
        anti_content1: window.account_anti_content(),
        anti_content2: window.account_anti_content()
      },
      msg: ""
    }
  }

  // return request<{ anti_content1: string, anti_content2: string }>(
  //   {
  //     url: "http://119.188.210.179:90/api/get_anti_content",
  //     encrypt: false,
  //     method: "get",
  //   },
  //   { showErrorMsg: false }
  // );
}
/**创建立减券 */
export async function createMerchantGoods(
  store: Store,
  list: Array<{
    goods_id: number;
    batch_desc: string;
    batch_start_time: number;
    batch_end_time: number;
    discount: number;
    init_quantity: number;
    user_limit: number;
    period_type?: number;
    duration?: number;
    status?: number;
  }>
) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: "https://mms.pinduoduo.com/madrid/merchant/async_create_merchant_goods_batch_v2",
    method: "post",
    headers,
    data: {
      tool_full_channel: "10921_77272_",
      //优惠卷类型
      source_type: 54,
      // create_request_list: [
      //   {
      //     goods_id: 550983730510,
      //     batch_desc: "商品立减券20231114",
      //     //开始时间 商品立减卷：开始时间为当前时间
      //     batch_start_time: 1699891200000,
      //     //结束时间 商品立减卷：最长时间为90天
      //     batch_end_time: 1707667199999,
      //     //券面额
      //     discount: 1000,
      //     //发行张数
      //     init_quantity: 30000,
      //     user_limit: 99,
      //     period_type: 2,
      //     duration: 2,
      //     status: 2,
      //   },
      // ],
      create_request_list: list.map((item) => {
        return {
          period_type: 2,
          duration: 2,
          status: 2,
          ...item,
        };
      }),
      ignore_low_price: false,
    },
  };
  return anyRequest(cloneDeep(config));
}

export async function getCouponList(store: Store, data: {
  batch_dimension?: string
  batch_status: number
  goods_list: string[]
  page_num?: number
  page_size?: number
  source_type?: string
}) {
  // goods_list: string[]
  const headers = await pddStoreRequestHeader(store, { authority: "mms.pinduoduo.com" })
  const config = {
    url: "https://mms.pinduoduo.com/madrid/query_mms_mall_batch_list",
    method: "post",
    headers,
    data: {
      batch_dimension: "",
      // batch_status: 0,
      // goods_list: goods_list.filter((item) => item),
      page_num: 1,
      page_size: 10,
      source_type: "",
      ...data
    },
  };
  return anyRequest(config);
}

/**结束优惠券 */
export async function closeMerchantCoupone(store: Store, data: { batch_id: string }) {
  const headers = await pddStoreRequestHeader(store, { authority: "mms.pinduoduo.com" })
  const config = {
    url: "https://mms.pinduoduo.com/madrid/closeMerchantCouponBatch",
    method: "post",
    headers,
    data,
  };
  return anyRequest(config);
}

/**小号领取商品立减券 */
// export function getMerchantCoupon(
//   data: {
//     coupon_code: string;
//     account: string;
//     mall_id: Store["mallId"];
//   },
//   options?: Options
// ) {
//   return request(
//     {
//       url: "/api/order/getMerchantCoupon",
//       data: {
//         ...data,
//         anti_content: window.get_anti_content(),
//       },
//     },
//     {
//       showErrorMsg: false,
//       ...options,
//     }
//   );
// }
export function getMerchantCouponV2(data: {
  coupon_code: string
  account: string
}, options?: Options, loading?: LoadingOptions) {
  return request(
    {
      url: "/api/order/getMerchantCouponV2",
      data,
    },
    {
      ...options,
      beforeRequest(config) {
        config.data.anti_content = window.get_anti_content()
      },
    },
    loading
  );
}

export async function forbid_edit_info(mall: Store, data: { goods_id: number }) {
  const headers = await pddStoreRequestHeader(mall)
  const config = {
    url: "https://mms.pinduoduo.com/glide/v2/mms/query/goods/forbid_edit_info",
    method: "post",
    headers,
    data
  }
  return anyRequest(config)
}
export async function changeSkuPrice(
  store: Store,
  data: {
    goods_id: number;
    market_price_in_yuan: string;
    market_price: number;
    need_delete_goods_commit?: boolean
    sku_prices: Array<{
      sku_id: number;
      multi_price_in_yuan: string;
      single_price_in_yuan: string;
      multi_price: number;
      single_price: number;
    }>;
  }
) {
  const res = await forbid_edit_info(store, { goods_id: data.goods_id })
  if (res.result) {
    data.need_delete_goods_commit = true
  }
  // await delayPromise(2000)
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: `https://mms.pinduoduo.com/glide/v2/mms/price/adjust/adjust_in_list`,
    method: "post",
    headers,
    data,
  };
  return anyRequest(config);
}

export async function confirmLowPrice(store: Store, data: {
  token_list: string[],
  tool_full_channel?: string
}) {
  const headers = await pddStoreRequestHeader(store, {
    authority: "mms.pinduoduo.com",
  });
  const config = {
    url: "https://mms.pinduoduo.com/madrid/low_price/confirm_low_price",
    method: "post",
    headers,
    data: {
      tool_full_channel: "10921_77271",
      ...data,

    },
  };
  return anyRequest(config);
}

export async function pddStoreGoodsDetails(
  store: Store,
  goods_id: number | string
) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: `https://mms.pinduoduo.com/glide/v2/mms/query/commit/on_shop/detail`,
    method: "post",
    headers,
    data: {
      goods_id,
    },
  };
  return anyRequest(config);
}

export async function pddStoreGoodsDetailsFloorList(
  store: Store,
  data: { goods_id: number; goods_commit_id: number }
) {
  return retry(async (index) => {
    const headers = await pddStoreRequestHeader(store)
    const config = {
      url: `https://mms.pinduoduo.com/gorse/mms/goods/decoration/commit/query/V2`,
      method: "post",
      headers,
      data,
    };

    const res = await anyRequest(config);
    if (index >= 3) {
      return res
    }
    if (res.success && res.result) {
      return res
    } else {
      return Promise.reject(res)
    }
  }, 3)
}

/**获取商品属性 */
export async function getGoodsProperties(store: Store, data: { goods_id: number; goods_commit_id: number, cat_id: number }) {
  const headers2 = await pddStoreRequestHeader(store)
  const { goods_id, goods_commit_id, cat_id } = data
  const property = await anyRequest({
    url: "https://mms.pinduoduo.com/draco-ms/mms/template/mall",
    headers: headers2,
    method: "get",
    params: {
      goodsId: goods_id,
      goodsCommitId: goods_commit_id,
      catId: cat_id
    },
  });
  return property
}
export async function getUpLoadData(
  store: Store,
  data: { goods_id: number; goods_commit_id: number, cat_id: number }
) {
  const { goods_commit_id, goods_id, cat_id } = data;
  const headers1 = await pddStoreRequestHeader(store)
  const goods_property = await anyRequest({
    url: "https://mms.pinduoduo.com/draco-ms/mms/query-goods-property",
    data: {
      goods_id,
    },
    headers: headers1,
    method: "post",
  });
  const headers2 = await pddStoreRequestHeader(store)
  const property = await anyRequest({
    url: "https://mms.pinduoduo.com/draco-ms/mms/template/mall",
    headers: headers2,
    method: "get",
    params: {
      goodsId: goods_id,
      goodsCommitId: goods_commit_id,
      catId: cat_id
    },
  });
  return {
    // decoration:decoration.result,
    goods_property: goods_property.result.goods_properties,
    property: property.result,
  };
}

export function transFromData(data: any) {
  console.log('transFromData', cloneDeep(data))
  return request({
    url: "/api/tool/pddTransform",
    data
  })
}

export async function newGoodsCommitId(store: Store, data: { goods_id: number }) {
  const headers = await pddStoreRequestHeader(store)
  return anyRequest({
    url: "https://mms.pinduoduo.com/glide/v2/mms/edit/commit/create_by_sn",
    method: "post",
    headers,
    data
  })
}