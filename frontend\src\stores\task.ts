import { ElMessage } from "element-plus";
import { defineStore } from "pinia";
import { nanoid } from "nanoid";
import {
  autoSupply,
  changeOrderPrice,
  getApplyInfo,
  getCoupon,
  sendMessageToShop,
  singleBuy,
  storeAntiContent,
  taskDelete,
  taskList,
  taskStart,
  updateOrder,
  updateOrderV2,
  visit as visitRequest,
} from "../apis/page";
import { useSubAccount } from "./pageData";
import { useSetting } from "./setting";
import { getRandomItem } from "../apis/address";
import { delayPromise, random, retry } from "../utils/common";
// import { useAutoApply } from "./autoApply";
import { autoSupply as createAutoSupplyRequest } from "../apis/page";
import { useAutoApply } from "./autoApply";
import { acquireQrCode } from "../apis/pifa";
const get_anti_content = window.get_anti_content;
enum Status {
  "IDLE", //空闲,
  "PENDING", //等待下一次执行
  "LOADING", //加载中 有任务在跑
  "PAUSED", //暂停
}

let loopTid: null | NodeJS.Timer = null;

type RequestItem = {
  task_id: TaskItem["task_id"];
  account?: number | string;
  account_site?: number;
  order_site?: number;
  /**请求次数 */
  requestCount: number;
  /**第几次请求 */
  sort: number;
  history: Array<{
    account_site: number;
    account: number;
    order_site: number;
    requestCount: number;
    res: responseData;
  }>;
  id: ReturnType<typeof nanoid>;
};

type TaskItem = {
  task_id: string;
  /**小号位置 */
  account_site: number;
  /**唯一标识 */
  order_site: number;
  /**下一次执行订单的时间 */
  nextExecTime: number;

  /**加载中 */
  loading: Map<RequestItem["id"], Required<RequestItem>>;
  /**排队中 */
  pending: Map<RequestItem["id"], RequestItem>;
  /**已完成(包括成功/失败) */
  finish: Map<RequestItem["id"], RequestItem>;
  /**状态 */
  status: Status;

  /** 目标请求次数*/
  order_num: number;
  /** 已请求次数*/
  complete_num: number;

  row: anyObj
};

export type { TaskItem, LogItem };

type TaskMap = Map<TaskItem["task_id"], TaskItem>;

type LogItem = {
  time: number;
  type?: "danger" | "primary" | "warning" | "success";
  msg: string;
  task_id?: TaskItem["task_id"];
};

enum SupplyStatus {
  Success,
  Failed,
  Verifing,
}
export const useTask = defineStore("order-task", {
  state() {
    const state: {
      /**接口返回的未完成列表 */
      unCompleteList: anyObj[];
      /**接口返回的已完成列表 */
      completeList: anyObj[];
      /**接口返回的已完成的总数 */
      completeTotal: number;
      /**状态 */
      status: Status;

      /**正在执行的任务 */
      loading: TaskMap;
      /**暂停的任务 */
      paused: TaskMap;
      /**已完成的任务 */
      finish: TaskMap;

      /**正在请求中的请求(一个任务可能有多个请求同时进行) */
      requestPool: Map<RequestItem["id"], Required<RequestItem>>;

      logList: LogItem[];

      requestCountMax: number;

      pointExecTime: number;

      supplyState: Array<{
        goods_id: number;
        shop_id: string;
        status: SupplyStatus;
        list: anyObj[];
      }>;
    } = {
      unCompleteList: [],
      completeList: [],

      completeTotal: 0,
      status: Status.IDLE,

      loading: new Map(),
      paused: new Map(),
      finish: new Map(),

      requestPool: new Map(),
      logList: [],

      requestCountMax: 0,

      /**计时器执行时间 */
      pointExecTime: 0,

      supplyState: [],
    };
    return state;
  },
  getters: {
    taskStatusDes(): string {
      switch (this.status) {
        case Status.IDLE: {
          return "空闲";
        }
        case Status.LOADING: {
          return "正在执行";
        }
        case Status.PAUSED: {
          return "已暂停";
        }
        default: {
          return "出错";
        }
      }
    },
    pageListData() {
      const data: {
        unStart: anyObj[];
        loading: anyObj[];
        paused: anyObj[];
      } = { unStart: [], loading: [], paused: [] };
      this.unCompleteList.forEach((item) => {
        const { task_id } = item;
        if (this.loading.has(task_id)) {
          data.loading.push(item);
        } else if (this.paused.has(task_id)) {
          data.paused.push(item);
        } else {
          data.unStart.push(item);
        }
      });
      return data;
    },
  },
  actions: {
    completeTask(task_id: TaskItem["task_id"]) {
      const ind = this.unCompleteList.findIndex(
        (item) => item.task_id === task_id
      );
      if (this.loading.has(task_id)) {
        this.finish.set(task_id, this.loading.get(task_id)!);
      }
      this.loading.delete(task_id);
      this.paused.delete(task_id);
      if (ind >= 0) {
        this.completeList.unshift(...this.unCompleteList.splice(ind, 1));
      }
      this.completeTotal++;
    },
    addToLog(data: Partial<LogItem>) {
      this.logList.push({
        msg: "",
        ...data,
        time: Date.now(),
      });
    },
    autoSupply(data: { shop_id: string; goods_id: number }) {
      const { shop_id, goods_id } = data;
      const supplyStateItem = this.supplyState.find(
        (item) => item.goods_id == goods_id && item.shop_id == shop_id
      );
      if (!supplyStateItem) {
        return;
      }
      supplyStateItem.status = SupplyStatus.Verifing;
      createAutoSupplyRequest({ mallId: shop_id, goods_id: Number(goods_id) })
        .then((res) => {
          if (res.code) {
            return Promise.reject(res);
          }
          // console.log("自动供货成功", res);
          this.addToLog({
            msg: `商品id${goods_id}设置自动供货成功!`,
            type: "success",
          });

          supplyStateItem.status = SupplyStatus.Success;
          this.addTask(supplyStateItem.list);
          supplyStateItem.list = [];
        })
        .catch((res) => {
          console.log("自动供货失败", res);
          this.addToLog({
            msg: `商品id${goods_id}设置自动供货失败:${res.msg}`,
            type: "danger",
          });
          supplyStateItem.status = SupplyStatus.Failed;
          supplyStateItem.list.forEach((item) => {
            this.addToLog({
              task_id: item.task_id,
              msg: "开始失败：批发商品设置自动供货失败",
              type: "warning",
            });
          });
          supplyStateItem.list = [];
        })
        .finally(() => { });
    },
    createTaskItem(item: (typeof this.completeList)[number]): TaskItem {
      const { task_id, account_site } = item;
      const { complete_num, order_num, order_site } = item;
      const pending: TaskItem["pending"] = new Map();
      for (let i = complete_num + 1; i <= order_num; i++) {
        const id = nanoid();
        pending.set(id, {
          id,
          task_id,
          requestCount: 0,
          sort: i,
          history: [],
        });
      }
      return {
        task_id,
        account_site,
        order_site,
        nextExecTime: 0,
        status: Status.PENDING,
        complete_num,
        order_num,
        loading: new Map(),
        pending,
        finish: new Map(),
        row: item
      };
    },

    addTask(list: typeof this.completeList) {
      list.forEach(async (item) => {
        const {
          task_id,
          account_site,
          complete_num,
          order_num,
          type,
          goods_id,
          shop_id,
          skus
        } = item;
        if (!skus) {
          this.addToLog({
            msg: '任务' + task_id + '已过期，请删除此任务重新创建',
            type:'danger',
            task_id
          })
          return
        }
        const { auto_supply } = useSetting().taskCreate;
        if (this.loading.has(task_id)) {
          return ElMessage.warning({
            message: "检测到已存在的任务,将自动跳过",
            grouping: true,
          });
        }
        if (complete_num >= order_num) {
          this.completeTask(task_id);
          return ElMessage.warning({
            message: "检测到已完成的任务,将自动跳过",
            grouping: true,
          });
        }

        /**自动批发供货 */
        if (auto_supply && type == "pifa") {
          // if()
          const supplyStateItem = this.supplyState.find((supplyStateItem) => {
            return (
              supplyStateItem.goods_id == goods_id &&
              supplyStateItem.shop_id == shop_id
            );
          });
          if (
            supplyStateItem &&
            supplyStateItem.status === SupplyStatus.Success
          ) {
            // this.addToLog({
            //   task_id,
            //   msg:'商品已有自动供货记录-跳过自动供货',
            // })
          } else if (
            supplyStateItem &&
            supplyStateItem.status === SupplyStatus.Verifing
          ) {
            this.addToLog({
              task_id,
              msg: "商品正在进行自动供货-请等待",
            });
            if (
              !supplyStateItem.list.find(
                (_item) => _item.task_id === item.task_id
              )
            ) {
              supplyStateItem.list.push(item);
            }
            return;
          } else if (
            supplyStateItem &&
            supplyStateItem.status === SupplyStatus.Failed
          ) {
            this.addToLog({
              task_id,
              msg: "检测到上次自动供货失败，商品即将重新执行自动供货-请等待",
            });
            supplyStateItem.list.push(item);
            this.autoSupply({
              shop_id,
              goods_id,
            });
            return;
          } else {
            this.addToLog({
              task_id,
              msg: "开始设置自动供货",
            });
            this.supplyState.push({
              shop_id,
              goods_id,
              status: SupplyStatus.Verifing,
              list: [item],
            });
            this.autoSupply({
              shop_id,
              goods_id,
            });
            return;
          }
        }

        if (this.paused.has(task_id)) {
          // 恢复执行
          this.pausedToPending(task_id);
        } else {
          this.addToLog({
            task_id,
            msg: "进入执行队列",
          });
          this.loading.set(task_id, this.createTaskItem(item));
        }
      });

      this.startTasks();
    },
    pausedToPending(task_id: TaskItem["task_id"]) {
      const taskItem = this.paused.get(task_id);
      if (taskItem) {
        this.paused.delete(task_id);
        this.loading.set(task_id, taskItem);
      }
    },

    /**检测执行列表中可以执行的任务并执行 */
    checkAndExecRequest() {
      // this.loading.forEach(item => {
      //   if(item.status === Status.LOADING){
      //     return
      //   }
      //   const currentTime = Date.now()
      //   if(item.nextExecTime)
      // })
      if (this.loading.size) {
        this.status = Status.LOADING;
      }
      let count = 1;
      for (let [task_id, taskItem] of this.loading) {
        if (this.requestCountMax && count++ > this.requestCountMax) {
          return;
        }
        if (this.requestPool.size >= 25) {
          break;
        }
        if (!taskItem.pending.size) {
          if (
            taskItem.complete_num +
            taskItem.pending.size +
            taskItem.loading.size <
            taskItem.order_num
          ) {
            this.loading.set(task_id, this.createTaskItem(taskItem));
          }
          continue;
        }
        const currentTime = Date.now();
        if (taskItem.nextExecTime <= currentTime) {
          this.createRequest(taskItem);
        }
      }
    },

    createRequest(taskItem: TaskItem) {
      const {
        task_id,
        pending,
        loading,
        order_num,
        complete_num,
        order_site,
        account_site,
      } = taskItem;
      if (!pending.size) {
        if (!loading.size) {
          // this.completeTask()
          if (complete_num >= order_num) {
            this.addToLog({
              task_id,
              msg: "已完成的任务，将跳过",
            });
            this.completeTask(task_id);
          } else if (complete_num + pending.size + loading.size >= order_num) {
          } else {
            this.loading.set(task_id, this.createTaskItem(taskItem));
          }
        }
        return;
      }
      const [requestId] = pending.keys();
      const requestItem = pending.get(requestId)!;

      /**页面中的数据 */
      const pageItem = this.unCompleteList.find(
        (item) => item.task_id === task_id
      );
      if (!pageItem) {
        this.addToLog({
          task_id,
          msg: "没有找到原始数据，将移除任务",
          type: "warning",
        });
        this.loading.delete(task_id);
        return;
      }

      // 设置下一次执行时间
      {
        const settingStore = useSetting();
        const { max, min, active } = settingStore.visit.orderDelay;
        let delay = 0.5;
        if (active) {
          delay = random(min, max);
        }
        taskItem.nextExecTime = Date.now() + delay * 1000;
      }

      //检测并分配小号
      if (!requestItem.account) {
        const accountResult = this.getAccount(
          requestItem.account_site || account_site
        );
        if (!accountResult) {
          this.pauseTask([task_id]);
          return;
        } else {
          /**分配小号 */
          requestItem.account = accountResult.data;
          /**更新任务的小号位置 */
          taskItem.account_site = accountResult.ind + 2;
          /** 更新位置时的标识 */
          requestItem.order_site = order_site + 1;
          /**小号位置 */
          requestItem.account_site = accountResult.ind + 2;
          /**更新页面中小号位置 */
          pageItem.account_site = taskItem.account_site;
          pageItem.order_site = requestItem.order_site;
          taskItem.order_site = requestItem.order_site;

          this.addToLog({
            task_id,
            msg: `已成功分配小号${requestItem.account!}`,
          });
        }
      }

      this.requestPool.set(requestId, requestItem as Required<RequestItem>);
      pending.delete(requestId);
      loading.set(requestId, requestItem as Required<RequestItem>);

      const endRequest = () => {
        loading.delete(requestId);

        this.requestPool.delete(requestId);
      };

      Promise.resolve([]).then(async () => {
        requestItem.requestCount++;
        const settingStore = useSetting();
        const {
          nameCode_active,
          nameCode_position,
          nameCode_str,
          addr_active,
          addr_position,
          addr_str,
          filterStr,
          type,
          appoint_address,
        } = settingStore.address;

        let addressData: anyObj = {
          appoint_address: appoint_address || void 0,
        };

        if (type === "diy") {
          const result = await getRandomItem();
          if (result && result.id) {
            Reflect.deleteProperty(result, "id");
            addressData = result;
          } else {
            this.addToLog({
              type: "warning",
              msg: "检测到自定义地址列表为空，已暂停任务!",
            });
            /**放回请求队列 */
            pending.set(requestId, requestItem);
            this.pauseTask([task_id]);
            endRequest();
            return;
          }
        }

        let data: Parameters<typeof taskStart>[0] = {
          sku_id: 0,
          task_id,
          // spec:pageItem.spec,
          account: requestItem.account!,
          account_site: requestItem.account_site!,
          order_site: requestItem.order_site!,
          filter_address: filterStr.replace("，", ","),
          address_cipher: addr_active ? addr_str : void 0,
          address_site: addr_active ? addr_position : void 0,
          name_cipher: nameCode_active ? nameCode_str : void 0,
          name_site: nameCode_active ? nameCode_position : void 0,
          anti_content: get_anti_content(),
          ...addressData,
        };
        // this.addToLog({
        //   msg:data.anti_content
        // })
        //假聊设置
        const { active } = settingStore.chat;
        if (active && requestItem.requestCount <= 1) {
          const chartTxtArr = settingStore.getChatArr();
          // console.log(chartTxtArr)
          chartTxtArr.forEach((msg, index) => {
            this.addToLog({
              task_id,
              msg: `正在发送第${requestItem.sort}次执行的第${index + 1
                }条消息,(小号：${data.account},信息：${msg})`,
            });
            sendMessageToShop({
              shop_id: pageItem.shop_id,
              account: data.account,
              content: msg,
            })
              .then((res) => {
                this.addToLog({
                  task_id,
                  msg: `第${requestItem.sort}次执行的第${index + 1
                    }条消息发送成功`,
                  type: "success",
                });
              })
              .catch((res) => {
                this.addToLog({
                  task_id,
                  msg: `第${requestItem.sort}次执行的第${index + 1
                    }条消息发送失败:${res.msg || ""}`,
                  type: "danger",
                });
              });
          });
        }

        if (requestItem.requestCount <= 1) {
          const { active, min, max } = settingStore.visit.visitDetails;
          if (active) {
            const delay = random(min, max);
            this.addToLog({
              task_id,
              msg: `第${requestItem.sort}次执行浏览商品详情${delay}秒`,
            });
            await delayPromise(delay * 1000);
          }
        }

        // 获取优惠券
        {
          if (
            requestItem.requestCount == 1 &&
            pageItem.setting.includes("coupon") &&
            pageItem.use_coupon &&
            pageItem.coupon_code
          ) {
            await new Promise((resolve) => {
              getCoupon(
                {
                  task_id: data.task_id,
                  account: data.account,
                },
                { showErrorMsg: false }
              )
                .then(() => {
                  this.addToLog({
                    task_id,
                    msg: "领取优惠券成功",
                    type: "success",
                  });
                  data.use_coupon = 1;
                })
                .catch((res) => {
                  this.addToLog({
                    task_id,
                    msg: `领取优惠券失败:${res.msg}`,
                    type: "danger",
                  });
                  data.use_coupon = 0;
                })
                .finally(() => {
                  resolve(true);
                });
            });
          }
        }

        // 执行
        this.addToLog({
          task_id,
          msg: `第${requestItem.sort}次执行第${requestItem.requestCount}次开始`,
        });
        // 执行访客
        {
          const { max, min, active } = settingStore.visit.visitCount;
          if (active && requestItem.requestCount <= 1) {
            const count = random(min, max + 1);
            this.addToLog({
              task_id,
              msg: `将对任务执行${count}次访客任务。`,
            });
            this.execGoodsVisit(pageItem.goods_id, count);
          } else {
            // this.addToLog({
            //   task_id,
            //   msg: "暂未开启访客功能。已跳过！",
            // });
          }
        }
        const skus: Array<{ skuId: number, spec: string }> = JSON.parse(pageItem.skus)
        let sku = Number(pageItem.sku);
        let spec = pageItem.spec
        if (!sku) {

          const skuItem = skus[random(0, skus.length)] || skus[0]
          sku = skuItem.skuId;
          spec = skuItem.spec;
        }
        data.sku_id = sku;
        // data.spec = spec;
        // console.log(data);
        // 执行任务
        taskStart(data, {
          isStop: () => !this.loading.has(requestItem.task_id),
          beforeRequest: async (config) => {
            config.data.anti_content = get_anti_content();
            config.data.store_anti_content = await storeAntiContent()
            if (taskItem.row.type === 'pifa') {
              config.data.singleBuy = void 0
              await new Promise(async resolve => {
                try {
                  await retry(async () => {
                    const createOrderItemList = [{
                      goodsId: taskItem.row.goods_id,
                      skuId: sku,
                      skuNum: 2
                    }]
                    let singleBuyData: any = void 0
                    await Promise.allSettled([
                      acquireQrCode({
                        "subScene": 1,
                        "createOrderDTO": {
                          "orderType": 1,
                          createOrderItemList
                        }
                      })
                        .then(res => {
                          if (res.success && res.result) {
                            singleBuyData = {
                              result: {
                                curl: 'https://mobile.yangkeduo.com/transac_order_coupon.html?_t_timestamp=transac_volume_checkout&secret_key=' + res.result.secretKey,
                                qrKey: res.result.secretKey
                              }
                            }
                          }
                        })
                      ,
                      singleBuy({
                        createOrderItemList
                      })
                        .then(res => {
                          if (res.success && res.result && res.result.curl && res.result.qrKey) {
                            singleBuyData = res
                          }
                        })
                    ])
                    if (singleBuyData) {
                      config.data.singleBuy = singleBuyData
                    } else {
                      return Promise.reject()
                    }
                  }, 50)
                  resolve(true)
                } catch (e) {
                  console.log(e)
                  resolve(true)
                }
                resolve(true)
              })
              // if (!config.data.singleBuy) {
              //   console.log('未得到', config)
              // }
            }
          }
        })
          .then(async (res) => {
            try {
              // console.log(res);

              // 成功,移出请求队列
              endRequest();
              pageItem.complete_num++;
              taskItem.complete_num++;
              this.addToLog({
                task_id,
                msg: `第${requestItem.sort}次执行完成`,
                type: "success",
              });
              /**记录错误信息 */
              requestItem.history.push({
                account: data.account as number,
                account_site: data.account_site,
                res,
                order_site: data.order_site,
                requestCount: requestItem.requestCount,
              });
              taskItem.finish.set(requestItem.id, requestItem);

              // const { auto_change_price } = pageItem;
              const { auto_change_price } = useSetting().taskCreate;
              if (auto_change_price) {
                this.changeOrderPrice(res.data);
              } else {
                this.execAutoApply(res.data?.order_sn);
              }

              // 当前任务已完成
              if (!loading.size && !pending.size) {
                // const orderAuto = useOrderAuto();

                // orderAuto.addOrder(res.data, pageItem);
                if (taskItem.complete_num >= taskItem.order_num) {
                  this.completeTask(task_id);
                  this.paused.delete(task_id);
                }
                this.loading.delete(task_id);
                if (!this.paused.has(task_id)) {
                  this.addToLog({
                    task_id,
                    msg: `已执行完成！`,
                    type: "success",
                  });
                }
              }
            } catch (e) { }
          })
          .catch((res) => {
            console.log(res, 'taskStart-error')
            /**记录错误信息 */
            requestItem.history.push({
              account: data.account as number,
              account_site: data.account_site,
              res,
              order_site: data.order_site,
              requestCount: requestItem.requestCount,
            });

            // 出错
            // 将小号重新放回请求队列
            // console.log(res);
            // console.log('error',res)
            endRequest();
            let msg = res?.msg || "网络超时";
            if(msg.includes('商品信息变更') && taskItem.row.type === 'pifa'){
              msg += ';批发下单请确认商品已设置供货'
            }
            this.addToLog({
              task_id,
              msg: `第${requestItem.sort}次执行第${requestItem.requestCount
                }次失败:(${msg})`,
              type: "danger",
            });
            if (true || res.code === 1) {
              if (res?.msg?.includes("任务已完成")) {
                this.paused.delete(task_id);
                this.completeTask(task_id);
              } else if (res?.msg.includes("小号")) {
                // item.requestList.push({
                //   count: 0,
                // });
                requestItem.account = void 0;
                requestItem.account_site = void 0;
                requestItem.order_site = void 0;
                requestItem.requestCount = 0;
                pending.set(requestId, requestItem);
              } else if (res.msg.includes("绑定电脑地址错误")) {
                this.pauseTask([task_id]);
                ElMessage.warning({
                  message: res.msg,
                  grouping: true,
                });
              //} else if (requestItem.requestCount < 10) {//------下单次数限制，修改为5次，防止过多导致小号出问题
              } else if (requestItem.requestCount < 5) {
                // item.requestList.push(requestItem);
                pending.set(requestId, requestItem);
              } else {
                this.addToLog({
                  task_id,
                  msg: `执行失败,将暂停任务`,
                  //msg: `有执行连续失败10次,将暂停任务`,
                  type: "warning",
                });
                this.pauseTask([task_id]);
              }
            } else if (res.code > 1) {
              this.addToLog({
                task_id,
                msg: `有执行遭遇未知错误，将暂停此任务`,
                type: "warning",
              });
              if (!this.paused.has(task_id)) {
                return;
              }
              this.pauseTask([task_id]);
            }
          })
          .finally(() => { });
      });
    },

    async changeOrderPrice(orderInfo: anyObj,logFn?: Function) {
      const addToLog  = logFn || this.addToLog;
      // console.log(orderInfo)
      const { type, order_amount, shop_id, shop_name, order_sn, id } =
        orderInfo;
      const settingStore = useSetting();
      const { pifaTargetPrice, othersTargetDiscount } = settingStore.taskCreate;
      let goodsDiscount: number;
      if (type === "pifa") {
        goodsDiscount = Number(order_amount - pifaTargetPrice * 100);
      } else {
        goodsDiscount = Number(order_amount * (10 - othersTargetDiscount)) / 10;
      }
      addToLog({
        msg: `订单${order_sn},将执行改价操作`,
      });
      await delayPromise(3000);
      changeOrderPrice({
        store_id: shop_id,
        shop_name: shop_name,
        goodsDiscount,
        order_sn: order_sn,
      })
        .then((res) => {
          // console.log("改价成功", res);
          addToLog({
            type: "success",
            msg: `订单${order_sn},改价成功`,
          });

          this.execAutoApply(order_sn);
        })
        .catch((res) => {
          addToLog({
            type: "danger",
            msg: `订单${order_sn},改价失败:${res.msg}`,
          });
          const { chance } = settingStore.pay;
          if (chance === "immediate") {
            addToLog({
              msg: "改价失败不会启动自动支付",
              type: "warning",
            });
          }
        })
        .finally(async () => {
          await delayPromise(1000)
          updateOrderV2(
            {
              ids: order_sn,
            },
            { showErrorMsg: false }
          )
        });
    },
    execAutoApply(order_sn: string,logFn?:Function) {
      if (!order_sn) {
        return;
      }
      const addToLog = logFn || this.addToLog;
      const settingStore = useSetting();
      // 自动支付
      const { chance, zfb_pass } = settingStore.pay;

      if (chance !== "immediate") {
        return;
      }
      if (!zfb_pass) {
        addToLog({
          msg: "没有设置支付密码，不会开启自动支付",
          type: "warning",
        });
        return;
      }
      addToLog({
        msg: `订单${order_sn},开始获取支付链接`,
      });
      getApplyInfo({
        order_sn,
      })
        .then((res) => {
          const autoApply = useAutoApply();
          // console.log('获取支付链接成功',res.data)
          addToLog({
            msg: `订单${order_sn},已进入自动支付队列`,
          });
          autoApply.addToPendding([
            {
              ...res.data,
              type: "auto",
            },
          ]);
        })
        .catch((e) => {
          addToLog({
            msg: `订单${order_sn},获取支付链接失败，不会自动支付`,
          });
        });
    },
    /** 执行访客*/
    execGoodsVisit(goods_id: number | string, count: number) {
      visitRequest(
        {
          goods_id,
          num: count,
        },
        { showErrorMsg: false }
      );
      // function visitRequestRepeat(current: number) {
      //   console.log("执行访客", goods_id);
      //   visitRequest(
      //     {
      //       goods_id,
      //     },
      //     { showErrorMsg: false }
      //   ).finally(() => {
      //     // console.log(`还剩${current}次`)
      //     if (current > 0) {
      //       visitRequestRepeat(current - 1);
      //     }
      //   });
      // }

      // visitRequestRepeat(count - 1);
    },

    startTasks() {
      if (!this.loading.size) {
        // 空闲
        this.addToLog({
          msg: "执行队列为空，等待新任务",
          type: "primary",
        });
        this.status = Status.IDLE;
        return;
      }
      // if(this.status !== Status.IDLE && this.status !== Status.LOADING)
      if (this.status === Status.PAUSED) {
        // 暂停
        this.addToLog({
          msg: "执行队列已暂停",
          type: "warning",
        });
        return;
      }
      // console.log("startTasks");
      loopTid && clearTimeout(loopTid);
      if (this.status === Status.IDLE || this.status === Status.LOADING) {
        loopTid = setTimeout(() => {
          loopTid = null;
          this.pointExecTime = Date.now();
          this.checkAndExecRequest();
          this.startTasks();
        }, 1000);
      }
    },
    // 获取小号
    getAccount(start: number) {
      const accountStore = useSubAccount();

      const result = accountStore.getAccount(start - 1 < 0 ? 0 : start - 1);

      if (result.status) {
        return result;
      } else {
        this.addToLog({
          msg: `分配小号出错：${result.msg}`,
          type: "danger",
        });
        this.status = Status.PAUSED;
      }
    },

    pauseTask(list: Array<TaskItem["task_id"]>) {
      list.forEach((task_id) => {
        const taskItem = this.loading.get(task_id);
        if (taskItem) {
          this.loading.delete(task_id);

          this.paused.set(task_id, taskItem);

          this.addToLog({
            task_id,
            msg: `已暂停`,
            type: "warning",
          });
        }
      });
      // if (list.length >= this.loading.size + this.pending.size) {
      //   this.status = Status.PAUSED;
      // }
      if (!this.loading.size) {
        this.status = Status.IDLE;
      }
    },

    taskAction(
      action: "pause" | "start" | "delete" | "reStart",
      list: anyObj[],
      getListParams?: { limit: number; page: number }
    ) {
      switch (action) {
        case "start": {
          // console.log(action,list)
          this.addTask(list);
          break;
        }
        case "pause": {
          this.pauseTask(list.map((item) => item.task_id));
          break;
        }
        case "reStart": {
          list.forEach((item) => {
            const taskItem = this.paused.get(item.task_id);
            if (taskItem) {
              this.loading.set(item.task_id, taskItem);
            }
            this.paused.delete(item.task_id);
          });
          this.status = Status.LOADING;
          this.startTasks();
          break;
        }
        case "delete": {
          taskDelete({
            task_id: list
              .map((item) => {
                const { task_id } = item;
                if (this.paused.has(task_id)) {
                  this.paused.delete(task_id);
                } else if (this.loading.has(task_id)) {
                  ElMessage.warning("当前任务正在执行不可删除");
                  return "";
                }
                return item.task_id;
              })
              .filter((item) => item)
              .join(","),
          }).then(() => {
            this.getTaskList(getListParams);
          });
          break;
        }
      }
    },

    getEnumStatus() {
      return Status;
    },

    getTaskList(
      data: { page: number; limit: number } = { page: 1, limit: 20 }
    ) {
      return taskList(data).then((res) => {
        console.log("taskList", res, data);
        this.unCompleteList = res.data.unfinished;
        this.completeList = res.data.finish.data;
        this.completeTotal = res.data.finish.total;

        this.unCompleteList.forEach((item) => {
          const { task_id } = item;
          if (
            item.complete_num > 0 &&
            item.complete_num < item.order_num &&
            !this.paused.has(task_id) &&
            !this.loading.has(task_id)
          ) {
            this.paused.set(item.task_id, this.createTaskItem(item));
          }
          if (this.paused.has(task_id) && item.complete_num >= item.order_num) {
            this.paused.delete(task_id);
          }
        });
        return res;
      });
    },
  },
});
