# 退款管理页面"添加选中商品"功能实现总结

## 实现概述

已成功在退款管理页面的商品弹窗中实现"添加选中商品"功能，当用户选中商品并点击按钮时，会调用后端API并进行参数加密处理。

## 主要修改

### 1. 导入依赖
```typescript
import request from '/@/apis/page';
```

### 2. 实现核心函数
```typescript
const handleAddSelectedGoods = async () => {
  // 检查是否选中商品
  if (goodsDialog.selectedGoods.length === 0) {
    ElMessage.warning('请先选择要添加的商品');
    return;
  }

  try {
    // 构建请求参数
    const goods = goodsDialog.selectedGoods.map(item => {
      const selectedSku = item._sku_select_list?.[0];
      
      if (!selectedSku) {
        throw new Error(`商品 ${item.goods_name} 未选择SKU`);
      }

      return {
        goods_name: item.goods_name,
        goods_id: item.id.toString(),
        goods_price: (selectedSku.groupPrice / 100).toFixed(2),
        jifen: (selectedSku.groupPrice / 100).toFixed(2)
      };
    });

    // 调用API
    const response = await request({
      url: '/api/ptorder/add',
      method: 'post',
      data: { goods },
      encrypt: true // 使用项目统一的加密处理
    });

    // 处理成功响应
    if (response.code === 0) {
      ElMessage.success(`成功添加 ${goods.length} 个商品到退款管理`);
      addOperationLog({
        action: '添加商品',
        status: `成功添加${goods.length}个商品`
      });
      goodsDialog.selectedGoods = [];
      await getRefundList();
    } else {
      throw new Error(response.msg || '添加商品失败');
    }

  } catch (error) {
    console.error('添加选中商品失败:', error);
    ElMessage.error(error instanceof Error ? error.message : '添加商品失败，请重试');
    addOperationLog({
      action: '添加商品',
      status: '添加失败'
    });
  }
};
```

## 技术要点

### 1. 参数加密
- 使用项目统一的 `encrypt: true` 配置
- 通过 `request` 函数自动处理加密
- 加密逻辑在后端（通过ipc）完成

### 2. 数据格式转换
- 商品ID: `number` → `string`
- 价格: 分 → 元 (`groupPrice / 100`)
- 积分: 暂时等于价格（可根据业务调整）

### 3. 错误处理
- 未选中商品时提示用户
- SKU未选择时抛出具体错误
- API调用失败时显示友好错误信息
- 所有操作都记录在日志中

### 4. 用户体验
- 成功后清空选中状态
- 自动刷新退款列表
- 显示操作结果消息
- 记录详细的操作日志

## API接口规范

**请求地址**: `POST /api/ptorder/add`

**请求参数**:
```json
{
  "goods": [
    {
      "goods_name": "商品名称",
      "goods_id": "商品ID",
      "goods_price": "商品价格",
      "jifen": "所需积分"
    }
  ]
}
```

**响应格式**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {}
}
```

## 使用流程

1. 用户打开退款管理页面
2. 点击"获取商品"按钮
3. 选择店铺并获取商品列表
4. 勾选需要添加的商品
5. 确保选中的商品都已选择SKU
6. 点击"添加选中商品"按钮
7. 系统自动处理并显示结果

## 注意事项

- 参数会自动进行加密处理
- 必须选择商品才能执行添加操作
- 每个商品必须选择对应的SKU
- 操作结果会记录在操作日志中
- 成功后会自动刷新退款列表
