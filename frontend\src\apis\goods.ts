import { ipc } from "./config";

/**
 * 读取记录列表
 * @param data
 * @returns
 */
export function getReadList(data: { page: number; limit: number;goods_id?:goodsReadRecord['goods_id'] }):Promise<{
  total:number
  list:goodsReadRecordInDateBase[]
}> {
  return ipc.invoke("controller.goods.getReadList", data);
}


// 添加一条记录
export function addGoodsRecord(data: Omit<goodsReadRecord, "id" | "add_time">) {
    const _data:anyObj = {...data}
    _data.skus = JSON.stringify(data.skus)
    _data.group_order_ids = JSON.stringify(data.group_order_ids)
    _data.group_id = JSON.stringify(data.group_id)
  return ipc.invoke("controller.goods.addGoodsRecord", {
    ..._data,
    add_time: Date.now(),
  })
  .then(res => {
    // console.log(res)
    if(res.code == 2){
        // console.log(_data)
        ipc.invoke('controller.goods.updateGoods',{..._data,add_time: Date.now()})
        .then(res => {
            console.log('update res')
        })
    }
    if(res.code){
        return Promise.reject(res)
    }else{
        return res
    }
  })
}
// 删除一条记录
export function delGoodsRecord(goods_id: goodsReadRecord["goods_id"]) {
  return ipc.invoke("controller.goods.delGoodsRecord", goods_id);
}
