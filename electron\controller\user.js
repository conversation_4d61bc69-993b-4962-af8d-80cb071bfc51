const { Controller } = require("ee-core");
const Storage = require("ee-core").Storage;
const { isDev, userAgent } = require('../config/config.app');
const { BrowserWindow } = require("electron/main");
const userJson = Storage.JsonDB.connection("user");
const path = require("path");
function saveUserInfo(data) {
  userJson.setItem("userInfo", data);
}
function getUserInfo() {
  return userJson.getItem("userInfo");
}
const pddVerify = {
  verify_auth_token: '',
  verify_auth_token_cb: null,
  verify_auth_token_win: null,
}
class UserController extends Controller {
  constructor(ctx) {
    super(ctx);

  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  async saveUserInfo(data) {
    saveUserInfo(data);
  }

  async userInfo() {
    return getUserInfo();
  }

  // 身份验证
  async auth() {
    let response = await this.service.request.auth();
    return response;
  }

  async setToken(token) {
    return this.service.request.setToken(token)
  }
  openPddVerify({ verify_auth_token }) {
    if (pddVerify.verify_auth_token_win) {
      pddVerify.verify_auth_token_win.close()
      pddVerify.verify_auth_token_cb && pddVerify.verify_auth_token_cb(false)
      pddVerify.verify_auth_token_win = null
      pddVerify.verify_auth_token_cb = null
      pddVerify.verify_auth_token = ''
    }
    const win = new BrowserWindow({
      width: 800, height: 600, resizable: false,
      webPreferences: { nodeIntegration: true, contextIsolation: false },
      title: '验证',
      alwaysOnTop: true,
      parent: this.app.electron.mainWindow,
      modal: true 
    })
    const url = isDev ? 'http://localhost:2341/#/pddVerify' : path.resolve(__dirname, "../../dist/index.html") + '#/pddVerify'
    win.loadURL(url, { userAgent })
    win.on('close', () => {
      if (pddVerify.verify_auth_token_cb) {
        pddVerify.verify_auth_token_cb(false)
      }
      pddVerify.verify_auth_token_win = null
      pddVerify.verify_auth_token_cb = null
      pddVerify.verify_auth_token = ''
    })
    pddVerify.verify_auth_token = verify_auth_token
    pddVerify.verify_auth_token_win = win
    return new Promise(resolve => {
      pddVerify.verify_auth_token_cb = resolve
    })
  }
  async get_verify_auth_token() {
    return pddVerify.verify_auth_token
  }
  verify_auth_token_result({ result }) {
    if (pddVerify.verify_auth_token_cb) {
      pddVerify.verify_auth_token_cb(result)
    }
    pddVerify.verify_auth_token_win && pddVerify.verify_auth_token_win.close()
  }

}

UserController.toString = () => "[class UserController]";
module.exports = UserController;
