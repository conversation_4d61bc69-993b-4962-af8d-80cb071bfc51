<template>
  <div class="eval-coll">
    <collectCommentVue ref="collectCommentEl" @log="(data) => log(data)" @finally="async ({ userSelectFolders }) => {
      addToUserSelectFolders(userSelectFolders)
      await ElMessageBox({
        title: '提示',
        message: '下载已完成，是否使用下载内容填充订单?',
        showCancelButton: true
      })
      inserComment(userSelectFolders)
    }" />
    <ProxyCollect ref="ProxyCollectEl" @down="(data) => collectCommentEl?.openConfigDialog(data)" />
    <div class="wrapper">
      <div class="left">
        <el-tabs type="border-card" :stretch="true" v-model="state.tabActive">
          <el-tab-pane label="文件夹批量上评" name="comment"></el-tab-pane>
          <el-tab-pane label="采集自定义上评价" name="collect"></el-tab-pane>
        </el-tabs>
        <div class="container collect" v-show="state.tabActive === 'collect'">
          <el-input placeholder="请输入商品ID或链接" v-model="state.goodsID" @change="goodsIdChange"
            @keyup.enter="goodsIdChange">
            <template #prepend>
              <el-select placeholder="采集模式" style="width: 115px" size="default" v-model="state.collectMode">
                <el-option label="普通采集" value="normal" />
                <!-- <el-option label="多多批发采集" value="pifa " /> -->
              </el-select>
            </template>
          </el-input>
          <p>
            <span class="label">采集数量</span>
            <el-input-number v-model="state.count" :precision="0" :min="20" :controls="false"
              class="m-l-5 m-r-5"></el-input-number>
            <!-- <el-radio-group v-model="state.collectType">
              <el-radio-button label="txt-img">文字图片</el-radio-button>
              <el-radio-button label="txt">仅文字</el-radio-button>
            </el-radio-group> -->
          </p>
          <p>
            <el-button type="success" v-blur
              :disabled="buttonLoading.collect || !currentGoods.infos || !currentUrl.isGoodsDetailsPage"
              :loading="buttonLoading.collect" @click="commentCollect">开始采集</el-button>
            <el-button type="success" @click="() => {
              if (state.goodsID) {
                copyStr('https://mobile.yangkeduo.com/goods.html?goods_id=' + state.goodsID)
              }
              ProxyCollectEl?.start()
            }">微信解析采集</el-button>
          </p>
          <el-scrollbar>
            <div class="log-container">
              <p v-for=" item in state.comment_list ">{{ item.comment }}</p>
            </div>
          </el-scrollbar>
          <p>
            <span>共{{ state.comment_list.length }}条评价 </span>
            <!-- <el-divider direction='vertical'></el-divider> -->
            <el-space>
              <el-link @click="saveCollectedComment" :underline="false" type="success"
                :disabled="!state.comment_list.length">保存评价</el-link>
              <el-link @click="state.comment_list.length = 0" :underline="false" type="danger"
                :disabled="!state.comment_list.length">清空</el-link>
            </el-space>
            <!-- <el-link :underline="false" type="primary" :disabled="!state.folderPath"
              @click="openFileWindow(state.folderPath)">查看文件夹</el-link> -->
          </p>
          <p>
            <el-checkbox v-model="state.ignoreCommentedOrder" label="已有评价信息的订单不自动填充"></el-checkbox>
            <el-checkbox v-model="state.deleteCommentedFolder" label="删除评价成功文件夹"></el-checkbox>
          </p>
          <p>
            <span class="label">分配方式：</span>
            <el-radio-group v-model="state.comment_type">
              <el-radio-button label="random">随机评</el-radio-button>
              <el-radio-button label="order">顺序评</el-radio-button>
            </el-radio-group>
          </p>
        </div>

        <div class="container comment" v-show="state.tabActive === 'comment'">
          <div class="scrollar-container">
            <el-scrollbar>
              <div class="log-container folder-container" v-drop @drop="dragFolders">
                <el-popover trigger='contextmenu' :visible="state.userFolderOption == item.path"
                  popper-class="folder-options-popover" :show-arrow="false" placement="right" :offset="-50"
                  v-for=" item in state.userSelectFolders ">
                  <template #reference>
                    <div class="folder" @dragstart="dragFoldersToOrder($event, item)"
                      @contextmenu="folderOptions($event, item)"
                      :class="{ active: item.path == state.userSelectFolder?.path, used: usedFolders.has(item.path) }"
                      @click="state.userSelectFolder = item">
                      <Icon href="icon-file"></Icon>
                      <p class="file-name">{{ item.name }}</p>
                      <span class="used-mask" v-show="usedFolders.has(item.path)">已使用（{{
                        usedFolders.get(item.path)! }}）</span>
                    </div>
                  </template>
                  <ul class="folder-options">
                    <li @click="userFoldersAction('open')">打开</li>
                    <li @click="userFoldersAction('delete')">删除</li>
                  </ul>
                </el-popover>
                <div class="folder add" @click="selectFolders()">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <p>
            <el-link type="success" :underline="false" :disabled="!state.userSelectFolders.length"
              @click="inserComment()">使用以上文件夹评价({{ state.userSelectFolders.length }})</el-link>
            <el-link :underline="false" type="danger" @click="() => {
              deleteAllUserFolders()
            }
              ">清空</el-link>
            <!-- <span>{{ state.userSelectFolder?.name }} </span> -->
            <!-- <el-divider direction='vertical'></el-divider> -->
            <el-link :underline="false" type="primary" :disabled="!state.userSelectFolder"
              @click="openFileWindow(state.userSelectFolder!.path)">查看文件夹</el-link>
          </p>
          <p>
            <el-checkbox v-model="state.ignoreCommentedOrder" label="已有评价信息的订单不自动填充"></el-checkbox>
            <el-checkbox v-model="state.deleteCommentedFolder" label="删除评价成功文件夹"></el-checkbox>
          </p>
          <p>
            <span class="label">分配方式：</span>
            <el-radio-group v-model="state.comment_type">
              <el-radio-button label="random">随机评</el-radio-button>
              <el-radio-button label="order">顺序评</el-radio-button>
            </el-radio-group>
          </p>
        </div>
      </div>
      <div class="right">
        <header>订单列表({{ state.commentOrderList.length }})
          <el-button v-blur type="danger" plain class="clear-form-button"
            @click="clearOrderCommentInfo()">清空所有</el-button>
          <el-button type="success" @click="diyCommentState.dialog = true">自定义评价和图片</el-button>
          <el-button @click="insertPreFolder">使用商品预设评价文件夹</el-button>
          <el-button @click="checkRepeat">评论去重</el-button>
        </header>

        <div class="list-container">
          <vxe-list :data="state.commentOrderList" :height="546">
            <template #default="{ items }">
              <el-form v-for=" item in items  " v-drop label-width="140px" @drop="drop($event, item)">

                <el-button v-blur type="danger" plain class="clear-form-button"
                  @click="clearOrderCommentInfo([item])">清空</el-button>

                <div class="commented" v-if="item.commented">

                  <div class="mask"></div>
                  <div class="txt">已评价</div>

                </div>
                <div class="row">
                  <el-form-item :label="`(${item.index})订单号：`">
                    {{ item.order.order_sn }}
                  </el-form-item>
                  <!-- <el-divider direction="vertical"></el-divider> -->
                  <el-form-item label="产品ID："> {{ item.order.goods_id }} </el-form-item>
                </div>

                <el-form-item label="规格：">

                  <div class="text-overflow" title="item.order.sku_spec">
                    {{ item.order.sku_spec }}
                  </div>

                </el-form-item>

                <el-form-item label="评价图片：">
                  <el-space :size="8" :wrap="true">
                    <div class="img-container" v-for=" img, index in item.imgs ">
                      <div class="close" @click="() => {
                        item.imgs.splice(index, 1)
                      }
                        ">
                        <icon href="icon-close"></icon>
                      </div>
                      <el-image :preview-teleported="true" :initial-index="index"
                        :preview-src-list="item.imgs.map((_img: string) => showSystemImg(_img))"
                        :src="showSystemImg(img)"></el-image>
                    </div>
                    <el-button @click="addOrderImg(item)" :disabled="item.imgs.length >= 6">导入图片</el-button>
                    <span>[{{ item.imgs.length }}张](加视频最大6张)</span>
                  </el-space>
                </el-form-item>

                <el-form-item label="评价视频：">
                  <ScreenVideo @action="(action) => {
                    if (action === 'delete') {
                      item.videos.splice(index, 1)
                    }
                  }
                    " v-for="( video, index ) in item.videos " :src="showSystemImg(video)" />
                  <span class="danger notice" v-if="item.videos.length">多个视频只取第一个视频</span>
                </el-form-item>

                <el-form-item label="文件夹：">
                  <el-link type="primary" :underline="false" @click="openFileWindow(item.folderName)">{{ item.folderName
                    }}</el-link>
                </el-form-item>

                <el-form-item label="评语：">
                  <el-input type="textarea" :rows="5" v-model="item.comment"></el-input>
                </el-form-item>

                <el-form-item label=" ">
                  <el-checkbox label="匿名" v-model="item.anonymous"></el-checkbox>
                  <el-checkbox label="同步到拼小圈" v-model="item.timeline_sync_type"></el-checkbox>
                </el-form-item>
              </el-form>
            </template>
          </vxe-list>
        </div>
        <footer class="bottom">
          <div class="config">
            <el-checkbox @change="anonymousAllChange" label="匿名" v-model="options.anonymousAll"
              :indeterminate="options.anonymousi_ideterminate"></el-checkbox>
            <el-checkbox @change="pxqChange" label="拼小圈" v-model="options.pxq_All"
              :indeterminate="options.pxq_ideterminate"></el-checkbox>
            <el-checkbox label="仅5星好评" v-model="options.only5Stars"></el-checkbox>
            <div class="cycle">
              同时评价
              <el-input-number v-model="options.batch_count" :min="1" :max="30" :precision="0"
                :controls="false"></el-input-number>
              单
            </div>
            <el-button type="primary" v-blur :disabled="!state.commentOrderList.length || buttonLoading.comment"
              @click="startComment" :loading="buttonLoading.comment">开始评价</el-button>

            <el-button type="danger" v-blur :disabled="!buttonLoading.comment" @click="stopComment">
              停止评价
            </el-button>
          </div>
          <div class="progress">
            <el-progress :percentage="commentPercentage" />
            <el-space>
              <span class="success">成功：{{ `${commentState.success} ` }}</span>
              <span class="danger">失败：：{{ `${commentState.failed} ` }}</span>
            </el-space>
          </div>
        </footer>
      </div>
    </div>

    <el-dialog title="自定义评价和图片" :close-on-press-escape="false" :append-to-body="true" v-model="diyCommentState.dialog"
      class="diy-comment-dialog">
      <el-form label-position="top">
        <el-form-item>
          <template #label>
            <span class="m-r-10">{{ `评价集合(${diyComments.length})` }} </span>
            <el-button type="primary" @click="selectDiyCommentTxt">选择文件填充</el-button>
            <el-button type="success" @click="() => {
              getCommonComment()
                .then(res => {
                  res && (diyCommentState.commnetStr = res)
                })
            }
              ">植入通用评价</el-button>
            <el-button plain type="danger" @click="diyCommentState.commnetStr = ''">清空</el-button>
          </template>
          <el-input v-drop @drop="diyCommentTxTdrop" type="textarea" v-model="diyCommentState.commnetStr" :rows="5"
            resize="none" placeholder="换行隔开"></el-input>
        </el-form-item>
        <el-form-item class="img-form-item">
          <template #label>
            <span class="m-r-10">{{ `图片集合(${diyCommentState.imgs.length})` }} </span>
            <el-button type="primary" @click="selectDiyCommentImgs">选择图片文件(jpg,png,jpeg)</el-button>
            <el-button plain type="danger" @click="diyCommentState.imgs.length = 0">清空</el-button>
          </template>
          <el-scrollbar :height="200">
            <div class="imgs-container">
              <div class="item" v-for=" item, index in diyCommentState.imgs " :key="item">
                <el-image :preview-src-list="diyCommentState.imgs.map(item => showSystemImg(item))"
                  :initial-index="index" :src="showSystemImg(item)" :alt="item"></el-image>
                <el-link :underline="false" type="danger" @click="deleteDiyCommentImage(item)">删除</el-link>
              </div>
            </div>
          </el-scrollbar>

        </el-form-item>
        <div class="row">
          <el-form-item label="填充方式">
            <template #label>
              <span>填充方式</span>
            </template>
            <el-radio-group v-model="diyCommentState.insertType">
              <el-radio-button label="order">顺序</el-radio-button>
              <el-radio-button label="random">随机</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="填充设置">
            <el-checkbox label="覆盖订单已有评价" v-model="diyCommentState.cover_txt"></el-checkbox>
            <el-checkbox label="覆盖订单已有图片" v-model="diyCommentState.cover_img"></el-checkbox>
          </el-form-item>
          <el-form-item label="一条评价填充图片数量(1-6)">
            <el-input-number v-model="diyCommentState.imgCount" :precision="0" :min="1" :max="6"></el-input-number>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <el-button @click="diyCommentState.dialog = false">取消</el-button>
        <el-button @click="diyCommentInset" type="primary">填充</el-button>
      </template>
    </el-dialog>

    <el-scrollbar ref="logScroll">
      <p v-for=" item in logState.list " :class="item.type" class="m-t-5 m-b-5">
        <span class="time m-r-12">{{ item.time }}</span>
        <span class="msg">{{ item.msg }}</span>
        <!-- {{ item.time }}:{{ item.msg }} -->
      </p>
    </el-scrollbar>
  </div>
</template>
<script lang="ts" setup>
  import { ElMessage, ElMessageBox, ScrollbarInstance, dayjs } from 'element-plus';
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import { collectComment, commentAppendRequest, commentRequest, getComment, getCommonComment, getFolderComment, updateOrder,updateOrderV2, uploadImage_base64, uploadVideo } from '/@/apis/page';
  import { useCurrentGoods, useCurrentPddUrl } from '/@/stores/current';
  import { checkFileType, copyStr, delayPromise, pddGoodsDetailUrl, random, showSystemImg } from '/@/utils/common';
  import { selectFile, selectFolder, getFolderFiles, openFileWindow, imgToBase64, readTXT, showOpenDialog, writeFile, isDirectory, deleteFolder, systemProcessList, getVideoInfo, deleteFile, videoResize } from '/@/apis/mainWindow';
  import { Plus } from '@element-plus/icons-vue'
  import { shuffle } from 'lodash'
  import { usePddAccount } from '/@/stores/pageData';
  import { useAppStore } from '/@/stores/app';
  import collectCommentVue from './collectComment.vue'
  import { useSetting } from '/@/stores/setting';
  import ProxyCollect from './proxyCollect.vue';
  import { pddWindowController } from '/@/apis/pddWindow';
  const emits = defineEmits < {
  (e: 'finishComment'): void
}> ()
  const props = defineProps < {
    orderList: anyObj[]
  } > ()

  const currentGoods = useCurrentGoods()
  const currentUrl = useCurrentPddUrl()

  type commentFileItem = {
    folder: string
    comment: string
    imgs: string[]
    txt: string[]
  }
  const state: {
    tabActive: 'collect' | 'comment',
    goodsID: string
    collectMode: 'normal' | 'pifa'
    count: number


    commentOrderList: Array<{
      timeline_sync_type: boolean,
      /**文件夹名字（路径） */
      folderName: string,
      comment: string,
      anonymous: boolean,
      imgs: string[],
      videos: string[],
      order: typeof props.orderList[number],
      commented?:
      boolean,
      index: number
    }>
    // comment_type_img: 'random' | 'order'
    // comment_type_txt: 'random' | 'order'
    comment_type: 'random' | 'order'
    comment_list: anyObj[],


    commentFileList: commentFileItem[]
    /**提供选择的评价文件夹列表 */
    userSelectFolders: Array<{ name: string, path: string }>
    /**选中的文件夹 */
    userSelectFolder?: { name: string, path: string }
    userFolderOption?: string

    /**忽略已有评价的订单 */
    ignoreCommentedOrder: boolean
    currentFolderPath?: string

    /**删除评价成功的文件夹 */
    deleteCommentedFolder: boolean
  } = reactive({
    tabActive: 'collect',
    goodsID: '',
    collectMode: 'normal',
    collectType: 'txt-img',
    count: 20,

    commentOrderList: [],

    // comment_type_img: 'random',
    // comment_type_txt: 'order',

    comment_type: 'order',

    comment_list: [],
    downloadTaskId: '',
    downloadCurrent: 0,
    downloadMax: 0,
    folderPath: '',
    commentFileList: [],
    userSelectFolders: [],

    ignoreCommentedOrder: true,

    deleteCommentedFolder: false,
  })

  const buttonLoading = reactive({
    collect: false,
    comment: false
  })



  /**已分配的文件夹 */
  const usedFolders = computed(() => {
    const map = new Map < string, number> ()
    state.commentOrderList.forEach((item) => {
      map.set(item.folderName, item.index)
    })
    return map
  })

  /**文件夹增量操作 */
  function userFoldersAction (type: 'delete' | 'open', userFolderOption = state.userFolderOption) {
    switch (type) {
      case 'open': {
        userFolderOption && openFileWindow(userFolderOption)
        break
      }
      case 'delete': {
        const ind = state.userSelectFolders.findIndex(item => item.path == userFolderOption)
        if (ind >= 0) {
          // 移除文件夹
          state.userSelectFolders.splice(ind, 1)
        }
        const orderInd = usedFolders.value.get(userFolderOption || '')
        if (orderInd !== undefined) {
          const order = state.commentOrderList.find(item => item.index === orderInd)!
          // order.folderName = ''
          // order.comment = ''
          // order.imgs = []
          // order.videos=[]
          clearOrderCommentInfo([order])
        }
      }
    }
    clearUserFolderOption()
  }

  function deleteAllUserFolders () {
    while (state.userSelectFolders.length) {
      const item = state.userSelectFolders.pop()!
      userFoldersAction('delete', item.path)
    }
  }

  function clearUserFolderOption () {
    state.userFolderOption = void 0
  }
  document.removeEventListener('click', clearUserFolderOption)
  document.addEventListener('click', clearUserFolderOption)

  const options: {
    anonymousAll: boolean
    anonymousi_ideterminate: boolean
    batch_count: number
    only5Stars: boolean

    pxq_All: boolean
    pxq_ideterminate: boolean
  } = reactive({
    anonymousAll: false,
    anonymousi_ideterminate: false,
    batch_count: 10,
    only5Stars: false,

    pxq_All: false,
    pxq_ideterminate: false
  })
  const settingStore = useSetting()
  watch(() => options.batch_count, (val) => {
    settingStore.setAnySetting('commentBatchCount', val)
  })
  settingStore.getAnySetting('commentBatchCount')
    .then(res => {
      if (res !== void 0) {
        options.batch_count = res
      }
    })
  watch(() => state.commentOrderList, (val) => {
    const anonymousArr = val.filter(item => item.anonymous)
    if (!val.length || !anonymousArr.length) {
      options.anonymousi_ideterminate = false
      options.anonymousAll = false
    }
    else if (val.length === anonymousArr.length) {
      options.anonymousi_ideterminate = false
      options.anonymousAll = true
    } else {
      options.anonymousi_ideterminate = true
      options.anonymousAll = false
    }
  }, { immediate: true, deep: true })

  // 全匿名，或全不匿名
  function anonymousAllChange (status: any) {
    state.commentOrderList.forEach(item => {
      item.anonymous = !!status
    })
  }
  watch(() => state.commentOrderList, (val) => {
    const anonymousArr = val.filter(item => item.timeline_sync_type)
    if (!val.length || !anonymousArr.length) {
      options.pxq_ideterminate = false
      options.pxq_All = false
    }
    else if (val.length === anonymousArr.length) {
      options.pxq_ideterminate = false
      options.pxq_All = true
    } else {
      options.pxq_ideterminate = true
      options.pxq_All = false
    }
  }, { immediate: true, deep: true })

  // 全匿名，或全不匿名
  function pxqChange (status: any) {
    state.commentOrderList.forEach(item => {
      item.timeline_sync_type = !!status
    })
  }

  watch(() => props.orderList, (value) => {
    if (buttonLoading.comment) {
      return
    }
    if (value && value.length) {
      state.commentOrderList = value.map((item, index) => {
        const data = {
          folderName: '',
          comment: '',
          anonymous: false,
          imgs: [],
          videos: [],
          order: item,
          timeline_sync_type: true,
          index: index + 1
        }
        return data
      })
    }
  }, { immediate: true })

  /**将预设的商品评价插入其中 */
  function insertPreFolder () {
    const map = new Map < string, typeof state.commentOrderList > ()
    state.commentOrderList.forEach(item => {
      const goods_id = item.order.goods_id
      if (map.has(goods_id)) {
        map.get(goods_id)!.push(item)
      } else {
        map.set(goods_id, [item])
      }
    })
    // const appStore = useAppStore()
    const folderPath = settingStore.orderManage.commentFolder
    if (!folderPath) {
      return
    }
    [...map.keys()].forEach(async item => {
      const res = await getFolderFiles(folderPath + '/' + item)
      if (res.length) {
        inserComment(res.map(fileName => {
          return {
            path: `${folderPath}` + '\\' + `${item}` + '\\' + `${fileName}`,
            name: fileName
          }
        }), map.get(item)!)
      }
    })
  }

  const logScroll = ref < ScrollbarInstance > ()
  type logItem = { time: string, msg: string, type?: 'danger' | 'success' | 'primary' | 'warning' }
  const logState = reactive({
    list: [] as logItem[]
  })
  function log (data: Omit<logItem, 'time'>) {

    logState.list.push({
      ...data,
      time: dayjs().format('YYYY-MM-DD HH:mm:ss')
    })
  }
  watch(() => logState.list.length, (val) => {
    nextTick(() => {
      let top = logScroll.value?.wrapRef?.scrollHeight || 0
      logScroll.value?.setScrollTop(top < 0 ? 0 : top)
    })
  })



  function goodsIdChange () {
    let { goodsID } = state
    if (goodsID.length <= 5) {
      return
    }
    if (goodsID.startsWith("https://")) {
      const reg = /goods_id=(\d+)/;
      const res = goodsID.match(reg);
      res && (goodsID = res[1]);
    }
    state.goodsID = goodsID
    currentUrl.changeUrl(pddGoodsDetailUrl(state.goodsID))
  }

  const ProxyCollectEl = ref < InstanceType < typeof ProxyCollect >> ()

  const collectCommentEl = ref < InstanceType < typeof collectCommentVue >> ()
  async function commentCollect () {
    const { collectMode, count } = state
    let { goodsID } = state
    if (!goodsID) {
      return ElMessage.warning({ message: '请输入商品id', grouping: true })
    }
    if (currentGoods.isErrorPage || currentUrl.isVerifiPage) {
      return ElMessage.warning({ message: '检测到账号已被风控，请验证或换号', grouping: true })
    }
    const cookies = await pddWindowController('getCookies') as Electron.Cookie[]
    const map = new Map(cookies.map(item => [item.name, item.value]))
    const keyArr = ["api_uid", "dilx", "_nano_fp", "webp", "jrpl", "njrpl", "PDDAccessToken", "pdd_user_id", "pdd_user_uin", "rec_list_personal", "pdd_vds"]
    const cookieStr = keyArr.map(item => `${item}=${map.get(item) || ''}`).join(';')
    state.goodsID = goodsID

    buttonLoading.collect = true
    collectComment({
      goodsID, collectMode, count, cookie: cookieStr
    })
      .then(res => {
        const { reviewList } = res.data
        state.comment_list = reviewList
        log({
          msg: `已获取列表`,
          type: "success"
        })
        collectCommentEl.value?.openConfigDialog(reviewList)
      })
      .catch(res => {
        // res.code 1正常错误 2 需要登录 3 需要验证
        log({
          msg: "任务出错:" + res.msg || '',
          type: "danger"
        })
        if (res.code == 2) {
          currentUrl.changeUrl('https://mobile.pinduoduo.com/login.html')
        } else if (res.code == 3) {

          currentUrl.changeUrl(`https://mobile.yangkeduo.com/psnl_verification.html?VerifyAuthToken=${res.verifyCode}`)
        }
      })
      .finally(() => {
        buttonLoading.collect = false
      })
  }

  // function deleteOrder(item: typeof state.commentOrderList[number]) {
  //   const ind = state.commentOrderList.findIndex(_item => item.order.order_sn === _item.order.order_sn)
  //   if (ind >= 0) {
  //     state.commentOrderList.splice(ind, 1)
  //   }
  // }

  /**拖拽选取评价文件夹 */
  async function dragFolders (e: DragEvent) {
    const files = e.dataTransfer!.files
    const promiseList: Promise<typeof state.userSelectFolders[number]>[] = []
    for (let i = 0; i < files.length; i++) {
      const { name, path } = files[i]
      if (name.includes('.')) {
        // 非文件夹
        promiseList.push(
          isDirectory(path)
            .then(() => {
              return { name, path }
            })
        )
      } else {
        promiseList.push(Promise.resolve({
          name, path
        }))
        // list.push()
      }
    }
    // console.log(list)
    Promise.allSettled(promiseList)
      .then(res => {
        //@ts-ignore
        const list = res.filter(item => item.status === 'fulfilled').map(item => item.value)
        addToUserSelectFolders(list)
      })

  }

  /** 选择评价文件夹*/
  async function selectFolders () {
    const result = await showOpenDialog({
      properties: ['createDirectory', 'multiSelections', 'openDirectory']
    })
    // console.log(result)
    if (!result.canceled) {
      addToUserSelectFolders((result.filePaths as string[]).map(path => {
        const strArr = path.split('\\')
        return {
          name: strArr.pop()!,
          path
        }
      }))

    }
  }

  function dragFoldersToOrder (e: DragEvent, item: typeof state.userSelectFolders[number]) {
    // console.log(e)
    // e.dataTransfer = new FileList
    e.dataTransfer!.setData("text/plain", item.path)
    // console.log(e.dataTransfer?.files)
  }

  /**添加到评价文件夹 */
  function addToUserSelectFolders (list: typeof state.userSelectFolders) {
    const pathSet = new Set(state.userSelectFolders.map(item => item.path));
    list.forEach(item => {
      if (pathSet.has(item.path)) {
        ElMessage.warning({
          message: "检测到相同路径的文件夹，已过滤!",
          grouping: true
        })
        return
      } else {
        state.userSelectFolders.push(item)
      }
    })
  }

  /**保存采集的评价 */
  async function saveCollectedComment () {
    if (!state.comment_list.length) {
      return ElMessage.warning('没有可保存的评价')
    }
    const path = await selectFolder()
    if (!path) {
      return
    }
    let fileName = `${state.comment_list.length}条评价${dayjs(Date.now()).format('YYYYMMDDHHmmss')}`
    let { value } = await ElMessageBox.prompt('请输入文件名称(不需要加.txt后缀)', '提示', {
      showCancelButton: true,
      inputPlaceholder: `默认为:${fileName}`
    })
    if (!value) {
      value = fileName
    }
    const filePath = path + '/' + value
    // writeTxT
    await writeFile({ dest: filePath + '.txt', data: state.comment_list.map(item => item.comment).join('\n') })
    openFileWindow(path)
  }

  /**插入评价 */
  function inserComment (list = state.userSelectFolders, commentOrderList = state.commentOrderList) {

    let fillList: typeof list = []
    list.forEach(item => {
      if (!usedFolders.value.has(item.path)) {
        // fillPathMap.set(item.path, item)
        fillList.push(item)
      }
    })

    if (!fillList.length) {
      ElMessage.warning({
        message: "供填充的列表没有数据",
        grouping: true
      })
      return
    }
    // console.log(fillPathMap)
    const { comment_type } = state
    let type: 'random' | 'order' = comment_type
    if (type === 'random') {
      fillList = shuffle(fillList)
    }
    log({
      msg: '开始分配'
    })
    commentOrderList.find((item, ind) => {
      // console.log(item)
      if (item.commented) {
        // 已评价 下一单
        return false
      }
      if (state.ignoreCommentedOrder && (item.comment || item.imgs.length)) {
        return false
      }
      const folder = fillList.shift()
      if (!folder) {
        return true
      }
      getFolderComment(folder.path)
        .then(res => {
          // console.log('res', res)
          const { comment, imgs, folder, videos } = res.data!
          item.comment = comment
          item.folderName = folder
          item.imgs = imgs.map(imgUrl => `${folder}/${imgUrl}`)
          item.videos = videos.map(imgUrl => `${folder}/${imgUrl}`)
        })
      return false
    })
    log({
      msg: '分配已完成',
      type: 'success'
    })
  }

  function folderOptions (e: MouseEvent, item: typeof state.userSelectFolders[number]) {
    e.preventDefault()
    e.stopPropagation()
    // console.log('test')
    state.userFolderOption = item.path
  }

  /**给订单增加图片 */
  function addOrderImg (item: typeof state.commentOrderList[0], url?: string) {
    if (item.imgs.length >= 6) {
      return ElMessage.warning({
        message: '一条评价最多能有6张图片',
        grouping: true
      })
    }
    if (url) {
      item.imgs.push(url)
      return
    }
    selectFile({
      title: "选择图片",
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'jpeg', 'png'] }
      ],
    })
      .then(res => {
        // console.log(res)
        // const set = new Set(item.imgs.concat)

        item.imgs.push(...res.data)
      })
  }

  function addOrderVideo (item: typeof state.commentOrderList[0], url?: string) {
    // if (item.imgs.length >= 6) {
    //   return ElMessage.warning({
    //     message: '一条评价最多能张图片',
    //     grouping: true
    //   })
    // }
    if (url) {
      item.videos.push(url)
      return
    }
    selectFile({
      title: "选择视频",
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'videos', extensions: ['.mp4', '.avi'] }
      ],
    })
      .then(res => {
        item.videos.push(...res.data)
      })
  }




  async function stopComment () {
    log({
      msg: "已停止评价(正在进行评价的订单不会停止)",
      type: "danger"
    })
    buttonLoading.comment = false
  }

  const commentState = reactive({
    max: 0,
    success: 0,
    failed: 0,
  })

  const commentPercentage = computed(() => {
    const { max, success, failed } = commentState
    if (!max) {
      return 0
    } else {
      return Number(((success + failed) / max * 100).toFixed(2))
    }
  })

  /**开始评价 */
  async function startComment () {
    if (options.batch_count <= 0) {
      ElMessage.warning({
        message: '检测到同时评论的订单数量小于等于0，或没有设置',
        grouping: true
      })
      return
    }
    const { commentOrderList } = state
    const { only5Stars } = options
    if (!commentOrderList.length || buttonLoading.comment) {
      return
    }
    const list = commentOrderList.filter(item => {
      // return true
      if (item.commented) {
        return false
      }
      else if (!only5Stars) {
        return item.imgs.length || item.comment
      } else {
        return true
      }

    })
    if (!list.length) {
      return ElMessage.warning({
        message: "没有可以提交的订单!",
        grouping: true
      })
    }
    logState.list.length = 0

    buttonLoading.comment = true

    const pddAccountStore = usePddAccount()

    log({
      msg: '正在做评价前的准备'
    })

    // 检测环境
    // await new Promise(async (resolve, reject) => {
    // const list = await systemProcessList()
    //   // console.log(res)
    //   let flag = false
    //   // let set = new Set<string>(['fiddler','fiddler'])
    //   let reg = /(fiddler.exe)/i
    //   list.forEach(item => {
    //     if (item[0] && reg.test(item[0])) {
    //       flag = true
    //     }
    //   })
    //   if (!flag) {
    //     resolve(flag)
    //   } else {

    //     buttonLoading.comment = false
    //     log({
    //       msg: '--在初始化时遭遇了未知错误--请重试',
    //       type: 'danger'
    //     })
    //     reject()
    //   }

    // })

    // 获取小号
    await new Promise((resolve, reject) => {
      pddAccountStore.getList(list.length + 10)
        .then(res => {
          log({
            msg: '准备开始评价--'
          })
          resolve(true)
        })
        .catch((res) => {
          reject(res)
          log({
            type: 'danger',
            msg: '准备阶段出现错误' + res.msg
          })
          buttonLoading.comment = false
        })
    })
    commentState.max = list.length
    commentState.success = 0
    commentState.failed = 0
    let stopNoticeFlag = false
    let noticeClearFlag = false
    const requestPool = new Set < string > ()

    for (let i = 0; i < options.batch_count; i++) {

      if (list.length) {
        await delayPromise(1000 * i)
        const item = list.shift()
        item && start(item)
      }

    }

    async function start (item: typeof list[number]) {
      requestPool.add(item.order.order_sn)
      await new Promise(async (resolve, reject) => {
        const { index, order, videos } = item
        let imgs = item.imgs.slice(0, videos.length ? 5 : 6)
        log({
          msg: `第${index}单准备开始执行`
        })
        let videoInfo: anyObj | undefined = void 0

        if (videos.length) {
          videoInfo = {}
          if (!videoInfo) {
            return
          }
          let deleteVideo = false
          let video = videos[0]
          await new Promise((resolve) => {
            videoResize({ url: video })
              .then(res => {
                video = res.data
                log({
                  msg: '调整大小成功'
                })
                deleteVideo = true
                resolve(res)
              })
              .catch(res => {
                log({
                  msg: '调整大小失败' + res.msg
                  , type: 'danger'
                })
                resolve(res)
              })
          })

          function deleteTempVideo () {
            try {
              deleteVideo && deleteFile(video)
            } catch (e) {

            }
          }

          const videoRes = await getVideoInfo({ url: video, timestamps: [0] })
          if (videoRes.code) {
            log({
              msg: `第${index}单处理视频失败：${videoRes.msg}`,
              type: 'danger'
            })
            reject(false)
            deleteTempVideo()
            return
          } else {
            const { imgs, info } = videoRes.data
            const { duration, size } = info.format
            videoInfo.duration = duration
            videoInfo.size = size
            await new Promise(resolve => {
              let imgBase64 = ''
              let upLoadImag = {
                max: 5,
                current: 0,
                delay: 2000,
              }
              let upLoadVideo = {
                max: 5,
                current: 0,
                delay: 2000,
              }
              function uploadImgRepeat () {
                log({
                  msg: `正在上传第${index}单的视频封面`
                })
                uploadImage_base64({
                  image: imgBase64!,
                  order_sn: order.order_sn,
                  tk: pddAccountStore.getItem()
                })
                  .then(res => {
                    // console.log(res)
                    log({
                      msg: `上传第${index}单的视频封面成功`,
                      type: "success"
                    })
                    videoInfo!.cove = res.data

                    uploadVideoRepeat()
                  })
                  .catch(async (res) => {
                    upLoadImag.current++
                    if (upLoadImag.current < upLoadImag.max) {
                      await delayPromise(upLoadImag.delay)
                      uploadImgRepeat()
                    } else {
                      log({
                        msg: `上传第${index}单的视频封面失败,${res.msg}`,
                        type: "danger"
                      })
                      deleteTempVideo()
                      resolve({
                        success: false
                      })
                    }
                  })
              }

              function uploadVideoRepeat () {
                log({
                  msg: `正在上传第${index}单的视频`
                })
                uploadVideo({
                  url: video,
                  order_sn: order.order_sn,
                  tk: pddAccountStore.getItem()
                })
                  .then(res => {
                    // console.log(res)
                    log({
                      msg: `上传第${index}单的视频成功`,
                      type: "success"
                    })
                    videoInfo!.videoUrl = res.data
                    resolve({
                      success: true
                    })
                  })
                  .catch(async (res) => {
                    upLoadVideo.current++
                    if (upLoadVideo.current < upLoadVideo.max) {
                      await delayPromise(upLoadVideo.delay)
                      uploadVideoRepeat()
                    } else {
                      log({
                        msg: `上传第${index}单的视频失败,${res.msg}`,
                        type: "danger"
                      })
                      resolve({
                        success: false
                      })
                    }
                  })
                  .finally(() => {
                    deleteTempVideo()
                  })
              }


              imgToBase64(imgs[0], true)
                .then(res => {
                  imgBase64 = res
                  deleteFile(imgs[0])
                  uploadImgRepeat()
                })
                .catch(res => {
                  deleteTempVideo()
                  resolve({
                    success: false,
                    msg: '处理视频封面失败'
                  })
                })

            })
          }
        }
        // console.log(videoInfo)
        let uploadImages: anyObj[] = []
        if (imgs.length) {
          // 转base64
          const base64Imgs: string[] = await new Promise((resolve) => {
            const result: string[] = []
            const list = [...imgs]
            function fn () {
              const item = list.shift()
              if (item) {
                imgToBase64(item, true, 30)
                  .then(res => {
                    // console.log('imgToBase64',String(res).length)
                    result.push(res)
                  }).finally(() => {
                    fn()
                  })
              }
              else {
                resolve(result)
              }
            }
            fn()
            // Promise.allSettled(imgs.map(async (img, index) => {
            //   await delayPromise(index * 300)
            //   return imgToBase64(img, true, 50)
            //     .then(res => {
            //       // result.push(`data:image/JPEG;base64,${res}`)
            //       result.push(res)
            //     })
            // }))
            //   .then(() => {

            //     resolve(result)
            //   })
          })

          if (base64Imgs.length < imgs.length) {
            // 有图转base64图片失败 
            log({
              type: "danger",
              msg: `第${index}单，图片失败`
            })
            // uploadComment()
            reject(false)
            return
          }
          // 上传图片
          const uploadImageReponse: responseData = await new Promise((resolve) => {
            const uploadImgRes: anyObj[] = []
            const response = {
              code: 0,
              data: uploadImgRes,
              msg: ''
            }

            const promiseArr = base64Imgs.map((imgBase64, _index) => {

              return new Promise((resolve, reject) => {

                const max = 5
                let current = 0
                const delay = 2000
                const img_index = _index + 1
                function uploadImgRepeat () {
                  current || log({
                    msg: `正在上传第${index}单的第${img_index}张图片`
                  })


                  uploadImage_base64({
                    image: imgBase64!,
                    order_sn: order.order_sn,
                    tk: pddAccountStore.getItem()
                  })
                    .then(res => {
                      console.log(res, imgBase64)
                      log({
                        msg: `上传第${index}单的第${img_index}张图片成功`,
                        type: "success"
                      })
                      uploadImgRes.push(res.data)
                      resolve(true)
                    })
                    .catch(async (res) => {
                      current++
                      // log({
                      //   msg: `上传第${index}单的第${img_index}张图片第${current}次失败`,
                      //   type: 'danger'
                      // })
                      if (!response.code && current < max) {
                        await delayPromise(delay)
                        uploadImgRepeat()
                      } else {
                        log({
                          msg: `上传第${index}单的第${img_index}张图片失败,${res.msg}`,
                          type: "danger"
                        })
                        response.code = 1
                        response.msg = '图片上传失败',
                          reject()
                      }
                    })
                }
                uploadImgRepeat()
              })
            })
            Promise.allSettled(promiseArr)
              .then(res => {
                resolve(response)
              })
          })
          console.log('uploadImageReponse', uploadImageReponse)

          if (!uploadImageReponse.code) {
            uploadImages = uploadImageReponse.data
          } else {
            // 上传图片失败 下一单
            // uploadComment()
            reject(false)
            return
          }
        }
        // reject(false)
        // return

        const requestMax = 30 //最多允许失败几次
        const repeatDelay = 500 //每一次间隔(毫秒)
        let currentCount = 0 //当前进行多少次
        function request (notice = false) {
          const { order, anonymous, comment, timeline_sync_type, index } = item!

          notice && log({
            msg: `第${index}单：${order.order_sn}开始执行评价`
          })
          // reject({ code: 1, msg: '打断' })
          // return
          // console.log(comment, uploadImages)
          // console.log(videoInfo,uploadImages)
          let promise: Promise<any>
          if (order.comment_status) {
            promise = commentAppendRequest({
              comment,
              anonymous: anonymous ? 1 : 0,
              pictures: JSON.stringify(uploadImages),
              goods_id: order.goods_id,
              timeline_sync_type: timeline_sync_type ? 1 : 2,
              order_sn: order.order_sn,
              anti_content: window.get_anti_content()
            }, {
              showErrorMsg: false, repeatMax: 10, beforeRequest (config) {
                config.data!.anti_content = window.get_anti_content()
                console.log(config)
              },
            })
          } else {
            const requestData: Parameters<typeof commentRequest>[0] = {
              comment,
              anonymous: anonymous ? 1 : 0,
              pictures: JSON.stringify(uploadImages),
              goods_id: order.goods_id,
              timeline_sync_type: timeline_sync_type ? 1 : 2,
              order_sn: order.order_sn,
              anti_content: window.get_anti_content()
            }
            if (videoInfo) {
              const { cove, duration, size, videoUrl } = videoInfo
              requestData.video = {
                duration,
                size,
                cover_image_height: cove.height,
                cover_image_width: cove.width,
                cover_image_url: cove.url,
                url: videoUrl.url,
                width: cove.width,
                height: cove.height
              }
            }

            // console.log(requestData)
            promise = commentRequest(requestData, { showErrorMsg: false, repeatMax: 10 })
          }

          promise.then(res => {
            resolve(res)
            updateOrderV2({
              ids: order.order_sn
            }, { showErrorMsg: false })
            log({
              type: 'success',
              msg: `第${index}单：${order.order_sn}评价成功`
            })
            // console.log(uploadImages,comment)
            item!.commented = true
            if (state.deleteCommentedFolder) {
              deleteCommentedFolder(item!)
            }
          })
            .catch(async (res) => {
              currentCount++

              // if (String(res.msg).includes('风控') && currentCount < requestMax) {
              //   if (currentCount <= 1) {
              //     log({
              //       type: 'warning',
              //       msg: '评价订单遇到风控，正在尝试突破订单评价风控'
              //     })
              //   }
              //   await delayPromise(repeatDelay)
              //   request()
              // }
              // else 
              if (String(res.msg).includes('网络') && currentCount <= 3) {
                await delayPromise(1500)
                request()
              }
              else {
                // console.log(res, 'res')
                reject(res)
                log({
                  type: "danger",
                  msg: `第${index}单：${order.order_sn}评价失败,${res.msg}`
                })
              }
            })
        }
        request(true)

      })
        .then((res) => {
          commentState.success++
          // return res
          // console.log('评价成功', res)
        })
        .catch(res => {
          commentState.failed++
          // return res
        })
        .finally(() => {
          requestPool.delete(item.order.order_sn)
          if (!buttonLoading.comment) {
            if (!stopNoticeFlag) {
              stopNoticeFlag = true
              log({
                msg: '检测到暂停指令,停止后续执行',
                type: 'warning'
              })
            }
            return
          }
          if (list.length) {
            start(list.shift()!)
          } else {
            if (!noticeClearFlag) {
              noticeClearFlag = true
              log({
                msg: '检测到所有订单已发送，停止功能将失效',
                type: 'primary'
              })
            }
          }
          if (!requestPool.size) {
            buttonLoading.comment = false
            log({
              msg: `评价结束`
            })
            emits('finishComment')
          }
        })
    }



  }

  /**删除评价成功的文件夹 */
  async function deleteCommentedFolder (item: typeof state.commentOrderList[number]) {
    if (!item) {
      return
    }
    const folderPath = item.folderName
    if (!folderPath) {
      return
    }
    await deleteFolder(folderPath)
    const userSelectFolder = state.userSelectFolders.find(item => item.path == folderPath)
    if (userSelectFolder) {
      userFoldersAction('delete', folderPath)
    }
  }



  async function drop (event: DragEvent, item: typeof state.commentOrderList[number]) {
    const folderName = event.dataTransfer?.getData('text')
    if (folderName) {
      getFolderComment(folderName)
        .then(res => {
          if (res.data) {
            item.imgs = res.data.imgs.map(imgUrl => `${folderName}/${imgUrl}`)
            item.folderName = res.data.folder
            item.comment = res.data.comment
            item.videos = res.data.videos.map(imgUrl => `${folderName}/${imgUrl}`)
          }

        })
      return
    }

    if (event.dataTransfer && event.dataTransfer.files.length) {
      const files = event.dataTransfer.files;
      // console.log('files', files)  
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const fileType = checkFileType(file.path)
        // cosnt
        if (fileType === 'image') {
          // item.imgs.push(file.path)

          addOrderImg(item, file.path)
        } else if (fileType === 'txt') {
          readTXT({ url: file.path })
            .then(res => {
              if (res) {
                item.comment = res
              }
            })
        } else if (fileType === 'video') {
          // item.videos.push(file.path)
          addOrderVideo(item, file.path)
        }
        else if (!file.path.includes('.')) {
          const result = await getFolderFiles(file.path)
          // console.log(result)
          item.folderName = file.path
          result.forEach((url) => {
            const fileType = checkFileType(url)
            if (fileType === 'image') {
              addOrderImg(item, `${file.path}/${url}`)
            } else if (fileType === 'video') {
              addOrderVideo(item, `${file.path}/${url}`)
            }
            else if (fileType === 'txt') {
              readTXT({ url: `${file.path}/${url}` })
                .then(res => {
                  if (res) {
                    item.comment = res
                  }
                })
            }
          })
        }
      }
    }
  }


  async function clearOrderCommentInfo (list = state.commentOrderList) {
    if (list === state.commentOrderList) {
      await ElMessageBox({
        title: '清空评价信息',
        message: '确定要清空吗？',
        type: "warning",
        showCancelButton: true
      })
    }
    list.forEach(item => {
      item.comment = ''
      item.folderName = ''
      item.imgs = []
      item.videos = []
    })
  }

  const diyCommentState: {
    dialog: boolean
    imgs: string[]
    commnetStr: string
    insertType: 'order' | 'random'
    imgCount: number
    cover_img: boolean
    cover_txt: boolean
  } = reactive({
    dialog: false,
    imgs: [],
    commnetStr: '',
    insertType: 'order',
    imgCount: 1,
    cover_img: false,
    cover_txt: false
  })

  const diyComments = computed(() => {
    const { commnetStr } = diyCommentState
    return commnetStr.split('\n').filter(item => item)
  })

  async function selectDiyCommentTxt () {
    const result = await showOpenDialog({
      properties: ['createDirectory'],
      filters: [{ extensions: ['txt'], name: "" }]
    })
    if (result.canceled) {
      return ElMessage.warning({
        message: "取消选择文件",
        grouping: true
      })
    }
    // console.log(result)
    const path = result.filePaths[0]
    const str = await readTXT({ url: path })
    console.log(str)
    if (diyCommentState.commnetStr && !diyCommentState.commnetStr.endsWith('\n')) {
      diyCommentState.commnetStr += '\n'
    }
    diyCommentState.commnetStr += str
  }
  async function diyCommentTxTdrop (e: DragEvent) {
    const files = e.dataTransfer?.files
    if (!files) {
      return
    }
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      if (file.name.endsWith('.txt')) {
        readTXT({ url: file.path })
          .then(res => {
            if (diyCommentState.commnetStr && !diyCommentState.commnetStr.endsWith('\n')) {
              diyCommentState.commnetStr += '\n'
            }
            diyCommentState.commnetStr += res
          })
      }
    }
  }
  async function selectDiyCommentImgs () {
    const result = await showOpenDialog({
      properties: ['createDirectory', 'multiSelections'],
      filters: [{ extensions: ['jpg', 'png', 'jpeg'], name: "" }]
    })
    if (result.canceled) {
      return ElMessage.warning({
        message: "取消选择文件",
        grouping: true
      })
    }

    const set = new Set([...diyCommentState.imgs, ...result.filePaths])
    console.log(set)
    diyCommentState.imgs = [...set]

  }
  function deleteDiyCommentImage (url: string) {
    const ind = diyCommentState.imgs.findIndex(item => item === url)
    if (ind >= 0) {
      diyCommentState.imgs.splice(ind, 1)
    }
  }
  function diyCommentInset () {
    let { imgCount, insertType, cover_img, cover_txt } = diyCommentState
    let commnets = [...diyComments.value]
    let imgs = [...diyCommentState.imgs]
    if (insertType == 'random') {
      commnets = shuffle(commnets)
      imgs = shuffle(imgs)
    }
    state.commentOrderList.find(item => {
      if (commnets.length || imgs.length) {
        imgs.length && (cover_img || !item.imgs.length) && (item.imgs = imgs.splice(0, imgCount));
        commnets.length && (cover_txt || !item.comment) && (item.comment = commnets.shift() || '')

        return false
      } else {
        return true
      }
    })
    diyCommentState.dialog = false
  }

  function getButtonLoading () {
    return buttonLoading
  }

  async function checkRepeat () {
    const map = new Map < string, typeof state.commentOrderList > ();
    state.commentOrderList.forEach(item => {
      if (!item.comment) {
        return
      }
      if (map.has(item.comment)) {
        const list = map.get(item.comment)!
        list.push(item)
      } else {
        map.set(item.comment, [item])
      }
    });
    [...map.values()].forEach(list => {
      if (list.length <= 1) {
        map.delete(list[0].comment)
      }
    });
    if (!map.size) {
      ElMessage.warning({
        message: "没有重复的评论",
        grouping: true
      })
      return false
    }
    await ElMessageBox({
      title: '重复评论',
      message: `检测到${map.size}个重复评论，是否重置？`,
      type: "warning",
      showCancelButton: true
    })
    map.forEach(list => {
      list.forEach(item => {
        item.comment = ''
      })
    })
    await ElMessageBox({
      title: '提示',
      message: '是否需要随机填充评论？(本地随机)',
      showCancelButton: true
    })
    const str = await getCommonComment()
    const commontList = str.split('\n').filter(item => item)
    map.forEach(list => {
      list.forEach(item => {
        const ind = random(0, commontList.length - 1)
        item.comment = commontList[ind] || ''
      })

    })
  }

  defineExpose({
    getButtonLoading
  })

</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
  .eval-coll {
    width: 100%;
    height: 100%;
    // background-color: var(--el-bg-color-page );
    // border-radius: 5px;
    overflow: hidden;

    .text-overflow {
      @include text-overflow();
    }

    .wrapper {
      display: flex;
      justify-content: space-between;
      height: 666px;

      .log-container {
        box-sizing: border-box;
        padding: 8px;
      }

      // .el-scrollbar {

      // }
      .left,
      .right {
        height: 666px;
        border: var(--el-border);
        box-shadow: var(--diy-shadow);
        border-radius: 6px;
        background-color: var(--el-fill-color-blank);
        overflow: hidden;
      }

      .left {
        width: 364px;

        .el-scrollbar {
          height: 390px;
          border: var(--el-border);
          background-color: var(--el-fill-color-light);
          border-radius: 5px;
        }

        .el-tabs {
          border: none;
          margin-bottom: 16px;

          :deep(.el-tabs__content) {
            display: none;
          }
        }

        .container {
          height: calc(100% - 55px);
          padding: 0 16px;
          box-sizing: border-box;
        }

        .collect,
        .comment {
          .el-input {
            :deep(.el-input-group__prepend) {
              .el-select {
                background-color: var(--el-fill-color-blank);
              }
            }
          }

          p {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 8px 0;

            .el-button {
              flex-grow: 1;
            }

            .el-radio-group {
              flex-grow: 1;

              .el-radio-button {
                flex-grow: 1;

                :deep(.el-radio-button__inner) {
                  // flex-grow: 1;
                  width: 100%;
                }
              }
            }
          }
        }

        .comment {
          .scrollar-container {
            height: 496px;
            position: relative;
            overflow: hidden;

            .no-list {
              position: absolute;
              width: 100%;
              left: 0;
              top: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: rgba($color: #000000, $alpha: .2);
              border-radius: 4px;
            }

            .folder-container {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 16px;
              min-height: 474px;
              align-content: flex-start;

              .folder {
                -webkit-user-drag: element;
                display: flex;
                flex-direction: column;
                align-items: center;
                font-size: 32px;
                cursor: pointer;
                width: 90px;
                height: 90px;
                justify-content: center;
                border-radius: 6px;
                position: relative;
                border: var(--el-border);

                &:hover,
                &.active {
                  background-color: var(--el-fill-color-blank)
                }

                p {
                  // @include text-overflow();
                  word-break: break-all;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  /* 这里是超出几行省略 */
                  -webkit-line-clamp: 2;
                  text-align: center;
                  overflow: hidden;
                  font-size: 12px;
                  width: 80px;
                }

                &.add {
                  background-color: var(--el-fill-color-blank)
                }

                .used-mask {
                  display: inline-block;
                  position: absolute;
                  width: 100px;
                  height: 20px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  left: 50%;
                  top: 20%;
                  transform: translate(-50%, -50%);
                  font-size: 14px;
                  background: rgba($color: skyblue, $alpha: .9);
                }
              }

            }
          }

          .el-scrollbar,
          .no-list {
            height: 100%;
          }

        }
      }

      .right {
        width: 624px;

        header {
          height: 40px;
          margin-bottom: 16px;
          box-sizing: border-box;
          padding: 10px 16px;
          color: #000;
          border-bottom: var(--el-border);
        }

        .el-scrollbar {
          height: 546px;
        }

        :deep(.list-container) {
          padding: 0 16px;
          // height: 546px;

          .el-form {
            border: var(--el-border);
            border-radius: 4px;
            box-sizing: border-box;
            padding: 16px;
            margin-bottom: 8px;
            position: relative;

            .el-form-item {
              margin-bottom: 8px;

            }

            .clear-form-button {
              position: absolute;
              left: 10px;
              top: 16px;
            }

            .commented,
            .mask,
            .txt,
            .options {
              position: absolute;
              left: 0;
              top: 0;
              // display: none;
              width: 100%;
              height: 100%;
              // z-index: -1;
              z-index: 2;
            }

            .mask {
              background: rgba($color: #000000, $alpha: .3);
              z-index: 10;
            }

            .txt {
              font-size: 34px;
              font-weight: bolder;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .options {
              width: 400px;
              height: 40px;
              padding: 5px 10px;
              left: 50%;
              top: 80%;
              transform: translateX(-50%);
              background-color: #fff;
              border-radius: 4px;

              p {
                line-height: 20px;
                text-align: center;
              }

              p.tips {
                font-size: 12px;
              }
            }


            .row {
              display: flex;
              align-items: center;

              // .el-divider {
              //   // margin-bottom: 16px;
              //   // margin-left: 25px;
              // }
            }

            .img-container {
              position: relative;

              .el-image {
                width: 40px;
                height: 40px;
                flex-shrink: 0;
                border-radius: 4px;
              }

              .close {
                width: 14px;
                height: 14px;
                border-radius: 50%;
                position: absolute;
                right: -7px;
                top: -7px;
                // border:1px solid  var(--el-color-primary);
                border: var(--el-border);
                color: var(--el-color-primary);
                background-color: #fff;
                font-size: 14px;
                z-index: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
              }
            }

            .screen-video {
              width: 120px;
              height: 80px;
              margin-right: 5px;
              margin-bottom: 5px;
            }

            span.notice.danger {
              display: inline-block;
              width: 120px;
              height: 80px;
              box-sizing: border-box;
              padding: 5px;
              text-align: center;
            }
          }
        }

        footer.bottom {
          height: 64px;
          box-sizing: border-box;
          background-color: var(--el-fill-color-extra-light);
          border-top: var(--el-border);
          padding: 10px 16px;
          display: flex;
          flex-direction: column;
          // align-items: center;
          justify-content: space-evenly;
          box-sizing: border-box;

          .progress {
            display: flex;
            margin-top: 5px;

            .el-progress {

              flex-grow: 1;
              font-size: 12px;
            }
          }

          .config {
            display: flex;
            align-items: center;
          }

          .cycle {
            margin: 0 5px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;

            .el-input-number {
              margin: 0 5px;
              width: 50px;
            }
          }

          .el-button {
            flex-grow: 1;
          }
        }
      }
    }

    &>.el-scrollbar {
      margin-top: 10px;
      height: 100px;
      border-left: var(--el-border);

      p {
        min-height: 22px;
        line-height: 24px;

        box-sizing: border-box;
        padding-left: 16px;
      }
    }
  }
</style>
<style lang="scss">
  .el-popover.folder-options-popover {
    padding: 0;
    width: 80px !important;
    min-width: 80px !important;

    ul.folder-options {
      margin: 0;
      padding: 0;
      list-style: none;

      li {
        padding: 4px 10px;
        margin: 0;
        width: 100%;
        height: 30px;
        cursor: pointer;
        box-sizing: border-box;
        display: flex;
        align-items: center;

        &:hover {
          color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }
      }
    }
  }

  .el-dialog.diy-comment-dialog {
    left: 140px;

    .row {
      display: flex;
      // justify-content: space-between;

      .el-form-item {
        // width: 30%;
        flex-grow: 1;
      }
    }

    .img-form-item {
      .el-form-item__content {
        flex-direction: column;

        .el-scrollbar {
          width: 100%;
        }

        p {
          margin-top: 10px;
        }
      }
    }

    .imgs-container {
      min-height: 190px;
      box-sizing: border-box;
      padding: 8px;
      border: var(--el-border);
      border-radius: 4px;
      display: grid;
      grid-template-columns: repeat(10, 1fr);
      gap: 10px;

      .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        cursor: pointer;

        .el-link {
          display: none;
        }

        &:hover {
          .el-link {
            display: block;
          }
        }
      }

      .el-image {
        width: 50px;
        height: 50px;
        border-radius: 4px;
      }

    }
  }
</style>