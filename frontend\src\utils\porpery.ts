import { cloneDeep } from "lodash";
import request, { storeAntiContent } from "../apis/page";
import { getUpLoadData } from "../apis/changeSales";
import { random, randomA2Z, randomInt } from "./common";
import { querySpecByName } from "../apis/store";
import { catData } from "./pddTempGoodsData";

const date = new Date()
export const shortcuts = [

  {
    text: "最近2小时",
    value: () => {
      const end = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 2);
      return [start, end];
    },
  },
  {
    text: "最近6小时",
    value: () => {
      const end = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 6);
      return [start, end];
    },
  },
  {
    text: "最近12小时",
    value: () => {
      const end = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 12);
      return [start, end];
    },
  },
  {
    text: "最近一天",
    value: () => {
      const end = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24);
      return [start, end];
    },
  },
  {
    text: "最近一周",
    value: () => {
      const end = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  // {
  //   text: "最近三个月",
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
  //     return [start, end];
  //   },
  // },
];


export const createRandomStr = () => {
  let str = ''
  for (let i = 0; i < random(10, 15); i++) {
    if (i < 2) {
      str += randomA2Z()
    } else {
      str += (Math.random() > 0.5 ? random(0, 10) : randomA2Z())
    }
  }
  return str
}

export const tempGoodsNames = {
  flagshipStore: ['全屋五金配件', '天地盖礼盒','工商注册专用款式'],
  common: ['全屋五金配件', '天地盖礼盒','工商注册专用款式']
}

export const changeSkuPirceList = [
  {
    market_price_in_yuan: '3.00',
    market_price: 300,
    multi_price_in_yuan: "0.01",
    single_price_in_yuan: "2.00",
    multi_price: 1,
    single_price: 200
  },
  {
    market_price_in_yuan: '3.00',
    market_price: 300,
    multi_price_in_yuan: "0.05",
    single_price_in_yuan: "2.00",
    multi_price: 5,
    single_price: 200
  },
  {
    market_price_in_yuan: '3.00',
    market_price: 300,
    multi_price_in_yuan: "0.21",
    single_price_in_yuan: "2.00",
    multi_price: 21,
    single_price: 200
  },
  {
    market_price_in_yuan: '3.00',
    market_price: 300,
    multi_price_in_yuan: "0.51",
    single_price_in_yuan: "2.00",
    multi_price: 51,
    single_price: 200
  },
  {
    market_price_in_yuan: '3.00',
    market_price: 300,
    multi_price_in_yuan: "0.52",
    single_price_in_yuan: "2.00",
    multi_price: 52,
    single_price: 200
  },
  {
    market_price_in_yuan: '5.00',
    market_price: 500,
    multi_price_in_yuan: "1.10",
    single_price_in_yuan: "3.00",
    multi_price: 110,
    single_price: 300
  },
  {
    market_price_in_yuan: '6.00',
    market_price: 600,
    multi_price_in_yuan: "2.10",
    single_price_in_yuan: "4.00",
    multi_price: 210,
    single_price: 400
  },
  {
    market_price_in_yuan: '8.00',
    market_price: 800,
    multi_price_in_yuan: "5.50",
    single_price_in_yuan: "7.00",
    multi_price: 550,
    single_price: 700
  },
]
export async function createPddGoodsInfo(data: {
  goods_id: number;
  goods_commit_id: string;
  gallery: anyObj[];
  detailsInfo: anyObj
  mall: Store
  cat_id?: number;
  otherData?: anyObj
  /**true 非旗舰店 false 旗舰店 */
  only_sku_property?: boolean
  useDefaultImage?: boolean
}) {
  // console.log("createPddGoodsInfo", data);
  const { goods_commit_id, goods_id, gallery, detailsInfo,
    //  cat_id = 10002,
    cat_id = 11254,
    otherData = {}, useDefaultImage, only_sku_property } = data;
  const crawlerInfo = await storeAntiContent()
  const { carousel_gallery, country_id = "0", check_status = 9, zhi_huan_bu_xiu = 0, quan_guo_lian_bao = 0, second_hand = 0, is_group_pre_sale = 0, is_pre_sale = 0, is_sku_pre_sale = 0 } = detailsInfo
  const mainImg = {
    type: 1,
    url: carousel_gallery[0]?.url,
    file_id: carousel_gallery[0]?.file_id
  };
  (function () {
    const index = randomInt(1, carousel_gallery.length - 2)
    const item = carousel_gallery[index]
    if (item) {
      mainImg.url = item.url
      mainImg.file_id = item.file_id
    }
  })();
  if (useDefaultImage || !mainImg.url) {
    // mainImg.url = "https://img.pddpic.com/mms-material-img/2024-04-01/ffb01101-359f-4911-92a4-483118015de3.jpeg"
    // mainImg.url = "https://img.pddpic.com/mms-material-img/2024-04-18/d048e968-cdbe-4d2c-9132-ecf7bb41a6eb.jpeg.a.jpeg"
    //mainImg.url = "https://img.pddpic.com/mms-material-img/2024-07-15/4667a00f-57e1-436c-ace7-a62fccf86c96.png.a.jpeg"
    mainImg.url = "https://img.pddpic.com/mms-material-img/2025-01-22/8cbf6e0c-1089-46e4-9b68-3b911ca783a1.jpeg"
    //mainImg.file_id = 48075505308
    // mainImg.file_id = 42693921590
    mainImg.file_id = null
    // if (only_sku_property) {
    //   mainImg.url = "https://img.pddpic.com/mms-material-img/2023-04-21/2d13a494-024e-4335-8836-f43ab4a64b89.jpeg.a.jpeg"
    //   mainImg.file_id = 22389709938
    // }
    if (data.only_sku_property) {
      const obj = catData.find((item) => item.cat_id == cat_id)
      if (obj && obj.mainImage) {
        mainImg.url = obj.mainImage.url
        mainImg.file_id = obj.mainImage.filed_id
      }
    }
  }

  Reflect.deleteProperty(otherData, 'goods_name')
  Reflect.deleteProperty(otherData, 'goods_desc')
  const goodsName = (function () {
    return '全屋五金配件滚动轴承定制咨询客服直拍不发' + Math.ceil(Math.random() * 100)
    const list = tempGoodsNames.common
    return list[random(0, list.length)] || '全屋定制户外防盗窗护栏落地窗加厚'
  })()

  const baseSpec = {
    parent_id: 1473,
    parent_name: "型号",
    spec_id: 22201231303,
    spec_name: "KH0001",
  }
  if (data.only_sku_property) {
    const str = `K${random(0, 10)}${random(0, 10)}${random(0, 10)}${random(0, 10)}`
    const res = await querySpecByName(data.mall, { cat_id: 10018, name: str, parent_id: baseSpec.parent_id })
    if (res.success) {
      if (res.result && typeof res.result === 'number') {
        baseSpec.spec_id = res.result
        baseSpec.spec_name = str
      }
    }
  }
  const baseInfo = {

    goods_id,
    goods_commit_id,
    goods_commit_check: [],
    reject_reason: [],
    reject_status: "",
    check_status,
    goods_name: goodsName,
    carousel_video: [],
    detail_video: [],
    goods_desc: goodsName,
    warm_tips: "",
    cat_id,
    cats: ["全屋定制", "窗", "防盗窗/窗防护栏", null],
    image_url: "",
    oversea_goods: {},
    spu: {},
    isbn_properties_related: {
      isInit: true,
    },
    propertys_standard: [],
    is_draft: false,
    goods_name_prefix: "",
    size_spec_id: null,
    is_gold_price_matched: null,
    hasBatchEnter: false,
    instruction_vo: {
      template_id: null,
      template_status: -1,
      template_name: null,
      rejected_reason: null,
    },
    explanation_video: [],
    third_type: 0,
    cat_ids: [9320, 9435, 10002, null],
    bad_fruit_claim: 0,
    booking_notes: {},
    instruction_template_id: null,
    first_upload_carousel: true,
    propertys_tid: 34921,
    service_time: Date.now(),
    decorationCanEdit: true,
    electric_status: 0,
    isGoodsLock: false,
    imageEnhanceGray: false,
    hide_apply: false,
    showGoodsInstruction: false,
    // decorationPreview:
    //   "https://mobile.yangkeduo.com/comm_goods_decoration_preview.html?goods_id=555676079206&_oak_decoration_token=s3hIleB22muPAp+5Y5Z1VrLHets5q8lz7M70ucgZxyDc+FM2/3QC4RRgVV1BFwdsg1+KhR0VlI1D/8SJfngl2w==",
    must_have_spu: false,
    can_not_change_cat: false,
    can_not_change_spu: false,
    is_transfer_spu: false,
    // title_keywords: [
    //   {
    //     keyword: "触摸屏",
    //     source: "1",
    //     hot: false,
    //     rk: null,
    //     bad_keyword: null,
    //     good_tag_desc: null,
    //     bad_tag_desc: null,
    //   },
    //   {
    //     keyword: "盲盒捡漏",
    //     source: "4",
    //     hot: true,
    //     rk: null,
    //     bad_keyword: null,
    //     good_tag_desc: null,
    //     bad_tag_desc: null,
    //   },
    //   {
    //     keyword: "幸运盒",
    //     source: "1",
    //     hot: false,
    //     rk: null,
    //     bad_keyword: null,
    //     good_tag_desc: null,
    //     bad_tag_desc: null,
    //   },
    //   {
    //     keyword: "智能手表",
    //     source: "1",
    //     hot: false,
    //     rk: null,
    //     bad_keyword: null,
    //     good_tag_desc: null,
    //     bad_tag_desc: null,
    //   },
    //   {
    //     keyword: "盲机",
    //     source: "1",
    //     hot: false,
    //     rk: null,
    //     bad_keyword: null,
    //     good_tag_desc: null,
    //     bad_tag_desc: null,
    //   },
    // ],
    title_recommend: null,
    tiny_name: "",
    size_parent_spec_id: null,
    is_shop: 0,
    goods_type: 1,
    invoice_status: 0,
    invoice_mode: null,
    zhi_huan_bu_xiu,
    quan_guo_lian_bao,
    second_hand,
    is_pre_sale,
    pre_sale_time: "",
    country_id,
    origin_country_id: 0,
    warehouse: "",
    customs: "",
    is_customs: 0,
    // shipment_limit_second: 7776000,
    shipment_limit_second: 172800,
    delivery_type: null,
    cost_template_id: 451511038512128,
    weight: "",
    is_all_weight_same: true,
    groups: {
      single_price: 0,
      group_price: 0,
      carnival_price: null,
      customer_num: 2,
      buy_limit: 999999,
      order_limit: 999999,
      regular_limit: null,
      regular_limit_duration: null,
      min_num_of_order: null,
      community_group_buying: 0,
    },
    is_folt: 0,
    is_refundable: 1,
    lack_of_weight_claim: 0,
    goods_certificate: 0,
    goods_pattern: 8,
    customizable: 0,
    local_service_id_list: null,
    shop_group_id: 0,
    schedule_sale: {
      sale_type: 0,
    },
    card_verification_list: [],
    is_spring_festival_open_tag: false,
    transport_type_list: [],
    is_refundable_fifteen_day: 0,
    is_warranty_one_year: 0,
    same_city_template_id: 0,
    origin_is_pre_sale: 0,
    goods_srv_templates: [],
    is_only_for_remote_area: false,
    default_template_gray: true,
    is_default_template_id: 1,
    allergy_refund: 0,
    is_customized: 0,
    isCoupon: false,
    isAddLocalCityGoods: false,
    allow_purchase_info_supply: false,
    effect_logistics_weight_limit: 100000,
    cost_template_error: "",
    elec_goods_attributes: null,
    delivery_one_day: 0,
    mai_jia_zi_ti: null,
    shang_men_an_zhuang: null,
    song_huo_an_zhuang: null,
    song_huo_ru_hu: null,
    skus: [
      {
        id: 0,
        limit_quantity: 9999999,
        out_sku_sn: "",
        is_onsale: 1,
        multi_price_in_yuan: "4000",
        price_in_yuan: "5001",
        multi_price: 400000,
        price: 500100,
        quantity_delta: 5000000,
        thumb_url: mainImg.url,
        thumb_url_file_id: mainImg.file_id,
        // ...(function () {
        //   const obj = {
        //     thumb_url: "https://img.pddpic.com/mms-material-img/2023-04-21/2d13a494-024e-4335-8836-f43ab4a64b89.jpeg.a.jpeg",
        //     thumb_url_file_id: 22389709938
        //   }
        //   const index = randomInt(1, carousel_gallery.length - 2)
        //   const item = carousel_gallery[index]
        //   if(item){
        //     obj.thumb_url = item.url
        //     obj.thumb_url_file_id = item.file_id
        //   }
        //   return obj
        // })(),
        weight: 0,
        spec: [baseSpec],
        oversea_sku: {},
        sku_srv_templates: "",
      },
    ],
    market_price_in_yuan: "5002",
    market_price: 500200,
    out_goods_sn: "",
    dead_line_seconds: 86399,
    two_pieces_discount: 95,
    gold_price_template_id: 0,
    processing_charges: 0,
    processing_charges_in_yuan: "0",
    gallery: [
      mainImg,
      // {
      //   url: "https://img.pddpic.com/mms-material-img/2023-04-21/2d13a494-024e-4335-8836-f43ab4a64b89.jpeg.a.jpeg",
      //   type: 1,
      //   file_id: 22389709938,
      // },
      (function () {
        let obj = {
          ...mainImg,
          type: 2,
        }
        if (!useDefaultImage) {
          const item = gallery.find(item => item.type == 2)
          if (item) {
            obj.url = item.url
            obj.file_id = item.file_id
          }
        }
        return obj;
      })()
    ],
    is_sku_pre_sale,
    is_group_pre_sale,
    goods_properties: [{ "template_pid": 265486, "template_module_id": 45242, "ref_pid": 310, "pid": 5, "vid": 1986, "value": "", "value_unit": "" }, { "template_pid": 265487, "template_module_id": 45242, "ref_pid": 317, "pid": 16, "vid": 13299, "value": "", "value_unit": "" }, { "template_pid": 265488, "template_module_id": 45242, "ref_pid": 1620, "pid": 66, "value": "", "value_unit": "" }, { "template_pid": 296510, "template_module_id": 45242, "ref_pid": 1011, "pid": 34, "vid": 55796, "value": "", "value_unit": "" }, { "template_pid": 298527, "template_module_id": 45242, "ref_pid": 861, "pid": 274, "value": "", "value_unit": "" }, { "template_pid": 342836, "template_module_id": 45242, "ref_pid": 2543, "pid": 619, "vid": 1813444, "value": "", "value_unit": "" }, { "template_pid": 352507, "template_module_id": 45242, "ref_pid": 482, "pid": 93, "vid": 24046, "value": "", "value_unit": "" }, { "template_pid": 363426, "template_module_id": 45242, "ref_pid": 438, "pid": 40, "vid": 1959277, "value": "", "value_unit": "" }] as any[],
    is_goods_copy: null,
    is_auto_save: false,
    // validate_message:
    //   "bdfe8125dec425a5cf7d5a17002797f72d9ea2b75dbf278a31d16bfea736e4ae",
    // validate_message_v2:
    //   "aa1e048f4fde91ef76f8c4a4459dff92472652936b69688aec3c80981453fa11",
    crawlerInfo,
    ...otherData,
  };
  if (data.only_sku_property) {
    const obj = catData.find((item) => item.cat_id == baseInfo.cat_id)
    if (obj) {
      const name = obj.goodsName() + Math.ceil(Math.random() * 100)
      baseInfo.goods_name = name
      baseInfo.goods_desc = name
      baseInfo.cat_ids = obj.cat_ids
      baseInfo.goods_properties = obj.goods_properties
    }
  }
  if (!data.only_sku_property) {
    // 旗舰店
    const { cat_id_1, cat_id_2, cat_id_3, cat_id_4, cats, goods_type, shipment_limit_second } = detailsInfo
    baseInfo.cats = cats
    baseInfo.cat_ids = [cat_id_1, cat_id_2, cat_id_3, cat_id_4]
    baseInfo.goods_type = goods_type
    baseInfo.shipment_limit_second = shipment_limit_second
  }
  // baseInfo.goods_name = createRandomStr() + baseInfo.goods_name
  const { skus } = detailsInfo
  const skuItem = (skus as anyObj[]).find(item => item.spec.length)
  if (!skuItem) {
    baseInfo.skus = []
  }
  // if (!data.only_sku_property && skuItem) {
  if (!data.only_sku_property && skuItem) {
    const spec: any[] = skuItem.spec
    // baseInfo.skus[0].spec = spec.map(specItem => cloneDeep(specItem))
    baseInfo.skus[0].spec = spec.map(specItem => {
      const data = { ...specItem }
      Reflect.deleteProperty(data, 'bundle_spec_list')
      Reflect.deleteProperty(data, 'spec_note')
      return data
    })
    const { result, newSpec } = await createPddGoodsProperty({
      store: data.mall, pddGoodsDetails: data.detailsInfo, spec, only_sku_property: data.only_sku_property
    })
    baseInfo.goods_properties = result as any[];
    if (newSpec.length) {
      baseInfo.skus[0].spec = newSpec as any[]
    }
    // baseInfo.skus[0].thumb_url = skuItem.thumb_url
  } else if (!data.only_sku_property) {
    const { result, newSpec } = await createPddGoodsProperty({
      store: data.mall, pddGoodsDetails: data.detailsInfo, only_sku_property: data.only_sku_property
    })
    baseInfo.goods_properties = result as any[];
    if (newSpec.length) {
      baseInfo.skus[0].spec = newSpec as any[]
    }
  }
  console.log('baseInfo', baseInfo)
  return baseInfo;
}

export async function createPddGoodsProperty(data: {
  store: Store,
  pddGoodsDetails: anyObj,
  spec?: anyObj[],
  only_sku_property?: boolean
}) {
  const { store, pddGoodsDetails, spec, only_sku_property } = data
  const { cat_id, goods_id, goods_commit_id } = pddGoodsDetails

  const res = await getUpLoadData(store, { goods_commit_id, goods_id, cat_id })
  const { property, goods_property } = res;
  console.log('property', property)
  // console.log('goods_property', goods_property)
  // console.log('pddGoodsDetails', pddGoodsDetails)
  const result: anyObj[] = [];
  const newSpec: anyObj[] = [];
  if (!only_sku_property) {
    (property.modules as anyObj[]).forEach(moduleItem => {
      (moduleItem.propertys as anyObj[]).forEach(m_p_item => {
        const obj: anyObj = {
          template_pid: m_p_item.id,
          template_module_id: moduleItem.id,
          ref_pid: m_p_item.ref_pid,
          pid: m_p_item.pid,
          value: '',
          value_unit: '',
          vid: void 0
        }
        if (m_p_item.goods_properties) {
          const _item = m_p_item.goods_properties[0]
          if (_item) {
            if (_item.vid) {
              obj.vid = _item.vid
            } else {
              obj.value = _item.v_value
              obj.value_unit = _item.p_unit
            }
          }
        }
        result.push(obj)
      })
    });
  }


  if (spec) {
    const specResult: typeof result = []
    const { spec_module } = property
    const specProperties: anyObj[] = spec_module.properties || []
    console.log(spec)
    console.log(spec_module)
    console.log(specProperties)
    spec.forEach(specItem => {
      const { parent_id } = specItem
      const specProperty = specProperties.find(item => item.spec_id === parent_id)
      if (!specProperty) {
        return
      }
      const custom_goods_property = (specProperty.custom_goods_properties as anyObj[]).find(item => item.spec_id === specItem.spec_id)
      // if (!custom_goods_property) {
      //   return
      // }
      const specProperty_goodsProperty = specProperty.goods_properties?.find((item: anyObj) => item.spec_id === specItem.spec_id)
      // console.log('specProperty', specProperty)
      // console.log('custom_goods_property', custom_goods_property)
      specResult.push({
        group_id: custom_goods_property?.group_vo?.id || specProperty_goodsProperty?.group_vo?.id || 0,
        parent_spec_id: parent_id,
        pid: specProperty.pid,
        ref_pid: specProperty.ref_pid,
        spec_id: specItem.spec_id,
        template_module_id: spec_module.id,
        template_pid: specProperty.id,
        value: specItem.spec_name,
        value_unit: specProperty.value_unit,
        vid: custom_goods_property?.vid || specProperty_goodsProperty?.vid || 0
      })
    })
    if (!specResult.length) {


      specProperties.forEach(specProperty => {
        let spec: anyObj = specProperty.values[0]


        // if (!spec) {
        //   spec = spec_module.custom_properties[0]
        //   if (spec) {
        //     spec._group_id = spec.group_vo?.id || 0
        //     spec.value = spec.v_value
        //   }
        // }
        const data = {
          group_id: spec?._group_id || 0,
          img_url: "",
          note: "",
          parent_spec_id: specProperty.spec_id,
          pid: specProperty.pid,
          ref_pid: specProperty.ref_pid,
          spec_id: spec?.spec_id,
          template_module_id: spec_module.id,
          template_pid: specProperty.id,
          value: spec?.value,
          // value_unit:spec.
          vid: spec?.vid,
          value_unit: ''
        }
        if (data.vid) {
          specResult.push(data)
          newSpec.push({
            parent_id: specProperty.spec_id,
            parent_name: specProperty.name_alias,
            spec_id: spec.spec_id,
            spec_name: spec.value
          })
        }
      })

    }
    if (!specResult.length && spec_module?.custom_properties) {
      const custom_properties = spec_module.custom_properties as anyObj[]
      const custom_property = custom_properties?.[0]

      if (custom_property) {
        const custom_goods_property = custom_property.goods_properties[0]
        const data = {
          group_id: custom_goods_property.group_vo?.id || 0,
          img_url: custom_goods_property.img_url,
          note: custom_goods_property.note,
          parent_spec_id: custom_goods_property.parent_spec_id,
          pid: 0,
          ref_pid: 0,

          spec_id: custom_goods_property.spec_id,
          template_module_id: 0,
          template_pid: 0,
          vid: custom_goods_property.vid,
          value: custom_goods_property.v_value,
          value_unit: custom_goods_property.p_unit
        }
        specResult.push(data)
        newSpec.push({
          parent_id: custom_property.spec_id,
          parent_name: custom_property.name_alias,
          spec_id: data.spec_id,
          spec_name: data.value
        })
      }
    }
    result.push(...specResult)
  }
  // console.log('result',result)
  return { result, newSpec }
  // console.log('getUpLoadData',res)
  // const res2 = await request({
  //   url: 'http://192.168.1.6:85/api/tool/get_goods_property',
  //   // url: '/api/tool/get_goods_property',
  //   data: {
  //     property,
  //     goods_property
  //   },
  //   method: 'post'
  // },{showErrorMsg:false})
  // console.log('res2',res2)
  // return res2
  // console.log(res2)
  // const goods_property_map = new Map()
  // goods_property.forEach((item: anyObj) => {
  //   goods_property_map.set(item.pid, item)
  // })
  // const result: anyObj = {

  // }
  // const modules: any = property.modules[0];
  // (modules.propertys as anyObj[]).forEach(item => {
  //   const obj: anyObj = {
  //     template_pid: item.id,
  //     template_module_id: modules.id,
  //     ref_pid: item.ref_pid,
  //     pid: item.pid,
  //     value: '',
  //     value_unit: '',
  //     vid: 0
  //   }
  //   const obj2: anyObj = {};
  //   const item2 = goods_property_map.get(item.ref_pid);
  //   if (item2) {
  //     (item2.values as anyObj[]).forEach(value => {
  //       obj2
  //     })
  //   }


  // })
}
