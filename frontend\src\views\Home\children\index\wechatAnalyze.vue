<template>
    <el-config-provider size="default">
        <!-- <div class="wechat-analyze" v-show="modelValue">
            <header>
                <div class="controls">
                    <div class="item" @click="proxyAction('close')">
                        <Icon href="icon-close" />
                    </div>
                </div>
            </header>
            <div class="top">
                <el-alert type="warning" :closable="false">

                    <p>1.第一次加载可能会稍慢</p>
                    <p>2.在微信中使用PDD小程序，进入商品详情页会自动解析该商品</p>
                    <p>3.也可在微信中点击商品详情链接解析商品</p>
                    <p class="danger">4.在关闭软件之前请关闭微信解析</p>

                </el-alert>
            </div>

            <div class="table-container"> -->

                <!-- <el-table height="648px" :data="tableData">
                    <el-table-column label="商品id" prop="goods_id" width="130"></el-table-column>
                    <el-table-column label="商品图片" prop="goods_img" width="80">
                        <template #default='scope'>
                           <el-image :src="scope.row.goods_img" :preview-src-list="[scope.row.goods_img]" :preview-teleported="true"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名" prop="goods_name" show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" prop="actions" width="120">
                        <template #default='scope'>
                            <el-space>
                                <el-link :underline="false" type="primary"
                                    @click="tableAction('load', scope.row)">加载</el-link>
                            </el-space>
                        </template>
                    </el-table-column>
                </el-table> -->
                <!-- <el-scrollbar height="368px">
                    <div class="log-list">
                        <p v-for="item in state.logList">
                            <span>{{ item.msg }}</span>
                            <span class="load primary" v-if="item.goods_id"
                                @click="tableAction('load', state.tableMap.get(item.goods_id!)!)">加载</span>
                        </p>
                    </div>
                </el-scrollbar>
            </div>
        </div> -->
    </el-config-provider>
</template>
<script lang='ts' setup>
import { computed, reactive } from 'vue';
import { ipc } from '/@/apis/config';
import { addGoodsRecord } from '/@/apis/goods';
import { ElMessage } from 'element-plus';
import { execJStoAnalyzeWindow, start_wechat, stop_wechat } from '/@/apis/analyze';

const emits = defineEmits<{
    (e: 'update:modelValue', data: boolean): any,
    (e: 'setGoodsInfo', data: GoodsInfo): any,
    (e: 'couponList', data: anyObj[]): any
}>()
const props = defineProps<{
    modelValue: boolean
}>()

const state: {
    tableMap: Map<GoodsInfo['goods_id'], GoodsInfo>
    logList: Array<{
        goods_id?: GoodsInfo['goods_id'],
        msg: string
    }>
} = reactive({
    tableMap: new Map(),
    logList: []
})

// const tableData = computed(() => {
//     return [...state.tableMap.values()]
// })

function resolveByWechatUrl(data: anyObj): { goodsInfo: GoodsInfo, couponList: anyObj[] } {
    const goods = data.store.initDataObj.goods as anyObj
    const mall = data.store.initDataObj.mall as anyObj

    const {
        goodsName,
        thumbUrl,
        minNormalPrice,
        minGroupPrice,
        goodsID,
        skus,
        combineGroups,
        groupTypes,
        activity
    } = goods;
    const { mallName, mallId } = mall;

    const goodsInfo: GoodsInfo = {
        goods_id: Number(goodsID),
        mallName,
        mallId,
        goods_name: goodsName,
        activity_id: activity?.activityID || 0,
        groupPrice: Number((minGroupPrice / 1).toFixed(2)),
        normalPrice: Number((minNormalPrice / 1).toFixed(2)),
        goods_img: thumbUrl,
        skus: skus.map((item: anyObj) => {
            const { skuId, thumbUrl, specs, normalPrice, groupPrice } = item
            return {
                skuId,
                spec: specs.map((spec: anyObj) => spec.spec_value).join('-'),
                skuImg: (thumbUrl as string).replaceAll('\u002F', '/'),
                groupPrice: Number((Number(groupPrice) / 1).toFixed(2)),
                normalPrice: Number((Number(normalPrice) / 1).toFixed(2))
            }
        }),
        group_order_ids: combineGroups.combineGroupList.map((item: anyObj) => item.groupOrderId),
        group_id: {
            multiple: []
        },
        coupon_code: "",
    }
    groupTypes.forEach((item: anyObj) => {
        if (Number(item.requireNum) <= 1) {
            goodsInfo.group_id.alone = item.groupID;
        } else {
            goodsInfo.group_id.multiple.push(item.groupID);
        }
    })
    let couponList: anyObj[] = []
    const list = data.store.initDataObj.oakData.subSections.discountPopSection?.data?.mallPromoList
    if (list && list.length) {
        couponList = list
    }
    // if (list && list.length) {
    //   list.find((item: anyObj) => {
    //     if (item.tagDesc === '店铺关注券') {
    //       goodsInfo.coupon_code = item.batchSn
    //       return true
    //     } else {
    //       return false
    //     }
    //   })
    //   orderCreate.couponList = list
    // }
    return { goodsInfo, couponList }
}
function resolveByWechatPage(data: anyObj): GoodsInfo {
    //  console.log(data)
    const goods = data.goods
    const skus = data.sku
    const mall = data.mall_entrance.mall_data
    const { unselect_normal_save_price, min_group_price } = data.price
    const { goods_id, mall_id, goods_name, thumb_url, group } = goods
    const { mall_name } = mall
    const group_id: GoodsInfo['group_id'] = {
        multiple: []
    };
    (group as anyObj[]).forEach(item => {
        if (item.customer_num <= 1) {
            group_id.alone = item.group_id
        } else {
            group_id.multiple.push(item.group_id)
        }
    });
    const goodsInfo: GoodsInfo = {
        goods_id: Number(goods_id),
        goods_name,
        goods_img: thumb_url,
        mallId: mall_id,
        mallName: mall_name,
        activity_id: 0,
        groupPrice: Number((min_group_price / 100).toFixed(2)),
        normalPrice: Number((unselect_normal_save_price / 100).toFixed(2)),
        coupon_code: '',
        group_id,
        group_order_ids: [],
        skus: (skus as anyObj[]).map(item => {
            const { sku_id, specs, thumb_url, normal_price, group_price } = item
            return {
                skuId: sku_id,
                spec: specs.map((spec: anyObj) => spec.spec_value).join('-'),
                skuImg: thumb_url,
                groupPrice: Number((Number(group_price) / 100).toFixed(2)),
                normalPrice: Number((Number(normal_price) / 100).toFixed(2)),
            }
        })
    }
    return goodsInfo
}

function resolveGoods(event: Electron.IpcRendererEvent, data: { type: 'wechat-url' | 'wechat-page', str: string, }) {
    console.log(data)
    const { type, str } = data
    try {
        const goodsInfoData = JSON.parse(str)
        let goodsInfo: GoodsInfo | undefined = void 0
        let couponList: anyObj[] = []

        switch (type) {
            case 'wechat-page': {
                goodsInfo = resolveByWechatPage(goodsInfoData)
                break
            }
            case 'wechat-url': {
                const result = resolveByWechatUrl(goodsInfoData)
                goodsInfo = result.goodsInfo
                couponList = result.couponList
                break
            }
       
        }
        if (goodsInfo) {
            state.tableMap.set(goodsInfo.goods_id, goodsInfo)
            tableAction('save', goodsInfo)
            tableAction('load', goodsInfo, couponList)
            execJStoAnalyzeWindow(`addLog('成功解析商品-${goodsInfo.goods_id}')`)
            state.logList.push({
                goods_id: goodsInfo.goods_id,
                msg: `${goodsInfo.goods_id}-已成功解析`
            })
        }

    } catch (e) {
        console.log(e)
    }
    // return goodsInfo
}
ipc.off('analyzeController.goodsDetails', resolveGoods)
ipc.on('analyzeController.goodsDetails', resolveGoods)

function tableAction(action: 'save' | 'load', row: GoodsInfo, couponList?: anyObj[]) {
    if (!row) {
        return
    }
    switch (action) {
        case 'load': {
            emits('setGoodsInfo', row)
            if (couponList) {
                emits('couponList', couponList)
            }
            break
        }
        case 'save': {
            addGoodsRecord(row)
            // .then(() => {
            //     ElMessage.success({
            //         message: '保存成功',
            //         grouping: true
            //     })
            // })
            // .catch(res => {
            //     if (res.code == 2) {
            //         ElMessage.success({
            //             message: '保存成功',
            //             grouping: true
            //         })
            //     } else {
            //         ElMessage.warning({
            //             message: '保存失败',
            //             grouping: true
            //         })
            //     }
            // })
            break
        }
    }
}

function proxyAction(action: 'start' | 'close') {
    switch (action) {
        case 'start': {
            state.tableMap.clear()
            state.logList.length = 0
            return start_wechat()
                .then(res => {
                    console.log(res)
                    if (res.success) {
                        ElMessage.success('已启动')
                        return Promise.resolve(res)
                    } else {
                        ElMessage.warning('启动失败' + res.msg.toString())
                        return Promise.reject(res)

                    }
                })
            break
        }
        case 'close': {
            return stop_wechat()
                .then(res => {
                    if (res === 'success') {
                        ElMessage.success({
                            message: '已停止',
                            grouping: true
                        })
                        emits('update:modelValue', false)
                        return Promise.resolve(res)
                    } else {
                        ElMessage.warning({
                            grouping: true,
                            message: res
                        })
                        return Promise.reject(res)
                    }
                })
            break
        }
    }
}


defineExpose({
    proxyAction
})

</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
.wechat-analyze {
    // width: 1070px;
    // height: 780px;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
    width: 500px;
    height: 500px;
    background-color: var(--el-bg-color-page);
    border: var(--el-border);
    box-sizing: border-box;
    position: absolute;
    z-index: 10;
    box-shadow: var(--diy-shadow);
    border-radius: 5px;
    overflow: hidden;
}

header {
    height: 30px;
    display: flex;
    justify-content: flex-end;
    background-color: var(--el-color-primary);

    .controls {
        padding: 0 5px;
        box-sizing: border-box;
        height: 100%;
        display: flex;
        align-items: center;

        .item {
            margin-left: 8px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background-color: var(--el-fill-color-blank);
            font-size: 16px;
            color: var(--el-color-black);
            cursor: pointer;
        }
    }
}

.top {
    display: flex;

    p {
        line-height: 25px;
        font-size: 14px;
    }

    .controls {
        width: 30%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 10px;

        .el-button {
            margin-top: 3px;
        }

        p,
        .el-button {
            width: 100%;
            flex-grow: 1;
        }

    }
}

.table-container {
    margin-top: 5px;

    .el-scrollbar {
        .log-list {
            min-height: 368px;

            // background-color: red
            p {
                font-size: 12px;
                box-sizing: border-box;
                padding: 2px 5px;

                .load {
                    margin-left: 5px;
                    cursor: pointer;
                }
            }
        }
    }

    .el-table {
        @include commonTableHeader();

        .el-image {
            width: 45px;
            height: 45px;
            border-radius: 5px;
        }
    }
}
</style>
