import { ElMessage } from "element-plus";
import { ipc, path } from "./config";
import request, { anyRequest, storeAntiContent } from "./page";
import {  pageStepRequest, pddResErrorMsg, retry, uuid } from "../utils/common";
import { antiContentFromWeb, pddStoreRequestHeader } from "./changeSales";

const controller = "controller.stores.";

/**
 * 读取记录列表
 * @param data
 * @returns
 */
export function getStoresList(data: { page: number; limit: number }) {
  return ipc.invoke("controller.stores.getStoresList", data);
}

// 添加一条记录
export function addStoreItem(data: Omit<Store, "id" | "update_time">) {
  const _data: anyObj = { ...data, update_time: Date.now() };
  return ipc
    .invoke(controller + "addStoreItem", {
      ..._data,
    })
    .then((res) => {
      // console.log(res)
      if (res.code == 2) {
        ipc.invoke(controller + "updateStore", { ..._data }).then((res) => {
          console.log("update res");
        });
      }
      if (res.code) {
        return Promise.reject(res);
      } else {
        return res;
      }
    });
}

export function updateStore(data: Partial<Store>) {
  return ipc.invoke(controller + "updateStore", { ...data });
}

// 删除一条记录
export function deleteStoreItem(mallId: Store["mallId"]) {
  return ipc.invoke(controller + "deleteStoreItem", mallId);
}
export function findStoreItem(mallId: Store["mallId"]) {
  return ipc.invoke(controller + "findStoreItem", mallId);
}




export async function getStoreGoodsList(store: Store, data: {
  goods_id_list?: string[]
  page?: number
  size?: number
  is_onsale?: number
  sold_out?: number
}) {
  const headers = await pddStoreRequestHeader(store, {
    "Referer": 'https://mms.pinduoduo.com/goods/goods_list',
  })
  const config = {
    url: "https://mms.pinduoduo.com/vodka/v2/mms/query/display/mall/goodsList",
    method: 'post',
    headers,
    data: {
      page: 1,
      size: 100,
      is_onsale: 1,
      sold_out: 0,
      ...data
    }
  }
  const res = await anyRequest(config,)

  return res
}
/**店铺批发商品列表 */
export async function getStorePifaGoodsList(
  store: Store
): Promise<{ code: number; msg: string; data: anyObj[] }> {
  const response: any = {
    code: 1,
    msg: "",
    data: []
  }
  const allList = await pageStepRequest({
    async request(currentPage) {
      const config = {
        url: "https://mms.pinduoduo.com/mille/mms/goods/pageQueryGoods",
        method: "post",
        data: { pageNum: currentPage, pageSize: 50, bizId: 1 },
        headers: await pddStoreRequestHeader(store),
      }
      const res = await anyRequest(config)
      if (!res.success) {
        response.code = 1
        response.msg = pddResErrorMsg(res)
        return Promise.reject()
      }

      const { total, goods_list } = res.result
      return { list: goods_list, total }
    }
  })
  response.data = allList
  return response

}

/**检测登录 */
export async function _checkStoreLogin(store: Store) {
  const config = {
    url: 'https://mms.pinduoduo.com/janus/api/checkLogin',
    method: 'post',
    headers: await pddStoreRequestHeader(store,{"Referer":"https://mms.pinduoduo.com/home"}),
    data: {}
  }
  return anyRequest(config)

}
export async function checkStoreLogin(store: Store) {
  return retry(async (index) => {
    const res = await _checkStoreLogin(store)
    if (res?.result?.login === true) {
      return res
    } else if (index >= 5) {
      return res
    } else {
      return Promise.reject(res)
    }
  }, 5, 1000)
}
/**检查运费险 */
export async function checkStoreInsurance(
  store: Store,
  options = { showErrorMsg: true }
) {
  const response:any = {
    code: 0,
    msg: "",
    data: {},
  };
  const config = {
    url: "https://mms.pinduoduo.com/mms-gateway/rfreight/postpaid/insurance/mall_insurance_status",
    method: "post",
    headers: await pddStoreRequestHeader(store),
  };
  const res = await anyRequest(config);
  response.code = res.success ? 0 : 1;
  response.data = res
  response.msg = pddResErrorMsg(res)
  if (response.code) {
    options.showErrorMsg &&
      ElMessage.warning({
        message: response.msg,
        grouping: true,
      });
    return Promise.reject(response)
  }
  return response
}
/**打开运费险 */
export async function openStoreInsurance(
  store: Store,
  options = { showErrorMsg: true }
) {
  const response:any = {
    code: 0,
    msg: "",
    data: {},
  };
  const config = {
    url: "https://mms.pinduoduo.com/rfreight/postpaid/pc/open_service",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: { chanel: "1033", serviceType: 0, serviceMode: 1 },
  };
  const res = await anyRequest(config);
  response.code = res.success ? 0 : 1;
  response.data = res
  response.msg = pddResErrorMsg(res)
  if (response.code) {
    options.showErrorMsg &&
      ElMessage.warning({
        message: response.msg,
        grouping: true,
      });
    return Promise.reject(response)
  }
  return response
}
/**关闭运费险 */
export async function closeStoreInsurance(
  store: Store,
  options = { showErrorMsg: true }
) {
  const response:any = {
    code: 0,
    msg: "",
    data: {},
  };
  const config = {
    url: "https://mms.pinduoduo.com/rfreight/postpaid/pc/close",
    method: "post",
    headers: await pddStoreRequestHeader(store),
    data: { action: 0 },
  };
  const res = await anyRequest(config);
  response.code = res.success ? 0 : 1;
  response.data = res
  response.msg = pddResErrorMsg(res)
  if (response.code) {
    options.showErrorMsg &&
      ElMessage.warning({
        message: response.msg,
        grouping: true,
      });
    return Promise.reject(response)
  }
  return response
}


export async function get_signature(store: Store) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: "https://mms.pinduoduo.com/galerie/business/get_signature",
    method: "post",
    headers,
    data: {
      bucket_tag: "mms-material-img",
    },
  };
  return anyRequest(config);
}

export async function uploadImageToStore(store: Store, imageBase64: string) {
  const signatureRes = await get_signature(store);
  if (!signatureRes.success) {
    return Promise.reject(signatureRes);
  }
  const headers = await pddStoreRequestHeader(store);
  return ipc.invoke(controller + "uploadImageToStore", {
    signature: signatureRes.result.signature,
    image: imageBase64.replace(/^data.*base64,/, ""),
    // image:imageBase64,
    headers,
  });
}

export async function storeCreateFile(store: Store, data: { url: string }) {
  const res = await antiContentFromWeb();
  const headers = await pddStoreRequestHeader(store, {
    "anti-content": res.data.anti_content1,
  })
  const name = uuid();
  const config = {
    url: "https://mms.pinduoduo.com/garner/mms/file/create",
    method: "post",
    headers,
    data: {
      ...data,
      name,
      extension: path.extname(data.url).replace(".", ""),
      create_time: Date.now(),
    },
  };
  console.log("[storeCreateFile-config]", config);
  return anyRequest(config);
}

/**白底图长图信息1白底3长图 返回结果为check_status 2才需要恢复 */
export async function materialInfo(
  store: Store,
  data: { goods_id: number | string; type: number }
) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: `https://mms.pinduoduo.com/giraffe/mms/material/goodsMaterialInfo`,
    method: "post",
    headers,
    data,
  };
  return anyRequest(config);
}

/**上传白底图 长图 */
export async function createMateria(store: Store, data: {
  content: string
  type: number
  cat_id_1: number | null
  cat_id_2: number | null
  cat_id_3: number | null
  cat_id_4: number | null
  goods_id: number
  mall_file_id: number
}) {
  const res = await antiContentFromWeb();
  const headers = await pddStoreRequestHeader(store,
    {
      "anti-content": res.data.anti_content1
    }
  )
  const config = {
    url: `https://mms.pinduoduo.com/giraffe/mms/material/create`,
    method: "post",
    headers,
    data: {
      ...data,
      source: 2
    },
  };
  return anyRequest(config);
}

/**
 * 店铺商品列表 
 * (controller.stores.getStoreGoodsList 和这个是同一个接口，不过后台的是获取全部)
 */
export async function storeGoodsListApi(sotre: Store, data: {
  page?: number
  size?: number
  is_onsale?: 0 | 1,
  sold_out?: 0 | 1
  goods_id_list?: string[]
  pre_sale_type?: number
}) {
  data.page = data.page || 1;
  data.size = data.size || 50;
  const res = await antiContentFromWeb();
  const headers = await pddStoreRequestHeader(sotre, {
    "content-type": 'application/json',
    "anti-content": res.data.anti_content1,
    "Referer": 'https://mms.pinduoduo.com/goods/goods_list',
  })
  const config = {
    url: "https://mms.pinduoduo.com/vodka/v2/mms/query/display/mall/goodsList",
    method: "post",
    headers,
    data,
  };
  return anyRequest(config);
}

export async function rejectGoodsListApi(store: Store, goods_id_list?: string[]) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: "https://mms.pinduoduo.com/glide/v2/mms/query/commit/list",
    method: "post",
    headers,
    data: {
      check_status: 3,
      length: 10,
      start: 0,
      goods_id_list
    }
  }
  return anyRequest(config);
}

/**赠品列表商品列表 */
export async function giftGoodsListApi(store: Store, data: {
  page?: number
  size?: number
  /**可以传ID */
  goods_name: string,
  type?: number
}) {
  data.page = data.page || 1;
  data.size = data.size || 10;
  data.type = data.type || 1;
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: "https://mms.pinduoduo.com//bordeaux/mms/gift/query_event_gift_list",
    method: "post",
    headers,
    data,
  }
  return anyRequest(config);
}

/**活动赠品绑定的主商品列表 */
export async function giftMainGoodsListApi(store: Store, data: {
  page?: number
  size?: number
  /**可以传ID */
  goods_name: string,
  gift_goods_id: number
}) {
  data.page = data.page || 1;
  data.size = data.size || 10;
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: "https://mms.pinduoduo.com//bordeaux/mms/gift/query_event_goods_list",
    method: "post",
    headers,
    data,
  }
  return anyRequest(config);
}

/**活动主商品SKU列表 */
export async function giftMainGoodsSku(store: Store, data: {
  /**活动主商品 */
  goods_id: number
  /**赠品id */
  gift_goods_id: number
}) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: 'https://mms.pinduoduo.com//bordeaux/mms/gift/query_event_skus_list',
    method: "post",
    headers,
    data
  }
  return anyRequest(config)
}

/**创建下单送赠品活动 */
export async function createGiftGoodsApi(store: Store, data: any) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: 'https://mms.pinduoduo.com//bordeaux/mms/gift/create_gift_promotion_event',
    method: "post",
    headers,
    data
  }
  return anyRequest(config)
}

export async function cancelGiftGoodsApi(store: Store, data: {
  promotion_event_sn: string
}) {
  const headers = await pddStoreRequestHeader(store)
  const config = {
    url: 'https://mms.pinduoduo.com//bordeaux/mms/gift/offline_gift_promotion_event',
    method: "post",
    headers,
    data
  }
  return anyRequest(config)
}

export async function getVerifyImage(verify_auth_token: string) {
  const anti_content = await storeAntiContent()
  const config = {
    url: `https://apiv2.pinduoduo.net/api/phantom/obtain_captcha`,
    method: "post",
    data: {
      anti_content,
      verify_auth_token
    }
  }
  return anyRequest(config)
}
export async function captchaCollect(verify_auth_token: string) {
  const anti_content = await storeAntiContent()
  const config = {
    url: 'https://apiv2.pinduoduo.net/api/phantom/vc_pre_ck_b',
    method: 'post',
    data: {
      anti_content,
      verify_auth_token,
      "sdk_type": 3,
      client_time: Date.now()
    }
  }
  return anyRequest(config)
}
export async function pdd_user_verify(data: { verify_code: string, captcha_collect: string, verify_auth_token: string, anti_content?: string }) {
  if (!data.anti_content) {
    const anti_content = await storeAntiContent()
    data.anti_content = anti_content
  }
  const config = {
    url: 'https://apiv2.pinduoduo.net/api/phantom/user_verify',
    method: 'post',
    data: {
      ...data
    }
  }
  console.log('pdd_user_verify', config)
  return anyRequest(config)
}

/** 通过规格名字查询(创建)规格ID */
export async function querySpecByName(mall: Store, data: {
  cat_id: number
  name: string
  parent_id: number
}) {
  const config = {
    url: 'https://mms.pinduoduo.com/glide/v2/mms/query/spec/by/name',
    method: 'post',
    data,
    headers: await pddStoreRequestHeader(mall)
  }
  return anyRequest(config)
}


/**获取令牌 */
export async function generateAccessTokenApi(mall: Store) {
  return anyRequest({
    url: 'https://mms.pinduoduo.com/janus/api/subSystem/generateAccessToken',
    method: 'post',
    headers: await pddStoreRequestHeader(mall, { etag: "ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k" }),
    data: {
      redirectUrl: "https://fuwu.pinduoduo.com/clint/api/login?redirect=https%3A%2F%2Ffuwu.pinduoduo.com%2Fservice-market%2F"
    }
  })
}

/**通过令牌获取服务市场token */
export async function getServiceMarketTokenApi(mall: Store, params: {
  accessToken: string
  redirect?: string
  username?: string
}) {
  return anyRequest({
    url: 'https://fuwu.pinduoduo.com/clint/api/login',
    method: 'GET',
    withCredentials: true,
    maxRedirects: 0,
    params: {
      redirect: 'https://fuwu.pinduoduo.com/service-market',
      username: JSON.parse(mall.infoJSON).username,
      ...params
    },
    headers: {
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "Host": 'fuwu.pinduoduo.com',
      'Referer': "https://mms.pinduoduo.com/",
      "Cookie": '_nano_fp=XpmoXpmyn5X8n0TjlT_TgplQ~r_6KO9l2oFi4DxQ; terminalFinger=9EoR4Sq4oCeb9OoXS47oTnd7O5TrDNNl; api_uid=rBU0BWVuvdiFQzTjUP2gAg==; _bee=YLyAxX6fC80AB3BmqEKVa2iKnSkSfVlq; _f77=8b5012e0-0249-4fc3-b78e-aee27a8acf9f; _a42=3fff55b3-5be3-4934-8ac9-d42f29d0e612; rckk=YLyAxX6fC80AB3BmqEKVa2iKnSkSfVlq; ru1k=8b5012e0-0249-4fc3-b78e-aee27a8acf9f; ru2k=3fff55b3-5be3-4934-8ac9-d42f29d0e612; sm_tid=82482%2C31aaccc0-1640-43e8-9a05-b151a7a8a412%2C2a23ec3b-a293-45c5-84d0-9bd44e359823%2Ca33ee29f-501a-41eb-ae89-3d28d1ad1090%2Cd04f5460-2ecf-4bcb-af3c-d3647cbdab89%2Cdddfc67a-3484-48c4-8611-2ade460125f1%2C84ec2bbf-a828-41c0-8993-fb9ed3d6295f%2Cd3ea153e-f9e3-45ce-a138-c05313520e16%2Cf438cb2e-1d98-4a83-b74b-66d6c7079c38%2C997009b6-5bfa-4ca3-b92e-addba888a0f5%2C5cf922eb-50ee-43a1-9d94-375a466d35ec%2C468477a1-b05e-430f-876d-6fc1cd2e26ba%2C1ecc0d8d-ac98-411e-8360-d95bd48a9f1d%2C151eb8b8-9953-4d20-9a66-2d1382ecd3bb%2Cf8a8faa5-0391-4c33-8543-2c6be4690480%2Cf293881e-e310-437e-9747-c7476c5c39b6%2C1b86a959-31b2-465b-a951-79afb8a67a08%2C20ceb8fa-c27c-4408-a068-2387a85d340b%2C99a2bef0-a385-4494-afcc-a4a7292fa72f%2C08a6d7a5-7daf-4963-ae09-22923704efc2%2C6e353ddb-68f7-40ed-8c6a-af7149cf5b76',
      "Connection": "keep-alive",
      "Accept-Encoding": "gzip, deflate, br"
    }
  })
}

export async function getSubPassId(mall: Store) {
  const res = await generateAccessTokenApi(mall)
  if (!res.success) {
    return res
  }
  const accessToken = res.result?.accessToken

  // const accessToken = 'eyJ0IjoiYjJ5S3lGaGc4VWRZaWxCeTRzZzM1M1pZdVU0eUlCNTZ0U2g4ckxWNUhMMVlPTTVVcGF6OWxxSjNRbEFxQ3VRSyIsInYiOjEsInMiOjExLCJtIjoyMjE3OTk0OTksInUiOjEzMDY2NzI5Mn0'
  const tokenRes = await getServiceMarketTokenApi(mall, { accessToken })
  if (!tokenRes.success) {
    return tokenRes
  }
  const cookieArr: string[] = tokenRes._responseHeader['set-cookie']
  const item = cookieArr.find(item => item.includes('SUB_PASS_ID'))
  if (!item) {
    return {
      success: false,
      msg: '获取cookie失败,find'
    }
  }
  const result = item.match(/SUB_PASS_ID=(.*?);/)
  const subPassId = result?.[1]
  if (!subPassId) {
    return {
      success: false,
      msg: '获取cookie失败,reg'
    }
  }
  return {
    subPassId,
    success: true
  }
}


/**服务市场商品详情 */
export async function getInfoApi(subPassId: string, data: {
  serviceId: number,
}) {

  return anyRequest({
    url: 'https://fuwu.pinduoduo.com/columbine/pop/service/get/info',
    method: 'post',
    headers: {
      "Origin": "https://fuwu.pinduoduo.com",
      "Referer": `https://fuwu.pinduoduo.com/service-market/service-detail?detailId=${data.serviceId}`,
      "Cookie": `api_uid=rBXNA2bAAI2FXi2IUYj0Ag==;rckk=ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k;_bee=ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k;ru1k=b93a0f2b-2ef5-459f-9428-8a40f6ec6b12;_f77=b93a0f2b-2ef5-459f-9428-8a40f6ec6b12;ru2k=2ba6fc6a-7521-46bd-bb02-ab7fe6bf9ba0; _a42=2ba6fc6a-7521-46bd-bb02-ab7fe6bf9ba0; _nano_fp=XpmxXqCylp9bn5dxX9_P2Z1uLvq_ieq6atkP6yHW;terminalFinger=gL6L4VZDVK1Gk9T0fF4QprRHykRw9xVP;webp=true;SUB_PASS_ID=${subPassId};sm_tid=ffa639b6-385a-4bce-b277-88a4da813591%2C82482%2C78f2b9a7-dd63-4297-98d8-4edc2c4396b3%2C82482%2C16e6da76-12e9-44eb-bad7-0cb904e626df%2C82482%2C41b182d2-50b5-4fe3-b9fc-02c4bfe0ca22%2C82482%2C3a2ce981-0169-4af4-81a2-08e6cff8f2a6%2C82482%2C764e790a-b47f-40a1-beb8-f99428b6e649%2C82482%2C4589bcfc-0de5-420a-8887-f395e6793879%2C82482%2C6150541b-2b5c-4997-969f-3de922fec98e%2C82482%2C2f54b237-3b2e-4abf-ae1e-b772d73120fb%2C82482%2C9f6219d9-32a3-4842-bc6d-9674e64c544c%2C82482%2Cea0ea85e-6650-4f78-9d64-5b4035d1e258%2C82482%2C0d138b0f-60b9-4e3e-bf10-395025ed358b%2C82482%2C7fd86a36-6a72-4ec1-b354-12999fd24d02%2C82482%2C3d17a536-dbee-4e46-a219-c536c85c98d9%2C82482%2Cac2d99ea-bc66-4930-a91d-4e0cdaa696c7%2C82482`,
      "Anti-Content": await storeAntiContent(),
      "content-type": "application/json;charset=UTF-8",
      "priority": 'u=1, i'
    },
    data
  })
}
export async function buyTryGoodsApi(opt: { row: anyObj, subPassId: string }) {
  let data: anyObj = {}
  let headers: anyObj = {}
  const subPassId = opt.subPassId
  try {
    const obj = JSON.parse(opt.row.data)
    data = obj.data,
      headers = obj.headers
  } catch (e) {
    return {
      success: false,
      error_msg: "获取cookie失败,reg"
    }
  }
  const infoRes = await getInfoApi(subPassId, { serviceId: data.serviceId })
  if (infoRes.success) {
    // console.log(infoRes)
    const skuItem = (infoRes.result.skuList as anyObj[]).find(item => item.skuId == Number(data.skuId))
    if (skuItem && skuItem.skuDisableDesc?.includes('不能再次试用')) {
      return {
        success: true,
        error_msg: "已订购"
      }
    }
  }
  return anyRequest({
    url: 'https://fuwu.pinduoduo.com/columbine/pop/service/common/order/create',
    method: 'post',
    data,
    headers: {
      ...headers,
      "Cookie": `api_uid=rBXNA2bAAI2FXi2IUYj0Ag==;rckk=ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k;_bee=ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k;ru1k=b93a0f2b-2ef5-459f-9428-8a40f6ec6b12;_f77=b93a0f2b-2ef5-459f-9428-8a40f6ec6b12;ru2k=2ba6fc6a-7521-46bd-bb02-ab7fe6bf9ba0; _a42=2ba6fc6a-7521-46bd-bb02-ab7fe6bf9ba0; _nano_fp=XpmxXqCylp9bn5dxX9_P2Z1uLvq_ieq6atkP6yHW;terminalFinger=gL6L4VZDVK1Gk9T0fF4QprRHykRw9xVP;webp=true;SUB_PASS_ID=${subPassId};sm_tid=ffa639b6-385a-4bce-b277-88a4da813591%2C82482%2C78f2b9a7-dd63-4297-98d8-4edc2c4396b3%2C82482%2C16e6da76-12e9-44eb-bad7-0cb904e626df%2C82482%2C41b182d2-50b5-4fe3-b9fc-02c4bfe0ca22%2C82482%2C3a2ce981-0169-4af4-81a2-08e6cff8f2a6%2C82482%2C764e790a-b47f-40a1-beb8-f99428b6e649%2C82482%2C4589bcfc-0de5-420a-8887-f395e6793879%2C82482%2C6150541b-2b5c-4997-969f-3de922fec98e%2C82482%2C2f54b237-3b2e-4abf-ae1e-b772d73120fb%2C82482%2C9f6219d9-32a3-4842-bc6d-9674e64c544c%2C82482%2Cea0ea85e-6650-4f78-9d64-5b4035d1e258%2C82482%2C0d138b0f-60b9-4e3e-bf10-395025ed358b%2C82482%2C7fd86a36-6a72-4ec1-b354-12999fd24d02%2C82482%2C3d17a536-dbee-4e46-a219-c536c85c98d9%2C82482%2Cac2d99ea-bc66-4930-a91d-4e0cdaa696c7%2C82482`,
      "Anti-Content": await storeAntiContent(),
    }
    // data: {
    //   "skuId": "428558",
    //   "serviceId": 82482,
    //   "serviceDetailReferrer": "",
    //   "orderChannel": 0,
    //   "bizType": 0,
    //   "pageId": "10794_1701756589409_ygrixhtkuf"
    // },
    // headers: {
    //   "Origin": "https://fuwu.pinduoduo.com",
    //   "Referer": `https://fuwu.pinduoduo.com/service-market/order-confirm?skuId=428558&activityId=&prizeId=&detailId=82482&serviceType=1`,
    //   "Cookie": `api_uid=rBXNA2bAAI2FXi2IUYj0Ag==;rckk=ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k;_bee=ewFocd0MmSLjUS9c1Cur6nwNGSRa9b8k;ru1k=b93a0f2b-2ef5-459f-9428-8a40f6ec6b12;_f77=b93a0f2b-2ef5-459f-9428-8a40f6ec6b12;ru2k=2ba6fc6a-7521-46bd-bb02-ab7fe6bf9ba0; _a42=2ba6fc6a-7521-46bd-bb02-ab7fe6bf9ba0; _nano_fp=XpmxXqCylp9bn5dxX9_P2Z1uLvq_ieq6atkP6yHW;terminalFinger=gL6L4VZDVK1Gk9T0fF4QprRHykRw9xVP;webp=true;SUB_PASS_ID=${subPassId};sm_tid=ffa639b6-385a-4bce-b277-88a4da813591%2C82482%2C78f2b9a7-dd63-4297-98d8-4edc2c4396b3%2C82482%2C16e6da76-12e9-44eb-bad7-0cb904e626df%2C82482%2C41b182d2-50b5-4fe3-b9fc-02c4bfe0ca22%2C82482%2C3a2ce981-0169-4af4-81a2-08e6cff8f2a6%2C82482%2C764e790a-b47f-40a1-beb8-f99428b6e649%2C82482%2C4589bcfc-0de5-420a-8887-f395e6793879%2C82482%2C6150541b-2b5c-4997-969f-3de922fec98e%2C82482%2C2f54b237-3b2e-4abf-ae1e-b772d73120fb%2C82482%2C9f6219d9-32a3-4842-bc6d-9674e64c544c%2C82482%2Cea0ea85e-6650-4f78-9d64-5b4035d1e258%2C82482%2C0d138b0f-60b9-4e3e-bf10-395025ed358b%2C82482%2C7fd86a36-6a72-4ec1-b354-12999fd24d02%2C82482%2C3d17a536-dbee-4e46-a219-c536c85c98d9%2C82482%2Cac2d99ea-bc66-4930-a91d-4e0cdaa696c7%2C82482`,
    //   "Anti-Content": await storeAntiContent(),
    // }
  })
}
