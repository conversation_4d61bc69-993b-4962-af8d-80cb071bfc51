const catData = [
    {
        cat_id: 11254,
        goodsName: () => "全屋五金配件滚动轴承定制咨询客服直拍不发",
        cats: ["五金工具", "机械五金", "滚动轴承", "直线运动轴承"],
        cat_ids: [9323, 9475, 10353, 11254],

        goods_properties:
            [
                {
                    "template_pid": 117434,
                    "template_module_id": 26437,
                    "ref_pid": 310,
                    "pid": 5,
                    "value": "",
                    "value_unit": "",
                    "content": ""
                },
                {
                    "template_pid": 117435,
                    "template_module_id": 26437,
                    "ref_pid": 1821,
                    "pid": 553,
                    "value": "",
                    "value_unit": "",
                    "content": ""
                },
                {
                    "template_pid": 117436,
                    "template_module_id": 26437,
                    "ref_pid": 1818,
                    "pid": 557,
                    "value": "",
                    "value_unit": "",
                    "content": ""
                },
                {
                    "template_pid": 117437,
                    "template_module_id": 26437,
                    "ref_pid": 1135,
                    "pid": 166,
                    "vid": 0,
                    "value_unit": "mm"
                },
                {
                    "template_pid": 117438,
                    "template_module_id": 26437,
                    "ref_pid": 1823,
                    "pid": 16,
                    "value": "",
                    "value_unit": "",
                    "content": ""
                },
                {
                    "template_pid": 351702,
                    "template_module_id": 26437,
                    "ref_pid": 482,
                    "pid": 93,
                    "value": "",
                    "value_unit": "",
                    "content": ""
                },
                {
                    "template_pid": 366718,
                    "template_module_id": 26437,
                    "ref_pid": 373,
                    "pid": 16,
                    "value": "",
                    "value_unit": "",
                    "content": ""
                }
            ]
    },
    {
        cat_id: 23462,
        goodsName: () => "高级天地盖礼盒定制产品包装盒定做空盒子硬盒",
        cats: ["节庆用品/礼品", "创意礼品", "定制礼品盒", null],
        cat_ids: [17134, 17135, 23462, 0],
        goods_properties: [
            {
                "template_pid": 355743,
                "template_module_id": 56977,
                "ref_pid": 310,
                "pid": 5,
                "vid": 1986,
                "value": "",
                "value_unit": "",
                "content": "Pepsi/百事"
            },
            {
                "template_pid": 358046,
                "template_module_id": 56977,
                "ref_pid": 317,
                "pid": 16,
                "vid": 12854,
                "value": "",
                "value_unit": "",
                "content": "塑料"
            }
        ]
    },
    {
        cat_id: 10391,
        goodsName: () => '全屋五金配件气动工具气钉枪定制咨询客服直拍不发',
        cats: ["五金工具", "气动工具", "气钉枪", null],
        cat_ids: [9323, 9479, 10391, 0],
        goods_properties: [
            {
                "template_pid": 109840,
                "template_module_id": 24579,
                "ref_pid": 310,
                "pid": 5,
                "vid": 1986,
                "value": "",
                "value_unit": "",
                "content": "Pepsi/百事"
            },
            {
                "template_pid": 356689,
                "template_module_id": 24579,
                "ref_pid": 482,
                "pid": 93,
                "vid": 24046,
                "value": "",
                "value_unit": "",
                "content": "其他海外地区"
            },
            {
                "template_pid": 357291,
                "template_module_id": 24579,
                "ref_pid": 317,
                "pid": 16,
                "vid": 13299,
                "value": "",
                "value_unit": "",
                "content": "不锈钢"
            }
        ]
    },
    {
        cat_id: 8932,
        goodsName: () => "工商注册专用款式",
        cats: ["本地化生活服务", "商务服务", "工商注册", null],
        cat_ids: [8721, 8750, 8932, null],
        goods_properties: [
            {
                "template_pid": 274975,
                "template_module_id": 46322,
                "ref_pid": 310,
                "pid": 5,
                "value": "",
                "value_unit": "",
                "content": ""
            }
        ],
        mainImage:{
            url:"https://img.pddpic.com/mms-material-img/2024-07-16/8d27bf52-ebb7-43b2-bf28-cd3621e04d6a.jpeg.a.jpeg",
            filed_id:48114004846
        }
    }
]

const provinceList = [
    {
        "id": 2,
        "regionName": "北京市",
        "nationalCode": "110000"
    },
    {
        "id": 27,
        "regionName": "天津市",
        "nationalCode": "120000"
    },
    {
        "id": 10,
        "regionName": "河北省",
        "nationalCode": "130000"
    },
    {
        "id": 23,
        "regionName": "山西省",
        "nationalCode": "140000"
    },
    {
        "id": 19,
        "regionName": "内蒙古自治区",
        "nationalCode": "150000"
    },
    {
        "id": 18,
        "regionName": "辽宁省",
        "nationalCode": "210000"
    },
    {
        "id": 15,
        "regionName": "吉林省",
        "nationalCode": "220000"
    },
    {
        "id": 12,
        "regionName": "黑龙江省",
        "nationalCode": "230000"
    },
    {
        "id": 25,
        "regionName": "上海市",
        "nationalCode": "310000"
    },
    {
        "id": 16,
        "regionName": "江苏省",
        "nationalCode": "320000"
    },
    {
        "id": 31,
        "regionName": "浙江省",
        "nationalCode": "330000"
    },
    {
        "id": 3,
        "regionName": "安徽省",
        "nationalCode": "340000"
    },
    {
        "id": 4,
        "regionName": "福建省",
        "nationalCode": "350000"
    },
    {
        "id": 17,
        "regionName": "江西省",
        "nationalCode": "360000"
    },
    {
        "id": 22,
        "regionName": "山东省",
        "nationalCode": "370000"
    },
    {
        "id": 11,
        "regionName": "河南省",
        "nationalCode": "410000"
    },
    {
        "id": 13,
        "regionName": "湖北省",
        "nationalCode": "420000"
    },
    {
        "id": 14,
        "regionName": "湖南省",
        "nationalCode": "430000"
    },
    {
        "id": 6,
        "regionName": "广东省",
        "nationalCode": "440000"
    },
    {
        "id": 7,
        "regionName": "广西壮族自治区",
        "nationalCode": "450000"
    },
    {
        "id": 9,
        "regionName": "海南省",
        "nationalCode": "460000"
    },
    {
        "id": 32,
        "regionName": "重庆市",
        "nationalCode": "500000"
    },
    {
        "id": 26,
        "regionName": "四川省",
        "nationalCode": "510000"
    },
    {
        "id": 8,
        "regionName": "贵州省",
        "nationalCode": "520000"
    },
    {
        "id": 30,
        "regionName": "云南省",
        "nationalCode": "530000"
    },
    {
        "id": 28,
        "regionName": "西藏自治区",
        "nationalCode": "540000"
    },
    {
        "id": 24,
        "regionName": "陕西省",
        "nationalCode": "610000"
    },
    {
        "id": 5,
        "regionName": "甘肃省",
        "nationalCode": "620000"
    },
    {
        "id": 21,
        "regionName": "青海省",
        "nationalCode": "630000"
    },
    {
        "id": 20,
        "regionName": "宁夏回族自治区",
        "nationalCode": "640000"
    },
    {
        "id": 29,
        "regionName": "新疆维吾尔自治区",
        "nationalCode": "650000"
    },
    {
        "id": 35,
        "regionName": "台湾省",
        "nationalCode": "710000"
    },
    {
        "id": 33,
        "regionName": "香港特别行政区",
        "nationalCode": "810000"
    },
    {
        "id": 34,
        "regionName": "澳门特别行政区",
        "nationalCode": "820000"
    }
]
export { catData, provinceList }