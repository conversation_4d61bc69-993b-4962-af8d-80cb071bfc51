const { Controller } = require("ee-core");
const Storage = require("ee-core").Storage;
const ffi = require("ffi-napi");
const ref = require("ref-napi");
const path = require("path");
const { isDev, } = require("../config/config.app");

const dllBasePath = isDev
  ? path.resolve(__dirname, "../../build/extraResources/dll")
  : path.resolve(__dirname, process.cwd() + "/resources/extraResources/dll");

const lib = ffi.Library(dllBasePath + "/Esperanto.dll", {
  pesa: [ref.types.bool, []],
  spe0: [ref.types.int, []],
  //     0、Fiddler.exe
  // 1、Charles.exe
  // 2、HTTPDebuggerSvc.exe
  // 3、HTTPDebuggerUI_CN.exe
  // 4、Wireshark.exe
  // 5、HttpWatchStudiox64.exe
  // 6、Csnas.exe,BurpSuitePro.exe
  // 7、HttpWatchStudiox64.exe
  // 8、Proxifier.exe
  // 9、Proxifier.exe
  // 未找到进程名或其他返回-1
  jcgm: [ref.types.int, []],
  //  10201=Fiddler.exe
  // 10202=Charles.exe
  // 10203=HTTPDebuggerSvc.exe
  // 10204=HTTPDebuggerUI_CN.exe
  // 10205=Wireshark.exe
  // 10206=HttpWatchStudiox64.exe
  // 10207=Csnas.exe
  // 10208=BurpSuitePro.exe
  // 10209=HttpWatchStudiox64.exe
  // 10210=Proxifier.exe
  //   失败返回-1
});



class extraController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */
  async isVirtual() {
    return lib.pesa();
  }
  async checkHttpTool() {
    const status = lib.jcgm();
    const res = {
      code: status,
      exe: "",
    };
    switch (status) {
      case -1: {
        break;
      }
      case 10201: {
        res.exe = "Fiddler.exe";
        break;
      }
      case 10202: {
        res.exe = "Charles.exe";
        break;
      }
      case 10203: {
        res.exe = "HTTPDebuggerSvc.exe";
        break;
      }
      case 10204: {
        res.exe = "HTTPDebuggerUI_CN.exe";
        break;
      }
      case 10205: {
        res.exe = "Wireshark.exe";
        break;
      }
      case 10206: {
        res.exe = "HttpWatchStudiox64.exe";
        break;
      }
      case 10207: {
        res.exe = "Csnas.exe";
        break;
      }
      case 10208: {
        res.exe = "BurpSuitePro.exe";
        break;
      }
      case 10209: {
        res.exe = "HttpWatchStudiox64.exe";
        break;
      }
      case 10210: {
        res.exe = "Proxifier.exe";
        break;
      }
    }
    return res
  }
}

extraController.toString = () => "[class extraController]";
module.exports = extraController;
