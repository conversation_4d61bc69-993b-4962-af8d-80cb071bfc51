const { Controller } = require("ee-core");
const { fork } = require("child_process");
const path = require("path");
let a_exe = fork(path.resolve(__dirname, "../utils/forks/a_exe.js"));

class ARequestController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  add(list) {
    // a_exe.send({
    //   item,
    //   config,
    // });
    const requestList = list.map((item) => {
      if (!item.url) {
        return;
      }
      try {
        const params = item.params ? JSON.parse(item.params) : {};
        const headers = item.header ? JSON.parse(item.header) : {};
        const config = {
          url: item.url,
          method: item.method,
          headers,
        };
        if (config.url?.toLocaleLowerCase() === "get") {
          config.params = params;
        } else {
          config.data = params;
        }
        return {
          item,
          config,
        };
      } catch (e) {
        return;
      }
    });
    a_exe.send(requestList)
  }
}

ARequestController.toString = () => "[class ARequestController]";
module.exports = ARequestController;
