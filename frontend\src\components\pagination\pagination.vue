<template>
  <div class="pagination">
    <div class="left">
        <slot name="left">
            <template v-if="(typeof selection !== 'undefined')">
               <div class="selected-count">
                已选 <strong>{{ selection.length }}</strong> 条
               </div>
                <!-- <el-button type="primary" plain @click="reverseSelection">反选</el-button> -->
            </template>
        </slot>
    </div>
    <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="pageSizes"
        :small="small"
        :background="background"
        :layout="layout"
        :total="total"
        @size-change="sizeChange"
        @current-change="currentChange"
        v-bind="$attrs"
        :pager-count="5"
      />
  </div>
</template>
<script lang='ts' setup>
import { TableInstance } from 'element-plus';
import { reactive } from 'vue';


const props = withDefaults(defineProps<{
    currentPage:number
    total:number
    pageSize:number
    selection?:anyObj[]

    table?:TableInstance

    pageSizes?:Array<number>
    small?:boolean
    background?:boolean
    layout?:string
}>(),{
    pageSizes:()=>[10, 20, 50, 100],
    small:true,
    background:true,
    layout:"total, sizes, prev, pager, next, jumper",
})



const pagination = reactive({
    currentPage:props.currentPage,
    pageSize:props.pageSize
})

const emits =  defineEmits(
    ['update:currentPage','update:pageSize','sizeChange','currentChange','reverseSelection']
    )

function sizeChange(limit:number){
    emits('update:pageSize',limit)
    emits('sizeChange',limit)
}
function currentChange(page:number){
    emits('update:currentPage',page)
    emits('currentChange',page)
}

/**
 * 反选
 */
function reverseSelection(){
    emits('reverseSelection')
    props.table?.toggleAllSelection()
}
</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
.pagination{
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 16px;
    padding-top: 20px;
    :deep(.el-pagination){
        .btn-prev,.btn-next,.number{
            border-radius: 4px;
            &.is-active{
                color: var(--el-color-primary);
                background-color: transparent;
                border:1px solid var(--el-color-primary);
            }
        }
    }
}
 .left{
    color: var(--el-text-color-secondary);
    .selected-count{
        display: flex;
        align-items: center;
        justify-content: center;
    }
    strong{
        min-width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--el-color-primary);
        font-size: 400;
    }
 }
</style>
