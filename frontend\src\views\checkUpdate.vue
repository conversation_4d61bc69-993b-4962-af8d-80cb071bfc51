<template>
  <div class="update-page drag-enable" v-if="updateState.show">
    <div class="mask"></div>
    <div class="container">
      <!-- <header>
        <h3>新版本</h3>
        <h3>抢先体验 {{ appStore.info.app?.version }}</h3>
      </header>  -->
      <div class="bottom">
        <el-form label-position="top">
          <el-form-item label="更新内容">
            <el-scrollbar :height="80">
              <p class="update-log-item" v-for="log in updateLogs">{{ log }}</p>
            </el-scrollbar>
          </el-form-item>
          <el-form-item label='新版本下载中...'>
            <Progress :percent="updateState.percent" />
            <p>{{ `${updateState.current || '-'}/${updateState.max || '-'}` }}</p>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { nextTick, reactive, ref, watch } from "vue";
import { checkUpdate as checkUpdateRequest } from "/@/apis/mainWindow";
import { useAppStore } from "../stores/app";
import Progress from '/@/components/progress/progress.vue'
import { ipc } from "../apis/config";
import { downloadApp } from "../apis/mainWindow";
import { anyRequest } from "../apis/page";
const appStore = useAppStore()
async function close() {
  await ElMessageBox({
    type: 'warning',
    message: "关闭后程序将退出",
    title: "提示",
    showCancelButton: true
  })
  appStore.closeApp(false)
}
watch(
  () => appStore.info.app.update_url,
  (value,oldV) => {
    if (value) {
      const updateUrl = value
      if (updateUrl && String(updateUrl).length > 8) {
        nextTick(() => {
          checkUpdateRequest(updateUrl.endsWith('/') ? updateUrl.replace(/\/$/, '') : updateUrl)
        })
        getUpdateLogs(updateUrl)
      }

    }
  }, {
  immediate: true
}
);

function getUpdateLogs(url: string) {
  anyRequest({
    url: url + '/latest.yml',
    method: 'get'
  })
    .then(res => {
      try {
        const str: string = res || ''
        updateLogs.value = str.split('\n').filter(item => item)
      }catch(e){

      }
  })

}

const updateLogs = ref<string[]>([])
const updateState = reactive({
  msg: "",
  show: false,
  percent: 0,
  max: "-",
  current: "-",
  conent: "",
  status: ''
});


ipc.on("app.updater", (event, content: string) => {
  //   error: -1,
  //   checking:0,
  //   available: 1,
  //   noAvailable: 2,
  //   downloading: 3,
  //   downloaded: 4,
  updateState.conent = content
  try {
    const _content = JSON.parse(content);
    let { status } = _content;
    status = Number(status);
    // console.log(content)
    updateState.status = status
    switch (status) {
      case 1: {
        updateState.show = true;
        updateState.percent = 0;
        downloadApp()
        break;
      }

      case 3: {
        updateState.show || (updateState.show = true);
        const { percentNumber, totalSize, transferredSize } = _content;
        updateState.percent = percentNumber;
        (updateState.max = totalSize), (updateState.current = transferredSize);

        break;
      }
      case 2: {
        // ElMessage.info('已是最新版本')
        updateState.show = false;
        break
      }
      case 4: {
        ElMessage.info('更新包已下载')
        updateState.show = false;
        break;
      }
    }
  } catch (e) {
    console.log(e);
  }
});
</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
.update-page {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  z-index: 99;

  .mask {
    position: absolute;
    left: 0;
    top: 0;
    background: rgba($color: #000000, $alpha: .3);
    width: 100%;
    height: 100%;
  }

  .container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 350px;
    height: 280px;
    border-radius: 10px;
    overflow: hidden;

    // header {
    //   height: 162px;
    //   background: url('/src/assets/image/update-bg.png');
    //   box-sizing: border-box;
    //   padding: 20px;
    //   color: #fff;
    //   display: flex;
    //   flex-direction: column;
    //   justify-content: flex-end;
    // } 

    .bottom {
      background-color: #fff;
      padding: 20px;
      height: 100%;
      box-sizing: border-box;
    }
  }
}

.el-scrollbar {
  border-radius: 5px;
  overflow: hidden;
  flex-grow: 1;

  :deep(.el-scrollbar__view) {
    background-color: var(--el-fill-color);
  }
}

.update-log-item {
  word-wrap: break-word;
  padding: 0 5px;
}</style>
