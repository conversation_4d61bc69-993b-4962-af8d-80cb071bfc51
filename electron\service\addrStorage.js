"use strict";

const Service = require("ee-core").Service;
const Storage = require("ee-core").Storage;
const _ = require("lodash");
const path = require("path");
const fs = require('fs')
const tableNames = {
  'ADDRESS':'address'
};

/**
 * 数据存储
 * @class
 */
class AddrStorageService extends Service {
  constructor(ctx) {
    super(ctx);

    // sqlite数据库
    this.sqliteFile = "sqlite-addrStorage.db";
    let sqliteOptions = {
      driver: "sqlite",
      default: {
        timeout: 6000,
        verbose: console.log, // 打印sql语法
      },
    };
    this.addressSqliteDB = Storage.JsonDB.connection(
      this.sqliteFile,
      sqliteOptions
    );
  }

  /*
   * 检查并创建表 (sqlite)
   */
  async checkAndCreateTableSqlite(tableName = "") {
    try {
      if (_.isEmpty(tableName)) {
        throw new Error(`table name is required`);
      }
      // 检查表是否存在
      const userTable = this.addressSqliteDB.db.prepare(
        `SELECT * FROM sqlite_master WHERE type = ? AND name = ?`
      );

      const result = userTable.get("table", tableName);
      if (result) {
        return;
      }

      // 创建表
      let create_table_str = "";

      switch (tableName) {
        case tableNames.ADDRESS: {
          create_table_str = `CREATE TABLE ${tableName}
              (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  name CHAR(20) NOT NULL,
                  phone CHAR(20) NOT NULL,
                  province CHAR(20) NOT NULL,
                  city CHAR(20) NOT NULL,
                  district CHAR(20) NOT NULL,
                  address CHAR(255) NOT NULL
              );`;
          break;
        }
      }
      create_table_str && this.addressSqliteDB.db.exec(create_table_str);
    } catch (e) {
      console.log(e);
    }
  }

  /*
   * 增 Test data (sqlite)
   */
  async addAddrItem(data) {
    // const { goods_id } = data;
    // const isExist = await this.checkGoodsItem(goods_id);
    // if (isExist) {
    //   return Promise.reject("已存在的商品");
    // }
    let table = tableNames.ADDRESS;
    const keys = Object.keys(data);
    const insert =  this.addressSqliteDB.db.prepare(
      `INSERT INTO ${table} (${keys.join(",")}) VALUES (${keys
        .map((item) => "@" + item)
        .join(",")})`
    );
    insert.run(data);

    return true;
  }

  async multipleAdd({keys,values}){
    console.log(keys,values)
    let table = tableNames.ADDRESS;
    // console.log(keys,values)
    await this.checkAndCreateTableSqlite(table)
    const insertStr = `INSERT INTO ${table} (${keys}) VALUES ${values}`
    // fs.writeFile(path.resolve(__dirname,'../../logs/inser.txt'),insertStr,()=>{})
    const insert =  this.addressSqliteDB.db.prepare(insertStr);
    insert.run();

    return true;
  }

  async checkAddrItem(id) {
    await this.checkAndCreateTableSqlite(tableNames.ADDRESS);
    const check = this.addressSqliteDB.db.prepare(
      `SELECT COUNT(*) AS total FROM ${tableNames.ADDRESS} WHERE id = ?`
    );
    const total = check.get(id).total;
    return total ? true : false;
  }

  /*
   * 删 Test data (sqlite)
   */
  async delAddrItem(id) {
    //console.log("delete name:", name);

    let table = tableNames.ADDRESS;
    await this.checkAndCreateTableSqlite(table)
    const isExist = await this.checkAddrItem(id);
    if (!isExist) {
      return Promise.reject("不存在当前商品");
    }
    const delUser =  this.addressSqliteDB.db.prepare(
      `DELETE FROM ${table} WHERE id = ?`
    );
    delUser.run(id);

    return true;
  }
  async delSelected(ids){
    let table = tableNames.ADDRESS; 
    await this.checkAndCreateTableSqlite(table)
    const delUser =  this.addressSqliteDB.db.prepare(
      `DELETE FROM ${table} WHERE id in (${ids})`
    );
    delUser.run();

    return true;
  }
  async delAddrAll() {
    //console.log("delete name:", name);

    let table = tableNames.ADDRESS;
    // const isExist = await this.checkAddrItem(id);
    // if (!isExist) {
    //   return Promise.reject("不存在当前商品");
    // }
    await this.checkAndCreateTableSqlite(table)
    const delUser =  this.addressSqliteDB.db.prepare(
      `DELETE FROM ${table} WHERE id >= 0`
    );
    delUser.run();

    return true;
  }

  async getRandomItem(){
    let table = tableNames.ADDRESS
    await this.checkAndCreateTableSqlite(table)
    const get = this.addressSqliteDB.db.prepare(`
     SELECT * FROM ${table}  ORDER BY RANDOM() LIMIT 1
    `)
    const item = get.get()
    // console.log(item)
    return item
  }

  /*
   * 改 Test data (sqlite)
   */
//   async updateGoods(data) {
//     //console.log("update :", {name, age});
//     let strs = [];
//     for (let key in data) {
//       if (key === "id") {
//         continue;
//       } else {
//         strs.push(`${key}=${value}`);
//       }
//     }

//     let table = tableNames.ADDRESS;
//     await this.checkAndCreateTableSqlite(table);

//     const updateUser = this.addressSqliteDB.db.prepare(
//       //   `UPDATE ${table} SET age = ? WHERE id = ?`
//       `UPDATE ${table} SET ${strs.join(",")} WHERE id = ${data.id}`
//     );
//     updateUser.run();

//     return true;
//   }

  /*
   * 查 Test data (sqlite)
   */
  async getList({page,limit}) {
    //console.log("select :", {age});

    let table = tableNames.ADDRESS;
    await this.checkAndCreateTableSqlite(table);

    const countTotal =  this.addressSqliteDB.db.prepare( `SELECT COUNT(*) as total  FROM ${table}`)
    const selectUser = this.addressSqliteDB.db.prepare(

        `SELECT *  FROM ${table}  LIMIT ${(page - 1 ) * limit},${limit}`
      // `SELECT *  FROM ${table} `
    );
    const users = selectUser.all();
    const total = countTotal.get().total
    //console.log("select users:", users);
    return {
      total,
      list:users
    };
  }

 



  /*
   * get data dir (sqlite)
   */
  async getDataDir() {
    const dir = this.demoSqliteDB.getStorageDir();

    return dir;
  }

  /*
   * set custom data dir (sqlite)
   */
  async setCustomDataDir(dir) {
    if (_.isEmpty(dir)) {
      return;
    }

    // the absolute path of the db file
    const dbFile = path.join(dir, this.sqliteFile);
    const sqliteOptions = {
      driver: "sqlite",
      default: {
        timeout: 6000,
        verbose: console.log,
      },
    };
    this.demoSqliteDB = Storage.JsonDB.connection(dbFile, sqliteOptions);

    return;
  }
}

AddrStorageService.toString = () => "[class AddrStorageService]";
module.exports = AddrStorageService;
