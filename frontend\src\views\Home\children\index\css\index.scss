.index {
    height: 786px;
    overflow: hidden;
    padding: 0 16px;
  
    :deep(.goods-info-box) {
      @include goods-info-box();
    }
  
    display: flex;
    flex-direction: column;
  
    h5 {
      @include icon-title(3px);
      display: flex;
      justify-content: space-between;
  
      .el-input {
        width: 233px;
        height: 26px;
        margin: 0 10px;
        --el-input-border-color: var(--el-color-primary);
  
        :deep(.el-input-group__append) {
          background-color: var(--el-color-primary);
          color: var(--el-fill-color-blank);
          box-shadow: none;
        }
      }
  
      .left,
      .right {
        display: flex;
        align-items: center;
      }
  
      .right {
        color: var(--el-text-color-secondary);
        font-weight: 400;
        font-size: 14px;
  
        .el-link {
          font-weight: bold;
        }
      }
    }
  
    .config-box {
      // border: var(--el-border);
      padding:5px 16px;
      box-sizing: border-box;
      border-radius: 6px;
      background-color: var(--el-fill-color-light);
      // background: url("/src/assets/image/task-bg-323.png") no-repeat;
      // background-repeat: round;
      border:  1px solid var(--el-color-primary);
      background-size: 100% 100%;
      position: relative;
      height: 323px;
  
      .img-box {
        display: flex;
        padding-bottom: 6px;
        border-bottom: var(--el-border);
  
        .el-image {
          width: 80px;
          height: 80px;
          border-radius: 6px;
          margin-right: 16px;
          border: var(--el-border);
          border-color: var(--el-border-color-darker);
        }
  
        .infos {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          flex-grow: 1;
  
          p {
            line-height: 22px;
          }
  
          p.name {
            font-size: 14px;
            font-weight: bolder;
          }
  
          p.sku {
            display: flex;
  
            .sku-select {
              display: inline-block;
              width: 105px;
              background-color: var(--el-fill-color-blank);
              border: var(--el-border);
              border-radius: 4px;
              padding: 3px 12px;
              box-sizing: border-box;
              cursor: pointer;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              @include text-overflow();
  
              &:hover {
                border-color: var(--el-color-primary);
              }
            }
  
            .el-select {
              flex-grow: 1;
              margin-left: 6px;
            }
          }
  
          span {
            font-size: 12px;
          }
  
          span.label {
            color: var(--el-text-color-primary);
            margin-right: 6px;
          }
  
          span.value {
            color: var(--el-text-color-regular);
            font-weight: bolder;
          }
        }
      }
      .bottom{
        display: flex;
      }
      .action-box{
        width: 350px;
        padding: 5px;
        border-left: var(--el-border);
        .btns{
          padding: 12px 0;
          border-top: var(--el-border);
        }
      }
      .el-form {
        padding-top: 20px;
        position: relative;
        flex-grow: 1;
        :deep(.el-form-item__label) {
          color: var(--el-text-color-primary);
        }
  
        .el-divider {
          margin: 0 16px 10px;
        }
  
        .el-form-item {
          margin-bottom: 10px;
          margin-right: 0;
  
          .el-radio-group {
            .el-radio {
              margin-right: 6px;
              background-color: #fff;
  
              &:nth-child(n + 11) {
                margin-top: 10px;
              }
            }
          }
        }
      }
  
      .btn {
        position: absolute;
        width: 134px;
        height: 34px;
        // background: url("/src/assets/image/task-add-btn.png") no-repeat;
        bottom: 5px;
        right: 5px;
        cursor: pointer;
      }
    }
  
    .table-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 44px;
      margin-top: 10px;
    }
  
    .table-configs {
      height: 40px;
      display: flex;
      align-items: center;
      font-size: 12px;
    }
  
    .el-table {
      @include commonTableHeader();
      // height: 260px;
      height: calc(100% - 505px);
      flex-grow: 1;
      border: var(--el-border);
      // box-shadow: var(--diy-shadow);
  
      .el-link+.el-link {
        margin-left: 8px;
      }
  
      .el-link {
        font-size: 14px;
  
        :deep(.ds-icon) {
          margin-right: 4px;
        }
      }
    }
  
    .table-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
  
      .pagination {
        padding: 0;
      }
    }
  }
  
  .sku-select-options {
    p.number-inputs {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 5px;
  
      span {
        margin: 0 10px;
      }
    }
  
    footer {
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      height: 34px;
    }
  }