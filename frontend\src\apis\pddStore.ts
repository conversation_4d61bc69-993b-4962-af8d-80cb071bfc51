import { da } from "element-plus/es/locale";
import { useAppStore } from "../stores/app";
import { useMallStore } from "../stores/store";
import { delayPromise, pddResErrorMsg, randomInt, retry } from "../utils/common";
import { pddStoreRequestHeader } from "./changeSales"
import request, { Options, anyRequest, getPifaGroupId, singleBuy } from "./page"
import { acquireQrCode } from "./pifa";
import dayjs from "dayjs";
type PddStore = Store
const createAnyAxios = anyRequest
export async function storeGoodsList(
  data: {
    goods_id_list?: string[];
    page: number;
    goods_name?: string
  },
  pddStore: Store
) {
  if (!data.goods_name) {
    Reflect.deleteProperty(data, 'goods_name')
  }
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/vodka/v2/mms/query/display/mall/goodsList",
    headers: await pddStoreRequestHeader(pddStore, {
      "Referer": 'https://mms.pinduoduo.com/goods/goods_list',
    }),
    data: {
      is_onsale: 1,
      size: 100,
      order_by: "sold_quantity:asc,id:desc",
      sold_out: 0,
      ...data,

    },
  };

  const res = await anyRequest(config);
  const response = {
    data: res,
    code: res.success ? 0 : 1,
    msg: res.error_msg || res.errorMsg || "",
  };
  return response;
}

/**商品发货模板列表 */
export async function costTemplateListApi(mall: Store, data: {
  "pageNo": number,
  "pageSize"?: number,
  "sourceKey"?: string
}) {
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/express_inf/cost_template/get_list",
    headers: await pddStoreRequestHeader(mall),
    data: {
      sourceKey: "MMS",
      pageSize: 10,
      ...data,

    }
  };
  return anyRequest(config)
}
/**创建运费模板 */
export async function createCostTemplateApi(mall: Store, _data: {
  "provinceId"?: number,
  "cityId"?: number,
  "districtId"?: number,
  "costType"?: number,
  "costTemplateName"?: string,
  "shipFromOutsideMainland"?: boolean,
  dispatchFree: {
    sfFreeType: number
    areaList: Array<{
      province: number
    }>
  }
  noShipConfig: Array<{
    "province": number,
    "noShipReasonType": number
  }>
  dispatchCost?: any[]
  sourceKey?: string
}) {
  const data: typeof _data = {
    "provinceId": 6,
    "cityId": 76,
    "districtId": 697,
    "costType": 0,
    "costTemplateName": "k-指定不发货",
    "shipFromOutsideMainland": false,
    "dispatchCost": [],
    "sourceKey": "MMS",
    ..._data
  }
  const config = {
    url: "https://mms.pinduoduo.com/express_inf/cost_template/create",
    method: "post",
    headers: await pddStoreRequestHeader(mall),
    data,
  };
  return anyRequest(config)
}

/**商品数据批量操作 */
export async function batchEditGoods(mall: Store, data: {
  edit_list: Array<{
    goods_id: number
    sku_id: number
    quantity_delta: number | string
  }>
}) {
  const last = data.edit_list[data.edit_list.length - 1]
  if (last) {
    last.quantity_delta = String(last.quantity_delta)
  }
  const config = {
    url: "https://mms.pinduoduo.com/guide-api/mms/inventory/batch_edit/submit",
    method: "post",
    headers: await pddStoreRequestHeader(mall),
    data,
  };
  return anyRequest(config)
}
/**设置供货前商品列表查询 */
export async function querySupplyGoods(pddStore: PddStore, _data: {
  bizId?: number
  goodsIds?: number[]
  pageNum?: number
  pageSize?: number
}) {
  const data: typeof _data = {
    bizId: 1,
    pageSize: 50,
    pageNum: 1,
    ..._data
  }
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/mille/mms/goods/pageQueryLegalGoods",
    data,
    headers: await pddStoreRequestHeader(pddStore),
  };
  const res = await createAnyAxios(config);
  return res
}


/**上供货,并修改折扣至90% */
export async function goodsSupply(
  /**不可超过20个 */
  goods_id: number | string | Array<number | string>,
  pddStore: Store
) {
  const response: {
    code: number
    msg: string,
    success: Set<number>
    failed: Set<number>,
    reason: Record<string, string>
  } = {
    code: 0,
    msg: '',
    success: new Set(),
    failed: new Set(),
    reason: {}
  }
  const goodsIds = (Array.isArray(goods_id) ? goods_id : [goods_id]).map(Number);
  const checkRes = await querySupplyGoods(pddStore, { goodsIds })
  if (!checkRes.success) {
    response.code = 1
    response.msg = pddResErrorMsg(checkRes)
    return response
  };
  const needAdd: Set<number> = new Set()
  const needUpdate: Set<number> = new Set();
  (checkRes.result.list as anyObj[]).forEach(item => {
    if (item.enrollEnable) {
      needAdd.add(item.goodsId)
      return
    }
    if ((item.reasons as string[]).includes('商品已参与批发活动')) {
      needUpdate.add(item.goodsId)
    } else {
      response.failed.add(item.goodsId)
      response.reason[item.goodsId] = item.reasons.join(';')
    }
  });
  if (needAdd.size) {
    const res = await addSupply([...needAdd.values()], pddStore)
    console.log(res, 'addSupply')
    if (!res.success) {
      response.code = 1
      response.msg = pddResErrorMsg(res)
    }
    const { failGoodsReasons } = res.result;
    Object.keys(failGoodsReasons).forEach(key => {
      const reason = failGoodsReasons[key];
      if (reason && reason !== "商品已存在") {
        response.code = 1;
        response.reason[key] = reason;
        response.failed.add(Number(key))
        needAdd.delete(Number(key))
      }
    });
    [...needAdd.values()].forEach(goodsId => response.success.add(goodsId))
  }
  if (needUpdate.size) {
    const res = await updateSupply([...needUpdate.values()], pddStore)
    console.log(res, 'updateSupply')
    if (!res.success) {
      response.code = 1
      response.msg = pddResErrorMsg(res)
    }
    const { failGoodsReasons = {} } = res.result;
    Object.keys(failGoodsReasons).forEach(key => {
      const reason = failGoodsReasons[key];
      if (reason && reason !== "商品已存在") {
        response.code = 1;
        response.reason[key] = reason;
        response.failed.add(Number(key))
        needUpdate.delete(Number(key))
      }
    });
    [...needUpdate.values()].forEach(goodsId => response.success.add(goodsId))
  }
  if (goodsIds.length === 1) {
    if (response.success.size) {
      response.code = 0
    }
    if (response.failed.size) {
      response.code = 1
      response.msg = response.reason[goodsIds[0]]
    }
  }
  return response
}

export async function delSupply(mall: PddStore, data: {
  bizId?: number
  goodsId: number
}) {
  if (!data.bizId) {
    data.bizId = 1
  }
  const config = {
    url: "https://mms.pinduoduo.com/mille/mms/goods/deleteGoods",
    method: "post",
    headers: await pddStoreRequestHeader(mall),
    data,
  };
  return createAnyAxios(config)
}


/**添加供货 */
export async function addSupply(goods_id: number | string | Array<number | string>, pddStore: PddStore) {
  const goodsIds = Array.isArray(goods_id) ? goods_id : [goods_id];
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/mille/mms/goods/addGoods",
    data: {
      activityGoodsConfigs: goodsIds.map(goodsId => ({
        goodsId: Number(goodsId),
        goodsLadderDiscounts: [{ ladderStartValue: 2, ladderDiscount: 90 }],
        supportDropShipping: 0,
      })),
      syncAllGoodsDiscount: false,
      bizId: 1,
    },
    headers: await pddStoreRequestHeader(pddStore, { referer: "https://mms.pinduoduo.com/supplier/wholesale/create" }),
  };
  const res1 = await createAnyAxios(config);
  const verifyAuthToken = res1.result?.verifyAuthToken
  if (!verifyAuthToken) {
    return res1
  }
  const isSuccess = await new Promise<Boolean>(resolve => {
    useAppStore().openPddVerify({
      verify_auth_token: verifyAuthToken,
      cb: resolve,
    })
  })
  if (!isSuccess) {
    return {
      success: false,
      error_msg: '未通过验证'
    }
  }
  return createAnyAxios(config)
}

/**修改供货折扣 */
export async function updateSupply(
  goods_id: number | string | Array<number | string>,
  pddStore: PddStore
) {
  const goodsIds = Array.isArray(goods_id) ? goods_id : [goods_id];
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/mille/mms/goods/batchUpdateGoodsDiscount",
    headers: await pddStoreRequestHeader(pddStore),
    data: {
      operateSource: 0,
      batchUpdate: goodsIds.map(goodsId => ({
        goodsId: Number(goodsId),
        goodsLadderDiscounts: [{ ladderStartValue: 2, ladderDiscount: 90 }],
        supportDropShipping: 0,
      })),
      syncAllGoodsDiscount: false,
      bizId: 1,
    },
  };
  const res1 = await createAnyAxios(config);
  const verifyAuthToken = res1.result?.verifyAuthToken
  if (!verifyAuthToken) {
    return res1
  }
  const isSuccess = await new Promise<Boolean>(resolve => {
    useAppStore().openPddVerify({
      verify_auth_token: verifyAuthToken,
      cb: resolve,
    })
  })
  if (!isSuccess) {
    return {
      success: false,
      error_msg: '未通过验证'
    }
  }
  return createAnyAxios(config)
}

/**获取拼团ID */
export function _getGroupId(
  data: { goods_id: number | string; sku_id: number[] },
  options?: Options,
  retryCount = 10
): Promise<responseData> {
  const fn = async (current: number) => {
    const index = randomInt(0, data.sku_id.length);
    const reqData = {
      goods_id: data.goods_id,
      sku_id: data.sku_id[index],
    }
    return getPifaGroupId(reqData)
  };
  return retry(fn, retryCount, 500,);
}

export function getGroupId(
  data: { goods_id: number | string; sku_id: number[] },
  options?: Options,
  _store?: PddStore | PddStore["mallId"]
) {

  if (!_store) {
    return _getGroupId(data, options);
  }
  let store: PddStore | undefined = void 0;
  if (typeof _store === "number" || typeof _store === "string") {
    const pddMall = useMallStore();
    store = pddMall.tableList.find((item) => item.mallId === Number(_store));
  } else {
    store = _store;
  }

  return _getGroupId(data, { showErrorMsg: false }, 5).catch(async (res) => {
    if (!store) {
      return Promise.reject(res);
    }
    const supplyRes = await goodsSupply(data.goods_id, store);
    if (supplyRes.code) {
      supplyRes.msg = '供货失败：' + supplyRes.msg;
      return Promise.reject(supplyRes)
    }
    await delayPromise(3000)
    return _getGroupId(data, options);
  });
}
/**获取商品立减券 创建规则*/
export async function query_filtered_mall_goods_list_api(_data:{
  start_time?:string
  end_time?:string
  goods_id:string
  page?:number
  size?:number
  sort_by?:string
  sort_type?:string
  source_type?:number
}, pddStore: PddStore) {
  const data:typeof _data = {
    start_time:dayjs().startOf('day').valueOf().toString(),
    end_time:dayjs().add(365,'day').endOf('day').valueOf().toString(),
    page:1,
    size:20,
    sort_by:'id',
    sort_type:'DESC',
    source_type:54,
    ..._data
  }
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/madrid/query_filtered_mall_goods_list",
    headers: await pddStoreRequestHeader(pddStore),
    data
  }
  return createAnyAxios(config)
}