<template>
  <div class="browser-view">
    <div class="controls">
      <div class="item" @click="currentUrl.changeUrl('home')">

        <el-icon class="m-r-5"><HomeFilled /></el-icon>首页
      </div>
      <div class="item" @click="currentUrl.changeUrl('back')">

        后退
      </div>
      <div class="item" @click="currentUrl.changeUrl('next')">

        前进
      </div>
      <div class="item" @click="currentUrl.changeUrl('reload')">

        刷新
      </div>
      <div class="item" @click="logout">

        退出并切换账号
      </div>
      <!-- <div class="item">
        <el-link :underline="false" @click="">登录页</el-link>
      </div> -->

    </div>
  </div>
</template>
<script lang='ts' setup>
  import { Refresh, ArrowRight, ArrowLeft, HomeFilled } from '@element-plus/icons-vue'
  import { useCurrentPddUrl } from '/@/stores/current';
  import { logout as pddLogout } from '/@/apis/pddWindow';
  import { ElMessage } from 'element-plus';
  const currentUrl = useCurrentPddUrl()


  async function logout () {
    currentUrl.changeUrl('https://mobile.pinduoduo.com/login.html', true)
  }
</script>
<style lang='scss' rel="stylesheet/scsss" scoped>
  .browser-view {
    width: 100%;
    // height: 848px;
    height: 100%;
    background: #F4F4F4;
    border: 1px solid var(--el-border-color);
    box-shadow: var(--diy-shadow);
    box-sizing: border-box;

    .controls {
      display: flex;
      justify-content: center;
      align-items: center;

      .item {
        margin-right: 5px;
        min-width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        font-size: 13px;
        color: var(--el-color-primary);
        justify-content: center;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }
</style>