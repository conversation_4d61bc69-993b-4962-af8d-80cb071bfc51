<template>
  <div class="store-manage">
    <div class="table-header">
      <el-space>
        <el-button @click="mallStore.addPddStore()" v-blur type="primary" size="default">添加店铺</el-button>
        <el-button @click="mallStore.getList" v-blur type="primary" size="default">刷新</el-button>
        <el-button @click="checkLogin()" type="success" :disabled="!state.selection.length" size="default"
          :loading="state.checkStatusLoding">重新检测选中店铺登录状态</el-button>
      </el-space>
    </div>


    <el-dialog title="全店解析" :width='880' class="store-goods-list" v-model="analyzeState.dialog"
      :close-on-click-modal="false" :close-on-press-escape="false" :on-close="closeAnalyze">
      <div class="m-b-10">
        <el-button type="success" @click="addToLocal">解析选中({{ analyzeTable.tableState.selection.length }})</el-button>
        <el-button type="danger" v-if="analyzeState.isGetList"
          @click="analyzeState.isGetList = false">停止加载后续商品</el-button>
      </div>
      <vxe-table :="{ ...analyzeTable.tableProps, ...analyzeTable.tableEvents }" ref="tableRef">
        <vxe-column type='checkbox' :width="50"></vxe-column>
        <vxe-column :width="150" field="id" title="商品ID" :filters="goodsIdFilter" :filter-method="({ row, option }) => {
          return String(row.id).includes(String(option.data))
        }">
          <template #filter="{ $panel, column }">
            <input type="type" v-for="(option, index) in column.filters" :key="index" v-model="option.data"
              @input="$panel.changeOption($event, !!option.data, option as any)">
          </template>

        </vxe-column>
        <vxe-column field="goods_name" title="商品名称"></vxe-column>
      </vxe-table>
      <template #footer>
        <el-button @click="closeAnalyze">关闭</el-button>
      </template>
    </el-dialog>

    <el-table stripe @selection-change="(data: typeof state.selection) => { state.selection = data }"
      :data="mallStore.tableList" class="store-table" ref="storeTableEl" height="700">
      <template #empty>
        <el-empty description="暂无店铺"></el-empty>
      </template>
      <el-table-column type="selection" width="30"></el-table-column>
      <el-table-column label="店铺名称" prop="mallName"></el-table-column>
      <el-table-column label="店铺ID" prop="mallId"></el-table-column>
      <el-table-column label="最近登录时间" prop="update_time">
        <template #default='scope'>
          {{ dayjs(Number(scope.row.update_time)).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
      <el-table-column label="店铺状态" width="120" prop="">
        <template #default='scope'>
          <el-tag v-if="mallStore.checkAvailable(scope.row)" type="success">在线</el-tag>
          <el-tag v-else type="info">失效</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" align="center" prop="" fixed="right">
        <template #default='scope'>
          <el-space>
            <el-button type="success" v-if="mallStore.checkAvailable(scope.row)" @click="storeAnalyze(scope.row)">
              全店解析
            </el-button>
            <el-button v-if="mallStore.checkAvailable(scope.row)" type="primary"
              @click="mallStore.openStorePage(scope.row)">打开店铺</el-button>

            <el-button type="primary" @click="mallStore.updatePddStore(scope.row)">登录</el-button>
            <el-button type="danger" @click="mallStore.deleteItem(scope.row.mallId)">删除</el-button>
          </el-space>
          <el-space class="m-t-8" v-if="mallStore.checkAvailable(scope.row)">
            <span>运费险:</span>
            <el-button :loading="tableRowLoading.insurance.has(`${scope.row.mallId}-check`)" plain type="primary"
              @click="insuranceAction(scope.row, 'check')">检查</el-button>
            <el-button :loading="tableRowLoading.insurance.has(`${scope.row.mallId}-open`)" plain type="success"
              @click="insuranceAction(scope.row, 'open')">打开</el-button>
            <el-button :loading="tableRowLoading.insurance.has(`${scope.row.mallId}-close`)" plain type="danger"
              @click="insuranceAction(scope.row, 'close')">关闭</el-button>
            <!-- <el-button @click="toPifaCookie(scope.row)">内个</el-button> -->
            <!-- <el-button @click="doThat(scope.row)">内个</el-button>  -->
          </el-space>

        </template>
      </el-table-column>
    </el-table>

    <ds-pagination :total="mallStore.pagination.total" v-model:current-page="mallStore.pagination.page"
      v-model:page-size="mallStore.pagination.limit" @current-change="mallStore.getList()"
      @size-change="mallStore.getList()" />
  </div>
</template>
<script lang='ts' setup>

import dayjs from 'dayjs';

import { useMallStore } from '/@/stores/store';
import { checkStoreInsurance, getStoreGoodsList,  openStoreInsurance, closeStoreInsurance,  checkStoreLogin, updateStore, buyTryGoodsApi } from '/@/apis/store';
import { addGoodsRecord, getReadList } from '/@/apis/goods';
import { ElLoading, ElMessage, ElMessageBox, TableInstance } from 'element-plus';
import {  onActivated, reactive, ref } from 'vue';
import { VxeTableInstance } from 'vxe-table';
import { useTable } from '/@/hooks/useTable';
import { delayPromise } from '/@/utils/common';
import { doThat } from '/@/apis/user';
const storeTableEl = ref<TableInstance>()
const state: {
  logList: Map<Store['mallId'], Array<{ type?: string, msg: string }>>
  logStore?: Store['mallId']
  selection: typeof mallStore.tableList,
  checkStatusLoding: boolean
} = reactive({
  logList: new Map(),
  selection: [],
  checkStatusLoding: false
})


const tableRef = ref<VxeTableInstance>()
const analyzeTable = useTable(
  500,
  {
    id: 'store-analyze-table',
    checkboxConfig: {
      checkMethod({ row }) {
        return row.goods_type != 19
      },
    }
  },
  { tableRef })
const analyzeState: {
  dialog: boolean
  store?: Store
  isGetList: boolean
} = reactive({
  dialog: false,
  isGetList: false
})

function storeAnalyze(mall: Store) {
  analyzeState.dialog = true
  analyzeState.store = mall
  analyzeTable.tableAction('clear')
  analyzeTable.tableProps.data = []
  getGoodsList(mall)
}

function getGoodsList(mall: Store) {
  const { tableProps } = analyzeTable
  let page = 1
  let total = 0
  const fn = async () => {
    tableProps.loadingConfig = {
      text: `加载第${page}页数据中。(${tableProps.data!.length}/${total})`
    }
    const res = await getStoreGoodsList(mall, { page: page++ })
    if (!res.success) {
      return Promise.reject(res)
    }
    const list = res.result?.goods_list || []
    total = res.result?.total || 0
    if (!list.length || tableProps.data!.length >= total) {
      return Promise.reject(res)
    }
    tableProps.data?.push(...list)
    return res
  }
  tableProps.loading = true
  analyzeState.isGetList = true
  const repeatFn = () => {
    fn()
      .then(async () => {
        if (analyzeState.isGetList && analyzeState.dialog) {
          await delayPromise(300)
          return repeatFn()
        }
        return Promise.reject()
      })
      .catch(() => {
        tableProps.loading = false
        analyzeState.isGetList = false
      })
  }
  repeatFn()

}
const goodsIdFilter = ref<any[]>([
  {
    data: '',
  }
])
async function addToLocal() {
  const { selection } = analyzeTable.tableState
  const list = selection.filter(item => item.goods_type != 19)
  if (!list.length) {
    ElMessage.warning({ grouping: true, message: '过滤后没有可用商品' })
    return
  }
  const store = analyzeState.store!
  const addP = list.map(async item => {
    const goodsInfo: GoodsInfo = {
      goods_name: item.goods_name,
      goods_id: item.id,
      goods_img: item.thumb_url,
      activity_id: 0,
      mallId: store!.mallId,
      mallName: store!.mallName,
      groupPrice: item.sku_group_price[0] / 100,
      normalPrice: item.sku_price[0] / 100,
      skus: item.sku_list.filter((skuItem: anyObj) => skuItem.isOnsale).map((skuItem: anyObj) => {
        return {
          skuId: skuItem.skuId,
          spec: skuItem.spec,
          skuImg: skuItem.skuThumbUrl,
          groupPrice: skuItem.groupPrice / 100,
          normalPrice: skuItem.normalPrice / 100
        }
      }),
      group_id: {
        multiple: [],
      },
      group_order_ids: [],
      coupon_code: ''
    }
    addGoodsRecord(goodsInfo)
  })
  await Promise.allSettled(addP)
  ElMessage.success({ grouping: true, message: '添加成功' })
  analyzeState.dialog = false
}

async function closeAnalyze() {
  if (analyzeState.isGetList) {
    await ElMessageBox({
      title: '提示',
      message: '当前数据正在加载，确定要关闭吗',
      showCancelButton: true
    })
  }
  analyzeState.dialog = false
}

const mallStore = useMallStore()
onActivated(() => {
  mallStore.getList()
})



const tableRowLoading: {
  insurance: Set<string>
} = reactive({
  insurance: new Set()
})

function insuranceAction(store: Store, type: 'check' | 'close' | 'open') {
  if (!mallStore.checkAvailable(store)) {
    return ElMessage({
      message: '店铺已过期',
      type: 'warning',
      grouping: true
    })
  }
  const key = `${store.mallId}-${type}`
  tableRowLoading.insurance.add(key)
  let promise: Promise<any>
  switch (type) {
    case 'check': {
      promise =
        checkStoreInsurance(store)
          .then(res => {
            if (res.data.result && res.data.result.configStatus) {
              ElMessage.success({
                message: '已开通',
                grouping: true
              })
            } else {
              ElMessage.warning({
                message: '未开通',
                grouping: true
              })
            }
            return res
          })

      break
    }
    case 'close': {
      promise = closeStoreInsurance(store)
      break
    }
    case 'open': {
      promise = openStoreInsurance(store)
      break
    }
  }
  promise
    .then(res => {
      if (type !== 'check') {
        ElMessage.success({
          message: '操作成功',
          grouping: true
        })
      }
    })
    .finally(() => {
      tableRowLoading.insurance.delete(key)
    })
}

async function checkLogin(list = state.selection) {
  state.checkStatusLoding = true
  await Promise.allSettled(list.map(async mall => {
    const res = await checkStoreLogin(mall)
    if (res.success && res.result.login) {
      if (!mallStore.checkAvailable(mall)) {
        await updateStore({
          mallId: mall.mallId,
          status: 1
        })
      }
    } else {
      if (mallStore.checkAvailable(mall)) {
        await updateStore({
          mallId: mall.mallId,
          status: 0
        })
      }
    }
  }))
  mallStore.getList()
  state.checkStatusLoding = false
}
// async function toPifaCookie(store:Store){
//   getPifaCookie(store)
// }


</script>
<style lang='scss' rel="stylesheet/scsss" scoped>
.table-header {
  height: 40px
}

.el-table.store-table {
  @include commonTableHeader();
  border: 1px solid var(--el-border-color-darker);

  .el-space {
    width: 100%;
    justify-content: flex-end;
  }
}

.table-footer {
  height: 30px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 10px;
}
</style>

<style lang="scss">
.store-analyze-log {
  // margin-left: 600px !important;
}

.store-goods-list {
  // margin-left: 550px !important;

  .el-table {
    @include commonTableHeader(false);
  }
}
</style>
