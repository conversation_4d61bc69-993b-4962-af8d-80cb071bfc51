<template>
    <div class="export-order">
        <el-dialog :width="800" v-model="state.configShow" title="导出内容" class="export-order-dialog" :append-to-body="true">

            <el-form label-position="top">
                <el-form-item label="导出数据(按选择的顺序排位)">
                    <el-checkbox-group v-model="settingStore.exportOrder.config">
                        <template v-for="item in checkList">
                            <el-checkbox v-if="item.excelRule" :label="(item.dataKey! as string)">{{ item.excelRule.label ||
                                item.title }}</el-checkbox>
                        </template>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="预览">


                    <el-space :wrap="true">
                        <span class="item" v-for="item in settingStore.exportOrder.config"> {{
                            checkListMap.get(item)?.excelRule?.label || checkListMap.get(item)?.title
                            || '' }}</span>
                    </el-space>

                    <!-- <el-table :data="state.list.slice(0, 2)">
                        <el-table-column
                            :label="checkListMap.get(item)!.title || checkListMap.get(item)!.excelRule?.label || ''"
                            v-for="item in state.checkList">
                            <template #default='scope'>
                                {{ checkListMap.get(item)?.excelRule!.value!(scope.row) }}
                            </template>
                        </el-table-column>
                    </el-table> -->
                </el-form-item>
                <el-form-item label="文件夹">
                    <el-space :wrap="true">
                        <span class="folder-container">{{ settingStore.exportOrder.savePath }}</span>
                        <el-link :underline="false" type="primary" @click="changeSavePath">{{
                            settingStore.exportOrder.savePath ? '更换' : '添加' }}</el-link>
                    </el-space>
                </el-form-item>
                <el-form-item label="文件名">

                    <el-input v-model="state.fileName"></el-input>


                </el-form-item>
            </el-form>

            <template #footer>
                <el-button size="default" type="" @click="state.configShow = false">取消</el-button>
                <el-button size="default" type="primary" @click="exportSubmit"
                    :disabled="!settingStore.exportOrder.config.length || !settingStore.exportOrder.savePath || !state.fileName">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang='ts' setup>
import { Column, ElMessage, } from 'element-plus';
import dayjs from 'dayjs'
import { computed, h, reactive, watch } from 'vue';
import { openFileWindow, selectFolder, writeExcel } from '/@/apis/mainWindow';
import { useSetting } from '/@/stores/setting';

const props = defineProps<{
    columns: Array<Column<any> & { excelRule?: Partial<exportExcelRule> }>
}>()

const checkList = computed(() => {
    const columns: typeof props.columns = [
        {
            title: '收件人',
            key: 'name',
            dataKey: "name",
            width: 80,
            cellRenderer: (scope) => h('div', {}, scope.rowData.name),
            excelRule: {
                value: row => row.name,
                '!cols': {
                    wch: '10'
                }
            }
        },
        {
            title: '手机号',
            key: 'phone',
            dataKey: "phone",
            width: 120,
            cellRenderer: (scope) => h('div', {}, scope.rowData.phone),
            excelRule: {
                value: row => row.phone,
                '!cols': {
                    wch: '10'
                }
            }
        },
        {
            title: '省',
            key: 'province',
            dataKey: "province",
            width: 120,
            cellRenderer: (scope) => h('div', {}, scope.rowData.province),
            excelRule: {
                value: row => row.province,
                '!cols': {
                    wch: '10'
                }
            }
        },
        {
            title: '市',
            key: 'city',
            dataKey: "city",
            width: 140,
            cellRenderer: (scope) => h('div', {}, scope.rowData.city),
            excelRule: {
                value: row => row.city,
                '!cols': {
                    wch: '10'
                }
            }
        },
        {
            title: '区',
            key: 'district',
            dataKey: "district",
            width: 120,
            cellRenderer: (scope) => h('div', {}, scope.rowData.district),
            excelRule: {
                value: row => row.district,
                '!cols': {
                    wch: '20'
                }
            }
        },
        {
            title: '详细地址',
            key: 'address',
            dataKey: "address",
            width: 120,
            // cellRenderer: (scope) => h('div', {}, scope.rowData.address)
            cellRenderer: (scope) => scope.rowData.address,
            excelRule: {
                value: row => row.address,
                '!cols': {
                    wch: '20'
                }
            }
        },
    ]
    return [...props.columns, ...columns]
})

const checkListMap = computed(() => {
    const map = new Map<string, typeof props.columns[number]>()
    checkList.value.forEach(item => {
        map.set((item.dataKey! as string), item)
    })
    return map
})

const settingStore = useSetting()

watch(() => settingStore.exportOrder, () => {
    const data = { ...settingStore.exportOrder }
    data.config = [...data.config]
    settingStore.setOrderSetting('exportOrder', data)
}, { deep: true })

const state: {
    configShow: boolean
    list: anyObj[]
    fileName: string
} = reactive({
    configShow: false,
    list: [],
    fileName: ''
})

function openConfig(data: typeof state.list) {
    state.list = data
    let fileName = `${data.length}单-${dayjs().format('YYYYMMDDHHmmss')}.xls`
    state.fileName = fileName
    state.configShow = true
}
defineExpose({
    openConfig
})

async function exportSubmit() {

    const list = state.list
    const path = settingStore.exportOrder.savePath

    let fileName = state.fileName || `${list.length}单-${dayjs().format('YYYYMMDDHHmmss')}.xls`
    if (!fileName.endsWith('.xls') && !fileName.endsWith('.xlsx')) {
        fileName += '.xls'
    }

    if (!list.length) {
        return ElMessage.warning({
            message: '没有需要导出的订单',
            grouping: true
        })
    }
    if (!settingStore.exportOrder.config.length) {
        return ElMessage.warning({
            message: '请选择需要导出的内容',
            grouping: true
        })
    }

    const excelRule: Array<exportExcelRule> = settingStore.exportOrder.config.map(key => {
        const item = checkListMap.value!.get(key)!
        const rule = { ...item.excelRule! } as exportExcelRule
        rule.label = rule.label || item.title || ''
        return rule
    })
    writeExcel({
        path: `${path}/${fileName}`,
        data: [{
            name: fileName,
            data: [excelRule.map(item => item.label), ...list.map(item => excelRule.map(_item => _item.value(item)))]
        }],
        options: {
            sheetOptions: {
                "!cols": excelRule.map(item => item["!cols"])
            }
        }
    })
        .then(res => {
            openFileWindow(path)
            state.configShow = false
        })
        .catch((e) => {
            ElMessage.warning('写入失败' + e)
        })
}

async function changeSavePath() {
    const path = await selectFolder()
    // console.log(path)
    if (!path) {
        return ElMessage.warning({
            message: "取消选择文件夹"
        })
    }
    settingStore.exportOrder.savePath = path
}


</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
.el-space {
    .item {
        padding: 0 3px;
        border: var(--el-border);
        border-radius: 2px;
    }

    .folder-container {
        min-width: 200px;
        background: rgba($color: #000000, $alpha: .1);
        padding: 0 2px;
    }
}
</style>
<style lang="scss">
.el-dialog.export-order-dialog {
    // margin-left: 600px !important;
}
</style>
