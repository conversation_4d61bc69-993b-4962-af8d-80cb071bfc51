const jimp = require("jimp");

// function sendResult(data) {
//   parentPort.postMessage(data);
// }

const compress = (data) => {
  return new Promise((resolve, reject) => {
    let result = {
      with: 0,
      height: 0,
      base64: "",
    }
    try {
      const { url, compress } = data;
      jimp.read(url, (error, _lenna) => {
        try {
          if (error) {
            result.base64 = ''
            resolve(result);
            return;
          }
          let lenna = _lenna;
          if (compress > 0 && compress < 100) {
            lenna = lenna.quality(compress);
          }
          if (lenna && lenna._exif && lenna._exif.tags && lenna._exif.tags.Orientation) {
            const deg = getOrientationDegree(lenna._exif.tags.Orientation)
            // console.log('deg', deg)
            if (deg) {
              lenna = lenna.rotate(deg)
            }
          }
          lenna.getBase64Async(jimp.MIME_JPEG)
            .then((res) => {
              result.base64 = res
              resolve(result);
            })
            .catch(() => {
              result.base64 = ''
              resolve(result);
            });
        } catch (e) {
          result.base64 = ""
          resolve(result);
        }


      });
    } catch (e) {
      result.base64 = ""
      resolve(result);
    }
  });
};

function getOrientationDegree(orientation) {
  let degree = 0;
  switch (orientation) {
    case 2:
      degree = 180;
      break;
    case 3:
      degree = 180;
      break;
    case 4:
      degree = -90;
      break;
    case 5:
      degree = 90;
      break;
    case 6:
      degree = 90;
      break;
    case 7:
      degree = -90;
      break;
    case 8:
      degree = 0; // 通常不需要处理，因为是正常方向  
      break;
  }
  return degree;
}
process.on('message', async (data) => {
  const result = await compress(data)
  process.send({ ...data, result })
})
