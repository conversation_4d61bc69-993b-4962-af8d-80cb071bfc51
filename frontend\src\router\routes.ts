import { RouteRecordRaw } from "vue-router";
import LoginVue  from '/@/views/Login/Login.vue'
const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: "/Login",
  },
  {
    path: "/home",
    name:"home",
    component: () => import("/@/views/Home/Home.vue"),
    children:[
      {
        path:"/home",
        redirect:"/home/<USER>"
      },
      {
        path:"index",
        name:"index",
        component:()=>import('/@/views/Home/children/index/index.vue'),
        meta:{
          name:"下单任务",
          icon:"icon-home-filled"
        }
      },
      {
        path:"orderManage",
        name:"orderManage",
        component:()=>import('/@/views/Home/children/orderManage/orderManage.vue'),
        meta:{
          name:"订单管理",
          icon:"icon-list"
        }
      },
      {
        path:"orderSetting",
        name:"orderSetting",
        component:()=>import('/@/views/Home/children/orderSetting/orderSetting.vue'),
        meta:{
          name:"下单设置",
          icon:"icon-ic_sharp-settings"
        }
      },
      {
        path:"subAccount",
        name:"subAccount",
        component:()=>import('/@/views/Home/children/subAccount/subAccount.vue'),
        meta:{
          name:"小号管理",
          icon:"icon-clarity_group-solid"
        }
      },
      {
        path:"storeManage",
        name:"storeManage",
        component:()=>import('/@/views/Home/children/storeManage/storeManage.vue'),
        meta:{
          name:"店铺管理",
          icon:"icon-s-shop"
        }
      },
      {
        path:"mallTask",
        name:"mallTask",
        component:()=>import('/@/views/Home/children/mallTask/mallTask.vue'),
        meta:{
          name:"全店任务",
          icon:"icon-sales-up"
        }
      },
      {
        path:"refundManage",
        name:"refundManage",
        component:()=>import('/@/views/Home/children/refundManage/refundManage.vue'),
        meta:{
          name:"代拍退",
          icon:"icon-money"
        }
      },
      // {
      //   path:"supin",
      //   name:"supin",
      //   component:()=>import('/@/views/Home/children/changeSales/changeSales.vue'),
      //   meta:{
      //     name:"全店改销量(活动)",
      //     icon:"icon-sales-up"
      //   }
      // },
      // {
      //   path:"CSGift",
      //   name:"CSGift",
      //   component:()=>import('/@/views/Home/children/CSGift/CSGift.vue'),
      //   meta:{
      //     name:"全店改销量(赠品)",
      //     icon:"icon-sales-up"
      //   }
      // },
      // {
      //   path:"bulletin",
      //   name:"bulletin",
      //   component:()=>import('/@/views/Home/children/bulletin/bulletin.vue'),
      //   meta:{
      //     name:"系统公告",
      //     icon:"icon-notice"
      //   }
      // },
      {
        path:"appInfo",
        name:"appInfo",
        component:()=>import('/@/views/Home/children/appInfo/appInfo.vue'),
        meta:{
          name:"应用信息",
          icon:"icon-notice",
          hiddenInTab:true
        }
      },
    ]
  },
  {
    path: "/Login",
    name:'Login',
    // component: () => import("/@/views/Login/Login.vue"),
    component:LoginVue,
  },
  {
    path:'/pddVerify',
    name:'pddVerify',
    component:()=>import('/@/views/pddVerify/pddVerify.vue')
  }
];

export default routes;
