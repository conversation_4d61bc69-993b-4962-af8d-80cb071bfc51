import { defineStore } from "pinia";
import { getAccountList, getReviewUid } from "../apis/page";
import CryptoJS from "crypto-js";
type SubAccountItem = {
  account: string;
  use_num: number;
  add_time: number;
  past_time: number;
  status: {
    account: string;
    status: number;
  };
};
export const useSubAccount = defineStore("sub-account", {
  state() {
    const state: {
      /**真实的账号列表 */
      list: SubAccountItem[];
      /**一个账号可使用次数 */
      useLimit: number;
      total: number;
    } = {
      list: [],

      total: 0,
      useLimit: 10,//------------------------------------------------------------------------------- 一个账号可使用次数
    };
    return state;
  },
  getters: {
    availableSet() {
      const set = new Set<string | number>();
      this.list.forEach((item) => {
        if (item.use_num < this.useLimit && item.status?.status) {
          set.add(item.account);
        }
      });
      return set;
    },
    accountMap() {
      const map = new Map<string, SubAccountItem>();
      this.list.forEach((item) => {
        map.set(item.account, item);
      });
      return map;
    },
  },
  actions: {
    getList() {
      return new Promise((resolve) => {
        let page = 1;
        let lastPage = 1
        const list:SubAccountItem[] = []
        const fn = ()=> {
          getAccountList({ limit: 5000, page }).then((res) => {
            console.log(res,'getAccountList')
            this.total = res.data.total
            list.push(...res.data.data)
            lastPage = res.data.last_page
            page ++
          })
          .finally(()=>{
            if(page > lastPage){
              this.list = list
              resolve(true)
            }else{
              fn()
            }
          })
        }
        fn()
      });
    },

    checkAvailable(item: SubAccountItem) {
      return item.use_num < this.useLimit && item.status.status == 1;
    },
    getAccount(start: number) {
      const res = {
        status: false,
        ind: -1,
        msg: "",
        data: "",
      };
      if (!this.availableSet.size) {
        res.msg = "没有可用小号";
        return res;
      }
      let ind = start % this.list.length;
      let flag = true;
      let count = 0;
      while (flag) {
        count++;
        const item = this.list[ind];
        if (item && this.checkAvailable(item)) {
          flag = false;
          res.status = true;
          res.data = item.account;
          item.use_num++;
          res.ind = ind;
          return res;
        } else {
          ind = ++ind % this.list.length;
          if (count > this.list.length + 10) {
            res.msg = "没有可用小号";
            return res;
          }
        }
      }
      res.msg = "意外的错误";
      return res;
    },
    recover(account:SubAccountItem['account']){
      if(this.accountMap.has(account)){
        this.accountMap.get(account)!.use_num--
      }
    }
  },
});

export const usePddAccount = defineStore("pdd-account", {
  state: () => {
    const state: {
      index: number;
      list: string[];
    } = {
      list: [],
      index: 0,
    };
    return state;
  },
  actions: {
    getList(num: number) {
      return getReviewUid({ num }).then((res) => {
         const str = res.data;
        const key = CryptoJS.enc.Utf8.parse("****************");
         const iv = CryptoJS.enc.Utf8.parse("****************");
         const d = CryptoJS.AES.decrypt(str, key, {
           iv,
           mode: CryptoJS.mode.CBC,
           padding: CryptoJS.pad.Pkcs7,
         });
         console.log(res,d.toString(CryptoJS.enc.Utf8))
        //console.log (res.data);
        try {
          //this.list = JSON.parse(res.data.toString(CryptoJS.enc.Utf8));
          this.list = JSON.parse(d.toString(CryptoJS.enc.Utf8));
          console.log(this.list)
          return this.list;
        } catch (e) {
          return Promise.reject({
            msg: "初始化出错",
          });
        }
      });
    },
    getItem() {
      if (this.index > this.list.length) {
        this.index = 0;
      }
      return this.list[this.index++];
    },
  },
});
