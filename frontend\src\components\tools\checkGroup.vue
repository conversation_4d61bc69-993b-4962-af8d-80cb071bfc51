
<template>
  <div>
    <el-checkbox-group v-model="value" size="default">
      <el-checkbox 
      v-for="item in list" 
      :key="item[valueKey]" 
      :label="item[valueKey]"
      >{{ item[title] }}</el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script lang='ts' setup>import { ref, watch } from 'vue';
const props = withDefaults(defineProps<{
  list:anyObj[]
  title?:string
  valueKey?:string
  defaultValue?:Array<string|number>
}>(),{
  title:'title',
  valueKey:'value'
})

function initValue():Array<string|number>{
  const {list,valueKey,defaultValue} = props
  if(!defaultValue){
    return []
  }else{
    const set = new Set(list.map(item => item[valueKey]))
    return defaultValue.filter(item => set.has(item))
  }
}

const emits = defineEmits(['change'])
const value = ref<Array<string|number>>(initValue())
  console.log(value)
watch(value,(val)=>{
    emits('change',val)
},{immediate:true})
</script>
<style lang='scss'  rel="stylesheet/scsss" scoped>
 
</style>

