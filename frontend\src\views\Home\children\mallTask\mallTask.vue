<template>
  <div class="mall-task">
    <el-radio-group v-model="active" style="margin-bottom: 10px;" size="default">
      <el-radio-button v-for="item in tabList" :label="item.key" :key="item.key">{{ item.label }}</el-radio-button>
    </el-radio-group>
    <div class="mall-task-content">
      <keep-alive>
        <component :is="current?.component" />
      </keep-alive>
    </div>
  </div>
</template>
<script lang='ts' setup>
import { computed, markRaw,  ref } from 'vue';
import CSActivity from './children/CSActivity/CSActivity.vue';
import CSGift from './children/CSGift/CSGift.vue';
import CSLimit from './children/CSLimit/CSLimit.vue';
import Order from './children/order/order.vue'
import GlobalBuy from './children/GlobalBuy/GlobalBuy.vue'
const active = ref('order')
const tabList = [
    {
        label:'活动',
        key:'activity',
        component:markRaw(CSActivity)
    },
    {
        label:'赠品',
        key:'gift',
        component:markRaw(CSGift)
    },
    {
        label:'快速下单',
        key:'order',
        component:markRaw(Order)
    },
    {
        label:'全球购',
        key:'globayBuy',
        component:markRaw(GlobalBuy)
    },
    {
        label:'0.1折改销量',
        key:'CSLimit',
        component:markRaw(CSLimit)
    },
]

const current = computed(()=>{
    return tabList.find(item=>item.key === active.value)
})
</script>
<style lang='scss'  rel="stylesheet/scss" scoped>
 
</style>
