'use strict';
const Service = require('ee-core').Service;


const noticeChannel = 'notice-to-view'

/**
 * 示例服务
 * @class
 */
class NoticeToViewService extends Service {

  constructor(ctx) {
    super(ctx);
  }
  async message(data){
    const _data = {
        type:'error',
        message:'系统频繁',
        grouping:true
    }
    this.app.electron.mainWindow.send(noticeChannel, {
        type:'message',
        data:Object.assign(_data,data)
    });
  }

  async notifiy(data){
    const _data = {
        title:"系统提示",
        type:'warning',
        message:'系统频繁',
    }
    this.app.electron.mainWindow.send(noticeChannel, {
        type:'notification',
        data:Object.assign(_data,data)
    });
  }

  async messageBox(data){
    const _data = {
        title:"系统提示",
        type:'info',
        message:'系统频繁',
    }
    this.app.electron.mainWindow.send(noticeChannel, {
        type:'messageBox',
        data:Object.assign(_data,data)
    });
  }
}

NoticeToViewService.toString = () => '[class NoticeToViewService]';
module.exports = NoticeToViewService;