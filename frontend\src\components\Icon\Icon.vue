<template>
  <div class="ds-icon">
    <template v-if="type === 'symbol' || !type">
      <svg class="icon iconfont" :class="class" aria-hidden="true">
        <use :xlink:href="href.includes('#') ? href : '#' + href"></use>
      </svg>
    </template>

    <template v-if="type === 'fontClass'">
      <i class="iconfont" :class="href"></i>
    </template>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps<{
  href: string;
  class?: string;
  type?: "fontClass" | "symbol";
}>();
</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
.ds-icon{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
