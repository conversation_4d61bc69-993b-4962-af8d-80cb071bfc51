import { path } from "../apis/config";
import { safeFileUrl } from "./common";

export function urlToBase64(
    url: string,
    quality?: number,
    type = "img"
  ): Promise<{ base64: string; duration?: number }> {
    return new Promise((resolve) => {
      let canvas = document.createElement("canvas");
      if (type == "img") {
        const img = new Image();
        img.crossOrigin = "Anonymous";
        img.onload = function () {
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d")!;
          ctx.drawImage(img, 0, 0);
          const ext = path.extname(url).replace(".", "");
          const dataURL = canvas.toDataURL("image/" + ext, quality);
          resolve({ base64: dataURL });
          // @ts-ignore
          canvas = null;
        };
        img.src = safeFileUrl(url);
      } else if (type == "video") {
        const video = document.createElement("video");
        document.body.appendChild(video);
        video.crossOrigin = "Anonymous";
        video.currentTime = 1;
  
        video.oncanplay = function () {
          canvas.width = video.clientWidth;
          canvas.height = video.clientHeight;
          const ctx = canvas.getContext("2d")!;
          ctx.drawImage(video, 0, 0);
          const dataURL = canvas.toDataURL("image/png", quality);
          resolve({ base64: dataURL, duration: video.duration,});
          document.body.removeChild(video);
          // @ts-ignore
          canvas = null;
        };
        video.src = safeFileUrl(url);
      } else {
        resolve({ base64: "" });
      }
    });
  }