import { defineStore } from "pinia";
import { getUserInfo } from "../apis/page";
import { useAppStore } from "./app";
import { ElMessageBox } from "element-plus";

type Info = anyObj;
type agentInfo = {
  username: string;
  tutor_name: string;
  tutor_avatar: string;
  tutor_qq: string;
  tutor_douyin: string;
  tutor_wechat: string;
  tutor_code: string;
  open_serve: number;
  wechat: string;
  wechat_code: string;
  pay_type: string;
}
type pait = anyObj;
export const useUserStore = defineStore("user", {
  state: () => {
    const state: {
      data: anyObj;
      refreshUserInfoLoading: boolean;
    } = {
      data: {},
      refreshUserInfoLoading: false,
    };
    return state;
  },
  getters: {
    user_info(): Info {
      return this.data.user_info;
    },
    app_info(): anyObj {
      return this.data.app_info;
    },
    agent():agentInfo | undefined {
      return this.data.agent || void 0;
    },
    pait():pait {
      return this.data.pait
    },
    token(): string {
      return this.data.token || "";
    },
  },
  actions: {
    refreshUserInfo() {
      this.refreshUserInfoLoading = true;
      return getUserInfo()
        .then((res) => {
          console.log(res);
          res.data.token = this.data.token;
          this.data = res.data;
          if(res.data.app_info?.is_past){
            ElMessageBox({
              title:'提示',
              message:'当前账号已过期,10秒后关闭应用!',
              type:'warning'
            })
            setTimeout(() => {
              //useAppStore().closeApp(false)
            }, 10 * 1000);
          }
        })
        .finally(() => {
          this.refreshUserInfoLoading = false;
        });
    },
  },
});
