import { ipc } from "./config";
import { AxiosRequestConfig } from "axios";
import { ElLoading, ElMessage, LoadingOptions } from "element-plus";
import { useAppStore } from "../stores/app";
import { cloneDeep } from "lodash";
import { batchRequest, delayPromise, pddResErrorMsg, randomIp, retry } from "../utils/common";
import { acquireQrCode } from "./pifa";
import lodash from 'lodash'
import { pddStoreRequestHeader } from "./changeSales";
import { useMallStore } from "../stores/store";
type Options = {
  showErrorMsg?: boolean;
  loading?: boolean;
  repeatWhenFailed?: boolean;
  repeatMax?: number;
  isStop?: () => boolean
  beforeRequest?: (config: AxiosRequestConfig) => any;
};

function request<T = any>(
  config: AxiosRequestConfig & { encrypt?: boolean },
  options: Options = {},
  loading: LoadingOptions = {}
): Promise<responseData<T>> {
    
    
  const appStore = useAppStore();
  // if(config.url == '/api/tool/getGoodsDetails'){ 
  //   config.url = 'http://**********:86/api/tool/getGoodsDetails2'
  // }
  if (config.url !== "/api/user/risk" && appStore.isRisk) {
    ElMessage.error({
      message: "-网络超时-",
      grouping: true,
    });
    return Promise.reject({
      code: 1,
      msg: "网络超时",
      data: "",
    });
  }

  options = {
    showErrorMsg: true,
    loading: false,
    repeatWhenFailed: false,
    ...options,
  };
  loading = Object.assign(
    {
      text: "--加载中--",
      target: document.getElementById("home-page-scroll") || void 0,
    },
    loading
  );

  let loadingInstance: undefined | ReturnType<typeof ElLoading.service> =
    void 0;
  if (options.loading) {
    loadingInstance = ElLoading.service(loading);
  }

  const fn = async () => {
    const _config: AxiosRequestConfig = {
      // timeout: 15 * 1000,
      ...config,
    };
    if (!_config.url?.startsWith("http")) {
      const appStore = useAppStore();
      _config.url =
        (appStore.currentUrl || appStore.info.app.software_url) + config.url;
        
      // _config.url =

      //   ("http://192.168.3.99:86") + config.url;

      // _config.url = 'http://111.230.71.218:86/' + config.url ;
      // _config.url = 'http://api.khzss.com:9001/' + config.url ;
      // _config.url = 'http://81.71.1.38:86' + config.url ;

      // _config.url = 'http://**********:86/' + config.url;
      // _config.url = 'http://211.99.98.137:86/' + config.url;

      // _config.url = 'http://*************:86/' + _config.url
      // _config.url = 'http://***********:85/' + _config.url
      // _config.url = 'http://**************:86/' + _config.url
      // _config.url = 'http://***********:85/' + _config.url
    }
    // if (_config.url?.includes("81.71.97")) {
    //   ElMessage.warning({
    //     message: "当前请求地址为：81.71.**.*:86",
    //     grouping: true,
    //   });
    // }
    console.log(config,options,loading,'请求信息');
    if (options.beforeRequest) {
      await Promise.allSettled([options.beforeRequest(_config)]);
    }
    // console.log(_config)
    return ipc
      .invoke("controller.page.request", {
        ..._config,
        time_diff: appStore.time_diff,
      })
      .then((res) => {
        loadingInstance && loadingInstance.close();
        if (typeof res !== "object") {
          return Promise.reject({
            code: 1,
            msg: "网络超时",
            data: res,
          });
        }
        if (!res || res.code) {
          return Promise.reject(res);
        } else {
          return res;
        }
      })
      .catch((res) => {
        loadingInstance && loadingInstance.close();
        return Promise.reject(res);
      });
      
  };

  return new Promise((resolve, reject) => {
    const max = options.repeatMax || 5;
    let current = 1;
    const errorHistory: anyObj[] = [];
    function fnRepeat() {
      fn()
        .then((res) => {
          appStore.urlErrorCount = 0;
          resolve(res);
        })
        .catch(async (res) => {
          /**是否重新请求 */
          const isRepeat = () => {
            if (options.isStop) {
              const isStop = options.isStop();
              if (isStop) {
                return false
              }
            }
            if (max < current) {
              // console.log("max > current");
              return false;
            }
            if (res.code && res.code == 829) {
              // console.log("es.code && res.code === 829");
              return true;
            }
            const msg = res.msg || '';
            if (msg.includes("网络频繁")) {
              // console.log("msg.includes('网络频繁')");
              return true;
            }
            if (msg.includes("网络超时")) {
              // console.log("msg.includes('网络超时')");
              return true;
            }
            if (msg.includes("网络异常")) {
              // console.log("msg.includes('网络超时')");
              return true;
            }
            return false;
          };
          // console.log(res,config)
          if (res.msg && res.msg.includes("网络频繁")) {
            appStore.selectUseableUrl();
          } else {
            appStore.urlErrorCount = 0;
          }
          current++;
          // 网络频繁 （829是因为今日为20230829） 则重新发送，
          if (isRepeat()) {
            errorHistory.push(res);
            await delayPromise(1000)
            fnRepeat();
          } else {
            if (options.showErrorMsg && res.msg) {
              ElMessage.error({
                message: res.msg,
                grouping: true,
              });
            }
            if (typeof res === "object") {
              res._requestCount = current - 1;
              res._error_his = errorHistory;
            }
            reject(res);
          }
        });
    }
    fnRepeat();
  });
}

export async function anyRequest(config: AxiosRequestConfig, opt?: {
  afterVerify?: (config: AxiosRequestConfig, isSuccess: boolean) => any
}) {
  const res = await ipc.invoke("controller.page.anyRequest", cloneDeep(config));
  try {
    if (typeof res === 'object' && res && res.result && res.result.verifyAuthToken) {
      const appStore = useAppStore()
      const verifyAuthToken = res.result.verifyAuthToken
      const isSuccess = await appStore.openPddVerify_outwin({ verify_auth_token: verifyAuthToken })
      opt?.afterVerify?.(config, isSuccess)
      if (config.url?.includes('/mms/edit/commit/submit')) {
        config.headers!.verifyauthtoken = verifyAuthToken
      }
      return ipc.invoke("controller.page.anyRequest", cloneDeep(config));
    }
  } catch (e) { }
  return res
}

export type { Options };

export default request;

const ipcRoute = {
  resolveApply: "controller.page.resolveApply",
  collectComment: "controller.page.collectComment",
  getComment: "controller.page.getComment",
  getFolderComment: "controller.page.getFolderComment",
  getJinBaoData: "controller.page.getJinBaoData",
  clearSession: "controller.page.clearSession",
  changeApplyWinCount: "controller.page.changeApplyWinCount",
  clearApplyList: "controller.page.clearApplyList",
  getGoodsDetails: "controller.page.getGoodsDetails",
  exchangeSubAccount: "controller.page.exchangeSubAccount",
  addPddStore: "controller.page.addPddStore",
  autoSupply: "controller.page.autoSupply",
  getCommonComment: "controller.page.getCommonComment",
  uploadImage_base64: "controller.page.uploadImage_base64",
  uploadVideo: "controller.page.uploadVideo",
};

export function storeAntiContent(): Promise<string> {
  return ipc.invoke('controller.page.store_anti_content')
}

export function singleBuy(data: {
  "createOrderItemList": Array<{
    "goodsId": number
    "skuId": number
    "skuNum": number
  }>
}) { 
  //   return acquireQrCode({
  //     "subScene": 1,
  //     "createOrderDTO":
  //     {
  //       "orderType": 1,
  //       "createOrderItemList": data.createOrderItemList
  //     }
  //   })
  return anyRequest({
    url: 'https://mms.pinduoduo.com/pifa/goods/singleBuy',
    method: 'post',
    headers: {
      "Anti-Content": window.account_anti_content(),
      Origin: 'https://mai.pinduoduo.com',
      "Content-type": 'application/json;charset=UTF-8'
    },
    data
  })
}


/**
 * 一些应用信息
 * @returns
 */
export function appInfo() {
  return request({
    url: "/api/Index/getAppInfo",
  }, { showErrorMsg: false });
}

export function attackList() {
  return request({
    url: "/api/tool/attackList",
    method: "get",
  }, { showErrorMsg: false });
}

/**
 * 获取用户信息
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function getUserInfo(
  data = {},
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/user/getUserInfo",
    },
    options,
    loading
  );
}

/**
 * 获取小号列表
 * @param data
 * @returns
 */
export function getAccountList(data: { page: number; limit: number }) {
  return request({
    url: "/api/account/getList",
    // url: "/api/account/getList_v2",
    data,
  });
}
/**
 * 购买小号
 * @param data
 * @returns
 */
export function accountBuy(data: {
  num: number;
  id: number | string;
  service_id: number;
}) {
  return request({
    url: "/api/account/buy",
    data,
    method: "post",
  });
}

/**
 * 小号套餐
 * @returns
 */
export function accountCommbo() {
  return request({
    url: "/api/account/getCombo",
  }, { showErrorMsg: false });
}
/**
 * 删除小号
 * @param data
 * @returns
 */
export function accountDelete(data: { account: string }) {
  return request({
    url: "/api/account/del",
    method: "post",
    data,
  });
}

/**
 * 检测小号
 * @returns
 */
export function checkAccount() {
  return request({
    url: "/api/account/check",
    timeout: 10 * 60 * 1000,
  });
}

/**
 * 恢复误删小号
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function recoverAccount(
  data = {},
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/account/recover",
    },
    options,
    loading
  );
}

/**
 * 风控账号
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function userRisk(
  data: {
    remark?: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/user/risk",
      data,
      encrypt: true,
    },
    options,
    loading
  );
}

/**获取商品详情 云端接口获取 */
export function getGoodsDetails_web(
  data: {
    goods_id: string | number;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      // url: "/api/tool/getGoodsDetails",
      // url: "/api/tool/getGoodsDetails2",
      url: "/api/tool/getGoodsDetails3",
      // url: "http://***********:85/api/tool/getGoodsDetails3",
      // url: "http://**********:86/api/tool/getGoodsDetails2",
      data,
      method: "post",
      encrypt: true,
    },
    options,
    loading
  )
    .then(res => {
      console.log(res)
      return res
    })
}
/**获取商品详情 （批发） */
export function getGoodsDetails_pifa(data: { goods_id: string | number }) {
  return ipc.invoke(ipcRoute.getGoodsDetails, data).then((res) => {
    // console.log(res);
    if (!res) {
      return Promise.reject({});
    } else if (res?.success) {
      return res;
    } else {
      return Promise.reject(res);
    }
  });
}

export function taskList(data: {
  limit: number;
  page: number;
  status?: number;
}) {
  return request({
    url: "/api/task/list",
    data,
  });
}

/**创建任务 */
export function addTask(data: {
  order_num: number;
  spell_num: number;
  start_site: number;
  type: string;
  mode: string;
  coupon: string;
  setting: string;
  start_time: number;
  goods_id: number | string;
  goods_name: string;
  price: string | number;
  sku: string | number;

  skus: string;

  sku_spec: string;
  shop_id: string | number;
  group_id: string | number;
  keyword: string;
  group_order_id?: string | number;
  shop_name: string;
  cps_sign?: string;
  duo_duo_pid?: string;

  // 活动ID
  activity_id: number;
  use_coupon: number;
  coupon_code: number | string;
  /**优惠券类型 */
  coupon_type: string;

  // auto_change_price:number
  // auto_supply:number
}) {
  return request({
    url: "/api/task/add",
    method: "post",
    data,
  });
}

/**删除任务 */
export function taskDelete(data: { task_id: string }) {
  return request({
    url: "/api/task/del",
    data,
  });
}
/**删除任务 */
export function taskPause(data: { task_id: string }) {
  return request({
    url: "/api/task/stop",
    params: data,
  });
}
/**
 * 创建任务的订单
 * account_site 小号位置 order_site 请求编号(累加)
 * @param data
 * @returns
 */
export function taskStart(data: {
  task_id: string | number;
  account: string | number;

  account_site: number;
  order_site: number;

  /**名称暗号 */
  name_cipher?: string;
  name_site?: "before" | "after";
  /**地址暗号 */
  address_cipher?: string;
  address_site?: string;
  /**过滤地址 */
  filter_address: string;
  /**指定地址 */
  appoint_address?: string;
  // 收货信息
  name?: string;
  phone?: string;
  province?: string;
  city?: string;
  district?: string;
  address?: string;

  use_coupon?: 0 | 1;
  anti_content: string;

  sku_id: number
  // spec:string
}, options?: Options) {
  console.log(data);
  return request(
    {
      // url: "/api/task/start",
      // url: "/api/task/start_v2",
      url: "/api/task/start_v3",
      method: "post",
      data,
    },
    {
      showErrorMsg: false,
      ...options
    }
  );
}

/*****************************************************订单管理 start */

/**
 * 订单列表
 * @returns
 */
export function orderList(
  data: {
    limit?: number;
    page?: number;
    status?: number;
    time?: string;
    type?: string;
    goods_id?: string;
    shop_name?: string;
    order_sn?: string;
    comment_status?: number;
    price?: string | number;
    shipping_status?: number;
    is_delete?: number;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      // url: "http://*************:86/api/order/getList",
      // url: "http://**************:81/api/order/getList",
      // url: appStore.specialApiUrl + "/api/order/getList",
      // url:  "http://*************:85/api/order/getList",
      // url: "/api/order/getList",
      url: "/api/order/getList",
      data: data,
      encrypt: true,
      timeout: 15 * 1000,
    },
    options,
    loading
  ).catch((res) => {
    const appStore = useAppStore();
    if (res.msg.includes("网络超时")) {
      appStore.specialApiUrl = "";
    }
    return Promise.reject(res);
  });
}

/**
 * 恢复订单
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function orderRecover(
  data: {
    order_sns: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/recover",
      data,
    },
    options,
    loading
  );
}

/**
 * 同步订单
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function updateOrder(
  data: {
    ids: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  const appStore = useAppStore();
  console.log(data);
  return request(
    {
      url: "/api/order/updateOrder",
      method: "post",
      data,
      timeout: 1000 * 10,
    },
    options,
    loading
  ).catch((res) => {
    if (res.msg.includes("网络超时")) {
      appStore.specialApiUrl = "";
    }
    return Promise.reject(res);
  });
}


/**
 * 同步订单V2
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function updateOrderV2(
  data: {
    ids: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  const appStore = useAppStore();
  console.log(data);
  return request(
    {
      url: "/api/order/updateOrderV2",
      method: "post",
      data,
      timeout: 1000 * 10,
    },
    options,
    loading
  ).catch((res) => {
    if (res.msg.includes("网络超时")) {
      appStore.specialApiUrl = "";
    }
    return Promise.reject(res);
  });
}


/**同步单个订单信息（返回同步信息） */
export function updateOrderInfo(
  data: {
    order_sn: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {

  return request(
    {
      url: "/api/order/updateOrderInfo",
      data,
    },
    options,
    loading
  );
}

/**
 * 同步待发货订单
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function updateNoPayOrder(
  data = {},
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/updateNoPayOrder",
    },
    options,
    loading
  );
}

/**
 * 修改价格
 * goodsDiscount 订单价格修改需要 《减少》 的价格
 */
export async function changeOrderPrice(data: {
  store_id: number | string;
  order_sn: string | number;
  goodsDiscount: number;
  shop_name: string;
}) {
  console.log('[changeOrderPrice]',data)
  if (data.goodsDiscount <= 0) {
    return Promise.reject({
      code: 1,
      msg: "修改价格不能小于等于0--"
    })
  }
  const responseCode = {
    /**成功 */
    SUCCESS: 0,
    /**正常错误 */
    ERROR: 1,
    /**需要验证（登录商家账号） */
    NEEDVERIFY: 2,
  };
  const response = {
    code: responseCode.ERROR,
    msg: "",
    data: {},
  };
  const { store_id, order_sn, goodsDiscount } = data
  const mall = useMallStore().tableList.find(item => item.mallId == store_id)
  if (!mall) {
    response.msg = "没有店铺信息";
    return Promise.reject(response);
  }

  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/latitude/message/sendOrderUrge",
    data: {
      orderSn: order_sn,
      urgeTypes: [1],
      goodsDiscount: goodsDiscount,
      anti_content: await storeAntiContent(),
    },
    headers: await pddStoreRequestHeader(mall),
  }

  const res = await anyRequest(config)

  if (!res.success) {
    response.msg = pddResErrorMsg(res)
    response.code = responseCode.ERROR
  } else {
    response.code = responseCode.SUCCESS
    response.data = res
  }
  return !response.code ? response : Promise.reject(response)
}
/**订单备注(文字 旗帜)
 * RED红色 PURPLE紫色 GREEN绿色 BLUE蓝色 YELLOW黄色
 */
export async function orderRemark(data: {
  store_id: number | string;
  orderSn: string | number;
  remark: string;
  remarkTagName: string | null;
  remarkTag: string | null;
}) {
  const response = {
    code: 1,
    msg: "",
    data: {},
  };
  const { store_id, orderSn, remark, remarkTagName, remarkTag } = data
  const mall = useMallStore().tableList.find(item => item.mallId == store_id)
  if (!mall) {
    response.msg = "没有店铺信息";
    return response;
  }
  const config = {
    method: "post",
    url: "https://mms.pinduoduo.com/pizza/order/noteTag/add",
    data: {
      orderSn,
      //备注
      remark,
      //RED红色 PURPLE紫色 GREEN绿色 BLUE蓝色 YELLOW黄色
      remarkTagName,
      remarkTag,
      source: 1,
    },
    headers: await pddStoreRequestHeader(mall),
  }
  const res = await anyRequest(config)
  if (res.success) {
    response.code = 0
    response.data = res
  } else {
    response.msg = pddResErrorMsg(res)
  }
  return response
}


/**
 * 获取支付宝支付链接
 * @param data
 * @returns
 */
export function getApplyInfo(
  data: {
    order_sn: string;
  },
  option?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/getPayLink",
      data,
      method: "post",
    },
    {
      repeatWhenFailed: true,
      ...option,
    },
    loading
  );
}

/**
 * 获取微信代付链接
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function wechatPay(
  data: {
    order_sn: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/wechatPay",
      data,
    },
    options,
    loading
  );
}

/**
 * 处理自动支付
 * @param data
 * @returns
 */
export function resolveApply(data: {
  // type:string,
  item: {
    url: string;
    order_sn: string;
    id: string | number;
    type: "code" | "auto";
  };
  win: {
    pass: string;
    passArr: Array<string | number>;
  };
}) {
  return ipc.invoke(ipcRoute.resolveApply, data);
}

export function ApplyWinCountChange(count: number) {
  return ipc.invoke(ipcRoute.changeApplyWinCount, count);
}

export function clearApplyList() {
  return ipc.invoke(ipcRoute.clearApplyList);
}

// 取消订单
export function cancelOrderRequest(
  data: {
    order_sn: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/cancel",
      data,
      method: "post",
    },
    options,
    loading
  );
}
/**
 * 删除订单
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function delOrderRequest(
  data: {
    order_sn: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/del",
      data,
      method: "post",
    },
    options,
    loading
  );
}
/**
 * 订单退款
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function orderRefund(
  data: {
    order_sn: string;
    after_sales_type: number;
    user_ship_status: number;
    question_type: string | number;
    question_desc: string;
    apply_amount?: number;
    images?: string;
    user_phone?: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/refund",
      data,
      method: "post",
    },
    options,
    loading
  );
}

/**取消退款 */
export function cancelRefundRequest(
  data: {
    order_sn: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/cancelRefund",
      data,
    },
    options,
    loading
  );
}

/**
 * 订单收货
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function orderConfirm(
  data: {
    order_sn: string;
    anti_content?: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  data.anti_content = window.get_anti_content();
  return request(
    {
      url: "/api/order/confirm",
      data,
      method: "post",
    },
    options,
    loading
  );
}
export function getSignature(data: {
  account: string
}) {
  return request({
    url: "/api/order/getSignature",
    data,
  }, { showErrorMsg: false })
}
/**申请平台介入 */
export function applyPlatform(data: {
  remark: string
  images: string
  order_sn: string
}, options?: Options,) {
  return request({
    url: "/api/order/applyPlatform",
    data,
    method: "post"
  }, options)
}

/**
 * 导出核销卡券
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function exportCardRequest(
  data: {
    order_sns: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/importCard",
      data,
      method: "post",
    },
    options,
    loading
  );
}

/**
 * 券自动核销
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function getAutoSellPassId(
  data: {
    order_sns: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/autoSell",
      data,
      method: "post",
    },
    options,
    loading
  );
}

/**
 * 订单免拼
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function autoSpellRequest(
  data: {
    order_sn: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/autoSpell",
      data,
      method: "post",
    },
    options,
    loading
  );
}

/**
 * 给商家发送消息
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function sendMessageToShop(
  data: {
    shop_id: string | number;
    account: string | number;
    content: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/task/sendMessage",
      method: "post",
      data,
    },
    options,
    loading
  );
}

/*****************************************************订单管理 end */

/**
 * 采集评论
 * @param data
 * @returns
 */
export function collectComment(data: {
  collectMode: string;
  goodsID: string;
  count: number;
  cookie: string
}) {
  return ipc.invoke(ipcRoute.collectComment, data).then((res) => {
    console.log(res)
    if (res.code) {
      // ElMessage.error({
      //   message: res.msg,
      //   grouping: true,
      // });
      return Promise.reject(res);
    } else {
      return res;
    }
  });
}

export function getComment(path?: string) {
  return ipc.invoke(ipcRoute.getComment, path).then((res) => {
    // console.log(res);
    if (res.code) {
      return Promise.reject(res);
    }
    return res;
  });
}

/**将路径当做评论文件夹读取图片和文字 */
export function getFolderComment(path: string): Promise<{
  code: number;
  msg: string;
  data?: {
    folder: string;
    imgs: string[];
    videos: string[];
    comment: string;
  };
}> {
  return ipc.invoke(ipcRoute.getFolderComment, path).then((res) => {
    if (res.code) {
      return Promise.reject(res);
    } else {
      return res;
    }
  });
}

export function getJinBaoData(url: string) {
  return ipc.invoke(ipcRoute.getJinBaoData, url).then((res) => {
    if (res.code) {
      return Promise.reject(res);
    }
    return res;
  });
}

// /**
//  * 将base64的图片转换为链接
//  * @param data
//  * @param options
//  * @param loading
//  * @returns
//  */
// export function uploadImage_base64(
//   data: {
//     image: string;
//     order_sn: string;
//   },
//   options?: Options,
//   loading?: LoadingOptions
// ) {
//   // console.log("data", data);
//   return request(
//     {
//       url: "/api/order/upload",
//       // url: "http://*************:86/api/order/upload",
//       data,
//       encrypt: false,
//     },
//     options,
//     loading
//   );
// }
export function uploadImage_base64(data: {
  image: string;
  order_sn: string;
  tk: string;
}) {
  // console.log("data", data);
  return ipc.invoke(ipcRoute.uploadImage_base64, data).then((res) => {
    if (res.code) {
      return Promise.reject(res);
    }
    return res;
  });
}
export function uploadVideo(data: {
  url: string;
  order_sn: string;
  tk: string;
}) {
  // console.log("data", data);
  return ipc.invoke(ipcRoute.uploadVideo, data).then((res) => {
    if (res.code) {
      return Promise.reject(res);
    }
    return res;
  });
}

/**
 * 获取小号token
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function getReviewUid(
  data: {
    num: number;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/tool/getReviewUid_v2",
      data,
    },
    options,
    loading
  );
}

/**
 * 评论
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function commentRequest(
  data: {
    order_sn: string;
    comment: string;
    dsr?: number;
    pictures: string;
    anonymous: 0 | 1;
    timeline_sync_type: 1 | 2;
    goods_id: string | number;
    anti_content: string;
    video?: {
      duration: number;
      size: number;
      cover_image_url: string;
      cover_image_width: number;
      cover_image_height: number;
      width: number;
      height: number;
      music_id?: string | number;
      url: string;
      extension_info?: anyObj;
    };
  },
  options?: Options,
  loading?: LoadingOptions
) {
  if (data.video) {
    data.video.music_id = data.video.music_id || "0";
    data.video.extension_info = data.video.extension_info || {
      extension_info: {
        pxq_param: { published: false, self: true, tags: ["others"] },
      },
    };
  }
  console.log("请求体", data);
  return request(
    {
      // url: "/api/order/comment",
      url: "/api/order/comment2",
      // url: "http://**********:86/api/order/comment2",
      data,
    },
    options,
    loading
  );
}
/**
 * 追加评论
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function commentAppendRequest(
  data: {
    order_sn: string;
    comment: string;
    dsr?: number;
    pictures: string;
    anonymous: 0 | 1;
    timeline_sync_type: 1 | 2;
    goods_id: string | number;
    anti_content?: string
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/appendReview",
      data,
    },
    options,
    loading
  );
}

/**
 * 获取地址列表
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function addressList(
  data: {
    pid: number | string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/getAddressList",
      data,
    },
    options,
    loading
  );
}

/**
 * 修改订单地址
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function updateOrderAddress(
  data: {
    order_sn: string;
    name: string;
    phone: number | string;
    province: string;
    city: string;
    district: string;
    address: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/order/updateOrderAddress",
      method: "post",
      data,
    },
    {
      ...options,
      beforeRequest(config) {
        config.data!.anti_content = window.account_anti_content()
      },
    },
    loading
  );
}

export function clearSession() {
  return ipc.invoke(ipcRoute.clearSession);
}

// 访客
export async function visit(
  data: {
    goods_id: number | string;
    num: number;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  // return request(
  //   {
  //     url: "/api/tool/visit",
  //     data,
  //     timeout: 60 * 1000,
  //   },
  //   options,
  //   loading
  // );
  await batchRequest(new Array(data.num || 1).fill(0), {
    batch: 2,
    request: async (list) => {
      await Promise.allSettled(list.map(() => visit_v2(data.goods_id)))
    }
  })
}

export async function visit_v2(
  goods_id: string | number
) {
  const config: AxiosRequestConfig = {
    url: 'https://th.yangkeduo.com/t.gif',
    method: 'post',
    headers: {
      "CLIENT_IP": randomIp(),
      "Content-Type": "application/json;charset=UTF-8",
      "Host": "t.yangkeduo.com",
      "REMOTE_ADDR": "**************",
      "Referer": "Android",
      "User-Agent": "android Mozilla/5.0 (Linux; Android 5.1.1; LG-E975 Build/H1I7FZ) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/******** Safari/537.36  phh_android_version/4.36.2 phh_android_build/511baf789d7a581f2363de954e6579b652270b3e phh_android_channel/xm",
      "Via": randomIp(),
      "X-Forwarded-For": randomIp(),
    },
    data: {
      local_ip: "",
      page_sn: '',
      ssid: "%22Tenda_4Y%22",
      page_id: Date.now() + '_' + lodash.random(1000000000, 9999999999),
      time: Date.now(),
      refer_page_element: 'goods',
      'network_operator': 46000,
      'event_type': 0,
      'goods_id': goods_id,
      "comment_show": 0,
      op: 'pv',
      page_name: "goods_detail",
      manufacture: "lge",
      refer_page_section: "goods_list",
      platform: "Android",
      network: 1,
      app_version: "4.36.2",
      refer_page_sn: "",
      refer_page_name: "",
      local_port: 0,
      refer_page_el_sn: '',
      user_id: "",
      refer_page_id: Date.now() + '_' + lodash.random(1000000000, 9999999999),
      'model': 'NX503A'
    }
  }
  return anyRequest(config)
}

/**
 * 获取批发商品的拼团ID
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export async function getPifaGroupId(
  data: {
    goods_id: string | number;
    sku_id: string | number;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  const createOrderItemList = [{
    goodsId: Number(data.goods_id),
    skuId: Number(data.sku_id),
    skuNum: 2
  }]
  let singleBuyData: any = void 0
  await Promise.allSettled([
    await retry(async (index) => {
      // console.log('获取商品信息' + index)
      await Promise.allSettled([
        acquireQrCode({
          "subScene": 1,
          "createOrderDTO": {
            "orderType": 1,
            createOrderItemList
          }
        })
          .then(res => {
            if (res.success && res.result) {
              singleBuyData = {
                result: {
                  curl: 'https://mobile.yangkeduo.com/transac_order_coupon.html?_t_timestamp=transac_volume_checkout&secret_key=' + res.result.secretKey,
                  qrKey: res.result.secretKey
                }
              }
            }
          })
        ,
        singleBuy({
          createOrderItemList
        })
          .then(res => {
            if (res.success && res.result && res.result.curl && res.result.qrKey) {
              singleBuyData = res
            }
          })
      ])
      if (singleBuyData) {
        return singleBuyData
      } else {
        return Promise.reject()
      }
    }, 20)
  ])
  // console.log('singleBuyData',singleBuyData,data)
  return request(
    {
      url: "/api/tool/getGroupId",
      data: cloneDeep({
        ...data,
        singleBuy: singleBuyData,
        anti_content: window.account_anti_content()
      }),
    },
    options,
    loading
  );
}

/**
 * 领取优惠券
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function getCoupon(
  data: {
    task_id: string | number;
    account: string | number;
    anti_content?: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  data.anti_content = window.get_anti_content();
  return request(
    {
      url: "/api/order/getCoupon",
      data,
    },
    options,
    loading
  );
}


/**
 * 兑换应用卡密
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function exchangeCami(
  data: {
    account: string;
    card: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/index/recharge",
      data,
    },
    options,
    loading
  );
}

/**
 * 卡密兑换小号
 * @param data
 * @param options
 * @param loading
 * @returns
 */
export function exchangeSubAccount(
  data: {
    card: string;
  },
  options?: Options,
  loading?: LoadingOptions
) {
  return request(
    {
      url: "/api/account/recharge",
      data,
      method: "post",
      timeout: 1000 * 90,
    },
    options,
    loading
  );
}

/**获取拼多多店铺信息 */
export function addPddStore(data: {
  store_name?: string;
}): Promise<false | { cookieObj: anyObj; userInfoJson: string,cookieArr:Electron.Cookie[] }> {
  return ipc.invoke(ipcRoute.addPddStore, data);
}

/**自动设供货 */
export function autoSupply(data: {
  mallId: number | string;
  goods_id: number;
}) {
  // const setting = useSetting();
  // const {pifaTargetPrice,othersTargetDiscount} = setting.taskCreate
  return new Promise<responseData>(async (resolve) => {
    const res = await ipc.invoke(ipcRoute.autoSupply, data)

    if (res.code !== 2) {
      return resolve(res)
    }
    const appStore = useAppStore()
    appStore.openPddVerify_outwin({
      verify_auth_token: res.data,
      cb: async (success) => {
        if (success) {
          const res2 = await ipc.invoke(ipcRoute.autoSupply, data)
          return resolve(res2)
        } else {
          resolve({
            ...res,
            msg: `${res.msg}【验证码未通过】`
          })
        }
      }
    })
  })
}
export async function autoSell(
  store: Store,
  data: { order_sn: string; password: number }
) {
  const { order_sn, password } = data
  const config = {
    url: "https://mms.pinduoduo.com/maryland/api/carolina/ota/verification",
    method: "post",
    data: {
      storeId: store.mallId,
      cardNo: password,
      storeName: store.mallName,
      orderSn: order_sn,
    },
    headers: await pddStoreRequestHeader(store, { Referer: "https://mms.pinduoduo.com/orders/order/verify" }),
  }
  const res = await anyRequest(config)
  return {
    code:res.success ? 0:1,
    data:res,
    msg:pddResErrorMsg(res)
  }
}

export function getCommonComment(): Promise<string> {
  return ipc.invoke(ipcRoute.getCommonComment);
}
