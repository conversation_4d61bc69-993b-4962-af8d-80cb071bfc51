<template>
    <div class="mall-taks">
        <el-form :inline="true">
            <el-form-item label="订单数量：">
                <el-input-number :min="1" :precision="0" :controls="false" v-model="taskCreate.num"></el-input-number>
            </el-form-item>
            <el-form-item label="每次购买：">
                <el-input-number :min="order_goods_count_min" :precision="0" :controls="false"
                    v-model="taskCreate.order_goods_count"></el-input-number>
            </el-form-item>
            <el-form-item label="小号开始位置：">
                <el-input-number v-model="start_site" :controls="false" :min="0" :precision="0"
                    style="width: 80px;"></el-input-number>
            </el-form-item>
            <!-- <br /> -->

            <br />
            <el-form-item label="操作店铺：">
                <el-space>
                    <el-select v-model="searchState.mallId" :disabled="loading.exec">
                        <el-option v-for="store in mallStore.tableList" :label="store.mallName" :value="store.mallId">
                        </el-option>
                    </el-select>
                </el-space>
            </el-form-item>
            <el-form-item label="下单类型：">
                <el-select style="width: 170px;" v-model="taskCreate.type" @change="orderTypeChange">
                    <el-option label="多多果园" value="guoyuan"></el-option>
                    <el-option label="多多批发" value="pifa"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="sku">
                <el-select v-model="taskCreate.skuType">
                    <el-option label="按选择" value="1"></el-option>
                    <el-option label="按最低价" value="2"></el-option>
                    <el-option label="最低价随机" value="3"></el-option>
                    <el-option label="按最高价" value="4"></el-option>
                    <el-option label="最高价随机" value="5"></el-option>
                    <el-option label="随机" value="6"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="其他设置">

                <el-checkbox-group v-model="taskCreate.otherSetting">
                    <el-checkbox label="follow">店铺关注</el-checkbox>
                    <el-checkbox label="collect">商品收藏</el-checkbox>
                    <!-- <el-checkbox label="changePrice">自动改价</el-checkbox> -->
                    <el-checkbox label="closeInsurance">自动关闭运费险</el-checkbox>
                </el-checkbox-group>


            </el-form-item>
            <el-form-item label="">
                <el-space>
                    <el-checkbox @change="setAutoChangePrice" v-model="taskCreate.changePrice">自动改价</el-checkbox>
                    <el-button @click="changePriceState.configShow = true">设置自动改价</el-button>
                </el-space>
            </el-form-item>

            <el-form-item>
                <span class="color-danger">请确保商品sku有足够库存</span>
            </el-form-item>
        </el-form>
        <div class="table-header between-flex">
            <div class="center-flex">
                <el-button @click="tableAction('check-all')">全选</el-button>
                <el-button @click="tableAction('reverse')">反选</el-button>
                <el-button @click="tableAction('area-select')">区域选择</el-button>
                <el-button @click="tableAction('clear')">取消选择</el-button>
                <el-button
                    @click="searchState.is_show_textarea = true; searchState.goods_id_textarea = searchState.goods_id">多ID筛选</el-button>
                <el-input v-model="searchState.goods_id" placeholder="商品ID搜索,逗号隔开"
                    style="margin-left: 12px;margin-right: 12px;width: 110px;"></el-input>

            </div>
            <div class="center-flex">
                <el-button type="danger" plain :loading="loading.delPifa" @click="delPifa()"
                    title="删除供货">删除批发</el-button>
                <el-button type="danger" plain :loading="loading.addStock" @click="addStock()"
                    title="添加商品库存">添加库存</el-button>
                <el-button type="primary" @click="getList()" :loading="tableProps.loading">获取商品</el-button>
                <el-button type="success" @click="exec()" :loading="loading.exec">开始任务</el-button>
                <el-button type="warning" @click="stopClick">停止任务</el-button>
            </div>
        </div>
        <vxe-table v-bind="{ ...tableProps, ...tableEvents }" ref="tableRef">
            <VxeColumn type='checkbox' :width="50"></VxeColumn>
            <VxeColumn type='seq' title="序号" :width="50"></VxeColumn>
            <VxeColumn title="商品ID" field="id" :width="130"></VxeColumn>
            <VxeColumn title="商品名称" field="goods_name" :width="150"></VxeColumn>
            <VxeColumn title="SKU" :width="150">
                <template #default='scope'>
                    <el-select v-model="scope.row._skuSelect" @click="scope.row._sku_select_list = scope.row.sku_list">
                        <el-option v-for="item in scope.row._sku_select_list" :label="item.spec || scope.row.goods_name"
                            :value="item.skuId"></el-option>
                    </el-select>
                </template>
            </VxeColumn>
            <VxeColumn title="进度" field="progress" :min-width="380" fixed='right'>

                <template #default="scope">
                    <el-space v-if="progressState.map.has(Number(scope.row.id))">
                        <span style="color:var(--el-color-success) ">已完成：{{
                            progressState.map.get(Number(scope.row.id))?.current
                            }}</span>
                        <span style="color:var(--el-color-primary) ">下单量：{{
                            progressState.map.get(Number(scope.row.id))?.target }}</span>
                        <span style="color:var(--el-color-warning) ">进行中：{{
                            progressState.map.get(Number(scope.row.id))?.requestCount
                            }}</span>
                    </el-space>
                </template>
            </VxeColumn>
            <VxeColumn title="提示" :field="rowKey.notice" :min-width="150" fixed='right' show-overflow='title'>
            </VxeColumn>
            <VxeColumn title="SKU拼团价格" :width="150">

                <template #default='scope'>
                    {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.groupPrice /
                        100 }}
                </template>
            </VxeColumn>

            <VxeColumn title="商品销量" field="sold_quantity" :width="80" sortable></VxeColumn>
        </vxe-table>
        <paginationVue v-bind="pagination" v-model:page-size="pagination.limit" v-model:current-page="pagination.page"
            :selection="tableState.selection" @current-change="getList()" />
        <Log v-model:list="logList">
        </Log>
        <!-- 批量输入商品id 逗号换行隔开 -->
        <el-dialog v-model="searchState.is_show_textarea" title="批量输入商品ID">
            <el-input type="textarea" v-model="searchState.goods_id_textarea" placeholder="用逗号,换行隔开" :rows="10"
                resize="none"></el-input>

            <template #footer>
                <el-button @click="searchState.is_show_textarea = false">取消</el-button>
                <el-button type="primary"
                    @click="searchState.is_show_textarea = false; searchState.goods_id = resolveInputStr(searchState.goods_id_textarea).join(',')">确定</el-button>
            </template>
        </el-dialog>
        <!-- 自动改价设置 -->
        <el-dialog v-model="changePriceState.configShow" :width="480" title="自动改价设置">
            <el-alert type="warning" :closable="false">
                <p>批发下单可随意修改价格,填入的值为最终订单价格</p>
                <p>其他下单只能按折扣修改，且最低为1折,填入的值为订单折扣</p>
                <p>系统默认值为1折和0.01</p>
            </el-alert>
            <el-form label-position="top">
                <el-form-item label="批发下单">
                    <el-space>
                        <el-input-number v-model="changePriceState.price" :min="0.01" :precision="2"
                            :controls="false"></el-input-number> 元
                    </el-space>
                </el-form-item>
                <el-form-item label="其他">
                    <el-space>
                        <el-input-number v-model="changePriceState.discount" :min="1" :precision="0"
                            :controls="false"></el-input-number> 折
                    </el-space>
                </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="changePriceState.configShow = false">取消</el-button>
                <el-button type="primary" @click="setChangePriceConfig">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang='ts' setup>
import {
    singleBuy,
    storeAntiContent,
} from "/@/apis/page";
import type { ProgressItem } from '.'
import { computed, reactive, ref, } from 'vue';
import { usePersistenceRef } from '/@/hooks/ref';
import { useSetting } from '/@/stores/setting'
import { batchRequest, dateFormat, pageStepRequest, pddResErrorMsg, randomInt, resolveInputStr, retry } from '/@/utils/common';
import { VxeTableInstance } from 'vxe-table';
import { useTable } from '/@/hooks/useTable';
import { delSupply, goodsSupply, storeGoodsList, batchEditGoods, getGroupId } from '/@/apis/pddStore';
import { CheckboxValueType, ElMessage, ElMessageBox } from 'element-plus';
// import { getGroupId } from '/@/apis/goods';
import paginationVue from '/@/components/pagination/pagination.vue';

import { useTask } from "/@/stores/task";
import { useMallStore } from "/@/stores/store";
import { useSubAccount } from "/@/stores/pageData";
import { getPifaGroupId } from "/@/apis/page";
import { getRandomItem } from "/@/apis/address";
import { closeStoreInsurance } from "/@/apis/store";
import { acquireQrCode } from "/@/apis/pifa";
import { quickOrder_mallTask as createOrderApi } from "/@/apis/changeSales";
import { isUserCanUse } from "../../utils";

const MAX_REQUEST_COUNT = 30

const start_site = usePersistenceRef(1, 'start_site')
const rowKey = {
    notice: '_notice'
}
const setting = useSetting()
const taskCreate = usePersistenceRef({
    num: 1,
    type: 'pifa',
    order_goods_count: 2,
    skuType: '6',
    changePrice: false,
    otherSetting: ['closeInsurance']
}, 'mallTask-taskCreate')
const searchState = reactive({
    goods_id: '',
    goods_id_textarea: '',
    /**批量输入是否显示 */
    is_show_textarea: false,
    mallId: '' as '' | number,

})
const loading = ref({
    exec: false,
    addStock: false,
    delPifa: false
})

const order_goods_count_min = computed(() => {
    const type = taskCreate.value.type
    if (type === 'pifa') {
        return 2
    }
    return 1
})
const logList = ref<LogItem[]>([])
function addtoLog(data: Partial<LogItem>) {
    logList.value.push({
        time: dateFormat(Date.now()),
        msg: "",
        ...data,
    });
}
function orderTypeChange(e: typeof taskCreate.value.type) {
    if (e === 'pifa') {
        if (taskCreate.value.order_goods_count < 2) {
            taskCreate.value.order_goods_count = 2
        }
    } else {
        if (taskCreate.value.order_goods_count === 2) {
            taskCreate.value.order_goods_count = 1
        }
    }
}
const tableRef = ref<VxeTableInstance>()
const tableInstance = useTable(380, { id: 'mallTask-table' }, { tableRef, pagination: { limit: 1000, pageSizes: [100, 1000, 2000] }, clearWhenDataChange: false })
const { tableProps, tableEvents, pagination, tableState, tableAction } = tableInstance
const mallStore = useMallStore()
async function getList() {
    const mall = mallStore.tableList.find(item => item.mallId == searchState.mallId)
    if (!mall) {
        ElMessage.warning('请先选择店铺')
        return
    }
    if (!mallStore.checkAvailable(mall)) {
        ElMessage.warning('店铺不可用')
        return
    }
    const { goods_id } = searchState
    const { limit, page } = pagination
    const times = Math.round(limit / 100)
    tableProps.loading = true
    if (goods_id) {
        pagination.page = 1
    }
    const allList = await pageStepRequest({
        delay: 1000,
        pageStart: (page - 1) * times + 1,
        pageEnd: (page) * times,
        tableInstance,
        isStop: () => !tableProps.loading,
        request: async (page) => {
            const { data: res } = await storeGoodsList({ goods_id_list: goods_id.split(',').filter(item => item), page }, mall!)
            if (!res.success) {
                return Promise.reject()
            }
            const goodsList: anyObj[] = res.result.goods_list
            goodsList.forEach(item => {
                // isOnsale
                item.sku_list = (item.sku_list as anyObj[]).filter(item => item.isOnsale)
                if (!item.sku_list.length) {
                    return
                }
                const seleteItem = (item.sku_list as anyObj[]).sort((a, b) => a.groupPrice - b.groupPrice)[0]
                item._skuSelect = seleteItem.skuId
                item._sku_select_list = [seleteItem]
                // state.tableList.push(item)
                // list.push(item)
            });
            const _total = res.result.total
            pagination.total = _total
            return { list: goodsList, total: _total }
        }
    })
    tableProps.data = allList
    tableProps.loading = false
}
function getSku(row: anyObj) {
    const { skuType } = taskCreate.value
    let skuId = 0
    let sku_list = [...row.sku_list] as anyObj[]
    const minPrice = sku_list[0].groupPrice
    const maxPrice = sku_list[sku_list.length - 1].groupPrice
    if (skuType == '1') {
        skuId = row._skuSelect
    } else if (skuType == '2') {
        skuId = sku_list[0]?.skuId
    } else if (skuType == '3') {
        const filterSkus = sku_list.filter(item => item.groupPrice <= minPrice)
        skuId = filterSkus[randomInt(0, filterSkus.length)]?.skuId
    }
    else if (skuType == '4') {
        skuId = sku_list[sku_list.length - 1]?.skuId
    } else if (skuType == '5') {
        const filterSkus = sku_list.filter(item => item.groupPrice >= maxPrice)
        skuId = filterSkus[randomInt(0, filterSkus.length)]?.skuId
    } else {
        skuId = sku_list[randomInt(0, sku_list.length)]?.skuId
    }
    if (!skuId) {
        skuId = row._skuSelect || sku_list[0]?.skuId
    }
    return {
        sku_id: skuId,
        sku_spec: sku_list.find(item => item.skuId == skuId)?.spec || ''
    }
    // return skuId
}

async function checkCreate() {
    const { map } = progressState.value
    const list = [...map.values()]
    if (!loading.value.exec) {
        list.forEach(item => {
            if (item.current < item.target) {
                item.row[rowKey.notice] = '停止'
            }
        })
        return
    }

    const isFinished = !list.some(item => !['finish', 'failed'].includes(item.status))
    if (isFinished) {
        addtoLog({ type: 'success', msg: '检测到所有任务已完成' })
        loading.value.exec = false
        return
    }
    let allRequestCount = list.reduce((total, item) => total + item.requestCount, 0)
    /**剩余可请求次数 */
    let left = MAX_REQUEST_COUNT - allRequestCount
    list.some(requestItem => {
        const { target, current, requestCount, status, groupId } = requestItem
        if (!groupId || status !== 'loading') {
            return false
        }
        let count = target - current - requestCount
        const _requestCount = Math.min(count, left)
        // createOrder(requestItem)
        if (_requestCount > 0) {
            left -= _requestCount
            createOrder(requestItem, _requestCount)
        }
        return left <= 0
    })
}
function createOrder(requestItem: ProgressItem, count: number) {
    if (requestItem.taskId !== progressState.value.currentTaskId) {
        return
    }
    if (!requestItem.groupId) {
        progressState.value.map.delete(requestItem.goodsId)
        return
    }
    const mall = mallStore.tableList.find(item => item.mallId == searchState.mallId)
    let isNoticeFailed = false
    requestItem.requestCount += count
    const { goodsId, row, groupId } = requestItem
    row[rowKey.notice] = '开始下单'
    const addressType = setting.address.type;
    const { order_goods_count, type, otherSetting } = taskCreate.value
    for (let i = 0; i < count; i++) {
        const fn = async () => {
            let data: any = {
                goods_id: goodsId,
                // sku_id: ,
                ...getSku(row),
                spell_num: order_goods_count,
                mode: 'open_group',
                type,
                group_id: groupId,
                shop_id: mall?.mallId || searchState.mallId,
                goods_name: row.goods_name,
                shop_name: mall?.mallName || ''
            }
            if (type === 'pifa') {
                if (data.spell_num! < 2) {
                    addtoLog({
                        msg: `批发下单每次购买不能小于2,已自动调整为${data.spell_num = 2}`,
                        type: "warning",
                    });
                }
            }
            if (addressType === 'diy') {
                const addressItem = await getRandomItem();
                data = { ...data, ...addressItem }
            }

            const subAccount = useSubAccount();
            const res = subAccount.getAccount(start_site.value);
            if (!res.status) {
                !isNoticeFailed && addtoLog({
                    msg: `小号获取失败:${res.msg},将终止执行`,
                    type: "danger",
                });
                loading.value.exec = false
                isNoticeFailed = true
                progressState.value.map.delete(goodsId)
                row[rowKey.notice] = '小号获取失败' + res.msg
                return
            }
            start_site.value = (start_site.value + 1) % subAccount.list.length
            data.account = res.data
            addtoLog({
                msg: "已分配小号" + res.data,
            });

            createOrderApi(extraCreateOrderData(data), {
                showErrorMsg: false,
                isStop: () => !loading.value.exec,
                async beforeRequest(config) {
                    config.data.anti_content = window.get_anti_content();
                    config.data.store_anti_content = await storeAntiContent()
                    if (type === 'pifa') {
                        config.data.singleBuy = void 0
                        await new Promise(async resolve => {
                            try {
                                await retry(async () => {
                                    const createOrderItemList = [{
                                        goodsId: data.goods_id,
                                        skuId: data.sku_id,
                                        skuNum: 2
                                    }]
                                    let singleBuyData: any = void 0
                                    await Promise.allSettled([
                                        acquireQrCode({
                                            "subScene": 1,
                                            "createOrderDTO": {
                                                "orderType": 1,
                                                createOrderItemList
                                            }
                                        })
                                            .then(res => {
                                                if (res.success && res.result) {
                                                    singleBuyData = {
                                                        result: {
                                                            curl: 'https://mobile.yangkeduo.com/transac_order_coupon.html?_t_timestamp=transac_volume_checkout&secret_key=' + res.result.secretKey,
                                                            qrKey: res.result.secretKey
                                                        }
                                                    }
                                                }
                                            })
                                        ,
                                        singleBuy({
                                            createOrderItemList
                                        })
                                            .then(res => {
                                                if (res.success && res.result && res.result.curl && res.result.qrKey) {
                                                    singleBuyData = res
                                                }
                                            })
                                    ])
                                    if (singleBuyData) {
                                        config.data.singleBuy = singleBuyData
                                    } else {
                                        return Promise.reject()
                                    }
                                }, 50)
                                resolve(true)
                            } catch (e) {
                                console.log(e)
                                resolve(true)
                            }
                            resolve(true)
                        })
                    }
                    console.log(config, '111111111111111')
                }
            })
                .then(res => {
                    if (res.code) {
                        return Promise.reject()
                    }
                    requestItem.fail_consecutive = 0
                    requestItem.current++
                    const { order_sn } = res.data;
                    addtoLog({
                        msg: "下单成功!订单号:" + order_sn,
                        type: "success",
                    })
                    const taksStore = useTask()
                    if (taskCreate.value.changePrice) {
                        taksStore.changeOrderPrice(res.data, addtoLog);
                    } else {
                        taksStore.execAutoApply(res.data.order_sn, addtoLog)
                    }
                })
                .catch((res) => {
                    addtoLog({
                        msg: `下单失败:${res.msg}`,
                        type: "danger",
                    });
                    // subAccount.recoverCount(data.account!);
                })
                .finally(() => {
                    requestItem.fail_consecutive++
                    requestItem.failCount++
                    requestItem.requestCount--
                    if (requestItem.current >= requestItem.target) {
                        row[rowKey.notice] = '完成目标'
                        requestItem.status = 'finish'
                    }
                    if (requestItem.fail_consecutive >= 5) {
                        requestItem.status = 'failed'
                        row[rowKey.notice] = '该任务连续失败超10次'
                    }
                    checkCreate()
                })
        }
        fn()
    }
}

function extraCreateOrderData(data: any) {
    const settingStore = useSetting();
    const {
        nameCode_active,
        nameCode_position,
        nameCode_str,
        addr_active,
        addr_position,
        addr_str,
        filterStr,
        type,
        appoint_address,
    } = settingStore.address;
    const result = {
        filter_address: filterStr.replace("，", ","),
        address_cipher: addr_active ? addr_str : void 0,
        address_site: addr_active ? addr_position : void 0,
        name_cipher: nameCode_active ? nameCode_str : void 0,
        name_site: nameCode_active ? nameCode_position : void 0,
        ...data,
    }
    if (type === 'random') {
        result.appoint_address = appoint_address || void 0
    }
    return result
}

const progressState = ref<{
    currentTaskId: string
    map: Map<ProgressItem['goodsId'], ProgressItem>

}>({
    map: new Map(),
    currentTaskId: ''
})

async function checkStop() {
    if (!loading.value.exec) {
        addtoLog({ msg: '检测到停止任务', type: 'warning' })
        return Promise.reject()
    }
}

async function exec() {
    if (!isUserCanUse()) {
        return
    }
    const rows = [...tableState.selection]
    if (!rows.length) {
        addtoLog({ msg: '请先选择商品', type: 'warning' })
        return
    }
    const mall = mallStore.tableList.find(item => item.mallId == searchState.mallId)
    if (!mall) {
        addtoLog({ msg: '请先选择店铺', type: 'warning' })
        return
    }
    loading.value.exec = true
    await checkStop()
    const currentTaskId = Date.now() + ''
    progressState.value.currentTaskId = currentTaskId
    if (taskCreate.value.otherSetting.includes('closeInsurance')) {
        try {
            // await mallStore.insuranceAction('close', mall, false)
            closeStoreInsurance(mall, { showErrorMsg: false })
        } catch (e) {
        }
    }
    await checkStop()
    const { type } = setting.address
    if (type === 'diy') {
        const addressItem = await getRandomItem();
        if (!addressItem) {
            addtoLog({ msg: '检测到使用本机地址下单，但没有检测到至少一个地址，请先添加地址', type: 'warning' })
            loading.value.exec = false
            return
        }
    }
    progressState.value.map.clear()
    rows.forEach(row => {
        row[rowKey.notice] = '开始排队'
        progressState.value.map.set(row.id, {
            goodsId: row.id,
            row,
            target: taskCreate.value.num,
            current: 0,
            requestCount: 0,
            taskId: currentTaskId,
            status: 'pending',
            fail_consecutive: 0,
            failCount: 0
        })
    })
    addtoLog({ msg: '正在重置小号信息' })
    await useSubAccount().getList()
    await checkStop()
    await batchRequest(rows, {
        delay: 1000,
        batch: 20,
        isStop: () => !loading.value.exec,
        request: async (reqRows) => {
            const rowMap = new Map(reqRows.map(item => [item.id, item]))
            addtoLog({ msg: '开始设置供货' })
            const res = await goodsSupply(reqRows.map(item => item.id), mall)
            if (res.code) {
                addtoLog({ msg: '供货失败：' + res.msg, type: 'danger' })
                loading.value.exec = false
                return
            }
            await checkStop()
            const { failed, reason, success } = res
            failed.forEach(id => {
                const row = rowMap.get(id)
                progressState.value.map.delete(id)
                if (!row) {
                    return
                }
                row[rowKey.notice] = '供货失败'
                addtoLog({ msg: `${id}-供货失败：${reason[id]}`, type: 'danger', })
            })
            if (success.size) {
                addtoLog({ msg: '开始获取拼团ID' })
                await batchRequest([...success], {
                    batch: 5,
                    isStop: () => !loading.value.exec,
                    request: async (ids) => {
                        await Promise.allSettled(ids.map(async id => {
                            const row = rowMap.get(id)
                            if (!row) {
                                return
                            }
                            await new Promise(async resovle => {
                                getGroupId(
                                    { goods_id: id, sku_id: row.sku_list.filter((item: any) => item.skuQuantity).map((item: any) => item.skuId) },
                                    { showErrorMsg: false, isStop: () => !loading.value.exec })
                                    .then(async res => {
                                        if (!loading.value.exec) {
                                            row[rowKey.notice] = '检测到停止任务'
                                            addtoLog({ msg: '检测到停止任务', type: 'primary' })
                                            return
                                        }
                                        if (res.data) {
                                            addtoLog({ msg: `${id},成功获取到拼团ID,${res.data}`, type: 'success' })
                                            row[rowKey.notice] = '成功获取到拼团ID'
                                            const requestItem = progressState.value.map.get(id)
                                            if (requestItem) {
                                                requestItem.groupId = res.data
                                                requestItem.status = 'loading'
                                            }

                                        }
                                    })
                                    .catch(res => {
                                        progressState.value.map.delete(id)
                                        addtoLog({ msg: `${id}-获取拼团ID失败:${res.msg}`, type: 'danger' })
                                        row[rowKey.notice] = '获取拼团ID失败'
                                    })
                                    .finally(() => resovle(true))
                            })
                            checkCreate()
                        }))
                    },
                    onStop: (remaingList) => {
                        remaingList.forEach(id => {
                            const row = rowMap.get(id)
                            if (!row) {
                                return
                            }
                            row[rowKey.notice] = '检测到停止任务'
                        })
                    }
                })
            }
        }
    })
    // loading.value.exec = false
}

const changePriceState = reactive({
    configShow: false,
    price: 0.01,
    discount: 1
})
function setAutoChangePrice(e: any) {
    if (e) {
        if (taskCreate.value.otherSetting.includes('changePrice')) {
            changePriceState.configShow = true
            const { pifaTargetPrice: price, othersTargetDiscount: discount } = setting.taskCreate
            changePriceState.discount = discount || 1
            changePriceState.price = price || 0.01
        }
    }
}
function setChangePriceConfig() {
    const { discount, price } = changePriceState
    setting.taskCreate.othersTargetDiscount = discount
    setting.taskCreate.pifaTargetPrice = price
    changePriceState.configShow = false
}

async function stopClick() {
    tableProps.loading = false
    const loadingKeys = Object.keys(loading.value) as (keyof typeof loading.value)[]
    for (const key of loadingKeys) {
        loading.value[key] = false
    }
}

async function delPifa(list = tableState.selection) {
    if (!list.length) {
        addtoLog({ msg: '请先选择商品', type: 'warning' })
        return
    }
    const mall = mallStore.tableList.find(item => item.mallId == searchState.mallId)
    if (!mall) {
        addtoLog({ msg: '请先选择店铺', type: 'warning' })
        return
    }
    loading.value.delPifa = true
    await batchRequest(list, {
        batch: 1,
        isStop: () => !loading.value.delPifa,
        request: async (reqList) => {
            await Promise.allSettled(reqList.map(async item => {
                const res = await delSupply(mall, { goodsId: item.id })
                if (res.success) {
                    addtoLog({ msg: `删除批发成功`, type: 'success' })
                    item[rowKey.notice] = '删除批发成功'
                } else {
                    const msg = pddResErrorMsg(res)
                    addtoLog({ msg, type: 'danger' })
                    item[rowKey.notice] = msg
                }
            }))
        }
    })
    loading.value.delPifa = false
}

async function addStock(list = tableState.selection) {
    if (!list.length) {
        addtoLog({ msg: '请先选择商品', type: 'warning' })
        return
    }
    const mall = mallStore.tableList.find(item => item.mallId == searchState.mallId)
    if (!mall) {
        addtoLog({ msg: '请先选择店铺', type: 'warning' })
        return
    }
    const { value } = await ElMessageBox.prompt('请输入要增加的库存', '提示', {
        inputPattern: /^[0-9]+$/,
    })
    let num = Number(value)
    if (Number.isNaN(num)) {
        addtoLog({ msg: '非数字', type: 'warning' })
        return
    }
    num = Math.round(num)
    if (num <= 0) {
        addtoLog({ msg: '请输入大于0的数字', type: 'warning' })
        return
    }
    loading.value.addStock = true
    await batchRequest(list, {
        batch: 10,
        isStop: () => !loading.value.addStock,
        async request(reqList) {
            const reqData: Parameters<typeof batchEditGoods>[1] = {
                edit_list: []
            }
            reqList.forEach(row => {
                const { id, sku_list = [] } = row
                sku_list.forEach((skuItem: anyObj) => {
                    reqData.edit_list.push({
                        goods_id: id,
                        sku_id: skuItem.skuId,
                        quantity_delta: num
                    })
                })
            })
            const res = await batchEditGoods(mall, reqData)
            if (res.success) {
                addtoLog({ msg: '已提交修改', type: 'success' })
            } else {
                addtoLog({ msg: '修改失败' + pddResErrorMsg(res), type: 'danger' })
            }
        }
    })
    loading.value.addStock = false
}

</script>

<style lang='scss' rel="stylesheet/scss" scoped>
.table-header {
    border-top: var(--el-border);
    border-bottom: var(--el-border);
    padding: 12px 0;
}
</style>