<template>
  <div class="bulletin">
    <div class="box">
      <div class="left">
        <h3 class="title f-s-20">
          免责声明
        </h3>
        <p class="time f-s-12">时间：{{ dayjs().format('YYYY-MM-DD') }}</p>
        <div class="content">
          <ol>
            <li>本软件仅供学习使用，禁止用于非法途径。如软件使用者未能遵守此规定，产生任何不良后果，均由用户自行承担责任，软件作者不承担任何责任。</li>
            <li>本软件不会收集任何用户数据，严禁利用本软件侵犯他人隐私权。如软件使用者未能遵守此规定，使用本软件而造成自身或他人隐私泄露等任何不良后果，均由用户自行承担责任，软件作者不承担任何责任。</li>
            <li>
              本软件不含任何病毒、木马等破坏用户数据的恶意代码。对于从非软件作者指定点下载的本软件产品，软件作者无法判断该软件是否感染计算机病毒、是否隐藏有伪装的木马程序或者黑客软件。用户自行决定使用，软件作者不承担任何责任。
            </li>
            <li>由于用户计算机软硬件环境的差异性和复杂性，本软件所提供的各项功能并不能保证在任何情况下都能正常执行或达到用户所期望的结果。用户使用本软件所产生的一切后果，均由用户自行承担责任，软件作者不承担任何责任。
              用户自行下载运行本软件，即表明用户信任软件作者，并接受本协议所有条款。如果用户不接受本协议，请立即删除本软件。如有病毒、木马、侵权等行为，请联系作者删除。软件作者将尽快移除相关内容。</li>
          </ol>
          <p> 声明：本声明最终解释权归软件作者所有。</p>
        </div>
      </div>
      <!-- <div class="right"></div> -->
    </div>
  </div>
</template>
<script lang="ts" setup>

  import dayjs from 'dayjs';

</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
  ol {
    padding-left: 16px;

    // list-style: none;
    li {
      text-indent: initial;
    }
  }

  .box {
    display: flex;
    border: var(--el-border);
    border-radius: 6px;
    box-shadow: var(--diy-shadow);
    min-height: 472px;

    .left,
    .right {
      // width: 50%;
      min-height: 100%;
      box-sizing: border-box;
    }

    .left {
      padding: 23px 27px;
      background-color: var(--el-color-white);
      overflow: hidden;

      .time {
        margin: 9px 0;
        color: var(--el-text-color-placeholder);
      }

      .content {
        font-size: 14px;
        line-height: 24px;
        color: var(--el-text-color-regular);

        text-indent: 2em;
      }
    }

    .right {
      // background-color: lightblue;
      background-image: url('/src/assets/image/disclaimer.png');
    }
  }
</style>