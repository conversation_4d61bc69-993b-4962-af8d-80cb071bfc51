<template>
    <div class="cs-limit">
        <el-form label-position="top">
            <el-form-item label="店铺选择">
                <el-space>
                    <el-select v-model="state.mallId">
                        <el-option v-for="item in pddMall.tableList" :key="item.mallId" :label="item.mallName"
                            :value="item.mallId"></el-option>
                    </el-select>
                    <el-button type="primary" @click="pddMall.addPddStore()">登录店铺</el-button>
                </el-space>
            </el-form-item>
            <el-form-item :label="`商品ID(${appointGoodsIds.length}个)`">
                <el-input type="textarea" placeholder="多个用换行和逗号隔开" v-model="state.goodsIds" :rows="3" resize="none">
                </el-input>
            </el-form-item>
            <el-form-item label="修改销量">
                <el-space>
                    <el-radio-group v-model="state.num_type">
                        <el-radio-button label="appoint">固定</el-radio-button>
                        <el-radio-button label="random">随机</el-radio-button>
                    </el-radio-group>

                    <el-space v-if="state.num_type === 'appoint'">
                        <span>每单购买：</span>
                        <el-input-number v-model="state.num.appoint.num_per_order" :controls="false" :min="1"
                            :precision="0" key="appoint-1"></el-input-number>
                        <span>订单数量：</span>
                        <el-input-number v-model="state.num.appoint.order_num" :controls="false" :min="1" :precision="0"
                            key="appoint-2"></el-input-number>
                    </el-space>
                    <el-space v-else-if="state.num_type === 'random'">
                        <span>每单购买：</span>
                        <el-input-number v-model="state.num.random.num_per_order_1" :controls="false" :min="1"
                            :precision="0" key="random-1"></el-input-number>
                        <span>-</span>
                        <el-input-number v-model="state.num.random.num_per_order_2" :controls="false" :min="1"
                            :precision="0" key="random-2"></el-input-number>
                        <span>订单数量：</span>
                        <el-input-number v-model="state.num.random.order_num_1" :controls="false" :min="1"
                            :precision="0" key="random-3"></el-input-number>
                        <span>-</span>
                        <el-input-number v-model="state.num.random.order_num_2" :controls="false" :min="1"
                            :precision="0" key="random-4"></el-input-number>
                    </el-space>
                    <!-- <el-input-number v-model="state.appointNum" :controls="false" :min="1"
                        v-show="state.num_type === 'appoint'" :precision="0"></el-input-number>
                    <el-input-number v-model="state.randomValue" :controls="false" :min="1"
                        v-show="state.num_type === 'random'" :precision="0"></el-input-number>
                    <span>{{ state.num_type === 'appoint' ? '销量' : '位数' }}</span> -->
                </el-space>
            </el-form-item>
            <el-form-item label="其他">
                <el-space>
                    <span>小号位置：</span>
                    <el-input-number :min="1" :precision="0" v-model="start_site"></el-input-number>
                    <el-checkbox v-model="state.useLine2" label="果园下单"></el-checkbox>
                </el-space>
            </el-form-item>

        </el-form>
        <p>
            <el-button type="danger" :disabled="!loading.create" @click="loading.create = false">停止</el-button>
            <el-button type="primary" :loading="loading.create" @click="create">执行任务</el-button>
        </p>
        <LogVue v-model:list="state.logList" height="360px" />
    </div>


</template>
<script lang='ts' setup>
import { Ref, computed, inject, reactive, ref } from 'vue';
import { useMallStore } from '/@/stores/store';
import { ElMessage } from 'element-plus';
import LogVue from '/@/components/Log/log.vue'
import { batchRequest, delayPromise, pddResErrorMsg, random, retry } from '/@/utils/common';
import { addAddress, closeMerchantCoupone, createLimitCouponApi, getCouponList, limitCouponGoodsList, limitCouponGoodsSkuList, marketingList, marketingStop, quickOrder } from '/@/apis/changeSales';
import dayjs from 'dayjs'
import { autoSupply, getApplyInfo, getPifaGroupId, changeOrderPrice as changeOrderPriceRequest, updateOrder, } from '/@/apis/page';
import { useSetting } from '/@/stores/setting';
import { getRandomItem } from '/@/apis/address';
import { useSubAccount } from '/@/stores/pageData';
import { useAutoApply } from '/@/stores/autoApply';
import { confirmLowPrice } from '/@/apis/changeSales';
import console from 'console';
import { usePersistenceRef } from '/@/hooks/ref';
type RequestData = {
    mall: Store
    goodsId: number,
    skuList: anyObj[],
    // num: number,
    /**订单量 长度 为下单数*/
    num: Array<{ num_per_order: number, account?: string }>
    // account: Array<string | number>,
    goodsName: string
    activity_detail_id?: number
}
const loading = ref({
    create: false
})
const start_site = usePersistenceRef(1, 'start_site')
const state = reactive({
    mallId: '',
    num_type: 'appoint' as 'appoint' | 'random',
    num: {
        appoint: {
            order_num: 1,
            num_per_order: 1
        },
        random: {
            order_num_1: 1,
            order_num_2: 1,
            num_per_order_1: 1,
            num_per_order_2: 1
        }
    },
    logList: [] as LogItem[],
    goodsIds: '',
    useLine2: false
})
function addToLog(data: Partial<LogItem>) {
    state.logList.push({
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        msg: '',
        ...data
    })
}
const appointGoodsIds = computed(() => {
    const { goodsIds } = state
    const list = goodsIds.split(/[,，\n]/)
    const result = new Set<number>()
    list.forEach(item => {
        if (item.length < 6) {
            return
        }
        const num = Number(item.trim())
        if (num && Number.isInteger(num)) {
            result.add(num)
        }
    })
    return [...result]
})
const pddMall = useMallStore()
async function create() {
    const mall = pddMall.tableList.find(item => item.mallId == Number(state.mallId))
    if (!mall) {
        return ElMessage.warning({ message: '请选择店铺', grouping: true })
    }
    if (!pddMall.checkAvailable(mall)) {
        return ElMessage.warning({ message: '当前店铺不可用', grouping: true })
    }
    const goodsIds = [...appointGoodsIds.value]
    if (!goodsIds.length) {
        return ElMessage.warning({ message: '请输入商品ID', grouping: true })
    }
    // if (state.num_type == 'random' && state.randomValue >= 5) {
    //     await ElMessageBox({
    //         title: '提示',
    //         message: '检测到当前为随机模式，且随机位数大于5(十万级以上销量),是否继续?',
    //         type: 'warning',
    //         showCancelButton: true
    //     })
    // }
    state.logList = []
    loading.value.create = true
    await batchRequest(goodsIds, {
        batch: 1,
        isStop: () => !loading.value.create,
        request: async (list) => {
            await Promise.allSettled(list.map(async (goodsId) => {
                const num = resolveBuyCount()
                const requestData: RequestData = {
                    mall,
                    goodsId,
                    skuList: [],
                    num,
                    goodsName: ''
                }
                await resolveAccount(requestData)
                await checkAndClose(requestData)
                await createActivity(requestData)
                await checkAndClose(requestData, false)
                await Promise.allSettled([createOrder(requestData)])
                checkAndClose(requestData)
            }))
        }
    })
    loading.value.create = false
    addToLog({ msg: '执行结束' })
}

async function checkAndClose(reqData: RequestData, isClose = true) {
    const { goodsId, mall } = reqData
    if (isClose) {
        addToLog({ msg: `${goodsId}开始检测商品立减券` })
        {
            const listRes = await getCouponList(mall, { goods_list: [String(goodsId || '')], batch_status: 1 })
            if (!listRes.success) {
                addToLog({ msg: `${goodsId}获取获取商品立减券列表失败:${pddResErrorMsg(listRes)}`, type: 'danger' })
                return Promise.reject()
            }
            // ************
            // console.log(listRes, 'listRes')
            const list = listRes.result.data_list as anyObj[]
            // console.log(list, 'list')
            await Promise.allSettled(list.map(async item => {
                const coupon_batch_id = item.batch_id
                if (coupon_batch_id) {
                    const closeRes = await closeMerchantCoupone(mall, { batch_id: coupon_batch_id })
                    if (closeRes.success) {
                        addToLog({
                            msg: '成功关闭优惠券领取',
                            type: 'success'
                        })
                    } else {
                        addToLog({
                            msg: `关闭优惠券领取失败${pddResErrorMsg(closeRes)}`,
                            type: 'danger'
                        })
                    }
                }
            }))


        }
        addToLog({ msg: `${goodsId}开始并关闭检测活动` })
        const res = await marketingList(mall, { goods_id: goodsId, activity_types: [12] })
        if (!res.success) {
            addToLog({ msg: `${goodsId}获取获取列表失败:${pddResErrorMsg(res)}`, type: 'danger' })
            return Promise.reject()
        }
        const { marketing_activity_list } = res.result
        const item = marketing_activity_list?.[0]
        if (item) {
            const { activity_id, activity_name } = item
            addToLog({ msg: `${goodsId}检测到活动${activity_name}` })
            const res = await marketingStop(mall, { goods_id: goodsId, activity_id })
            if (!res) {
                addToLog({ msg: `${goodsId}关闭活动失败:${pddResErrorMsg(res)}`, type: 'danger' })
                return Promise.reject()
            } else {
                addToLog({ msg: `${goodsId}关闭活动成功` })
            }
        }
    } else {
        return retry(async (index) => {
            addToLog({ msg: `${goodsId}检测活动第${index}次` })
            const res = await marketingList(mall, { goods_id: goodsId, activity_types: [12] })
            if (!res.success) {
                return Promise.reject(res)
            }
            const item = res.result.marketing_activity_list?.[0]
            // const activity_id = res.result.marketing_activity_list?.[0]?.activity_id
            if (item) {
                const activity_detail_id = item.price_info?.[0]?.activity_detail_id
                if (activity_detail_id) {
                    addToLog({ msg: `${goodsId}检测到活动ID${activity_detail_id}` })
                    reqData.activity_detail_id = activity_detail_id
                }
            } else {
                return Promise.reject()
            }
        }, 10, 2000)
            .catch(res => {
                addToLog({ msg: `${goodsId}检测活动失败,请前往后台关闭限时活动`, type: 'danger' })

                return Promise.reject(res)
            })
    }

}
async function createActivity(reqData: RequestData) {
    const { goodsId, mall } = reqData
    {
        const res = await limitCouponGoodsList(mall, { goods_id_list: [goodsId], query_activity_type: 12 })
        if (!res.success) {
            addToLog({ msg: `${goodsId}获取商品列表失败:${pddResErrorMsg(res)}`, type: 'danger' })
            return Promise.reject()
        }
        reqData.goodsName = res.result.goods_list?.[0]?.goods_name
    }
    {
        const res = await limitCouponGoodsSkuList(mall, { search_marketing_tool_sku_request_list: [{ goods_id: goodsId }] })
        if (!res.success) {
            addToLog({ msg: `${goodsId}获取商品sku列表失败:${pddResErrorMsg(res)}`, type: 'danger' })
            return Promise.reject()
        }
        const { search_marketing_tool_sku_result_list } = res.result
        const item = (search_marketing_tool_sku_result_list as anyObj[]).find(item => item.goods_id == goodsId)
        if (!item) {
            addToLog({ msg: `${goodsId}获取商品sku列表失败:未找到该商品`, type: 'danger' })
            return Promise.reject()
        }
        // goods_name
        const skuList = item.valid_sku_volist as anyObj[]
        if (!skuList.length) {
            addToLog({ msg: `${goodsId}获取商品sku列表失败:该商品无可参加活动sku`, type: 'danger' })
            return Promise.reject()
        }

        return new Promise((resolve, reject) => {
            let isCheckLowPrice = false
            const fn = async (check_low_price = true) => {
                const createRes = await createLimitCouponApi(mall, {
                    batch_stage_activities: [{
                        stage_activities: [{
                            activity_name: '限量' + dayjs().format('YYYY-MM-DD'),
                            activity_type: 12,
                            auto_create_after_finished: false,
                            tool_full_channel: "10921_77271__normal",
                            goods_id: goodsId,
                            pattern: 11,
                            check_low_price,
                            goods_unified_activity: true,
                            start_time: dayjs().startOf('day').unix(),
                            end_time: dayjs().add(1, 'day').endOf('day').unix(),
                            price_list: [{
                                user_activity_limit: 0,
                                quantity: (function () {
                                    const num = skuList.reduce((acc, cur) => acc + cur.quantity, 0)
                                    return num > 100000 ? 100000 : num
                                })(),
                                sku_price_dtos: skuList.map(skuItem => {
                                    let groupPrice = skuItem.sku_group_price as number
                                    groupPrice = groupPrice * 0.1
                                    return {
                                        sku_id: skuItem.sku_id,
                                        activity_price: groupPrice
                                    }
                                })
                            }]
                        }]
                    }]
                })
                if (!createRes.success) {
                    addToLog({ msg: `${goodsId}创建活动失败:${pddResErrorMsg(createRes)}`, type: 'danger' })
                    return reject()
                }
                const { success_goods_id_list, fail_list } = createRes.result;
                const failReson = fail_list.find((item: any) => item.goods_id == goodsId)
                if (failReson) {
                    // token_list
                    if (failReson.fail_reason.includes('低价校验失败')) {
                        if (!isCheckLowPrice) {
                            const tokenList = failReson.low_price_promotion_info_volist.map((item: any) => item.token)
                            await confirmLowPrice(mall, { token_list: tokenList })

                            isCheckLowPrice = true
                            fn(false)
                            return
                        }
                    }
                    addToLog({ msg: `${goodsId}创建活动失败:${failReson.fail_reason}`, type: 'danger' })
                    return reject()
                } else {
                    console.log(createRes)
                    addToLog({ msg: `提交${goodsId}创建活动成功` })
                    reqData.skuList = skuList
                    resolve(true)
                }
            }
            try {
                fn()
            } catch (e) {
                reject()
            }
        })
    }
}
async function createOrder(reqData: RequestData) {
    await delayPromise(10 * 1000)
    const { mall, skuList, goodsId, goodsName } = reqData
    const supplyRes = await autoSupply({ mallId: mall.mallId, goods_id: goodsId })
    if (supplyRes.code) {
        addToLog({ msg: `${goodsId}供货失败:${supplyRes.msg}`, type: 'danger' })
        // return Promise.reject()
    }
    let groupId: number = 0
    await retry(async (index) => {
        addToLog({
            msg: `正在获取拼团ID第${index}次`,
        })
        const skuIds: number[] = reqData.skuList.map(item => item.sku_id)
        return getPifaGroupId({ goods_id: goodsId, sku_id: skuIds[random(0, skuIds.length)] || skuIds[0] }, { showErrorMsg: false })
    }, 5)
        .then(res => {
            groupId = res.data
            if (groupId) {
                addToLog({ msg: `${goodsId}获取拼团ID成功${groupId}` })
            } else {
                return Promise.reject({ msg: '没有返回拼团ID' })
            }
        })
        .catch(res => {

            addToLog({
                msg: `${goodsId}获取拼团ID失败:${res}`,
                type: 'danger'
            })
            return Promise.reject(res)
        })
    skuList.sort((a, b) => a.sku_group_price - b.sku_group_price)
    console.log(reqData, 'reqData')
    await batchRequest(reqData.num, {
        batch: 25,
        async request(list) {
            await Promise.allSettled(list.map(async ({ num_per_order, account }) => {
                const addressData = await resolveAddress(String(account))
                const createOrderRequestData: any = {
                    account,
                    sku: skuList[0].sku_id,
                    sku_spec: skuList[0].sku_name,
                    shop_id: mall.mallId,
                    shop_name: mall.mallName,
                    goods_id: goodsId,
                    num: num_per_order,
                    // num: 50001,
                    mode: 'open_group',
                    // activity_id: coupon_code,
                    goods_name: goodsName,
                    group_id: groupId,
                    use_coupon: false,
                    activity_id: reqData.activity_detail_id,
                    type: state.useLine2 ? 'guoyuan' : 'paidan',
                    ...addressData,
                }
                // console.log(createOrderRequestData, 'createOrderRequestData')
                return retry(async () => {
                    return quickOrder(createOrderRequestData, { showErrorMsg: false })
                        .then(res => {
                            addToLog({
                                msg: `${createOrderRequestData.goods_name}:下单成功。订单号:${res.data.order_sn}`,
                                type: 'success'
                            })
                            changPriceAndApply(res.data)
                        })
                }, 3)
                    .catch(res => {
                        addToLog({
                            msg: `${createOrderRequestData.goods_name}:下单失败:${res.msg}`,
                            type: 'danger'
                        })
                    })
            }))
        }
    })
}

async function resolveAddress(account: string): Promise<anyObj> {
    addToLog({ msg: '正在获取地址信息' })
    const settingStore = useSetting()
    const {
        nameCode_active,
        nameCode_position,
        nameCode_str,
        addr_active,
        addr_position,
        addr_str,
        filterStr,
        type,
        appoint_address,
    } = settingStore.address;
    let addressData: anyObj = {
        appoint_address: appoint_address || void 0,
    }
    if (type == 'diy') {
        const result = await getRandomItem();
        if (result) {
            Reflect.deleteProperty(result, "id");
            addressData = result;
        } else {
            addToLog({
                msg: '本机地址,没有找到可用地址，将使用系统随机地址',
                type: 'warning'
            })
        }
    }

    return addAddress({ account: account, ...addressData }, { showErrorMsg: false })
        .then(res => {
            addressData = {
                ...addressData,
                ...res.data,
                filter_address: filterStr.replace("，", ","),
                address_cipher: addr_active ? addr_str : void 0,
                address_site: addr_active ? addr_position : void 0,
                name_cipher: nameCode_active ? nameCode_str : void 0,
                name_site: nameCode_active ? nameCode_position : void 0,
            }
            addToLog({ msg: '获取地址信息成功' })
            return addressData
        })
        .catch(res => {
            // console.log('添加地址返回值-error：', res)

            useSubAccount().recover(account)
            addToLog({
                msg: '添加地址出错:' + res.msg,
                type: 'danger'
            })
            return Promise.reject()
        })
}

async function resolveAccount(requestData: RequestData) {
    addToLog({ msg: '正在分配小号' })
    const subAccount = useSubAccount()
    let isErrorFlag = false
    requestData.num.some(item => {
        const res = subAccount.getAccount(start_site.value - 1)
        if (res.status) {
            item.account = res.data
            const ind = (res.ind + 2) % subAccount.list.length
            start_site.value = ind === 0 ? subAccount.list.length : ind
            //    accounts
        } else {
            addToLog({
                type: 'danger',
                msg: '获取小号出错' + res.msg
            })
            // return Promise.reject()
            isErrorFlag = true
        }
        return !item.account
    })

    if (isErrorFlag) {
        addToLog({ msg: '小号可用次数不足,当前需要' + requestData.num.length })
        loading.value.create = false
        requestData.num.forEach(item => {
            if (!item.account) {
                return
            }
            subAccount.recover(item.account)
        })
        return Promise.reject()
    }
    return true

}
function resolveBuyCount(): RequestData['num'] {
    const { num_type } = state
    const list: RequestData['num'] = []
    if (num_type === 'appoint') {
        const { num_per_order, order_num } = state.num.appoint
        for (let i = 0; i < order_num; i++) {
            list.push({ num_per_order })
        }

    } else {
        const { num_per_order_1, num_per_order_2, order_num_1, order_num_2 } = state.num.random
        const order_num = random(order_num_1, order_num_2)
        for (let i = 0; i < order_num; i++) {
            list.push({ num_per_order: random(num_per_order_1, num_per_order_2) })
        }
    }
    return list

}

async function changPriceAndApply(orderInfo: anyObj) {
    const { type, order_amount, shop_id, shop_name, order_sn, } = orderInfo
    const settingStore = useSetting()
    let goodsDiscount: number;
    if (type === "pifa") {
        goodsDiscount = Number(order_amount - 0.01 * 100);
    } else {
        goodsDiscount = Number(order_amount - (order_amount) * 0.1);
    }
    addToLog({
        msg: `订单${order_sn},将执行改价操作`,
    });
    await delayPromise(3000);
    await changeOrderPriceRequest({
        store_id: shop_id,
        shop_name: shop_name,
        goodsDiscount,
        order_sn: order_sn,
    })
        .then((res) => {
            // console.log("改价成功", res);
            addToLog({
                type: "success",
                msg: `订单${order_sn},改价成功`,
            });
            updateOrder(
                {
                    ids: order_sn,
                },
                { showErrorMsg: false }
            )

            resolveApply(order_sn);
        })
        .catch((res) => {
            addToLog({
                type: "danger",
                msg: `订单${order_sn},改价失败:${res.msg}`,
            });
            const { chance } = settingStore.pay;
            if (chance === "immediate") {
                addToLog({
                    msg: "改价失败不会启动自动支付",
                    type: "warning",
                });
            }
        });

}

function resolveApply(order_sn: string) {
    const settingStore = useSetting()
    const { chance, zfb_pass } = settingStore.pay
    if (chance === 'immediate') {
        if (!zfb_pass) {
            addToLog({
                msg: "没有设置支付密码，不会开启自动支付",
                type: "warning",
            });
            return;
        }
        addToLog({ msg: '获取支付链接' })
        getApplyInfo({
            order_sn,
        })
            .then((res) => {
                const autoApply = useAutoApply();
                // console.log('获取支付链接成功',res.data)
                addToLog({
                    msg: `订单${order_sn},已进入自动支付队列`,
                });
                autoApply.addToPendding([
                    {
                        ...res.data,
                        type: "auto",
                    },
                ]);
            })
            .catch((e) => {
                addToLog({
                    msg: `订单${order_sn},获取支付链接失败，不会自动支付`,
                });
            });
    }
}
</script>
<style lang='scss' rel="stylesheet/scss" scoped>
.cs-limit{
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
}
</style>
