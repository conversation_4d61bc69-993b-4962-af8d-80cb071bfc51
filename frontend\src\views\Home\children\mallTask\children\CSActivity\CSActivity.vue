<template>
  <div class="change-sales no-padding">
    <el-config-provider size="default">
      <div class="settings">
        <div class="item account">
          <header>
            <h3>1.账号配置</h3>
          </header>
          <div class="content">
            <el-form label-position="top" size="small">
              <el-form-item label="店铺:">
                <el-select v-model="config.mallId">
                  <el-option v-for="store in mallStore.tableList" :label="store.mallName" :value="store.mallId">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="小号位置:">
                <el-input-number v-model="config.start_site" :controls="false" style="flex-grow: 1;"></el-input-number>
              </el-form-item>
              <el-form-item label="收货信息:">
                跟随<span class="primary">下单设置</span>的配置
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="item buy">
          <header>
            <h3>2.购买配置</h3>
          </header>
          <div class="content">
            <el-form label-position="top" :inline="true" size="small">
              <el-form-item label="下单模式">
                <el-radio-group v-model="config.activity">
                  <!-- <el-radio label="19521">
                    <el-space>
                      <span>西藏包邮-19521</span>
                      <el-link @click="checkStoreActivity('19521')" :underline="false" type="primary">
                        检查</el-link>
                    </el-space>
                  </el-radio> -->
                  <el-radio label="20981">
                    <el-space>
                      <span>全类目-20981</span>
                      <el-link @click="checkStoreActivity('20981')" :underline="false" type="primary">
                        检查</el-link>
                    </el-space>
                  </el-radio>
                  <!-- <el-radio label="21365">
                    <el-space>
                      <span> 限量抢-21365</span>
                      <el-link @click="checkStoreActivity('21365')" :underline="false" type="primary">
                        检查</el-link>
                    </el-space>

                  </el-radio>
                  <el-radio label="other">
                    <el-space>
                      <span> 其他活动</span>
                      <el-link @click="checkStoreActivity(config.activity_diy || '')" :underline="false"
                        type="primary">检查</el-link>
                    </el-space>
                  </el-radio> -->

                </el-radio-group>
                <!-- <p> <el-input :disabled="config.activity !== 'other'" v-model="config.activity_diy"></el-input></p> -->
              </el-form-item>
              <el-form-item label="购买数量">
                <el-radio-group v-model="config.num_type">
                  <el-radio label="random">随机购买</el-radio>
                  <el-radio label="random-range">范围随机</el-radio>
                  <el-radio label="appoint">指定数量</el-radio>

                </el-radio-group>
                <el-space v-show="config.num_type === 'appoint'">
                  <el-input-number style="width: 70px;" :controls="false" v-model="config.num_appoint" :precision="0"
                    :min="10"></el-input-number>
                  <span>件</span>
                </el-space>
                <el-space v-show="config.num_type === 'random'">
                  <el-input-number style="width: 58px;" :controls="false" v-model="config.num_times" :precision="0"
                    :min="1" :max="5"></el-input-number>
                  <span>位数</span>
                </el-space>
                <el-space v-show="config.num_type === 'random-range'">
                  <el-input-number style="width: 58px;" :controls="false" v-model="config.num_range.v1" :precision="0"
                    :min="1"></el-input-number>
                  <span>-</span>
                  <el-input-number style="width: 58px;" :controls="false" v-model="config.num_range.v2" :precision="0"
                    :min="1"></el-input-number>
                </el-space>


              </el-form-item>
              <el-form-item label="库存设置">
                <el-radio-group v-model="config.recoverQuantity">
                  <el-radio label="1">自动加库存(不影响原有库存)</el-radio>
                  <el-radio label="2">按现有库存(会影响原有库存)</el-radio>
                  <el-radio label="3" class="no-visible" :disabled="true">按现有库存(会影响原有库存)</el-radio>
                </el-radio-group>
                <p>活动价格：(设置0.8或1.2)</p>
                <p><el-input-number :precision="2" :min="0.01" :controls="false"
                    v-model="config.activityPrice"></el-input-number></p>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="item ctrl">
          <header>
            <h3>3.控制台</h3>
            <el-checkbox label="突破风控" v-model="isResetSku"></el-checkbox>
            <el-checkbox title="开启后会自动检测并关闭商品是否参与限时限量购活动" label="关闭其他活动"
              v-model="config.isCancelActivity"></el-checkbox>
          </header>
          <div class="content">
            <p>
              <span>催付金额：自动运算最低</span>
              <el-checkbox v-model="config.lowPriceSku" label="获取最低价SKU"></el-checkbox>
            </p>
            <p><el-button :loading="buttonLoading.getGoodsList" type="success" @click="getGoodsList">获取商品</el-button>
            </p>
            <p><el-button type="primary" :disabled="!state.tableSelection.length" @click="start"
                :loading="buttonLoading.start">开始</el-button></p>
            <p><el-button type="primary" @click="stopSubmit" :disabled="buttonLoading.stopFlag">停止</el-button></p>

          </div>
        </div>
      </div>

      <div class="table-container">
        <!-- <div class="table-header">
          <el-input>商品ID</el-input>
        </div> -->
        <VxeTable :scroll-y="{ enabled: true }" :loading="buttonLoading.getGoodsList"
          :checkbox-config="{ checkField: 'checkField' }"
          @checkbox-change="() => { state.tableSelection = vxeTableEl?.getCheckboxRecords() || [] }"
          @checkbox-all="() => { state.tableSelection = vxeTableEl?.getCheckboxRecords() || [] }" ref="vxeTableEl"
          :data="state.tableList" :height="310" :stripe="true" :column-config="{ resizable: true }"
          show-overflow='title'>
          <VxeColumn type='checkbox' :width="50"></VxeColumn>
          <VxeColumn type='seq' title="序号" :width="50"></VxeColumn>
          <VxeColumn title="商品ID" field="id" :width="130"></VxeColumn>
          <VxeColumn title="商品名称" field="goods_name"></VxeColumn>
          <VxeColumn title="SKU">
            <template #default='scope'>
              <el-select v-model="scope.row._skuSelect">
                <el-option v-for="item in scope.row.sku_list" :label="item.spec || scope.row.goods_name"
                  :value="item.skuId"></el-option>
              </el-select>
            </template>
          </VxeColumn>
          <VxeColumn title="SKUID" :width="140">
            <template #default='scope'>
              {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.skuId }}
            </template>
          </VxeColumn>
          <VxeColumn title="SKU拼团价格">
            <template #default='scope'>
              {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.groupPrice / 100 }}
            </template>
          </VxeColumn>
          <VxeColumn title="库存">
            <template #default='scope'>
              {{ (scope.row.sku_list as anyObj[]).find(item => item.skuId == scope.row._skuSelect)?.skuQuantity }}
            </template>
          </VxeColumn>
          <VxeColumn title="商品销量" field="sold_quantity" sortable :width="100"></VxeColumn>
          <VxeColumn title="状态" field="" :width="150">
            <template #default='scope'>
              <span :class="rowStatusMap.get(scope.row.id)?.type">{{ rowStatusMap.get(scope.row.id)?.msg }}</span>
            </template>
          </VxeColumn>
        </VxeTable>
        <footer class="table-footer">
          <p>
            <!-- <el-space>
              一次操作

              <el-select style="width: 60px;" v-model="config.goodsBatch">
                <el-option v-for="item in 5" :label="item" :value="item"></el-option>
              </el-select>

              个商品
            </el-space> -->
            <span>
              运行状态 ：
              <el-tag type="success" v-if="!buttonLoading.start && msgState.max">任务已完成</el-tag>
              <span v-else>

                <span class="primary">{{ `商品总数：${msgState.max}` }}</span>
                <span class="primary" style="margin:0 20px;">{{ `正在运行第${msgState.current}次` }}</span>
              </span>
            </span>


            <el-space>
              <el-link :underline="false" @click="tableAction('check-all')">全选</el-link>
              <el-link :underline="false" @click="tableAction('check-reverse')">反选</el-link>
              <el-link :underline="false" @click="tableAction('area-check')">区域选择</el-link>
              <el-link :underline="false" @click="tableAction('id-check')">商品id选择</el-link>
              <el-link :underline="false" @click="tableAction('cancel-check')">取消选择</el-link>
              <el-input @keyup.enter="getGoodsList()" placeholder="ID搜索,多个逗号隔开" v-model="state.goods_id"></el-input>
              <el-link :underline="false" @click="inputGoodsIdMultiple">大量输入</el-link>
            </el-space>
          </p>
          <p>
          <div>

            已完成： <span class="success">{{ msgState.success }}</span>,
            获取拼团ID失败 ：<span class="danger">{{ msgState.get_group_id_false }}</span>,
            商品无法报活动： <span class="danger">{{ msgState.activityReject }}</span>
          </div>
          <paginationVue @size-change="() => {
            pagination.page = 1
            getGoodsList()
          }" @current-change="getGoodsList" :total="pagination.total" :page-sizes="pagination.pageSizes"
            v-model:current-page="pagination.page" v-model:page-size="pagination.limit" />
          </p>
        </footer>
      </div>

      <LogVue v-model:list="state.logList" height="90px" />
      <!-- <div class="log-container">
        <header>
          <span>任务日志</span>
          <el-space class="actions">
            <el-link :underline="false" type="danger" @click="state.logList.length = 0">清空</el-link>
          </el-space>
        </header>
        <el-scrollbar height="80px" ref="LogScroll">
          <div class="log-list">
            <p class="log-item" :class="item.type" v-for="item in state.logList">
              <span> {{ dayjs(item.date).format('YYYY-MM-DD HH:mm:ss') }}</span>
              <span>{{ item.msg }}</span>
            </p>
          </div>
        </el-scrollbar>
      </div> -->
    </el-config-provider>
  </div>
</template>
<script lang='ts' setup>
import { h, nextTick, reactive, ref, watch } from 'vue';
import { useMallStore } from '/@/stores/store';
import { useSetting } from '/@/stores/setting';
import { ElMessage, ElMessageBox, ElNotification, ScrollbarInstance, TableInstance } from 'element-plus';
import { activityCancel, claimCoupon, createPrivateCoupon, couponList, activityDetails, activityGoodsList, activitySubmit, changeSkuQuantity, checkActivityEnable, getGoodsSkuDetails, quickOrder, storeGoodsList, addAddress, marketingList, marketingStop } from '/@/apis/changeSales';
import LogVue from '/@/components/Log/log.vue'
import { batchRequest, delayPromise, random, retry } from '/@/utils/common';
import { autoSupply, changeOrderPrice as changeOrderPriceRequest, getApplyInfo, getPifaGroupId, updateOrder } from '/@/apis/page';
import { useSubAccount } from '/@/stores/pageData';
import { useAutoApply } from '/@/stores/autoApply';
import { getRandomItem } from '/@/apis/address';
import paginationVue from '/@/components/pagination/pagination.vue';
import { VxeTableInstance, VxeTextarea } from 'vxe-table';
import { isUserCanUse } from '../../utils';
import { usePersistenceRef } from '/@/hooks/ref';
const settingStore = useSetting()
const mallStore = useMallStore()


const config: {
  mallId?: Store['mallId']
  start_site: number
  activity: string
  activity_diy: string
  activityPrice: number

  num_type: 'random' | 'appoint' | 'random-range',
  /**随机多少位 */
  num_times: number,
  /**指定多少件 */
  num_appoint: number,

  num_range: {
    v1: number
    v2: number
  }

  /**一次执行多少个商品 */
  goodsBatch: number
  isSaveMoney: boolean
  /**是否执行前检测限时限量购活动 并关闭 */
  isCancelActivity: boolean
  recoverQuantity: string
  lowPriceSku: boolean
} = reactive({
  start_site: 1,
  activity: '20981',
  activity_diy: "",
  activityPrice: 0.8,
  goodsBatch: 1,
  num_type: 'random',
  num_times: 3,
  num_range: {
    v1: 100,
    v2: 200
  },
  num_appoint: 10,
  isSaveMoney: false,
  isCancelActivity: false,
  recoverQuantity: '1',
  lowPriceSku: true
})

const isResetSku = usePersistenceRef(false, 'changeSalesConfig.isResetSku')
watch(config, (val) => {
  settingStore.setAnySetting('changeSalesConfig', { ...val })
}, { deep: true })

settingStore.getAnySetting('changeSalesConfig').then(res => {
  if (res) {
    config.activity = res.activity,
      config.activity_diy = res.activity_diy
    config.goodsBatch = res.goodsBatch
  }
})

type ResolveGoodsData = {
  rows: Array<{
    row: Row, goods_id: number, sku_select_id: number,
    /**参加活动成功？ */
    isActivitySuccess?: boolean,
    /**成功清除库存？ */
    isClearStockSuccess?: boolean
    group_id: number, account: string, addressData?: anyObj,
    /**活动管理ID */
    activity_goods_id?: number
    /**下单时上传的id */
    activity_price_detail_id?: number,
    skuList: Array<anyObj>
    /**一次购买多少件 */
    num: number
  }>,
  /**活动ID */
  activity_id: number | string

  /**一次购买多少件 */
  // num: number

  mall: Store,
}

type LogItem = {
  date: number
  msg: string,
  type?: 'success' | 'danger' | 'warning'
}
type Row = anyObj
const state: {
  tableList: Row[]
  tableSelection: Row[]
  logList: LogItem[]
  goods_id: string

} = reactive({
  tableList: [],
  tableSelection: [],
  logList: [],
  goods_id: ""
})


async function inputGoodsIdMultiple() {
  let goodsIdStr = state.goods_id
  await ElMessageBox({
    title: '修改商品ID筛选',
    showCancelButton: true,
    message: h('div', {}, [
      h('p', { class: 'm-b-5 primary' }, '请输入商品ID,多个用逗号或换行隔开！'),
      h(VxeTextarea, {
        modelValue: state.goods_id,
        onChange: ({ value }) => { goodsIdStr = value },
        resize: 'none',
        rows: 10,
        placeholder: '请输入商品ID,多个用逗号或换行隔开！'
      })
    ])
  })
  state.goods_id = goodsIdStr.split(/[,，\n]/).filter(item => item.trim()).join(',')

}

const pagination: Pagination = reactive({
  page: 1,
  limit: 100,
  pageSizes: [100, 500, 1000],
  total: 0
})

const LogScroll = ref<ScrollbarInstance>()
watch(() => state.logList.length, (value) => {
  let top = LogScroll.value?.wrapRef?.scrollHeight || 0
  top && nextTick(() => {
    LogScroll.value?.setScrollTop(top < 0 ? 0 : top)
  })
}, { immediate: true })
const rowStatusMap = ref<Map<number, { msg: string, type?: string }>>(new Map())
function log(data: Omit<LogItem, 'date'>, row?: Row) {
  state.logList.push({
    ...data,
    date: Date.now()
  })
  if (row) {
    const id = row.id || row.goods_id
    const msg = data.msg || ''
    if (rowStatusMap.value.has(id)) {
      rowStatusMap.value.get(id)!.msg = msg
      rowStatusMap.value.get(id)!.type = data.type
    } else {
      rowStatusMap.value.set(id, { msg, type: data.type })
    }
  }
}

function logNotice() {
  log({
    msg: '  ①：停掉当前产品的限时限量购活动',
    type: 'danger'
  })
  log({
    msg: '  ②：当前产品在营销活动里面报的活动需要下架',
    type: 'danger'
  })
  log({
    msg: '  ③：当前产品的物流模板需要改为默认模板（新疆西藏包邮）',
    type: 'danger'
  })
  log({
    msg: '  ④：如果开通了运费险的需要先暂停掉，不介意的可以不停(店铺管理页面)',
    type: 'danger'
  })
  log({
    msg: '  ⑤：如果下单遇到风控导致失败，会缓存商品在后面重复操作，最多重复3次，提高成功率',
    type: 'danger'
  })
}
logNotice()
const buttonLoading = reactive({
  getGoodsList: false,
  start: false,
  stopFlag: true
})

async function checkStoreActivity(activity: string) {
  const { mallId } = config
  const store = mallStore.tableList.find(item => item.mallId == mallId)
  if (!store) {
    ElMessage.warning({
      message: '请选择店铺',
      grouping: true
    })
    return Promise.reject()
  }
  if (!mallStore.checkAvailable(store)) {

    ElMessage.warning({
      message: '当前店铺已过期，请重新登录',
      grouping: true
    })
    return Promise.reject()
  }
  if (!activity) {
    ElMessage.warning({
      message: '请选择活动'
    })
    return Promise.reject()
  }
  log({
    msg: '正在检测店铺是否可参加活动'
  })
  const res = await checkActivityEnable(store, activity)
  // console.log(checkRes, 'checkRes')
  const data = res
  if (!data.result.pass_mall_rules) {
    ElMessageBox({
      title: '不可参加活动',
      message: h('div', {}, (data.result.mall_requirement as anyObj[]).filter(item => !item.passed).map((item: anyObj, index: number) => {
        return h('p', { class: item.passed ? 'success' : 'danger' }, `(${index + 1})${item.requirement}`)
      }))
    })
    return Promise.reject()
  }
  log({
    msg: '当前店铺可以参加选中活动',
    type: 'success'
  })
  return store
}


async function getGoodsList() {
  // if (!checkPastTime()) {
  //   return
  // }
  const { mallId } = config
  const store = mallStore.tableList.find(item => item.mallId == mallId)
  if (!store) {
    return ElMessage.warning({
      message: '请选择店铺',
      grouping: true
    })
  }
  if (!mallStore.checkAvailable(store)) {
    return ElMessage.warning({
      message: '当前店铺已过期，请重新登录',
      grouping: true
    })
  }
  const { goods_id } = state
  if (goods_id) {
    pagination.page = 1
  }

  buttonLoading.getGoodsList = true
  /**一页对应真实页数多少页 */
  const times = Math.round(pagination.limit / 100)
  let basePage = (pagination.page - 1) * times
  let page = 1
  const max = pagination.page * times
  const list: typeof state.tableList = []

  /**已经请求到多少条数据,用于计算后续是否还有数据 */
  let requestGoodsCount = 0
  await new Promise((resolve) => {

    function request() {
      log({
        msg: `正在获取第${pagination.page}-${page}页商品`
      })
      storeGoodsList(store!, basePage + page, goods_id)
        .then(res => {
          if (res.success) {
            state.tableList = [];
            const goodsList: anyObj[] = res.result.goods_list
            requestGoodsCount += goodsList.length
            // state.tableList.push(...)
            goodsList.forEach(item => {
              // isOnsale
              item.sku_list = (item.sku_list as anyObj[]).filter(item => item.isOnsale)
              if (!item.sku_list.length) {
                return
              }
              item._skuSelect = (item.sku_list as anyObj[]).sort((a, b) => a.groupPrice - b.groupPrice)[0].skuId
              // state.tableList.push(item)
              list.push(item)
            });
            pagination.total = res.result.total
            log({
              // msg: `获取商品第${page}页成功,当前已获取${state.tableList.length}/${res.result.total}条`,
              msg: `获取商品第${pagination.page}-${page}页成功`,
              type: 'success'
            })
            if (res.result.total > requestGoodsCount && basePage + page < max) {
              if (buttonLoading.getGoodsList) {
                page++
                request()
              } else {
                log({
                  msg: '停止获取',
                  type: 'warning'
                })
                resolve(true)
              }

            } else {
              resolve(true)
            }
          } else {
            // console.log(res)
            let errorMsg = res.errorMsg || res.error_msg || ''
            if (errorMsg == '会话已过期') {
              errorMsg += ',请重新登录店铺'
            }
            log({
              msg: '获取失败：' + errorMsg,
              type: 'danger'
            })
            resolve(true)
          }
        })
        .catch((res) => {
          // console.log(res) 
          log({
            msg: '意外的错误,停止获取',
            type: 'danger'
          })
          resolve(true)
        })
    }
    request()
  })
  state.tableList = list

  log({
    msg: `获取商品列表结束`
  })
  buttonLoading.getGoodsList = false
}

function stopSubmit() {
  buttonLoading.stopFlag = true
  log({
    msg: '已发送停止指令',
    type: 'danger'
  })
  log({
    msg: "请不要关闭软件,等待任务停止,",
    type: 'danger'
  })
  ElMessageBox({
    title: '提示',
    type: 'warning',
    message: h('div', {}, [
      h('p', { class: 'danger mt-5 fs-18' }, '请不要关闭软件'),
      h('p', { class: 'mt-5' }, '等待系统自动停止,意外的中断需要手动去店铺取消活动'),
    ])
  })
}

/**检测过期 */
// function checkPastTime(notice = true) {
//   const userStore = useUserStore()
//   if (userStore.app_info.past_time * 1000 - 3 * 24 * 60 * 60 * 1000 < Date.now()) {
//     notice && ElMessage.warning({
//       message: "当前账号剩余时间小于3天，请续费后再尝试",
//       grouping: true
//     })
//     return Promise.reject(false)
//   } else {
//     return true
//   }
// }



function execAutoApply(order_sn: string) {
  if (!order_sn) {
    return;
  }
  const settingStore = useSetting();
  // 自动支付
  const { chance, zfb_pass } = settingStore.pay;

  if (chance !== "immediate") {
    return;
  }
  if (!zfb_pass) {
    log({
      msg: "没有设置支付密码，不会开启自动支付",
      type: "warning",
    });
    return;
  }
  log({
    msg: `订单${order_sn},开始获取支付链接`,
  });
  getApplyInfo({
    order_sn,
  })
    .then((res) => {
      const autoApply = useAutoApply();
      // console.log('获取支付链接成功',res.data)
      log({
        msg: `订单${order_sn},已进入自动支付队列`,
      });
      autoApply.addToPendding([
        {
          ...res.data,
          type: "auto",
        },
      ]);
    })
    .catch((e) => {
      log({
        msg: `订单${order_sn},获取支付链接失败，不会自动支付`,
      });
    });
}



// const tableEl = ref<TableInstance>()
const vxeTableEl = ref<VxeTableInstance>()
async function tableAction(action: 'check-all' | 'check-reverse' | 'area-check' | 'cancel-check' | 'id-check') {
  switch (action) {
    case 'check-all': {
      state.tableList.forEach(item => {
        // tableEl.value?.toggleRowSelection(item, true)
        vxeTableEl.value?.setAllCheckboxRow(true)
      })
      break
    }
    case 'check-reverse': {
      const checkSet = new Set(state.tableSelection.map(item => item.id))
      console.log(checkSet)
      state.tableList.forEach(item => {
        // tableEl.value?.toggleRowSelection(item, !checkSet.has(item.id))
        vxeTableEl.value?.setCheckboxRow(item, !checkSet.has(item.id))
      })
      break
    }
    case 'cancel-check': {
      // state.tableList.forEach(item => {
      //   tableEl.value?.toggleRowSelection(item, false)
      // })
      vxeTableEl.value?.clearCheckboxRow()
      break
    }
    case 'area-check': {
      const res = await ElMessageBox.prompt('请输入需要选中的商品(用-隔开),留空表示起始值或末尾值\n(例：20-30 ; -30 ; 20- ; -)', '区域选择', {
        showCancelButton: true,
        inputPlaceholder: "请输入范围"
      })
      const { value } = res
      let [min, max] = value.split('-').map(item => Number(item.trim()))
      min = min - 1 < 0 ? 0 : min - 1
      max = max || state.tableList.length
      if (min > max) {
        return ElMessage.warning({
          message: '最小值大于最大值',
          grouping: true
        })
      }
      await tableAction('cancel-check')
      // const checkSet = new Set(state.tableList.slice(min, max).map(item => item.id))
      // state.tableList.forEach(item => {
      //   tableEl.value?.toggleRowSelection(item, checkSet.has(item.id))
      // })
      state.tableList.slice(min, max).forEach(item => {
        vxeTableEl.value?.setCheckboxRow(item, true)
      })
      break
    }
    case 'id-check': {
      const res = await ElMessageBox.prompt('请输入需要选中的商品id(多个用,逗号隔开)', 'id选择', {
        showCancelButton: true,
        inputPlaceholder: "请输入id"
      })

      const { value } = res
      const ids = new Set(value.split(/[,，]/).filter(item => item).map(item => item.trim()))
      state.tableList.forEach(item => {
        // tableEl.value?.toggleRowSelection(item, ids.has(String(item.id)))
        vxeTableEl.value?.setCheckboxRow(item, ids.has(String(item.id)))
      })


      break
    }
  }
  state.tableSelection = vxeTableEl.value?.getCheckboxRecords() || []
}

settingStore.getAnySetting('start_site')
  .then(res => {
    if (res) {
      config.start_site = res
    }
  })
watch(() => config.start_site, (val) => {
  settingStore.setAnySetting('start_site', val)
})


const msgState = reactive({
  success: 0,
  max: 0,
  get_group_id_false: 0,
  current: 0,
  activityReject: 0
})

/**没有成功获取到活动管理(也就是没有成功取消的商品) */
let noCancelActivityGoods: Set<number> = new Set()
async function resolveNoCancelActivity(store: Store, activity_id: string | number) {

  await delayPromise(2000)
  if (!noCancelActivityGoods.size) {
    return
  }
  const list = [...noCancelActivityGoods.values()]
  noCancelActivityGoods.clear()
  log({
    msg: `开始尝试取消${list.length}个未成功取消活动的商品`,
  });
  const activityGoodsListRes = await activityGoodsList(store, list)
  const a_goods_list: anyObj[] = (activityGoodsListRes.result.result as anyObj[]).filter(item => item.activity_id == Number(activity_id))
  if (!a_goods_list.length) {
    log({ msg: '没有获取到活动列表,请前往pdd后台检查并取消活动', type: 'danger' })
    // ElNotification({
    //   title: '提示',
    //   message: '没有获取到活动列表,请前往pdd后台检查并取消活动',
    //   type: 'warning',
    //   duration: 0,
    //   position: 'top-right'
    // })
    return
  }
  a_goods_list.forEach(item => {
    cancelActivity(store, item.activity_goods_id, item.goods_id, false)
      .catch(e => {
        log({
          msg: '取消活动失败,请前往PDD后台营销活动管理查看并取消',
          type: 'danger'
        })
      })
  })
}

const cache = {
  reapeat: {} as Record<string, number>,
  groupId: {} as Record<string, number>
}
async function start() {
  if (!isUserCanUse()) {
    return
  }
  cache.reapeat = {}
  cache.groupId = {}
  const _list = [...state.tableSelection]
  const max = _list.length
  if (!_list.length) {
    return ElMessage.warning({
      message: '请选择商品',
      grouping: true
    })
  }

  const { activity, activity_diy, goodsBatch = 1 } = config
  let activity_id = activity === 'other' ? activity_diy : activity
  if (!activity_id) {
    return ElMessage.warning({
      message: '请选择活动',
      grouping: true
    })
  }
  const { type } = settingStore.address
  if (type === 'diy') {
    const result = await getRandomItem();
    if (!(result && result.id)) {
      return ElMessage.warning({
        message: '检测到自定义地址列表为空,请确保有发货地址',
        grouping: true
      })
    }
  }
  // await checkPastTime()
  const mall = await checkStoreActivity(activity_id)
  const list: Array<typeof _list> = []
  while (_list.length) {
    // list.push(_list.splice(0, goodsBatch))
    list.push(_list.splice(0, 1))
  }
  buttonLoading.start = true
  buttonLoading.stopFlag = false
  state.logList = []
  {
    msgState.current = 0
    msgState.get_group_id_false = 0
    msgState.success = 0
    msgState.max = max
    msgState.activityReject = 0
  }
  exec()
  async function checkStop() {
    if (buttonLoading.stopFlag) {
      return Promise.reject('stop')
    }
  }

  async function exec(_goodsList?: typeof list[number]) {
    if (buttonLoading.stopFlag) {
      list.length = 0
    }
    const goodsList = _goodsList || list.shift()
    if (!goodsList) {
      ElMessage.success({
        message: '任务结束',
        grouping: true
      })
      buttonLoading.start = false
      buttonLoading.stopFlag = false
      resolveNoCancelActivity(mall, activity_id)
      return
    }
    msgState.current += goodsList.length
    const resolveGoodsData: ResolveGoodsData = {
      rows: goodsList.map(row => {
        return {
          row,
          goods_id: row.id,
          group_id: 0,
          account: '',
          sku_select_id: row['_skuSelect'],
          skuList: [],
          num: getBuyCount(activity_id),
        }
      }),
      activity_id,
      // num: getBuyCount(activity_id),
      mall,
    }
    try {
      await checkStop()
      await addStock(resolveGoodsData)
      await checkStop()
      await delayPromise(5000)
      await checkStop()
      await getGroupId(resolveGoodsData)
      await checkStop()
      await getAccount(resolveGoodsData)
      await checkStop()
      await getAddress(resolveGoodsData)
      await checkStop()
      await otherActivity(resolveGoodsData)
      await checkStop()
      await participateActivity(resolveGoodsData)
      await resolveActivityDetails(resolveGoodsData)
      await createOrder(resolveGoodsData, (row) => list.push([row.row]))

    } catch (e) {
      console.log('error', e)
    }
    exec()
  }
}



/**获取下单数量 */
function getBuyCount(activeity_id: string | number) {
  const { num_appoint, num_type, num_times = 3, num_range } = config
  let num = 1
  switch (num_type) {
    case 'appoint': {
      num = num_appoint || 1;
      break
    }
    case 'random': {
      num = random(Math.pow(10, num_times - 1), Math.pow(10, num_times))
      break
    }
    case 'random-range': {
      const { v1, v2 } = num_range
      num = random(v1, v2);
      break
    }
  }
  if (num <= 0) {
    num = 1
  }
  if (activeity_id == '21365') {
    if (num > 300 || num < 100) {
      if (num_type === 'appoint') {
        num = num < 100 ? 100 : 300
        log({
          msg: `当前活动只能下单100-300,已自动修正到${num}`,
          type: 'warning'
        })
      } else {
        num = random(100, 300)
        log({
          msg: `当前活动只能下单100-300,已自动修正到${num}`,
          type: 'warning'
        })
      }
    }
  }
  return num
}

async function getAccount(resolveGoodsData: ResolveGoodsData) {
  const subAccount = useSubAccount()
  resolveGoodsData.rows.forEach(item => {
    const result = subAccount.getAccount(config.start_site)
    if (!result.status) {
      log({
        msg: '分配小号出错：' + result.msg
      }, item.row)
      return
    }
    item.account = result.data
    config.start_site = result.ind + 1
  })
  resolveGoodsData.rows = resolveGoodsData.rows.filter(item => item.account)
  if (!resolveGoodsData.rows.length) {
    log({
      msg: '没有商品通过小号分配,已跳过',
      type: 'warning'
    })
    return Promise.reject()
  }
}

/**库存操作 */
async function addStock(resolveGoodsData: ResolveGoodsData) {
  const list: typeof resolveGoodsData.rows = []
  await Promise.allSettled(resolveGoodsData.rows.map(async item => {
    await addStockHandle(item, resolveGoodsData)
    list.push(item)
  }))
  resolveGoodsData.rows = list
  if (!list.length) {
    log({
      msg: '没有商品通过库存操作,已跳过',
      type: 'warning'
    })
    return Promise.reject()
  }
}
async function addStockHandle(item: ResolveGoodsData['rows'][number], resolveGoodsData: ResolveGoodsData) {
  let num = item.num
  const mall = resolveGoodsData.mall
  const { recoverQuantity } = config
  if (recoverQuantity == '2') {
    const res = await getGoodsSkuDetails(mall, item.goods_id)
    const skuSelect = (res.result as anyObj[]).find(item => item.skuId == item._skuSelect)
    if (skuSelect?.skuQuantity && skuSelect.skuQuantity - 10 < num) {
      num = skuSelect.skuQuantity - 10
      log({
        msg: `当前商品库存不足,已自动修正购买数量,`,
        type: 'warning'
      }, item.row)
      item.num = num
    }

  } else if (config.recoverQuantity == '1') {
    await changeSkuQuantity([{
      goodsId: item.goods_id,
      quantity: num,
      skuId: item.sku_select_id
    }], mall)
    log({
      msg: '自动加库存成功',
      type: 'success'
    }, item.row)

  }
}

/**获取拼团ID */
async function getGroupId(resolveGoodsData: ResolveGoodsData) {
  // await Promise.allSettled(resolveGoodsData.rows.map(async item => {
  //   const groupId = await getGroupIdHandle(item, resolveGoodsData.mall)
  //   item.group_id = Number(groupId)
  // }))
  await batchRequest(resolveGoodsData.rows, {
    batch: 1,
    request: async ([item]) => {
      const _g_id = cache.groupId[item.goods_id]
      if (_g_id) {
        log({
          msg: `${item.row.goods_name},获取拼团id成功(缓存),${_g_id}`
        }, item.row)
        item.group_id = _g_id
        return
      }
      const groupId = await getGroupIdHandle(item, resolveGoodsData.mall)
      item.group_id = Number(groupId)
      cache.groupId[item.goods_id] = item.group_id
    }
  })
  resolveGoodsData.rows = resolveGoodsData.rows.filter(item => item.group_id)
  if (!resolveGoodsData.rows.length) {
    log({
      msg: "当前次序没有商品获取到拼团ID,将跳过",
      type: 'danger'
    })
    return Promise.reject()
  }
}

async function getGroupIdHandle(item: ResolveGoodsData['rows'][number], store: Store): Promise<string | number> {
  return new Promise(async (resolve, reject) => {
    log({
      msg: `${item.row.goods_name},正在获取拼团id`
    }, item.row)
    //上批发获取
    const autoApplyFn = async () => {
      const r = await autoSupply({
        mallId: store!.mallId,
        goods_id: item.goods_id
      })

      if (r.code) {
        log({
          msg: `${item.row.goods_name},自动供货失败：${r.msg}`,
          type: 'danger'
        }, item.row)
      } else {
        log({
          msg: `${item.row.goods_name},自动供货成功`,
          type: 'success'
        }, item.row)
      }
      return r
    }
    let isAutoApplyExec = false
    await delayPromise(2000)
    const getId = (retryCount = 10) => {
      retry(async () => {
        return getPifaGroupId({
          goods_id: item.goods_id,
          sku_id: item.sku_select_id
        }, { showErrorMsg: false })
      }, retryCount)
        .then(res => {
          log({
            msg: `${item.row.goods_name},获取拼团id成功:${res.data}`,
            type: 'success'
          }, item.row)
          resolve(res.data)
        })
        .catch(async (res) => {
          if (!isAutoApplyExec) {
            isAutoApplyExec = true
            const r = await autoApplyFn()
            if (!r.code) {
              await delayPromise(5000)
              /**自动供货成功 再获取一次拼团ID */
              getId()
              return
            }
          }
          reject()
          msgState.get_group_id_false++
          log({
            msg: `${item.row.goods_name},获取拼团id失败:${res.msg}`,
            type: 'danger'
          }, item.row)
        })
    }
    getId(3)
  })
}


/**获取下单地址 */
async function getAddress(resolveGoodsData: ResolveGoodsData) {
  const { rows } = resolveGoodsData
  log({
    msg: '正在处理下单地址'
  })
  await Promise.allSettled(rows.map(async row => {
    await getAddressHandle(row, resolveGoodsData)
  }))
  resolveGoodsData.rows = resolveGoodsData.rows.filter(item => item.addressData)
  if (!resolveGoodsData.rows.length) {
    log({
      msg: '没有商品获取到地址,已跳过',
      type: 'warning'
    })
    return Promise.reject()
  }
}
async function getAddressHandle(row: ResolveGoodsData['rows'][number], resolveGoodsData: ResolveGoodsData) {
  const settingStore = useSetting()
  const {
    nameCode_active,
    nameCode_position,
    nameCode_str,
    addr_active,
    addr_position,
    addr_str,
    filterStr,
    type,
    appoint_address,
  } = settingStore.address;
  let addressData: anyObj = {
    appoint_address: appoint_address || void 0,
  }
  if (type == 'diy') {
    const result = await getRandomItem();
    Reflect.deleteProperty(result, "id");
    addressData = result;
  }

  return addAddress({ account: row.account, ...addressData }, { showErrorMsg: false })
    .then(res => {
      addressData = {
        ...addressData,
        ...res.data,
        filter_address: filterStr.replace("，", ","),
        address_cipher: addr_active ? addr_str : void 0,
        address_site: addr_active ? addr_position : void 0,
        name_cipher: nameCode_active ? nameCode_str : void 0,
        name_site: nameCode_active ? nameCode_position : void 0,
      }
      row.addressData = addressData
    })
    .catch(res => {
      // console.log('添加地址返回值-error：', res)
      row.addressData = void 0
      useSubAccount().recover(row.account)
      log({
        msg: '添加地址出错:' + res.msg,
        type: 'danger'
      }, row.row)
      return Promise.reject()
    })
}

/**参与活动 */
async function participateActivity(resolveGoodsData: ResolveGoodsData) {
  await checkActivityParticipate(resolveGoodsData)
  const { mall: store, activity_id, rows } = resolveGoodsData

  await Promise.allSettled(rows.map(async item => {
    const { result } = await getGoodsSkuDetails(store!, item.goods_id)
    item.skuList = result.filter((item: anyObj) => item.is_onsale)
  }))
  resolveGoodsData.rows = rows.filter(item => item.skuList.length)
  if (!resolveGoodsData.rows.length) {
    log({
      msg: "当前次序没有商品没有成功获取到sku列表的商品,将跳过",
      type: 'danger'
    })
    return
    // return Promise.reject('没有获取到sku')
  }
  let resetRes: any = ''
  if (resolveGoodsData.activity_id == '21365') {
    resetRes = resetSkuQuantity(resolveGoodsData, 'clear')
  } else {
    await resetSkuQuantity(resolveGoodsData, 'clear')
    // if (buttonLoading.stopFlag) {
    //   log({
    //     msg: '检测到停止',
    //     type: 'warning'
    //   })
    //   await resetSkuQuantity(resolveGoodsData, 'recover')
    //   return Promise.reject('停止')
    // }
  }

  const res = await activitySubmit(store!, activity_id, resolveGoodsData.rows.map(item => {
    // const data = {
    //   goods_id: item.goods_id,
    //   create_activity_price_vo: {
    //     create_time_vo: {},
    //     create_quantity_vo: {
    //       goods_activity_quantity: activity_id == '21365' ? String(item.num) : void 0
    //     },
    //     create_sku_vos: item.row.sku_list.map((sku: anyObj) => {
    //       const price = Math.round(config.activityPrice * 100)
    //       const data = {
    //         sku_id: sku.skuId,
    //         sku_activity_price: price,
    //         sku_front_ctx_vo: {
    //           require_price: price,
    //           filling_sku_price: price
    //         }
    //       }
    //       if (activity_id == '21365') {
    //         Reflect.deleteProperty(data, 'sku_front_ctx_vo')
    //       }
    //       return data
    //     })
    //   }
    // }
    const data = {
      goods_id: item.goods_id,
      create_discount_vo: {
        hit_multi_percent: null,
        multi_percent_discount: null
      },
      create_material_vo: {
        create_goods_image_vo: {},
        create_goods_title_vo: {},
      },
      view_confirmed_goods_protocol_vo: {
        confirm_copy_goods_for_customer_num: false,
        confirm_hit_high_commission: false
      },
      create_finance_cost_vo: {},
      create_goods_coupon_vo: {},
      create_activity_price_vo: {
        create_time_vo: {},
        create_quantity_vo: {
          // goods_activity_quantity: activity_id == '21365' ? "40" : void 0
        },
        create_sku_vos: item.row.sku_list.map((sku: anyObj) => {
          const price = Math.round(config.activityPrice * 100)
          const data = {
            sku_id: sku.skuId,
            sku_activity_price: price,
            sku_front_ctx_vo: {
              // require_price: price,
              // filling_sku_price: price
              algorithm_decorate_sku_price: null,
              filling_sku_price: 381,
              filling_sku_price_gear_rule: 1001,
              gear_rule: 23,
              require_price: 381,
              tags: null,
            }
          }
          if (activity_id == '21365') {
            Reflect.deleteProperty(data, 'sku_front_ctx_vo')
          }
          return data
        }),
        "create_traffic_protect_vo": {
          "max_cut_price_amount": 19
        }
      }
    }
    return data
  }))

  if (res.success) {
    const failList = res.result.enroll_fail_goods_list as anyObj[]
    const successList = res.result.enroll_success_goods_list as anyObj[]
    const map = new Map<number, ResolveGoodsData['rows'][number]>()
    resolveGoodsData.rows.forEach(item => {
      map.set(Number(item.goods_id), item)
    })
    failList.forEach(errorItem => {
      const mapItem = map.get(Number(errorItem.goods_id))
      if (mapItem) {
        log({
          msg: `${mapItem?.row.goods_name}参与活动失败：${errorItem.error_msg}`,
          type: 'danger'
        }, mapItem.row)
        // resetSkuQuantity(resolveGoodsData, 'recover', mapItem?.goods_id)
      }
    })
    successList.forEach(successItem => {
      const mapItem = map.get(Number(successItem.goods_id))
      if (mapItem) {
        log({
          msg: `${mapItem?.row.goods_name}参与活动成功`,
          type: 'success'
        }, mapItem.row)
        mapItem.activity_goods_id = successItem.activity_goods_id
        mapItem.isActivitySuccess = true
      }
    })
    if (!resolveGoodsData.rows.some(item => item.isActivitySuccess)) {
      log({
        msg: '没有商品成功参加活动',
        type: 'warning'
      })
      return
      // return Promise.reject({
      //   msg: '没有商品成功参加活动',
      //   type: 'warning'
      // })
    }
  } else {
    log({
      msg: '参与活动失败-' + (res.error_msg || res.errorMsg || ''),
      type: 'danger'
    })
    // return Promise.reject(
    //   {
    //     msg: '参与活动失败-' + (res.error_msg || res.errorMsg || ''),
    //     type: 'danger'
    //   }
    // )
  }
  await resetRes
}
/**获取活动管理ID */
async function resolveActivityDetails(resolveGoodsData: ResolveGoodsData) {
  const { mall: store, activity_id, } = resolveGoodsData
  // await delayPromise(3000)
  // const activityGoodsListRes = await retry(async (index) => {
  //   const res = await activityGoodsList(store, rows.filter(item => item.isActivitySuccess).map(item => item.goods_id), String(activity_id))

  //   const a_goods_list: anyObj[] = (activityGoodsListRes.result.result as anyObj[]).filter(item => item.activity_id == Number(activity_id))
  //   rows.forEach(item => {
  //     if (!item.isActivitySuccess) {
  //       return
  //     }
  //     const activitGoodsItem = a_goods_list.find(_item => {
  //       return _item.goods_id == item.goods_id
  //     })
  //     if (activitGoodsItem) {
  //       item.activity_goods_id = activitGoodsItem.activity_goods_id
  //     }
  //   })
  //   if (index >= 3) {
  //     return res
  //   }
  //   if (rows.some(item => item.isActivitySuccess && !item.activity_goods_id)) {
  //     // 有商品还未获取到活动管理ID
  //     return Promise.reject()
  //   }
  //   return res
  // }, 5, 2000)
  resolveGoodsData.rows.forEach(item => {
    if (item.isActivitySuccess && !item.activity_goods_id) {
      log({
        msg: '成功参与活动但未获取到活动管理ID',
        type: 'danger'
      }, item.row)
      resetSkuQuantity(resolveGoodsData, 'recover', item.goods_id)
      noCancelActivityGoods.add(item.goods_id)
    }
    if (item.isActivitySuccess && !item.isClearStockSuccess) {
      log({
        msg: '成功参与活动但未成功清除sku',
        type: 'danger'
      }, item.row)
      cancelActivity(store, item.activity_goods_id!, item.goods_id)
    } else if (!item.isActivitySuccess && item.isClearStockSuccess) {
      log({
        msg: '成功清除sku但未成功参与活动',
        type: 'danger'
      }, item.row)
      resetSkuQuantity(resolveGoodsData, 'recover', item.goods_id)
    }
  })
  console.log(resolveGoodsData)
  // if (!resolveGoodsData.rows.some(item => item.isActivitySuccess && item.activity_goods_id && item.isClearStockSuccess)) {
  //   // 没有商品成功获取到管理ID
  //   log({
  //     msg: '没有商品成功获取到活动管理ID',
  //     type: 'warning'
  //   })
  //   return Promise.reject()
  // }
  resolveGoodsData.rows = resolveGoodsData.rows.filter(item => item.isActivitySuccess && item.activity_goods_id && item.isClearStockSuccess)

  await Promise.allSettled(resolveGoodsData.rows.map(async item => {
    if (!item.isActivitySuccess) {
      return
    }
    return retry(async (index) => {
      const res = await activityDetails(store!, Number(item.activity_goods_id)!, item.goods_id)
      console.log('activityDetails', index, res)
      log({
        msg: `${item.row.goods_name}获取活动信息第${index}次,`
      }, item.row)
      if (index >= 60) {
        return res
      }
      const activity_price_detail_id = res.result.resource_price?.activity_price_detail_id || res.result.big_sale_price?.activity_price_detail_id
      if (!activity_price_detail_id) {
        if (res.result.status == 104) {
          return res
        }
        return Promise.reject(res)
      }
      item.activity_price_detail_id = activity_price_detail_id
      return res
    }, 60, 0)
      .then(async res => {
        // console.log(res)
        if (!item.activity_price_detail_id) {
          item.activity_price_detail_id = res.result.resource_price?.activity_price_detail_id || res.result.big_sale_price?.activity_price_detail_id
        }
        if (!item.activity_price_detail_id) {
          // console.log(res)
          if (res.result.status == 104) {
            msgState.activityReject++
            log({
              msg: item.row.goods_name + ':活动被驳回:' + res.result.audit_list?.[0]?.action_desc,
              type: 'danger'
            }, item.row)
          } else {
            log({
              msg: `获取活动购买id失败`,
              type: 'danger'
            }, item.row)
            cancelActivity(store, item.activity_goods_id!, item.goods_id)
          }
          resetSkuQuantity(resolveGoodsData, 'recover', item.goods_id)
        }
      })
      .catch(e => {
        log({
          msg: '-获取活动详情失败',
          type: 'danger'
        }, item.row)
        cancelActivity(store, item.activity_goods_id!, item.goods_id)
        log({
          msg: String(e),
          type: 'danger'
        }, item.row)
      })
  }))

}

/**检查并取消活动 */
async function checkActivityParticipate(resolveGoodsData: ResolveGoodsData) {
  const { mall: store, activity_id, rows } = resolveGoodsData
  const activityGoodsListRes = await activityGoodsList(store, rows.map(item => item.goods_id), String(activity_id))
  const a_goods_list = (activityGoodsListRes.result.result as anyObj[]).filter(item => item.activity_id == Number(activity_id))
  if (a_goods_list.length) {
    log({
      msg: `检测到当前商品已经参加活动，将先取消活动`,
      type: 'success'
    })
    await Promise.allSettled(a_goods_list.map(async item => {
      await cancelActivity(store, item.activity_goods_id, item.goods_id)
    }))
  }

}

async function resetSkuQuantity(resolveGoodsData: ResolveGoodsData, type: 'clear' | 'recover', goods_id?: number) {


  // if (!isResetSku || resolveGoodsData.activity_id == '21365') {
  if (!isResetSku.value) {
    if (type === 'clear') {
      resolveGoodsData.rows.forEach(item => {
        item.isClearStockSuccess = true
      })

    }
    return { success: true }
  }
  log({
    msg: `正在执行${type === 'clear' ? '清除库存' : '恢复库存'}操作`,
  })
  const { rows, mall } = resolveGoodsData


  await Promise.allSettled(rows.filter(item => goods_id ? item.goods_id === goods_id : true).map(async item => {
    const res = await changeSkuQuantity(item.skuList.map((item: anyObj) => {
      const data = {
        goodsId: item.goods_id,
        quantity: item.quantity,
        skuId: item.sku_id
      }
      if (type === 'clear') {
        data.quantity = -Number(data.quantity)
      }
      return data
    }), mall)
    if (!res.success) {
      switch (type) {
        case 'clear': {
          log({
            msg: '清除库存失败:' + (res.errorMsg || ''),
            type: 'danger'
          }, item.row)
          item.skuList = []
          item.isClearStockSuccess = false
          break
        }
        case 'recover': {
          log({
            msg: '恢复库存失败:' + (res.errorMsg || ''),
            type: 'danger'
          }, item.row)
        }
      }
    } else {
      if (type === 'recover') {

        // log({
        //   msg: `库存操作成功`,
        //   type: 'success'
        // }, item.row)
      } else {
        item.isClearStockSuccess = true
      }
    }
  }))
  // if (type === 'clear') {
  //   resolveGoodsData.rows = resolveGoodsData.rows.filter(item => item.skuList.length)
  // }
  // if (!resolveGoodsData.rows.length) {
  //   log({
  //     msg: '没有清除库存成功的商品',
  //     type: 'warning'
  //   })
  //   return Promise.reject('没有清除库存成功的商品')
  // }
  let delay = 15
  if (type === 'recover') {
    delay = 0
  }
  await delayPromise(delay * 1000)
}

/**取消活动 */
async function cancelActivity(store: Store, activity_goods_id: number, goods_id: number, addToNoCacelSet = true) {
  log({
    msg: `准备取消活动:${activity_goods_id}`
  })
  const result = await activityCancel(store, activity_goods_id, goods_id)
  if (result.success) {
    log({
      msg: `${activity_goods_id},取消活动成功`,
      type: 'success'
    })
  } else {
    addToNoCacelSet && noCancelActivityGoods.add(goods_id)
    log({
      msg: `${activity_goods_id},取消活动失败：${result.error_msg || result.errorMsg || ''}`,
      type: 'danger'
    })
    return Promise.reject()
  }
}

async function createOrder(resolveGoodsData: ResolveGoodsData, repeat: (row: ResolveGoodsData['rows'][number]) => any) {
  console.log(resolveGoodsData)

  const { mall: store, rows } = resolveGoodsData
  const list = rows.filter(item => {
    return item.activity_price_detail_id
  })
  if (buttonLoading.stopFlag) {
    list.forEach(item => {
      item.activity_goods_id && cancelActivity(store, item.activity_goods_id!, item.goods_id)
    })
    return Promise.reject('停止执行')
  }
  if (!list.length) {
    return Promise.reject('没有参加活动的商品')
  }
  await new Promise(resolve => {
    const fn = async () => {
      const item = list.shift()
      if (!item) {
        resolve(true)
        return
      }
      // await resetSkuQuantity(resolveGoodsData, 'recover', item.goods_id)
      setTimeout(() => {
        resetSkuQuantity(resolveGoodsData, 'recover', item.goods_id)
      }, 10);
      const skuSelect = (item.row.sku_list as anyObj[]).find(_item => _item.skuId == item.sku_select_id)
      const requestData = {
        account: item.account,
        sku: item.sku_select_id,
        sku_spec: skuSelect?.spec || item.row.goods_name,
        shop_id: store.mallId,
        shop_name: store.mallName,
        goods_id: item.goods_id,
        num: item.num,
        activity_id: item.activity_price_detail_id!,
        goods_name: item.row.goods_name,
        group_id: item.group_id!.toString(),
        use_coupon: false,
        type: 'activity',
        ...item.addressData
      }
      // console.log(requestData)
      return retry(async () => {
        if (buttonLoading.stopFlag) {
          return Promise.reject({ msg: '停止执行' })
        }
        return quickOrder(requestData, { showErrorMsg: false })
          .then(res => {
            msgState.success++
            log({
              msg: `${requestData.goods_name}:下单成功。订单号:${res.data.order_sn}`,
              type: 'success'
            }, item.row)
            res.data.shop_id = res.data.shop_id || store.mallId
            changeOrderPrice(res.data, 0)
          })
      }, 3)
        .catch(res => {
          log({
            msg: `${requestData.goods_name}:下单失败:${res.msg}`,
            type: 'danger'
          }, item.row)
          // repeat
          const count = cache.reapeat[requestData.goods_id]
          if (!count || count < 3) {
            repeat(item)
            cache.reapeat[requestData.goods_id] = count ? count + 1 : 1
          }
        })
        .finally(async () => {
          await Promise.allSettled([cancelActivity(store, item.activity_goods_id!, item.goods_id)])
          fn()
        })
    }
    fn()
  })
  // await Promise.allSettled(list.map(async item => {
  //   const skuSelect = (item.row.sku_list as anyObj[]).find(_item => _item.skuId == item.sku_select_id)
  //   const requestData = {
  //     account: item.account,
  //     sku: item.sku_select_id,
  //     sku_spec: skuSelect?.spec || item.row.goods_name,
  //     shop_id: store.mallId,
  //     shop_name: store.mallName,
  //     goods_id: item.goods_id,
  //     num,
  //     activity_id: item.activity_price_detail_id!,
  //     goods_name: item.row.goods_name,
  //     group_id: item.group_id!.toString(),
  //     use_coupon: false,
  //     ...item.addressData
  //   }
  //   // console.log(requestData)
  //   return retry(async () => {
  //     return quickOrder(requestData, { showErrorMsg: false })
  //       .then(res => {
  //         log({
  //           msg: `${requestData.goods_name}:下单成功。订单号:${res.data.order_sn}`,
  //           type: 'success'
  //         })
  //         changeOrderPrice(res.data, 0)
  //       })
  //   }, 3)
  //     .catch(res => {
  //       log({
  //         msg: `${requestData.goods_name}:下单失败:${res.msg}`,
  //         type: 'danger'
  //       })
  //     })
  //     .finally(() => {
  //       cancelActivity(store, item.activity_goods_id!, item.goods_id)
  //     })
  // }))
}

async function changeOrderPrice(orderInfo: anyObj, priceDiff: number) {
  // console.log(orderInfo)
  const { type, order_amount, shop_id, shop_name, order_sn, id } = orderInfo;

  let goodsDiscount: number;
  if (type === "pifa") {
    goodsDiscount = Number(order_amount - 0.01 * 100);
  } else {
    goodsDiscount = Number(order_amount - (order_amount + priceDiff * 100) * 0.1);
  }
  // console.log('[changeOrderPrice]:', orderInfo, priceDiff, goodsDiscount)
  log({
    msg: `订单${order_sn},将执行改价操作`,
  });
  await delayPromise(3000);
  changeOrderPriceRequest({
    store_id: shop_id,
    shop_name: shop_name,
    goodsDiscount,
    order_sn: order_sn,
  })
    .then((res) => {
      // console.log("改价成功", res);
      log({
        type: "success",
        msg: `订单${order_sn},改价成功`,
      });
      updateOrder(
        {
          ids: order_sn,
        },
        { showErrorMsg: false }
      ).catch((res) => {
        console.log(res);
      });

      execAutoApply(order_sn);
    })
    .catch((res) => {

      log({
        type: "danger",
        msg: `订单${order_sn},改价失败:${res.msg}`,
      });
      const { chance } = settingStore.pay;
      if (chance === "immediate") {
        log({
          msg: "改价失败不会启动自动支付",
          type: "warning",
        });
      }
    });
}

async function otherActivity(resolveGoodsData: ResolveGoodsData) {
  if (!config.isCancelActivity) {
    return
  }
  const { mall, rows } = resolveGoodsData
  const listRes = await marketingList(mall, { goods_id: rows.map(item => item.goods_id) })
  log({
    msg: '开始检测商品是否在参与限时限量购',
  })
  const map = new Map<number, ResolveGoodsData['rows'][number]>();
  resolveGoodsData.rows.forEach(item => {
    map.set(item.goods_id, item)
  })
  try {
    if (listRes.success) {
      const marketing_activity_list = (listRes.result.marketing_activity_list as anyObj[])
      await Promise.allSettled(
        marketing_activity_list.map(async item => {
          // activity_type 3 限量  12 限时
          const { goods_id, activity_type, activity_id, activity_name } = item
          if ((activity_type == 3 || activity_type == 12)) {
            const row = map.get(goods_id)
            if (row) {
              log({
                msg: `检测到参与${activity_name},准备取消`,
                type: 'warning'
              }, item.row)
              const cancelRes = await marketingStop(mall, { goods_id, activity_id })
              if (cancelRes.success) {
                log({
                  msg: `${activity_name}结束成功`,
                  type: 'success'
                }, item.row)
              } else {
                log({
                  msg: `${activity_name}结束失败,${cancelRes.error_msg}`,
                  type: 'danger'
                }, item.row)
              }
            }
          }

        })
      )
    } else {
      log({
        msg: `获取限时限量购活动列表失败:${listRes.error_msg}`,
        type: 'danger'
      })
    }
  } catch (e) {
    log({
      msg: `限时限量购意外的错误`,
      type: 'danger'
    })
  }

}

</script>
<style src="../../css.scss" scoped></style>
<style lang='scss' rel="stylesheet/scsss" scoped>
.table-container {
  .vxe-table {
    @include commonTableHeader();
  }
}
</style>
