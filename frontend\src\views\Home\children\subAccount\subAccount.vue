<template>
  <div class="sub-account no-padding" v-loading="state.pageLoading">
    <div class="padding">
      <!-- <div class="top">

        <template v-if="!userStore.agent?.open_serve">
          <div class="balance">

            <strong class="danger">
              余额:{{ userStore.user_info.balance }}金币
              <el-link :underline="false" type="primary" @click="userStore.refreshUserInfo">
                <el-icon class="m-l-5">
                  <Refresh />
                </el-icon>

              </el-link>
            </strong>
            <p class="btns">
              <el-button type="danger" v-blur @click="
                createNewWindow({ url: clientUrl('/specil/recharge', {}, true), title: '充值', width: 1280, height: 800 })
                  .then(async res => {
                    await delayPromise(1000)
                    userStore.refreshUserInfo()
                  })
                ">充值金币</el-button>

            </p>
          </div>
          <el-divider direction="vertical"></el-divider>
        </template>

<div class="price" @click="getAccountCommbo">

  <strong class="danger">{{ currentCommbo?.price || "-" }}金币/个</strong>
  <p class="old-price">原价：<del>2.0金币/个</del></p>
</div>


<el-divider direction="vertical"></el-divider>
<div class="content">
  <p>
    购买数量：
    <el-input-number :min="1" :precision="0" controls-position="right" v-model="state.num"></el-input-number>
    <el-button :disabled="!currentCommbo || buttonLoading.buyAccount" type="primary" v-blur @click="buyAccount"
      :loading="buttonLoading.buyAccount">购买</el-button>
  </p>
  <p class="tips">
    <strong class="danger">一个小号最多使用{{ subAccoutStore.useLimit }}次，订单确认收货评价支持15天内</strong>
  </p>

</div>
-->
      <el-config-provider size="default">
        <div class="table-header">
          <el-space :size="10">
            <el-input placeholder="搜索小号" v-model="accountState.searchKey"></el-input>
            <el-button v-blur @click="accountCheck" :loading="buttonLoading.check"
              :disabled="buttonLoading.check">检测小号</el-button>

            <el-button title="只有还能用的小号才可恢复" v-blur @click="accountRecover" type="success"
              :loading="buttonLoading.recover" :disabled="buttonLoading.recover">恢复删除小号</el-button>


            <el-link :underline="false" type="primary" @click="getList()">
              <el-icon class="m-r-5">
                <Refresh />
              </el-icon>
              同步小号
            </el-link>
          </el-space>

          <div class="actions">
            <el-input type="textarea" resize="none" :rows="1" placeholder="请输入小号卡密" class="m-r-8"
              v-model="state.accountCami"></el-input>
            <el-button v-blur type="primary" @click="exchangeSubAccount()"
              :disabled="!state.accountCami || buttonLoading.getCami" :loading="buttonLoading.getCami">读取小号</el-button>
            <el-divider direction='vertical'></el-divider>
            <el-button v-blur type="success" @click="importSubAccount()" :loading="buttonLoading.uploadCami"
              :disabled="buttonLoading.uploadCami">
              <span>导入小号</span>
              <span v-show="buttonLoading.uploadCami">{{ importAccountState.percentage }}%</span>
            </el-button>
            <!-- <el-button v-blur type="primary" plain @click="onlineBuy"
              :loading="buttonLoading.onlineBuy">在线取号</el-button> -->
          </div>
        </div>
      </el-config-provider>
      <el-dialog title="导入分析" :width="480" class="dialog-480" v-model="importAccountState.logShow">
        <el-form>
          <el-form-item lable="展示">
            <el-radio-group v-model="importAccountState.showType">
              <el-radio-button :label="-1">全部({{ importAccountState.logs.length }})</el-radio-button>
              <el-radio-button :label="0">仅成功</el-radio-button>
              <el-radio-button :label="1">仅失败</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <!-- <el-scrollbar height="400px"> -->
        <div class="able-select" v-loading="buttonLoading.uploadCami" element-loading-text="导入中">
          <VxeList
            :data="importAccountState.logs.filter(item => importAccountState.showType < 0 ? true : item.code === importAccountState.showType)"
            :height="400">
            <template #default="{ items }">
              <p v-for="item, index in items" :class="item.code ? 'danger' : 'success'">
                {{ item.card }}: {{ item.msg }}
              </p>
            </template>
          </VxeList>

        </div>
        <!-- </el-scrollbar> -->
      </el-dialog>
      <div class="table-container able-select" v-drop @drop="tableDrop">
        <!-- <el-table :border="true" height="520" v-loading="state.tableLoading"
          :data="accountState.showList.slice((accountState.page - 1) * accountState.limit, accountState.limit * accountState.page)">
          <el-table-column label="序号" width="80" type="index">
            <template #default='scope'>
              {{ scope.$index + 1 + (accountState.page - 1) * (accountState.limit) }}
            </template>
          </el-table-column>
          <el-table-column label="小号" prop="account"></el-table-column>
          <el-table-column label="使用次数" prop="use_num"></el-table-column>
          <el-table-column label="状态" prop="status.status" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.status.status == 1 && scope.row.use_num >= subAccoutStore.useLimit"
                type="warning">已用完</el-tag>
              <el-tag v-else-if="scope.row.status.status == 1" type="success">正常</el-tag>
              <el-tag v-else type="info">失效</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="add_time" width="170">
            <template #default="scope">
              {{
                dayjs(scope.row.add_time * 1000).format("YYYY-MM-DD HH:mm:ss")
              }}
            </template>
          </el-table-column>
          <el-table-column label="剩余时间" prop="past_time" width="100">
            <template #default="scope">
              {{
                // ((scope.row.past_time - scope.row.add_time)) + '秒'
                getLessTime((scope.row.past_time * 1000 - Date.now()))
              }}
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="action" width="80">
            <template #default='scope'>
              <el-link :underline="false" type="danger" @click="deleteSelect([scope.row])">删除</el-link>
            </template>
          </el-table-column>
        </el-table>

        <ds-pagination v-model:current-page="accountState.page" v-model:page-size="accountState.limit"
          :total="accountState.total" :small="true" @current-change="getList()" @size-change="getList()"
          :page-sizes="[100, 200, 500, 1000, 2000]" /> -->
        <vxe-table border :column-config="{ resizable: true }" :loading="state.tableLoading" stripe
          :data="accountState.showList" height="650" :row-config="{ height: 40 }" show-header-overflow="tooltip"
          show-overflow="tooltip">
          <vxe-column type="seq" width="100" title="序号"></vxe-column>
          <vxe-column field="account" title="小号"></vxe-column>
          <vxe-column field="use_num" title="使用次数"></vxe-column>
          <vxe-column field="status.status" title="状态" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.status.status == 1 && scope.row.use_num >= subAccoutStore.useLimit"
                type="warning">已用完</el-tag>
              <el-tag v-else-if="scope.row.status.status == 1" type="success">正常</el-tag>
              <el-tag v-else type="info">失效</el-tag>
            </template>
          </vxe-column>

          <vxe-column field="add_time" title="创建时间">
            <template #default="scope">
              {{
              dayjs(scope.row.add_time * 1000).format("YYYY-MM-DD HH:mm:ss")
              }}
            </template>
          </vxe-column>
          <vxe-column field="past_time" title="剩余时间">
            <template #default="scope">
              {{
              // ((scope.row.past_time - scope.row.add_time)) + '秒'
              getLessTime((scope.row.past_time * 1000 - Date.now()))
              }}
            </template>
          </vxe-column>

          <vxe-column filed="action" title="操作" width="120">
            <template #default='scope'>
              <el-link :underline="false" type="danger" @click="deleteSelect([scope.row])">删除</el-link>
            </template>
          </vxe-column>
        </vxe-table>

        <footer>
          共 <span class="primary">{{ accountState.total }} / {{ subAccoutStore.total }}</span> 条数据
        </footer>
      </div>
    </div>
    <el-config-provider size="default">
      <footer class="controls">
        <el-space :size="6">
          <span class="f-s-14" style="color: var(--el-text-color-regular)">删除小号不影响收货，评价等任何功能，只为方便管理下单
            <!-- <span class="danger">(只能从当前页筛选)</span> -->
          </span>
          <el-button v-blur @click="openAccountDel(1)">按序号删</el-button>
          <el-button v-blur @click="openAccountDel(2)">按日期删</el-button>
          <el-button v-blur @click="accountDel(3)">删除无效</el-button>
          <el-button v-blur @click="accountDel(4)">删除已用完</el-button>
        </el-space>
      </footer>
    </el-config-provider>


    <el-dialog :width="400" :title="accountDelState.title" v-model="accountDelState.dialog" class="dialog-480">
      <el-form label-position="top">
        <el-form-item label="输入序号" v-show="accountDelState.type === 1">
          <el-space :size="8">
            <el-input-number :controls="false" v-model="accountDelState.ind_min" :min="0"
              :max="accountDelState.ind_max"></el-input-number>
            <span>至</span>
            <el-input-number :controls="false" v-model="accountDelState.ind_max" :min="accountDelState.ind_min"
              :max="subAccoutStore.list.length"></el-input-number>
          </el-space>
        </el-form-item>
        <el-form-item label="选择日期" v-show="accountDelState.type === 2">
          <el-date-picker v-model="accountDelState.date" type="datetimerange" range-separator="至"
            start-placeholder="开始时间" end-placeholder="结束时间" value-format="x" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="accountDelState.dialog = false">取消</el-button>
        <el-button :loading="buttonLoading.delete" :disabled="buttonLoading.delete" type="primary"
          @click="accountDel()">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog v-if="onlineBuyState.show" title="提取小号" :width="480" v-model="onlineBuyState.show"
      :close-on-click-modal="false">
      <el-alert type="success" :closable="false" size="default">
        <p>
          <span style="color: var(--el-color-primary)">
            价格：{{ currentCommbo?.price || "-" }}/个
          </span>
          <del style="margin-left: 20px; color: gray;" class="old-price">原价：2.0/个</del>
        </p>
      </el-alert>
      <el-form label-position="top" style="margin-top: 20px">
        <el-form-item label="提取数量">
          <el-input-number :precision="0" :min="1" v-model="onlineBuyState.num"></el-input-number>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-radio-group v-model="onlineBuyState.pay_type">
            <el-radio label="wxpay">微信</el-radio>
            <el-radio label="alipay">支付宝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="二维码">
          <div class="img-box" v-if="onlineBuyState.payImg" style="
          border: var(--el-border);
          width: 300px;
          height: 300px;
          padding: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          ">
            <img :src="onlineBuyState.payImg" alt="" style="max-width: 100%;max-height: 100%">

            <p style="font-size: 40px;color: var(--el-color-primary);margin-top: 10px">{{ onlineBuyState.price }}</p>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onlineBuyState.show = false">取消</el-button>
        <el-button v-if="!onlineBuyState.payImg" type="primary" @click="getPayQrcode"
          :loading="buttonLoading.buyAccount">提取</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { Refresh } from "@element-plus/icons-vue";
  import { ElMessage, ElMessageBox } from "element-plus";
  import { computed, reactive, ref, watchEffect } from "vue";
  import { accountBuy, accountCommbo, getAccountList, accountDelete, checkAccount, exchangeSubAccount as exchangeSubAccountRequest, recoverAccount } from "/@/apis/page";
  import dayjs from "dayjs";
  import { useSubAccount } from "/@/stores/pageData";
  import { clientUrl, delayPromise, getLessTime } from "/@/utils/common";
  import { openExternal, createNewWindow, showOpenDialog, resolveExcel, readTXT } from "/@/apis/mainWindow";
  import { useUserStore } from "/@/stores/user";
  import QRCode from 'qrcode'
  import { buyGoods, getGoodsList, getPayResult } from "/@/apis/onlineService";
  const subAccoutStore = useSubAccount()
  const userStore = useUserStore()

  const accountState: {
    showList: typeof subAccoutStore.list
    total: number
    searchKey: string
  } = reactive({
    showList: [],
    total: 0,
    limit: 100,
    searchKey: '',
    page: 1
  })

  watchEffect(() => {
    const { list } = subAccoutStore
    console.log(list, '??什么');

    const { searchKey } = accountState
    const filterList = list.filter(item => item.account.toString().includes(searchKey))
    accountState.total = filterList.length
    accountState.showList = filterList
  })

  const state: {
    pageLoading: boolean;
    tableLoading: boolean;
    commboList: Array<{
      ID: number;
      name: string;
      service_id: number;
      price: string;
      add_time?: number;
      use_date: string;
    }>;
    num: number;

    accountCami: string
  } = reactive({
    pageLoading: false,
    tableLoading: false,
    commboList: [],
    num: 1,
    accountCami: ""
  });

  const buttonLoading = reactive({
    recover: false,
    check: false,
    uploadCami: false,
    getCami: false,
    delete: false,
    commbo: false,
    buyAccount: false,
    /**在线取号 */
    onlineBuy: false
  })

  const currentCommbo = computed(() => {
    if (state.commboList.length) {
      return state.commboList[0];
    } else {
      return null;
    }
  });


  function getList () {
    state.tableLoading = true;
    subAccoutStore.getList().finally(() => {
      state.tableLoading = false;
    });
  }
  getList();

  // function buyAccount() {
  //   if (currentCommbo.value) {
  //     buttonLoading.buyAccount = true
  //     const { num } = state;
  //     const { ID, service_id } = currentCommbo.value;
  //     accountBuy({
  //       num,
  //       service_id: service_id,
  //       id: ID,
  //     })
  //       .then((res) => {
  //         getList();
  //         userStore.refreshUserInfo()
  //         ElMessage.success("购买成功");
  //       })
  //       .finally(() => {
  //         buttonLoading.buyAccount = false;
  //       });
  //   }
  // }



  function getAccountCommbo () {
    if (buttonLoading.commbo) {
      return
    }
    buttonLoading.commbo = true
    getGoodsList().then((res) => {
      state.commboList = res.data;
      // console.log(res.data)
    }).finally(() => {
      buttonLoading.commbo = false
    })
  }
  getAccountCommbo();

  const accountDelState: {
    type: 1 | 2 | 3 | 4;
    title: string;
    dialog: boolean;
    ind_min: number;
    ind_max: number;
    date?: [number, number];

  } = reactive({
    type: 1,
    title: "",
    dialog: false,
    ind_max: 1,
    ind_min: 1,
    date: void 0,

  });

  function openAccountDel (type: typeof accountDelState["type"]) {
    accountDelState.type = type;
    let title = "";
    switch (type) {
      case 1: {
        title = "按序号删除";
        break;
      }
      case 2: {
        title = "按日期删除";
        break;
      }
    }
    accountDelState.title = title;
    accountDelState.dialog = true;
  }
  /**检查小号 */
  function accountCheck () {
    buttonLoading.check = true
    checkAccount()
      .then(() => {
        ElMessage.success('检查完毕')
        return getList()
      })
      .finally(() => {
        buttonLoading.check = false
      })
  }

  function deleteSelect (list: typeof accountState.showList) {
    if (list.length) {

      accountDelete({
        account: list.map(item => item.account).join(',')
      })
        .then(res => {
          console.log(res)
          ElMessage.success('删除成功')
          getList()
        })
        .finally(() => {
        })
    } else {
      ElMessage.warning({
        message: "无可删除小号",
        grouping: true
      })

    }
  }

  /**恢复已删除的可用小号 */
  function accountRecover () {
    buttonLoading.recover = true
    recoverAccount()
      .then((res) => {
        console.log(res, 'aa');

        return getList()
      })
      .finally(() => {
        buttonLoading.recover = false
      })
  }

  function accountDel (type = accountDelState.type) {
    if (!subAccoutStore.list.length) {
      return ElMessage.warning({
        message: '小号列表没有数据',
        grouping: true
      })
    }
    let { date, ind_max, ind_min } = accountDelState;
    let list: typeof subAccoutStore.list = [];
    switch (type) {
      case 1: {
        ind_min = ind_min - 1 < 0 ? 0 : ind_min - 1;
        list = subAccoutStore.list.slice(ind_min, ind_max);
        break;
      }
      case 2: {
        if (date) {
          const [time_min, time_max] = date
          list = subAccoutStore.list.filter((item) => {
            let time = item.add_time * 1000
            return time >= time_min && time <= time_max
          });
        } else {
          ElMessage.warning("没有选择时间");
          return;
        }

        break;
      }
      case 3: {
        list = subAccoutStore.list.filter(item => {
          return item.status.status != 1
        })
        break;
      }
      case 4: {
        list = subAccoutStore.list.filter(item => item.use_num >= subAccoutStore.useLimit)
        break;
      }
    }
    if (list.length) {
      // state.pageLoading = true
      buttonLoading.delete = true
      accountDelete({
        account: list.map(item => item.account).join(',')
      })
        .then(res => {
          ElMessage.success('删除成功')
          accountDelState.dialog = false
          getList()
        })
        .finally(() => {
          // state.pageLoading = false
          buttonLoading.delete = false
        })
    } else {
      ElMessage.warning({
        message: "无可删除小号",
        grouping: true
      })
      accountDelState.dialog = false
    }

  }

  const importAccountState: {
    logs: Array<{ code: number, msg: string, card: string }>
    logShow: boolean
    percentage: number
    showType: number
  } = reactive({
    percentage: 0,
    logs: [],
    logShow: false,
    showType: -1
  })

  function exchangeSubAccount (list = state.accountCami.split('\n').filter(Boolean)) {
    console.log(list, state.accountCami)
    if (!list.length) {
      return ElMessage.warning({
        message: "没有可上传的卡密",
        grouping: true
      })
    }
    // if (type === 'input') {
    //   buttonLoading.getCami = true
    //   exchangeSubAccountRequest({
    //     card: list[0]
    //   })
    //     .then(res => {
    //       ElMessage.success('获取成功')
    //       getList()
    //     })
    //     .finally(() => {
    //       buttonLoading.getCami = false
    //     })
    //   return
    // }
    // console.log(list)
    //   return
    importAccountState.logShow = true
    importAccountState.logs = []
    buttonLoading.uploadCami = true
    buttonLoading.getCami = true
    const taskList: Array<typeof list> = []
    while (list.length) {
      taskList.push(list.splice(0, 100))
    }

    const batchMax = 1
    let count = 0
    function requestRepeat () {
      const requestList = taskList.slice(count, batchMax + count)

      count += requestList.length
      const requestPromiseArr = requestList.map(item => {
        return exchangeSubAccountRequest({
          card: item.join(',')
        }, { showErrorMsg: false })
          .then(res => {
            // console.log('*********', res)
            importAccountState.logs.push(...res.data)
          }).catch(res => {
            console.log('error', res)
            importAccountState.logs.push({
              code: 1,
              msg: res.msg,
              card: "系统提示"
            })
          })
      })

      Promise.allSettled(requestPromiseArr)
        .then(res => {
          importAccountState.percentage = Math.floor((count / taskList.length) * 100)
          if (count >= taskList.length) {
            buttonLoading.uploadCami = false
            buttonLoading.getCami = false
            // ElMessage.success('获取成功')
            getList()
            state.accountCami = ''
          } else {
            if (buttonLoading.uploadCami) {
              requestRepeat()
            }
          }
        })

    }
    requestRepeat()
  }

  async function importSubAccount (path?: string) {
    let filePath = path
    if (!filePath) {
      const result = await showOpenDialog({
        properties: ['createDirectory'],
        filters: [{ extensions: ['txt'], name: '' }]
      })
      if (result.canceled) {
        return ElMessage({
          message: '取消文件选择'
        })
      } else {
        filePath = result.filePaths[0]
      }
    }

    if (!filePath) {
      return ElMessage.warning('没有文件路径')
    }
    const res = await readTXT({ url: filePath })
    const camis = res.split(/[\r\n]/).filter(item => item)
    // console.log(res,camis)
    exchangeSubAccount(camis)
  }

  function tableDrop (event: DragEvent) {
    // console.log(event)
    if (buttonLoading.uploadCami) {
      return ElMessage.warning({
        message: '当前有任务在上传,请稍后',
        grouping: true
      })
    }
    const files = event.dataTransfer?.files
    if (files && files.length) {
      const excelFilePaths: string[] = []
      for (let i = 0; i < files.length; i++) {
        const path = files[i].path
        if (path.endsWith('.txt')) {
          excelFilePaths.push(path)
        }
      }
      if (!excelFilePaths.length) {
        return ElMessage.warning({
          message: "选择的文件中没有txt格式的文件",
          grouping: true
        })
      }
      const camis: string[] = []
      Promise.all(excelFilePaths.map(path => {
        return readTXT({ url: path }).then(res => {
          res.split(/[\r\n]/).forEach(item => {
            if (item) {
              camis.push(item)
            }
          })
        })
      }))
        .then(() => {
          if (!camis.length) {
            ElMessage.warning({
              message: "选择的文件中没有获取到卡密数据",
              grouping: true
            })
          } else {
            exchangeSubAccount(camis)
          }
        })

      return
    }
  }
  const onlineBuyState = ref({
    num: 10,
    show: false,
    payImg: "",
    pay_type: "alipay",
    checkId: '',
    price: 0
  })



  function getPayQrcode () {
    if (!currentCommbo.value) {
      return
    }
    buttonLoading.buyAccount = true
    const { num, pay_type } = onlineBuyState.value
    const { ID } = currentCommbo.value;
    // console.log(currentCommbo.value)
    const reqData = {
      pay_type,
      num,
      id: ID,
    }
    buyGoods(reqData)
      .then(res => {
        // console.log(res)
        onlineBuyState.value.price = res.data.money
        onlineBuyState.value.checkId = res.data.id
        QRCode.toDataURL(res.data.qrcode)
          .then(imgBase64 => {
            onlineBuyState.value.payImg = imgBase64
          })
        checkPayStatus()
      })
      .finally(() => {
        buttonLoading.buyAccount = false
      })
  }

  let tid: NodeJS.Timeout | undefined = void 0
  function checkPayStatus () {
    if (!onlineBuyState.value.show) {
      return
    }
    getPayResult({
      id: onlineBuyState.value.checkId,
    }, { showErrorMsg: false })
      .then(res => {
        console.log(res, 'success')
        subAccoutStore.getList()
        onlineBuyState.value.show = false
      })
      .catch((res) => {
        tid = setTimeout(() => {
          checkPayStatus()
        }, 2000);
      })
  }


  async function onlineBuy () {
    getAccountCommbo()
    onlineBuyState.value.show = true
    onlineBuyState.value.payImg = ''
    // QRCode.toDataURL('123123')
    //   .then(imgBase64 => {
    //     // payInfo.imgBase64 = imgBase64
    //     onlineBuyState.value.payImg = imgBase64
    //   })
  }
</script>
<style lang="scss" rel="stylesheet/scsss" scoped>
  .sub-account {
    overflow: hidden;
    height: 787px;
    position: relative;
  }

  .padding {
    padding: 0 24px;
  }

  .top {
    height: 88px;
    padding: 16px;
    box-sizing: border-box;
    border-radius: 6px;
    // border: var(--el-border);
    border: 1px solid var(--el-border-color-darker);
    background-color: var(--el-fill-color-darker);
    display: flex;

    .price,
    .balance {
      strong {
        font-size: 18px;
        display: flex;
        align-items: center;
      }

      p {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-top: 7px;
      }

      p.btns {
        display: flex;
        justify-content: space-evenly;

        .el-button {
          flex-grow: 1;
        }
      }
    }

    .el-divider {
      height: 100%;
      margin: 0 16px;
    }

    .content {
      font-size: 14px;

      .el-input-number {
        margin: 0 16px;
      }

      p.tips {
        font-size: 12px;
        color: var(--el-text-color-regular);
        line-height: 20px;
        margin-top: 8px;
      }
    }
  }

  .table-header {
    // height: ;
    // margin: 16px 0 5px 0;
    margin: 0px 0 5px 0;
    display: flex;
    justify-content: space-between;

    .actions {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .table-container {
    border: var(--el-border);
    // box-shadow: var(--diy-shadow);

    .el-table {
      @include commonTableHeader();
    }

    footer {
      height: 30px;
      display: flex;
      align-items: center;
      padding: 10px;
      background-color: #fff;
      justify-content: flex-end;
      padding: 10px;
      box-sizing: border-box;
      font-size: 12px;

      span {
        margin: 0 10px;
      }
    }

    :deep(.pagination) {
      padding: 10px;
      background-color: #fff;
      border-top: var(--el-border);
    }
  }

  footer.controls {
    margin-top: 16px;
    height: 70px;
    background-color: var(--el-bg-color-page);
    border: var(--el-border);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 24px;
  }
</style>