const { Controller } = require("ee-core");
const axios = require("axios");
const { userAgent } = require("../config/config.app");
const FormData = require('form-data')

class storesController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。invoke()方法时，event == IpcMainInvokeEvent; send()/sendSync()方法时，event == IpcMainEvent
   */

  async getStoresList({ page, limit }) {
    limit = limit || 20;
    page = page || 1;
    return this.service.stores.getStoresList({ limit, page });
  }

  async addStoreItem(data) {
    return this.service.stores.addStoreItem(data);
  }

  async deleteStoreItem(mallId) {
    return this.service.stores.deleteStoreItem(mallId);
  }
  async updateStore(data) {
    return this.service.stores.updateStore(data);
  }
  async findStoreItem(mallId) {
    return this.service.stores.findStoreItem(mallId);
  }

 


  async uploadImageToStore({headers,image,signature}){
    const formData = new FormData();
    formData.append('upload_sign',signature)
    const buffer = Buffer.from(image,'base64')
    formData.append('image',buffer)
    try{
      const response = await axios({
        url:"https://file.pinduoduo.com/v3/store_image",
        method:"post",
        headers:{
          ...headers,
          "User-Agent":userAgent,
          'Content-Type': 'application/octet-stream'
        },
        data:formData
      })
      return response.data
    }catch(e){
      return e.response.data
    }

  }
}

storesController.toString = () => "[class storesController]";
module.exports = storesController;
