import { AxiosRequestConfig } from "axios"
import { pddStoreRequestHeader } from "./changeSales"
import { anyRequest, storeAntiContent } from "./page"

// const pifaCookieMap = new Map<Store['mallId'], { cookie: string, addTime: number }>()
// export async function generateAccessToken(mall: Store) {
//     const config = {
//         url: 'https://mms.pinduoduo.com/janus/api/subSystem/generateAccessToken',
//         method: 'post',
//         headers: await pddStoreRequestHeader(mall, {
//             referer: 'https://mms.pinduoduo.com/login/sso?platform=wholesale&redirectUrl=https%3A%2F%2Fpifa.pinduoduo.com%2Fclint%2Fapi%2Flogin%3Fredirect%3Dhttps%253A%252F%252Fpifa.pinduoduo.com%252F',
//         })
//     }
//     return anyRequest(config)
// }

// export async function getPifaCookie(mall: Store) {
//     let accessToken = ''
//     const tokenRes = await generateAccessToken(mall)
//     console.log(tokenRes)
//     if (tokenRes.success) {
//         accessToken = tokenRes.result.accessToken
//     }else{
//         return Promise.reject()
//     }

//     const config: AxiosRequestConfig = {
//         url: "https://pifa.pinduoduo.com/clint/api/login",
//         method: 'get',
//         params: {
//             redirect: 'https%3A%2F%2Fpifa.pinduoduo.com%2F&',
//             accessToken
//         },
//         headers: await pddStoreRequestHeader(mall, {
//             "upgrade-insecure-requests": 1,
//             authority: "pifa.pinduoduo.com",
//             referer: 'https://mms.pinduoduo.com/'
//         })
//     }
//     const res = await anyRequest(config)
//     console.log(res)

// }
// export async function acquireQrCode(mall: Store) {
//     const config: AxiosRequestConfig = {
//         url: "https://pifa.pinduoduo.com/pifa/pc/order/acquireQrCode",
//         method: 'get',
//         headers:{
//             "anti-content":await storeAntiContent(),
//             Cookie:pifaCookieMap.get(mall.mallId)?.cookie,
//             "Origin":"https://pifa.pinduoduo.com",
//             referer:"https://pifa.pinduoduo.com/goods/detail/"
//         }
//     }
//     return anyRequest(config)
// }

export async function acquireQrCode(data: {
    "subScene": 1,
    "createOrderDTO":
    {
        "orderType": 1,
        "createOrderItemList": Array<{
            goodsId: number
            skuId: number
            skuNum: number
        }>,

        // [{ "goodsId": "************", "skuId": "1562269110195", "skuNum": "2" }]
    }
}) {
    const config: AxiosRequestConfig = {
        url: "https://mms.pinduoduo.com/pifa/bapp/order/acquireQrCode",
        method: 'post',
        headers: {
            "Accept": 'application/json',
            // "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "anti-content": await storeAntiContent(),
            "Connection": 'Keep-Alive',
            // "Content-Length": 135,
            "Content-Type": "application/json;charset=UTF-8",
            "Host": 'mms.pinduoduo.com',
            "Origin": "https://mai.pinduoduo.com",
            "Referer": "https://mai.pinduoduo.com/",
            "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36 Edg/87.0.664.66"
        },
        data
    }
    const res = await anyRequest(config)
    return res



}