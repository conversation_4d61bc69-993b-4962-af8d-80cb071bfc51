<template>
    <div class="log">
        <header class="between-flex">
            <slot name="header">
                <slot name="header-left">
                    <span class="f-s-12">任务日志</span>
                </slot>
                <div class="right">
                    <slot name="header-right">

                        <el-button type="primary" size="small" @click="emits('update:list', [])">清空</el-button>
                    </slot>
                    <el-button type="primary" plain @click="state.dialog = true" size="small">打开日志</el-button>
                </div>
            </slot>
        </header>
        <div class="container" ref="container" :style="{ height }">
            <div class="wrapper">
                <p class="item" :class="item.type && `${item.type}`" v-for="item in list.slice(-50)">
                    <span class="time" v-if="item.time">{{ item.time }}</span>
                    <span class="msg"> {{ `${item.msg}` }}</span>
                </p>
            </div>
        </div>
        <el-dialog :destroy-on-close="true" :append-to-body="true" class="log-dialog" v-model="state.dialog" top="80px"
            title="任务日志" width="1000px">
            <div class="container" ref="container2" style="height: 530px;">
                <!-- <vxe-list :data="list" :="listProps" ref="listRef">
                    <template #default="{ items }"> -->
                <div class="wrapper">
                    <p class="item" :class="item.type && `${item.type}`" v-for="item in (props.list as LogItem[])">
                        <span class="time" v-if="item.time">{{ item.time }}</span>
                        <span class="msg"> {{ `${item.msg}` }}</span>
                    </p>
                </div>
                <!-- </template>
</vxe-list> -->
            </div>
        </el-dialog>
    </div>
</template>

<script lang='ts' setup>
import { onActivated } from 'vue';
import { watch, ref, nextTick } from 'vue';
import { useList } from '/@/hooks/useList';
import { VxeListInstance } from 'vxe-table';
const listRef = ref<VxeListInstance>()
const state = ref({
    dialog: false
})
const { listProps } = useList(530, {
    autoResize: true,
    scrollY: {
        gt: 0,
        sItem: '.item',
        oSize: 10
    }
}, { ListInstance: listRef })
const emits = defineEmits(['update:list'])
const props = withDefaults(defineProps<{
    list: LogItem[],
    height?: string
}>(), {
    height: '100px'
})
const container = ref<HTMLElement>()
const container2 = ref<HTMLElement>()
watch(() => props.list.length, (len) => {
    // if (len > 1000) {
    //     emits('update:list', props.list.slice(900, 1000))
    // }

    nextTick(() => {
        const wrapper = container.value?.querySelector('.wrapper')
        container.value?.scrollTo({ top: wrapper?.clientHeight, behavior: 'smooth' })
        const wrapper2 = container2.value?.querySelector('.wrapper')
        container2.value?.scrollTo({ top: wrapper2?.clientHeight, behavior: 'auto' })
        // listRef.value?.scrollTo(null,props.list.length * 24)
    })
})

onActivated(() => {
    nextTick(() => {
        const wrapper = container.value?.querySelector('.wrapper')
        container.value?.scrollTo({ top: wrapper?.clientHeight, behavior: 'auto' })
        const wrapper2 = container2.value?.querySelector('.wrapper')
        container2.value?.scrollTo({ top: wrapper2?.clientHeight, behavior: 'auto' })
    })
})


</script>

<style lang='scss' rel="stylesheet/scsss" scoped>
.log {
    // height: 130px;
    border: var(--el-border);
    border-radius: 4px;
    padding: 3px;
    box-sizing: border-box;

    header {
        height: 22px;
        margin-bottom: 2px;
    }

    .container {
        border: var(--el-border);
        border-radius: 4px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16) inset;
        // height: calc(100% - 18px);
        padding: 2px;
        overflow-y: auto;
        font-size: 12px;

        .item {
            .time {
                color: var(--el-color-primary);
                margin-right: 5px;
            }
        }
    }
}
</style>

<style lang="scss">
.el-dialog.log-dialog {
    .container {
        border: var(--el-border);
        border-radius: 4px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.16) inset;
        padding: 2px;
        overflow-y: auto;
        font-size: 12px;

        .item {
            .time {
                color: var(--el-color-primary);
                margin-right: 5px;
            }
        }
    }
}
</style>
