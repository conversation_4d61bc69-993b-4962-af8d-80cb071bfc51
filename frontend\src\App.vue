<script setup lang="ts">
  import { reactive, ref, watch } from "vue";
  import {
    ElConfigProvider,
    ElMessage,
    ElMessageBox,
    ElNotification,
  } from "element-plus";
  import zhCn from "element-plus/lib/locale/lang/zh-cn";
  // import { ipc } from "./apis/config";
  import { useAppStore } from "./stores/app";
  import { ipc } from "./apis/config";

  import { useSetting } from "./stores/setting";
  import { resize as appResize } from "./apis/mainWindow";
  import { resize as pddResize } from "./apis/pddWindow";
  import { throttle } from "lodash";
  import { useRoute } from "vue-router";
  import { anyRequest } from "./apis/page";
  import { delayPromise } from "./utils/common";

  let locale = ref(zhCn);
  function changeTheme () {
    document.body.classList.add("default");
  }
  changeTheme();

  const NoticeState = {
    notification: {
      lastMsg: "",
      lastTime: 0,
      count: 0
    }
  }
  const NoticeToView: (
    event: Electron.IpcRendererEvent,
    data: { type: string; data: any }
  ) => any = (event, { type, data }) => {
    switch (type) {
      case "message": {
        ElMessage(data);
        break;
      }
      case "notification": {
        const { lastMsg, lastTime, count } = NoticeState.notification
        if (data.message !== lastMsg || Date.now() - lastTime >= 3000) {
          count && (data.message += `(${count})`)
          ElNotification(data);
          NoticeState.notification.lastTime = Date.now()
          NoticeState.notification.count = 0
        }
        break;
      }
      case "messageBox": {
        ElMessageBox(data);
        break;
      }
    }
  };
  ipc.off("notice-to-view", NoticeToView)
  ipc.on("notice-to-view", NoticeToView);


  const appStore = useAppStore();
  appStore.getAppInfo_System()


  const setting = useSetting()
  setting.getAnySetting('appWindow')
    .then(res => {
      if (location.href.includes('#/pddVerify')) {
        return
      }
      if (res) {
        setting.appWindow = res
      }
    })
  const route = useRoute()
  watch(() => setting.appWindow.scale, (_scale) => {
    // const {appWidth,appHeight} = setting.appWindow
    if (location.href.includes('#/pddVerify')) {
      return
    }
    let scale = _scale
    if (_scale > 2) {
      scale = _scale / 100
    }
    setting.setAnySetting('appWindow', {
      ...setting.appWindow,
      scale
    })
    if (route.name === 'Login') {
      appResize({
        scale,
        width: 1212,
        height: 760
      })
    } else {
      appResize()
    }

    pddResize()
    // @ts-ignore
    document.documentElement.style.zoom = scale
  }, { immediate: true })


  function getServerTime () {
    anyRequest({
      url: 'https://f.m.suning.com/api/ct.do',
      method: 'get'
    })
      .then(res => {
        // console.log(res)
        if (res.code == 1) {
          appStore.time_diff = res.currentTime - Date.now()
        }
      })
      .finally(async () => {
        await delayPromise(30000)
        getServerTime()
      })
  }
  getServerTime()


</script>

<template>
  <div id="app" ref="app">
    <el-config-provider :locale="locale" :size="'small'">
      <router-view />
    </el-config-provider>
  </div>
</template>

<style scoped></style>

<style lang="scss">
  body {
    box-sizing: border-box;
    margin: 0;
    overflow: hidden;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
      margin: 0;
    }

    --app-width: 1550px;
    --app-height: 920px;
    // --app-height: 1280px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // height: 100%;
    // height: 100vh;
  }

  body.login {
    --app-width: 1200px;
    --app-height: 660px;
    padding-top: 50px;
  }

  body.pddVerify {
    --app-width: 800px;
    --app-height: 600px;

  }


  #app {
    margin: 0;
    box-sizing: border-box;
    width: var(--app-width);
    height: var(--app-height);
    box-sizing: border-box;

    // border-radius: 10px;
    // border: 1px solid #ccc;
    overflow: hidden;
    color: var(--el-text-color-primary);
  }


  .icon {
    width: 1.2em;
    height: 1.2em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
</style>