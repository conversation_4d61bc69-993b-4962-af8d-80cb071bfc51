const { app }    = require( "electron" );
const appName    = "CK多多助手";
const appName_en = "ckpdd";
//const apiUrl     = "http://110.42.57.224:8301/";
const apiUrl     = "http://8.134.32.152:2636";
//const apiUrl     = "http://a.rtmaet.cn/";

/**托盘 */
const tray = {
    // 名称
    title : appName,
    // 图标
    icon : "/public/images/tray_logo.png",
};

const pddWindow     = {
    normalSize : {
        // width  : 412,
        // height : 818,
        width  : 1212,
        height : 760,
        x      : 13,
        y      : 86,
    },
};
const userAgent     = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0';
const isDev         = !app.isPackaged;
/**资源目录 */
const resourcesRoot = process.cwd() + ( isDev ? "\\build" : "\\resources" );
module.exports      = {
    appName,
    appName_en,
    tray,
    pddWindow,
    apiUrl,
    isDev,
    userAgent,
    resourcesRoot,
};
