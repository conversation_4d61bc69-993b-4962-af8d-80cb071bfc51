# 添加选中商品功能测试说明

## 功能概述
在退款管理页面的弹窗中，当用户选中商品前面的勾选框后，点击"添加选中商品"按钮时，会调用 `POST /api/ptorder/add` 接口。

## 实现要点

### 1. API调用
- **接口地址**: `POST /api/ptorder/add`
- **加密处理**: 使用项目统一的加密方式 (`encrypt: true`)
- **请求方式**: 通过 `request` 函数发送POST请求

### 2. 参数格式
```json
{
  "goods": [
    {
      "goods_name": "商品名称",
      "goods_id": "商品ID", 
      "goods_price": "商品价格",
      "jifen": "所需积分"
    }
  ]
}
```

### 3. 数据处理
- 从选中的商品中提取必要信息
- 价格从分转换为元 (`groupPrice / 100`)
- 商品ID转换为字符串
- 积分暂时设置为与价格相同（可根据业务需求调整）

### 4. 错误处理
- 检查是否选中商品
- 检查商品是否选择了SKU
- API调用失败时显示错误信息
- 记录操作日志

### 5. 成功处理
- 显示成功消息
- 清空选中状态
- 刷新退款列表
- 记录操作日志

## 测试步骤

1. 打开退款管理页面
2. 点击"获取商品"按钮打开弹窗
3. 选择店铺并获取商品列表
4. 勾选一个或多个商品
5. 确保每个选中的商品都选择了SKU
6. 点击"添加选中商品"按钮
7. 观察是否显示成功消息
8. 检查操作日志是否记录
9. 验证退款列表是否刷新

## 注意事项

- 必须先选择商品才能添加
- 每个选中的商品必须选择SKU
- 参数会自动加密处理
- 操作结果会记录在日志中
